{"doc": "\n 面试缓存服务接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "cacheSession", "paramTypes": ["org.dromara.app.domain.InterviewSession"], "doc": "\n 缓存面试会话信息\r\n\r\n @param session 会话信息\r\n"}, {"name": "getCachedSession", "paramTypes": ["java.lang.String"], "doc": "\n 获取缓存的会话信息\r\n\r\n @param sessionId 会话ID\r\n @return 会话信息\r\n"}, {"name": "evictSession", "paramTypes": ["java.lang.String"], "doc": "\n 删除会话缓存\r\n\r\n @param sessionId 会话ID\r\n"}, {"name": "cacheSessionQuestions", "paramTypes": ["java.lang.String", "java.util.List"], "doc": "\n 缓存会话问题列表\r\n\r\n @param sessionId 会话ID\r\n @param questions 问题列表\r\n"}, {"name": "getCachedSessionQuestions", "paramTypes": ["java.lang.String"], "doc": "\n 获取缓存的会话问题列表\r\n\r\n @param sessionId 会话ID\r\n @return 问题列表\r\n"}, {"name": "evictSessionQuestions", "paramTypes": ["java.lang.String"], "doc": "\n 删除会话问题缓存\r\n\r\n @param sessionId 会话ID\r\n"}, {"name": "cacheUserActiveSession", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 缓存用户当前活跃会话\r\n\r\n @param userId    用户ID\r\n @param sessionId 会话ID\r\n"}, {"name": "getUserActiveSession", "paramTypes": ["java.lang.Long"], "doc": "\n 获取用户当前活跃会话\r\n\r\n @param userId 用户ID\r\n @return 会话ID\r\n"}, {"name": "evictUserActiveSession", "paramTypes": ["java.lang.Long"], "doc": "\n 删除用户活跃会话缓存\r\n\r\n @param userId 用户ID\r\n"}, {"name": "cacheInterviewResult", "paramTypes": ["java.lang.String", "org.dromara.app.domain.vo.InterviewResponseVo.InterviewResult"], "doc": "\n 缓存面试结果\r\n\r\n @param sessionId 会话ID\r\n @param result    面试结果\r\n"}, {"name": "getCachedInterviewResult", "paramTypes": ["java.lang.String"], "doc": "\n 获取缓存的面试结果\r\n\r\n @param sessionId 会话ID\r\n @return 面试结果\r\n"}, {"name": "evictInterviewResult", "paramTypes": ["java.lang.String"], "doc": "\n 删除面试结果缓存\r\n\r\n @param sessionId 会话ID\r\n"}, {"name": "cacheAiEvaluation", "paramTypes": ["java.lang.String", "java.lang.String", "org.dromara.app.domain.vo.InterviewResponseVo.FeedbackInfo"], "doc": "\n 缓存AI评估结果\r\n\r\n @param questionId 问题ID\r\n @param answer     答案内容\r\n @param feedback   评估结果\r\n"}, {"name": "getCachedAiEvaluation", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取缓存的AI评估结果\r\n\r\n @param questionId 问题ID\r\n @param answer     答案内容\r\n @return 评估结果\r\n"}, {"name": "cachePopularQuestions", "paramTypes": ["java.lang.Long", "java.util.List"], "doc": "\n 缓存热门问题\r\n\r\n @param jobId     岗位ID\r\n @param questions 问题列表\r\n"}, {"name": "getCachedPopularQuestions", "paramTypes": ["java.lang.Long"], "doc": "\n 获取缓存的热门问题\r\n\r\n @param jobId 岗位ID\r\n @return 问题列表\r\n"}, {"name": "cacheUserStats", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.InterviewResponseVo.UserStats"], "doc": "\n 缓存用户统计信息\r\n\r\n @param userId 用户ID\r\n @param stats  统计信息\r\n"}, {"name": "getCachedUserStats", "paramTypes": ["java.lang.Long"], "doc": "\n 获取缓存的用户统计信息\r\n\r\n @param userId 用户ID\r\n @return 统计信息\r\n"}, {"name": "warmupCache", "paramTypes": ["java.lang.String"], "doc": "\n 预热缓存\r\n\r\n @param sessionId 会话ID\r\n"}, {"name": "cleanExpiredCache", "paramTypes": [], "doc": "\n 清理过期缓存\r\n"}, {"name": "clearAllCache", "paramTypes": [], "doc": "\n 清理所有缓存\r\n"}, {"name": "getCacheStats", "paramTypes": [], "doc": "\n 获取缓存统计信息\r\n\r\n @return 缓存统计\r\n"}], "constructors": []}