{"doc": "\n 支付订单请求DTO\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "productId", "doc": "\n 商品ID\r\n"}, {"name": "productType", "doc": "\n 商品类型\r\n"}, {"name": "productTitle", "doc": "\n 商品标题\r\n"}, {"name": "amount", "doc": "\n 支付金额\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "paymentMethod", "doc": "\n 支付方式\r\n"}, {"name": "clientIp", "doc": "\n 客户端IP\r\n"}, {"name": "userAgent", "doc": "\n 用户代理\r\n"}, {"name": "remark", "doc": "\n 备注信息\r\n"}], "enumConstants": [], "methods": [], "constructors": []}