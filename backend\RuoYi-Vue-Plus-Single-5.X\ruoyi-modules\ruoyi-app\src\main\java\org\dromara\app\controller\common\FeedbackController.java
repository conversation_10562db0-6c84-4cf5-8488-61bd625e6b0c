package org.dromara.app.controller.common;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.app.domain.bo.FeedbackBo;
import org.dromara.app.domain.vo.FeedbackVo;
import org.dromara.app.service.IFeedbackService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.core.validate.QueryGroup;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 意见反馈控制器
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/feedback")
public class FeedbackController extends BaseController {

    private final IFeedbackService feedbackService;

    /**
     * 查询意见反馈分页列表
     */
    @SaCheckLogin
    @GetMapping("/list")
    public TableDataInfo<FeedbackVo> list(@Validated(QueryGroup.class) FeedbackBo bo, PageQuery pageQuery) {
        // 设置当前用户ID，只查询当前用户的反馈
        Long userId = StpUtil.getLoginIdAsLong();
        bo.setUserId(userId);
        return feedbackService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询当前用户的反馈列表
     */
    @SaCheckLogin
    @GetMapping("/my")
    public R<List<FeedbackVo>> getMyFeedbackList() {
        Long userId = StpUtil.getLoginIdAsLong();
        List<FeedbackVo> list = feedbackService.selectUserFeedbackList(userId);
        return R.ok(list);
    }

    /**
     * 获取意见反馈详细信息
     *
     * @param id 反馈主键
     */
    @SaCheckLogin
    @GetMapping("/{id}")
    public R<FeedbackVo> getInfo(@NotNull(message = "反馈ID不能为空")
                                 @PathVariable Long id) {
        FeedbackVo feedbackVo = feedbackService.queryById(id);
        // 检查是否为当前用户的反馈
        Long userId = StpUtil.getLoginIdAsLong();
        if (!userId.equals(feedbackVo.getUserId())) {
            return R.fail("无权访问该反馈信息");
        }
        return R.ok(feedbackVo);
    }

    /**
     * 新增意见反馈
     */
    @SaCheckLogin
    @Log(title = "意见反馈", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Map<String, String>> add(@Validated(AddGroup.class) @RequestBody FeedbackBo bo) {
        Long userId = StpUtil.getLoginIdAsLong();
        bo.setUserId(userId);
        // 设置默认状态为待处理
        bo.setStatus("PENDING");

        boolean result = feedbackService.insertByBo(bo);
        if (result) {
            Map<String, String> response = new HashMap<>();
            response.put("id", bo.getId().toString());
            return R.ok("反馈提交成功", response);
        }
        return R.fail("反馈提交失败");
    }

    /**
     * 修改意见反馈（仅限用户修改自己的反馈）
     */
    @SaCheckLogin
    @Log(title = "意见反馈", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FeedbackBo bo) {
        // 检查是否为当前用户的反馈
        Long userId = StpUtil.getLoginIdAsLong();
        FeedbackVo existingFeedback = feedbackService.queryById(bo.getId());
        if (existingFeedback == null) {
            return R.fail("反馈不存在");
        }
        if (!userId.equals(existingFeedback.getUserId())) {
            return R.fail("无权修改该反馈");
        }
        // 用户只能修改待处理状态的反馈
        if (!"PENDING".equals(existingFeedback.getStatus())) {
            return R.fail("只能修改待处理状态的反馈");
        }

        return toAjax(feedbackService.updateByBo(bo));
    }

    /**
     * 删除意见反馈
     *
     * @param ids 反馈主键串
     */
    @SaCheckLogin
    @Log(title = "意见反馈", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "反馈ID不能为空")
                          @PathVariable Long[] ids) {
        // 检查所有反馈是否都属于当前用户
        Long userId = StpUtil.getLoginIdAsLong();
        List<Long> idList = Arrays.asList(ids);
        for (Long id : idList) {
            FeedbackVo feedback = feedbackService.queryById(id);
            if (feedback == null || !userId.equals(feedback.getUserId())) {
                return R.fail("无权删除该反馈");
            }
            // 用户只能删除待处理状态的反馈
            if (!"PENDING".equals(feedback.getStatus())) {
                return R.fail("只能删除待处理状态的反馈");
            }
        }

        return toAjax(feedbackService.deleteWithValidByIds(idList, true));
    }

    /**
     * 获取反馈统计信息
     */
    @SaCheckLogin
    @GetMapping("/stats")
    public R<Map<String, Object>> getStats() {
        Long userId = StpUtil.getLoginIdAsLong();
        Map<String, Object> stats = feedbackService.getUserFeedbackStats(userId);
        return R.ok(stats);
    }

    /**
     * 撤销反馈（用户可撤销待处理状态的反馈）
     *
     * @param id 反馈ID
     */
    @SaCheckLogin
    @Log(title = "撤销反馈", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/cancel")
    public R<Void> cancelFeedback(@PathVariable Long id) {
        Long userId = StpUtil.getLoginIdAsLong();
        FeedbackVo feedback = feedbackService.queryById(id);

        if (feedback == null) {
            return R.fail("反馈不存在");
        }
        if (!userId.equals(feedback.getUserId())) {
            return R.fail("无权操作该反馈");
        }
        if (!"PENDING".equals(feedback.getStatus())) {
            return R.fail("只能撤销待处理状态的反馈");
        }

        FeedbackBo bo = new FeedbackBo();
        bo.setId(id);
        bo.setStatus("CLOSED");
        bo.setReply("用户主动撤销");

        return toAjax(feedbackService.updateByBo(bo));
    }
}
