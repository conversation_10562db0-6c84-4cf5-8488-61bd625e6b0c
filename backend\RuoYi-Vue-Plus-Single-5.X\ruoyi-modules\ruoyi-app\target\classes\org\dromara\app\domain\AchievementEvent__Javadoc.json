{"doc": "\n 成就事件实体\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [{"name": "id", "doc": "\n 事件ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "eventType", "doc": "\n 事件类型\r\n"}, {"name": "eventData", "doc": "\n 事件数据（JSON格式）\r\n"}, {"name": "eventTime", "doc": "\n 事件时间\r\n"}, {"name": "relatedObjectId", "doc": "\n 关联对象ID\r\n"}, {"name": "relatedObjectType", "doc": "\n 关联对象类型\r\n"}, {"name": "eventValue", "doc": "\n 事件值（计数类事件）\r\n"}, {"name": "eventSource", "doc": "\n 事件来源\r\n"}, {"name": "sessionId", "doc": "\n 会话ID\r\n"}, {"name": "clientInfo", "doc": "\n 客户端信息\r\n"}, {"name": "processStatus", "doc": "\n 处理状态（0=未处理，1=已处理，2=处理失败）\r\n"}, {"name": "processTime", "doc": "\n 处理时间\r\n"}, {"name": "processResult", "doc": "\n 处理结果（JSON格式）\r\n"}, {"name": "triggeredAchievements", "doc": "\n 触发的成就IDs（用逗号分隔）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}