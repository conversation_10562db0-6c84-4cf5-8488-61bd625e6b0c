{"doc": "\n 面试事件监听器\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleInterviewStarted", "paramTypes": ["org.dromara.app.event.InterviewEvents.InterviewStartedEvent"], "doc": "\n 监听面试开始事件\r\n"}, {"name": "handleQuestionAnswered", "paramTypes": ["org.dromara.app.event.InterviewEvents.QuestionAnsweredEvent"], "doc": "\n 监听问题回答事件\r\n"}, {"name": "handleQuestionSkipped", "paramTypes": ["org.dromara.app.event.InterviewEvents.QuestionSkippedEvent"], "doc": "\n 监听问题跳过事件\r\n"}, {"name": "handleInterviewCompleted", "paramTypes": ["org.dromara.app.event.InterviewEvents.InterviewCompletedEvent"], "doc": "\n 监听面试完成事件\r\n"}, {"name": "handleAiEvaluation", "paramTypes": ["org.dromara.app.event.InterviewEvents.AiEvaluationEvent"], "doc": "\n 监听AI评估事件\r\n"}, {"name": "handleSystemError", "paramTypes": ["org.dromara.app.event.InterviewEvents.SystemErrorEvent"], "doc": "\n 监听系统错误事件\r\n"}], "constructors": []}