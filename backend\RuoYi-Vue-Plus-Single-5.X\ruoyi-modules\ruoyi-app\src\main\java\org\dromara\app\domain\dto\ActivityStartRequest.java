package org.dromara.app.domain.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.app.domain.enums.ActivityType;

import java.util.Map;

/**
 * 开始活动请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class ActivityStartRequest {

    /**
     * 会话ID
     */
    @NotBlank(message = "会话ID不能为空")
    private String sessionId;

    /**
     * 活动类型
     */
    @NotNull(message = "活动类型不能为空")
    private ActivityType type;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 活动对象ID(如题目ID、课程ID等)
     */
    private String activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 分类ID(如题库ID、课程分类ID等)
     */
    private String categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 额外元数据
     */
    private Map<String, Object> metadata;
}
