{"doc": "\n 问题标签Mapper接口\r\n\r\n <AUTHOR> Assistant\r\n @date 2025-07-19\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByCategory", "paramTypes": ["java.lang.String"], "doc": "\n 根据分类查询标签\r\n\r\n @param category 标签分类\r\n @return 标签列表\r\n"}, {"name": "selectHotTags", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询热门标签\r\n\r\n @param limit 限制数量\r\n @return 热门标签列表\r\n"}, {"name": "incrementUsageCount", "paramTypes": ["java.lang.String"], "doc": "\n 增加标签使用次数\r\n\r\n @param tagName 标签名称\r\n @return 影响行数\r\n"}, {"name": "selectByNames", "paramTypes": ["java.util.List"], "doc": "\n 根据名称查询标签\r\n\r\n @param names 标签名称列表\r\n @return 标签列表\r\n"}], "constructors": []}