{"doc": " 书籍阅读记录Service业务层处理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryByUserIdAndBookId", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 根据用户ID和书籍ID查询阅读记录\n"}, {"name": "queryUserReadingHistory", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.Long"], "doc": " 查询用户的阅读历史（分页）\n"}, {"name": "queryRecentReading", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询用户最近阅读的书籍\n"}, {"name": "queryUserReadingStats", "paramTypes": ["java.lang.Long"], "doc": " 统计用户阅读数据\n"}, {"name": "saveOrUpdateReadingRecord", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.util.Map"], "doc": " 保存或更新阅读记录\n"}, {"name": "mark<PERSON><PERSON><PERSON>erCompleted", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.String"], "doc": " 标记章节已完成\n"}, {"name": "markBookFinished", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 标记书籍已读完\n"}, {"name": "addReadingTime", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Integer"], "doc": " 增加阅读时长\n"}, {"name": "deleteReadingRecord", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 删除阅读记录\n"}, {"name": "processReadingRecordData", "paramTypes": ["org.dromara.app.domain.BookReadingRecord"], "doc": " 处理阅读记录数据转换\n"}, {"name": "convertMapToJson", "paramTypes": ["java.util.Map"], "doc": " 将Map转换为JSON字符串\n"}, {"name": "calculateReadingStreak", "paramTypes": ["java.util.List"], "doc": " 计算连续阅读天数\n"}], "constructors": []}