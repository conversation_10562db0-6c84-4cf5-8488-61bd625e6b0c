{"doc": "\n 用户行为记录Service接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "processUserBehavior", "paramTypes": ["org.dromara.app.domain.dto.TrackEventDto"], "doc": "\n 处理用户行为数据\r\n\r\n @param trackEventDto 埋点事件数据\r\n"}, {"name": "batchProcessUserBehavior", "paramTypes": ["java.util.List"], "doc": "\n 批量处理用户行为数据\r\n\r\n @param trackEventDtos 埋点事件数据列表\r\n"}, {"name": "insertUserBehavior", "paramTypes": ["org.dromara.app.domain.bo.UserBehaviorBo"], "doc": "\n 保存用户行为记录\r\n\r\n @param userBehaviorBo 用户行为业务对象\r\n @return 保存结果\r\n"}, {"name": "batchInsertUserBehavior", "paramTypes": ["java.util.List"], "doc": "\n 批量保存用户行为记录\r\n\r\n @param userBehaviorBos 用户行为业务对象列表\r\n @return 保存结果\r\n"}, {"name": "queryByUserIdAndBehaviorType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 根据用户ID和行为类型查询行为记录\r\n\r\n @param userId       用户ID\r\n @param behaviorType 行为类型\r\n @return 行为记录列表\r\n"}, {"name": "countByUserIdAndBehaviorType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 统计用户某种行为的次数\r\n\r\n @param userId       用户ID\r\n @param behaviorType 行为类型\r\n @return 行为次数\r\n"}, {"name": "getConsecutiveLoginDays", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户连续登录天数\r\n\r\n @param userId 用户ID\r\n @return 连续登录天数\r\n"}, {"name": "getTotalStudyMinutes", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户累计学习时长（分钟）\r\n\r\n @param userId 用户ID\r\n @return 累计学习时长\r\n"}], "constructors": []}