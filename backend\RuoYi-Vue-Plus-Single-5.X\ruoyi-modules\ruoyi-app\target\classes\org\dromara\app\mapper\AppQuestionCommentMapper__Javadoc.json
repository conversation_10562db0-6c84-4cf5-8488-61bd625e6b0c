{"doc": "\n 题目评论Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectCommentListWithReplies", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.Long", "java.lang.String", "java.lang.String"], "doc": "\n 分页查询题目评论列表（包含用户信息和回复列表）\r\n\r\n @param page           分页对象\r\n @param questionId     题目ID\r\n @param orderBy        排序字段\r\n @param orderDirection 排序方向\r\n @return 评论列表\r\n"}, {"name": "selectRepliesByParentId", "paramTypes": ["java.lang.Long"], "doc": "\n 获取评论的回复列表\r\n\r\n @param parentId 父评论ID\r\n @return 回复列表\r\n"}, {"name": "selectLikeStatus", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 根据用户ID和评论ID查询点赞状态\r\n\r\n @param userId    用户ID\r\n @param commentId 评论ID\r\n @return 是否已点赞\r\n"}, {"name": "incrementLikeCount", "paramTypes": ["java.lang.Long"], "doc": "\n 增加评论点赞数\r\n\r\n @param commentId 评论ID\r\n @return 更新结果\r\n"}, {"name": "decrementLikeCount", "paramTypes": ["java.lang.Long"], "doc": "\n 减少评论点赞数\r\n\r\n @param commentId 评论ID\r\n @return 更新结果\r\n"}, {"name": "incrementReplyCount", "paramTypes": ["java.lang.Long"], "doc": "\n 增加回复数\r\n\r\n @param commentId 评论ID\r\n @return 更新结果\r\n"}, {"name": "decrementReplyCount", "paramTypes": ["java.lang.Long"], "doc": "\n 减少回复数\r\n\r\n @param commentId 评论ID\r\n @return 更新结果\r\n"}, {"name": "insertLikeRecord", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 插入点赞记录\r\n\r\n @param commentId 评论ID\r\n @param userId    用户ID\r\n @return 插入结果\r\n"}, {"name": "deleteLikeRecord", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 删除点赞记录\r\n\r\n @param commentId 评论ID\r\n @param userId    用户ID\r\n @return 删除结果\r\n"}, {"name": "updateQuestionCommentCount", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 更新题目评论数\r\n\r\n @param questionId 题目ID\r\n @param increment  增量（正数增加，负数减少）\r\n @return 更新结果\r\n"}], "constructors": []}