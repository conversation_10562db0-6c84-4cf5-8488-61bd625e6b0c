{"doc": "\n 问题管理增强Service业务层处理\r\n\r\n <AUTHOR> Assistant\r\n @date 2025-07-19\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "createDifficultyLevel", "paramTypes": ["java.lang.Integer", "java.lang.String", "java.lang.String", "java.util.List", "java.lang.Integer", "java.lang.Double"], "doc": "\n 创建难度级别\r\n"}, {"name": "identifyMissingAreas", "paramTypes": ["org.dromara.app.domain.Job", "java.util.List"], "doc": "\n 识别缺失领域\r\n"}, {"name": "getRequiredCategoriesForDomain", "paramTypes": ["java.lang.String"], "doc": "\n 获取技术领域必需的问题类型\r\n"}, {"name": "calculateCoverageScore", "paramTypes": ["java.util.List", "java.util.List"], "doc": "\n 计算覆盖度分数\r\n"}], "constructors": []}