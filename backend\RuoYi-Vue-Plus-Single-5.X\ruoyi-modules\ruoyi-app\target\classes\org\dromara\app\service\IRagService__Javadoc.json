{"doc": "\n RAG知识库服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryPageList", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeBaseBo"], "doc": "\n 分页查询知识库列表\r\n\r\n @param bo 查询条件\r\n @return 分页结果\r\n"}, {"name": "queryList", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeBaseBo"], "doc": "\n 查询知识库列表\r\n\r\n @param bo 查询条件\r\n @return 知识库列表\r\n"}, {"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": "\n 根据ID查询知识库详情\r\n\r\n @param id 知识库ID\r\n @return 知识库详情\r\n"}, {"name": "queryDocumentPageList", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeDocumentBo"], "doc": "\n 分页查询知识库文档列表\r\n\r\n @param bo 查询条件\r\n @return 分页结果\r\n"}, {"name": "queryDocumentList", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeDocumentBo"], "doc": "\n 查询知识库文档列表\r\n\r\n @param bo 查询条件\r\n @return 文档列表\r\n"}, {"name": "queryDocumentById", "paramTypes": ["java.lang.Long"], "doc": "\n 根据ID查询文档详情\r\n\r\n @param id 文档ID\r\n @return 文档详情\r\n"}, {"name": "createKnowledgeBase", "paramTypes": ["org.dromara.app.domain.KnowledgeBase"], "doc": "\n 创建知识库\r\n\r\n @param knowledgeBase 知识库信息\r\n @return 创建结果\r\n"}, {"name": "addDocument", "paramTypes": ["org.dromara.app.domain.KnowledgeDocument"], "doc": "\n 添加文档到知识库\r\n\r\n @param document 文档信息\r\n @return 添加结果\r\n"}, {"name": "processDocument", "paramTypes": ["java.lang.Long"], "doc": "\n 处理文档（分块、向量化）\r\n\r\n @param documentId 文档ID\r\n @return 处理结果\r\n"}, {"name": "searchKnowledge", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer"], "doc": "\n 搜索相关知识\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @param query           查询文本\r\n @param topK            返回数量\r\n @return 相关文档列表\r\n"}, {"name": "hybridSearch", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer"], "doc": "\n 混合搜索（向量 + 关键词）\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @param query           查询文本\r\n @param topK            返回数量\r\n @return 相关文档列表\r\n"}, {"name": "getKnowledgeBases", "paramTypes": [], "doc": "\n 获取知识库列表\r\n\r\n @return 知识库列表\r\n"}, {"name": "getKnowledgeBase", "paramTypes": ["java.lang.Long"], "doc": "\n 获取知识库详情\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @return 知识库信息\r\n"}, {"name": "getDocuments", "paramTypes": ["java.lang.Long"], "doc": "\n 获取知识库文档列表\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @return 文档列表\r\n"}, {"name": "deleteDocument", "paramTypes": ["java.lang.Long"], "doc": "\n 删除文档\r\n\r\n @param documentId 文档ID\r\n @return 删除结果\r\n"}, {"name": "deleteKnowledgeBase", "paramTypes": ["java.lang.Long"], "doc": "\n 删除知识库\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @return 删除结果\r\n"}, {"name": "updateKnowledgeBase", "paramTypes": ["org.dromara.app.domain.KnowledgeBase"], "doc": "\n 更新知识库\r\n\r\n @param knowledgeBase 知识库信息\r\n @return 更新结果\r\n"}, {"name": "rebuildIndex", "paramTypes": ["java.lang.Long"], "doc": "\n 重建知识库索引\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @return 重建结果\r\n"}, {"name": "getKnowledgeBaseStats", "paramTypes": ["java.lang.Long"], "doc": "\n 获取知识库统计信息\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @return 统计信息\r\n"}], "constructors": []}