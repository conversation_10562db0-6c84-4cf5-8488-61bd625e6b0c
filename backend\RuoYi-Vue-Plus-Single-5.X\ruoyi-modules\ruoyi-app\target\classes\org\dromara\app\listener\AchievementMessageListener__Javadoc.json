{"doc": "\n 成就系统消息监听器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleAchievementCheck", "paramTypes": ["org.dromara.app.domain.dto.TrackEventDto", "org.springframework.amqp.core.Message", "com.rabbitmq.client.Channel"], "doc": "\n 处理成就检查消息\r\n"}, {"name": "handleAchievementNotification", "paramTypes": ["java.lang.String", "org.springframework.amqp.core.Message", "com.rabbitmq.client.Channel"], "doc": "\n 处理成就通知消息\r\n"}, {"name": "handleDeadLetterMessage", "paramTypes": ["java.lang.String", "org.springframework.amqp.core.Message", "com.rabbitmq.client.Channel"], "doc": "\n 处理死信队列消息\r\n"}, {"name": "processAchievementNotification", "paramTypes": ["java.lang.String"], "doc": "\n 处理成就通知的具体逻辑\r\n"}], "constructors": []}