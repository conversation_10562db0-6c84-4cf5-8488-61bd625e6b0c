package org.dromara.common.pay.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.util.StringUtils;

/**
 * 支付宝配置属性类
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "pay.alipay")
public class AlipayProperties {

    /**
     * 支付宝功能开关
     */
    private Boolean enabled = false;

    /**
     * 环境类型 - sandbox(沙箱) 或 production(生产)
     */
    private String environment = "sandbox";

    /**
     * 应用ID - 支付宝应用ID
     */
    private String appId;

    /**
     * 商户私钥 - 应用私钥
     */
    private String privateKey;

    /**
     * 支付宝公钥 - 支付宝公钥
     */
    private String alipayPublicKey;

    /**
     * 服务器异步通知页面路径 - 支付成功后支付宝异步通知的地址
     */
    private String notifyUrl;

    /**
     * 页面跳转同步通知页面路径 - 支付成功后页面跳转的地址
     */
    private String returnUrl;

    /**
     * 应用服务器地址 - 用于构建回调URL的基础地址
     */
    private String serverUrl = "http://localhost:8080";

    /**
     * 签名方式 - 默认RSA2
     */
    private String signType = "RSA2";

    /**
     * 字符编码格式 - 默认UTF-8
     */
    private String charset = "UTF-8";

    /**
     * 返回格式 - 默认JSON
     */
    private String format = "JSON";

    /**
     * 获取支付宝网关地址
     * 根据环境类型自动选择对应的网关地址
     *
     * @return 网关地址
     */
    public String getGatewayUrl() {
        if ("production".equals(environment)) {
            return "https://openapi.alipay.com/gateway.do";
        } else {
            return "https://openapi-sandbox.dl.alipaydev.com/gateway.do";
        }
    }

    /**
     * 是否为沙箱环境
     *
     * @return true-沙箱环境 false-生产环境
     */
    public boolean isSandbox() {
        return "sandbox".equals(environment);
    }

    /**
     * 获取异步通知URL，如果未配置则使用默认值
     *
     * @return 异步通知URL
     */
    public String getNotifyUrl() {
        return StringUtils.hasText(notifyUrl) ? notifyUrl : buildDefaultNotifyUrl();
    }

    /**
     * 设置异步通知URL
     *
     * @param notifyUrl 异步通知URL
     */
    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    /**
     * 获取同步回调URL，如果未配置则使用默认值
     *
     * @return 同步回调URL
     */
    public String getReturnUrl() {
        return StringUtils.hasText(returnUrl) ? returnUrl : buildDefaultReturnUrl();
    }

    /**
     * 设置同步回调URL
     *
     * @param returnUrl 同步回调URL
     */
    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }

    /**
     * 构建默认的异步通知URL
     *
     * @return 默认异步通知URL
     */
    private String buildDefaultNotifyUrl() {
        return normalizeServerUrl() + "/app/pay/notify";
    }

    /**
     * 构建默认的同步回调URL
     *
     * @return 默认同步回调URL
     */
    private String buildDefaultReturnUrl() {
        return normalizeServerUrl() + "/app/pay/return";
    }

    /**
     * 规范化服务器URL，确保不以斜杠结尾
     *
     * @return 规范化后的服务器URL
     */
    private String normalizeServerUrl() {
        String url = StringUtils.hasText(serverUrl) ? serverUrl : "http://localhost:8080";
        return url.endsWith("/") ? url.substring(0, url.length() - 1) : url;
    }
}
