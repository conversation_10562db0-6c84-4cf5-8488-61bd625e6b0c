{"doc": "\n 任务队列管理服务接口\r\n 用于管理分析任务的队列和调度\r\n\r\n <AUTHOR>\r\n @date 2025-01-19\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "startTaskProcessor", "paramTypes": [], "doc": "\n 启动任务队列处理器\r\n"}, {"name": "stopTaskProcessor", "paramTypes": [], "doc": "\n 停止任务队列处理器\r\n"}, {"name": "getQueueStatus", "paramTypes": [], "doc": "\n 获取队列状态\r\n\r\n @return 队列状态信息\r\n"}, {"name": "getPendingTasks", "paramTypes": ["int"], "doc": "\n 获取待处理任务列表\r\n\r\n @param limit 限制数量\r\n @return 任务列表\r\n"}, {"name": "getRunningTasks", "paramTypes": [], "doc": "\n 获取正在执行的任务列表\r\n\r\n @return 任务列表\r\n"}, {"name": "getCompletedTasks", "paramTypes": ["int"], "doc": "\n 获取已完成的任务列表\r\n\r\n @param limit 限制数量\r\n @return 任务列表\r\n"}, {"name": "cleanExpiredTasks", "paramTypes": [], "doc": "\n 清理过期任务\r\n\r\n @return 清理的任务数量\r\n"}, {"name": "retryFailedTask", "paramTypes": ["java.lang.String"], "doc": "\n 重试失败的任务\r\n\r\n @param taskId 任务ID\r\n @return 是否重试成功\r\n"}, {"name": "setMaxQueueCapacity", "paramTypes": ["int"], "doc": "\n 设置队列最大容量\r\n\r\n @param maxCapacity 最大容量\r\n"}, {"name": "setConcurrency", "paramTypes": ["int"], "doc": "\n 设置并发执行数量\r\n\r\n @param concurrency 并发数量\r\n"}, {"name": "pauseQueue", "paramTypes": [], "doc": "\n 暂停队列处理\r\n"}, {"name": "resumeQueue", "paramTypes": [], "doc": "\n 恢复队列处理\r\n"}, {"name": "isQueuePaused", "paramTypes": [], "doc": "\n 获取队列是否暂停\r\n\r\n @return 是否暂停\r\n"}], "constructors": []}