{"doc": " 成就通知服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendAchievementUnlockNotification", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.AchievementVo"], "doc": " 发送成就解锁通知\n\n @param userId      用户ID\n @param achievement 成就信息\n"}, {"name": "batchSendAchievementUnlockNotification", "paramTypes": ["java.lang.Long", "java.util.List"], "doc": " 批量发送成就解锁通知\n\n @param userId       用户ID\n @param achievements 成就列表\n"}, {"name": "sendAchievementProgressNotification", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.UserAchievementVo"], "doc": " 发送成就进度更新通知\n\n @param userId           用户ID\n @param userAchievement  用户成就信息\n"}, {"name": "sendAchievementReminderNotification", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.AchievementVo", "java.lang.String"], "doc": " 发送成就提醒通知\n\n @param userId      用户ID\n @param achievement 成就信息\n @param message     提醒消息\n"}, {"name": "sendLeaderboardNotification", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.String"], "doc": " 发送成就排行榜通知\n\n @param userId   用户ID\n @param ranking  排名\n @param category 类别\n"}, {"name": "markNotificationAsRead", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 标记通知为已读\n\n @param userId         用户ID\n @param notificationId 通知ID\n @return 操作结果\n"}, {"name": "getUnreadNotificationCount", "paramTypes": ["java.lang.Long"], "doc": " 获取用户未读通知数量\n\n @param userId 用户ID\n @return 未读通知数量\n"}, {"name": "getUserNotifications", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 获取用户通知列表\n\n @param userId 用户ID\n @param limit  限制数量\n @return 通知列表\n"}, {"name": "cleanExpiredNotifications", "paramTypes": ["java.lang.Integer"], "doc": " 清理过期通知\n\n @param days 保留天数\n @return 清理数量\n"}], "constructors": []}