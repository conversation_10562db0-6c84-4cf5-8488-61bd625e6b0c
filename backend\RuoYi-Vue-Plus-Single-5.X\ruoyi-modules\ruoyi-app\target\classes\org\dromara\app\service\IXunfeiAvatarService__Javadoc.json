{"doc": "\n 讯飞数字人服务接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-20\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "startSession", "paramTypes": ["org.dromara.app.domain.dto.avatar.AvatarStartDto"], "doc": "\n 启动数字人会话\r\n\r\n @param startDto 启动参数\r\n @return 会话信息\r\n"}, {"name": "sendTextDriver", "paramTypes": ["java.lang.String", "org.dromara.app.domain.dto.avatar.AvatarTextDto"], "doc": "\n 发送文本驱动\r\n\r\n @param sessionId 会话ID\r\n @param textDto   文本参数\r\n @return 是否发送成功\r\n"}, {"name": "sendTextInteract", "paramTypes": ["java.lang.String", "org.dromara.app.domain.dto.avatar.AvatarTextDto"], "doc": "\n 发送文本交互\r\n\r\n @param sessionId 会话ID\r\n @param textDto   文本参数\r\n @return 是否发送成功\r\n"}, {"name": "sendAudioDriver", "paramTypes": ["java.lang.String", "java.lang.String", "int"], "doc": "\n 发送音频驱动\r\n\r\n @param sessionId  会话ID\r\n @param audioData  音频数据（Base64编码）\r\n @param status     数据状态（0开始，1过渡，2结束）\r\n @return 是否发送成功\r\n"}, {"name": "resetAvatar", "paramTypes": ["java.lang.String"], "doc": "\n 重置（打断）数字人\r\n\r\n @param sessionId 会话ID\r\n @return 是否重置成功\r\n"}, {"name": "stopSession", "paramTypes": ["java.lang.String"], "doc": "\n 停止数字人会话\r\n\r\n @param sessionId 会话ID\r\n @return 是否停止成功\r\n"}, {"name": "sendHeartbeat", "paramTypes": ["java.lang.String"], "doc": "\n 发送心跳\r\n\r\n @param sessionId 会话ID\r\n @return 是否发送成功\r\n"}, {"name": "sendAction", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 发送动作指令\r\n\r\n @param sessionId   会话ID\r\n @param actionType  动作类型\r\n @param actionValue 动作值\r\n @return 是否发送成功\r\n"}, {"name": "setMessageHandler", "paramTypes": ["java.lang.String", "java.util.function.Consumer"], "doc": "\n 设置消息处理器\r\n\r\n @param sessionId      会话ID\r\n @param messageHandler 消息处理器\r\n"}, {"name": "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.util.function.Consumer"], "doc": "\n 设置错误处理器\r\n\r\n @param sessionId    会话ID\r\n @param errorHandler 错误处理器\r\n"}, {"name": "isSessionActive", "paramTypes": ["java.lang.String"], "doc": "\n 检查会话状态\r\n\r\n @param sessionId 会话ID\r\n @return 是否连接中\r\n"}, {"name": "getSessionInfo", "paramTypes": ["java.lang.String"], "doc": "\n 获取会话信息\r\n\r\n @param sessionId 会话ID\r\n @return 会话信息\r\n"}, {"name": "updateStreamUrl", "paramTypes": ["java.lang.String"], "doc": "\n 更新会话推流地址\r\n\r\n @param sessionId 会话ID\r\n @return 是否更新成功\r\n"}], "constructors": []}