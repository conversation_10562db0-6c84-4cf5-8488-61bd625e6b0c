package org.dromara.common.chat.config;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebSocket配置 - AI Agent聊天
 *
 * <AUTHOR>
 */
@AutoConfiguration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 注册AI Agent聊天的WebSocket处理器
        registry.addHandler(chatWebSocketHandler(), "/ai-chat")
            .setAllowedOrigins("*")
            .withSockJS(); // 添加SockJS支持以提高兼容性
    }

    @Bean
    public ChatWebSocketHandler chatWebSocketHandler() {
        return new ChatWebSocketHandler();
    }
}
