package org.dromara.system.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.domain.bo.QuestionBo;
import org.dromara.system.domain.vo.QuestionVo;

import java.util.Collection;
import java.util.List;

/**
 * 题目Service接口
 *
 * <AUTHOR>
 */
public interface IQuestionService {

    /**
     * 查询题目
     *
     * @param questionId 题目主键
     * @return 题目
     */
    QuestionVo queryById(Long questionId);

    /**
     * 查询题目列表
     *
     * @param bo 题目查询条件
     * @return 题目集合
     */
    List<QuestionVo> queryList(QuestionBo bo);

    /**
     * 分页查询题目列表
     *
     * @param bo        题目查询条件
     * @param pageQuery 分页参数
     * @return 题目分页集合
     */
    TableDataInfo<QuestionVo> queryPageList(QuestionBo bo, PageQuery pageQuery);

    /**
     * 根据题库ID分页查询题目列表
     *
     * @param bankId    题库ID
     * @param bo        题目查询条件
     * @param pageQuery 分页参数
     * @return 题目分页集合
     */
    TableDataInfo<QuestionVo> queryPageListByBankId(Long bankId, QuestionBo bo, PageQuery pageQuery);

    /**
     * 新增题目
     *
     * @param bo 题目信息
     * @return 新增结果
     */
    Boolean insertByBo(QuestionBo bo);

    /**
     * 修改题目
     *
     * @param bo 题目信息
     * @return 修改结果
     */
    Boolean updateByBo(QuestionBo bo);

    /**
     * 校验并批量删除题目信息
     *
     * @param ids 需要删除的题目主键集合
     * @return 删除结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids);

    /**
     * 根据题目编码查询题目
     *
     * @param questionCode 题目编码
     * @return 题目信息
     */
    QuestionVo queryByQuestionCode(String questionCode);

    /**
     * 根据题库ID查询题目列表
     *
     * @param bankId 题库ID
     * @return 题目集合
     */
    List<QuestionVo> queryByBankId(Long bankId);

    /**
     * 根据分类查询题目列表
     *
     * @param category 分类
     * @return 题目集合
     */
    List<QuestionVo> queryByCategory(String category);

    /**
     * 根据难度查询题目列表
     *
     * @param difficulty 难度
     * @return 题目集合
     */
    List<QuestionVo> queryByDifficulty(Integer difficulty);

    /**
     * 根据题目类型查询题目列表
     *
     * @param type 题目类型
     * @return 题目集合
     */
    List<QuestionVo> queryByType(Integer type);

    /**
     * 查询用户收藏的题目列表
     *
     * @param userId 用户ID
     * @return 题目集合
     */
    List<QuestionVo> queryBookmarkedQuestions(Long userId);

    /**
     * 查询热门题目列表
     *
     * @param limit 限制数量
     * @return 题目集合
     */
    List<QuestionVo> queryHotQuestions(Integer limit);

    /**
     * 随机查询题目列表
     *
     * @param bankId 题库ID
     * @param limit  限制数量
     * @return 题目集合
     */
    List<QuestionVo> queryRandomQuestions(Long bankId, Integer limit);

    /**
     * 更新题目练习次数
     *
     * @param questionId 题目ID
     * @return 更新结果
     */
    Boolean updatePracticeCount(Long questionId);

    /**
     * 更新题目评论数
     *
     * @param questionId 题目ID
     * @return 更新结果
     */
    Boolean updateCommentCount(Long questionId);

    /**
     * 更新题目正确率
     *
     * @param questionId  题目ID
     * @param correctRate 正确率
     * @return 更新结果
     */
    Boolean updateCorrectRate(Long questionId, Integer correctRate);

    /**
     * 检查题目编码是否唯一
     *
     * @param bo 题目信息
     * @return 是否唯一
     */
    Boolean checkQuestionCodeUnique(QuestionBo bo);

    /**
     * 批量导入题目
     *
     * @param list 题目列表
     * @return 导入结果
     */
    String importQuestion(List<QuestionBo> list);

    /**
     * 导出题目数据
     *
     * @param bo 题目查询条件
     * @return 题目集合
     */
    List<QuestionVo> exportQuestion(QuestionBo bo);

    /**
     * 启用/停用题目
     *
     * @param questionId 题目ID
     * @param status     状态
     * @return 操作结果
     */
    Boolean changeStatus(Long questionId, String status);

    /**
     * 复制题目
     *
     * @param questionId 源题目ID
     * @param title      新题目标题
     * @return 操作结果
     */
    Boolean copyQuestion(Long questionId, String title);

    /**
     * 根据题库ID删除题目
     *
     * @param bankId 题库ID
     * @return 删除结果
     */
    Boolean deleteByBankId(Long bankId);

    /**
     * 统计题库下的题目数量
     *
     * @param bankId 题库ID
     * @return 题目数量
     */
    Integer countByBankId(Long bankId);
}
