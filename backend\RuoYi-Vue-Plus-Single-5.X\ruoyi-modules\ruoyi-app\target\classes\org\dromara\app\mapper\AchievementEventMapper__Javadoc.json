{"doc": "\n 成就事件数据层\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectUnhandledEvents", "paramTypes": ["int"], "doc": "\n 获取未处理的事件\r\n\r\n @param limit 最大数量\r\n @return 未处理的事件列表\r\n"}, {"name": "selectUserEvents", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": "\n 查询用户特定类型的事件\r\n\r\n @param userId    用户ID\r\n @param eventType 事件类型\r\n @param limit     最大数量\r\n @return 事件列表\r\n"}, {"name": "countUserEvents", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 统计用户特定类型的事件数量\r\n\r\n @param userId    用户ID\r\n @param eventType 事件类型\r\n @return 事件数量\r\n"}, {"name": "sumUserEventValues", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 统计用户特定类型的事件值总和\r\n\r\n @param userId    用户ID\r\n @param eventType 事件类型\r\n @return 事件值总和\r\n"}, {"name": "selectRecentEvents", "paramTypes": ["java.lang.String", "int"], "doc": "\n 获取用户最近的事件\r\n\r\n @param userId 用户ID\r\n @param limit  最大数量\r\n @return 最近的事件列表\r\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "int"], "doc": "\n 批量更新事件处理状态\r\n\r\n @param eventIds 事件ID列表\r\n @param status   处理状态\r\n @return 更新行数\r\n"}, {"name": "deleteExpiredEvents", "paramTypes": ["int"], "doc": "\n 删除过期事件\r\n\r\n @param days 保留天数\r\n @return 删除行数\r\n"}], "constructors": []}