{"doc": " 题目评论管理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.QuestionCommentBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询题目评论列表\n"}, {"name": "listByQuestionId", "paramTypes": ["java.lang.Long", "org.dromara.system.domain.bo.QuestionCommentBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 根据题目ID查询评论列表\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.QuestionCommentBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出题目评论列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取题目评论详细信息\n\n @param commentId 评论主键\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.QuestionCommentBo"], "doc": " 新增题目评论\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.QuestionCommentBo"], "doc": " 修改题目评论\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除题目评论\n\n @param commentIds 评论主键串\n"}, {"name": "getByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询评论列表\n\n @param userId 用户ID\n"}, {"name": "getByParentId", "paramTypes": ["java.lang.Long"], "doc": " 根据父评论ID查询子评论列表\n\n @param parentId 父评论ID\n"}, {"name": "getCommentTree", "paramTypes": ["java.lang.Long"], "doc": " 查询评论树形结构\n\n @param questionId 题目ID\n"}, {"name": "getHotComments", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询热门评论列表\n\n @param questionId 题目ID\n @param limit      限制数量\n"}, {"name": "getLatestComments", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询最新评论列表\n\n @param questionId 题目ID\n @param limit      限制数量\n"}, {"name": "likeComment", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 点赞/取消点赞评论\n\n @param commentId 评论ID\n @param userId    用户ID\n"}, {"name": "replyComment", "paramTypes": ["org.dromara.system.domain.bo.QuestionCommentBo"], "doc": " 回复评论\n"}, {"name": "countByQuestionId", "paramTypes": ["java.lang.Long"], "doc": " 统计题目下的评论数量\n\n @param questionId 题目ID\n"}, {"name": "countByUserId", "paramTypes": ["java.lang.Long"], "doc": " 统计用户的评论数量\n\n @param userId 用户ID\n"}, {"name": "auditComment", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 审核评论\n\n @param commentId 评论ID\n @param status    审核状态\n"}, {"name": "batchAuditComments", "paramTypes": ["java.lang.Long[]", "java.lang.String"], "doc": " 批量审核评论\n\n @param commentIds 评论ID集合\n @param status     审核状态\n"}], "constructors": []}