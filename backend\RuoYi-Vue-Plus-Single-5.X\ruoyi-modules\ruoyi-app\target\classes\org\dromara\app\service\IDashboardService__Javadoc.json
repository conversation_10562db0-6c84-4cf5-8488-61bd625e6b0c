{"doc": "\n 首页仪表盘服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getDashboardSummary", "paramTypes": ["java.lang.Long"], "doc": "\n 获取首页仪表盘汇总数据\r\n\r\n @param userId 用户ID\r\n @return 仪表盘汇总数据\r\n"}, {"name": "getUserAbilities", "paramTypes": ["java.lang.Long"], "doc": "\n 获取用户能力评估数据\r\n\r\n @param userId 用户ID\r\n @return 用户能力评估数据\r\n"}, {"name": "getStudyStats", "paramTypes": ["java.lang.Long"], "doc": "\n 获取学习统计数据\r\n\r\n @param userId 用户ID\r\n @return 学习统计数据\r\n"}, {"name": "getSmartTasks", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.String"], "doc": "\n 获取智能推荐任务\r\n\r\n @param userId 用户ID\r\n @param limit  任务数量限制\r\n @param type   任务类型\r\n @return 智能推荐任务列表\r\n"}, {"name": "getRecentInterviews", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 获取最近面试记录\r\n\r\n @param userId 用户ID\r\n @param limit  记录数量限制\r\n @param page   页码\r\n @return 最近面试记录列表\r\n"}, {"name": "updateTargetPosition", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 更新用户目标岗位\r\n\r\n @param userId         用户ID\r\n @param targetPosition 目标岗位\r\n @return 更新结果\r\n"}, {"name": "completeTask", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 标记任务为已完成\r\n\r\n @param userId 用户ID\r\n @param taskId 任务ID\r\n @return 更新结果\r\n"}, {"name": "getDashboardData", "paramTypes": ["java.lang.Long"], "doc": "\n 获取首页所有数据（聚合接口）\r\n\r\n @param userId 用户ID\r\n @return 所有数据\r\n"}], "constructors": []}