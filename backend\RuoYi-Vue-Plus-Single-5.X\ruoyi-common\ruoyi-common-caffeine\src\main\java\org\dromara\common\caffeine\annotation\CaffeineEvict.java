package org.dromara.common.caffeine.annotation;

import java.lang.annotation.*;

/**
 * Caffeine缓存清除注解
 * 用于清除缓存，类似于Spring Cache的@CacheEvict
 *
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CaffeineEvict {

    /**
     * 缓存名称（可选）
     */
    String cacheName() default "";

    /**
     * 缓存键的SpEL表达式
     * 默认使用方法参数生成键
     */
    String key() default "";

    /**
     * 缓存键前缀
     */
    String keyPrefix() default "";

    /**
     * 缓存条件的SpEL表达式
     * 当条件为true时才进行清除
     */
    String condition() default "";

    /**
     * 是否清除所有缓存
     * 如果为true，则忽略key参数，清除所有缓存
     */
    boolean allEntries() default false;

    /**
     * 是否在方法执行前清除缓存
     * 默认为false，即在方法执行后清除
     */
    boolean beforeInvocation() default false;

}
