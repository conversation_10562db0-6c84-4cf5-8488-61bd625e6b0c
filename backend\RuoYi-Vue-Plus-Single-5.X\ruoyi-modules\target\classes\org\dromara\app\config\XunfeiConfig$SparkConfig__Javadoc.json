{"doc": "", "fields": [{"name": "appId", "doc": " 应用ID（已废弃，保留兼容性）\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "doc": " API密钥（已废弃，保留兼容性）\n"}, {"name": "apiSecret", "doc": " API密钥（已废弃，保留兼容性）\n"}, {"name": "apiPassword", "doc": " API密码（HTTP调用认证）\n"}, {"name": "baseUrl", "doc": " 服务地址\n"}, {"name": "model", "doc": " 模型版本\n"}, {"name": "temperature", "doc": " 默认温度\n"}, {"name": "maxTokens", "doc": " 最大Token数\n"}, {"name": "connectTimeout", "doc": " 连接超时时间（秒）\n"}, {"name": "readTimeout", "doc": " 读取超时时间（秒）\n"}], "enumConstants": [], "methods": [{"name": "getVersion", "paramTypes": [], "doc": " 获取模型版本（兼容旧版本配置）\n"}, {"name": "setVersion", "paramTypes": ["java.lang.String"], "doc": " 设置模型版本（兼容旧版本配置）\n"}], "constructors": []}