package org.dromara.app.domain.vo;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.InterviewMode;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T11:28:43+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class InterviewModeVoToInterviewModeMapperImpl implements InterviewModeVoToInterviewModeMapper {

    @Override
    public InterviewMode convert(InterviewModeVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        InterviewMode interviewMode = new InterviewMode();

        interviewMode.setColor( arg0.getColor() );
        interviewMode.setDescription( arg0.getDescription() );
        interviewMode.setDifficulty( arg0.getDifficulty() );
        interviewMode.setDuration( arg0.getDuration() );
        List<String> list = arg0.getFeatures();
        if ( list != null ) {
            interviewMode.setFeatures( new ArrayList<String>( list ) );
        }
        interviewMode.setIcon( arg0.getIcon() );
        interviewMode.setId( arg0.getId() );
        interviewMode.setName( arg0.getName() );
        interviewMode.setSortOrder( arg0.getSortOrder() );
        interviewMode.setStatus( arg0.getStatus() );

        return interviewMode;
    }

    @Override
    public InterviewMode convert(InterviewModeVo arg0, InterviewMode arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setColor( arg0.getColor() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setDifficulty( arg0.getDifficulty() );
        arg1.setDuration( arg0.getDuration() );
        if ( arg1.getFeatures() != null ) {
            List<String> list = arg0.getFeatures();
            if ( list != null ) {
                arg1.getFeatures().clear();
                arg1.getFeatures().addAll( list );
            }
            else {
                arg1.setFeatures( null );
            }
        }
        else {
            List<String> list = arg0.getFeatures();
            if ( list != null ) {
                arg1.setFeatures( new ArrayList<String>( list ) );
            }
        }
        arg1.setIcon( arg0.getIcon() );
        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setSortOrder( arg0.getSortOrder() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
