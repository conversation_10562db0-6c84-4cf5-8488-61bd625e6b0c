package org.dromara.app.domain.enums;

/**
 * 题目类型枚举
 *
 * <AUTHOR>
 */
public enum QuestionTypeEnum {

    /**
     * 单选题
     */
    SINGLE_CHOICE(1, "单选题"),

    /**
     * 多选题
     */
    MULTIPLE_CHOICE(2, "多选题"),

    /**
     * 判断题
     */
    TRUE_FALSE(3, "判断题"),

    /**
     * 简答题
     */
    SHORT_ANSWER(4, "简答题"),

    /**
     * 编程题
     */
    PROGRAMMING(5, "编程题");

    private final Integer code;
    private final String description;

    QuestionTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取描述
     *
     * @param code 类型代码
     * @return 类型描述
     */
    public static String getDescriptionByCode(Integer code) {
        if (code == null) {
            return "未知";
        }
        for (QuestionTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getDescription();
            }
        }
        return "未知";
    }

    /**
     * 根据描述获取代码
     *
     * @param description 类型描述
     * @return 类型代码
     */
    public static Integer getCodeByDescription(String description) {
        if (description == null || description.trim().isEmpty()) {
            return null;
        }
        for (QuestionTypeEnum type : values()) {
            if (type.getDescription().equals(description.trim())) {
                return type.getCode();
            }
        }
        return null;
    }

    /**
     * 根据代码获取枚举实例
     *
     * @param code 类型代码
     * @return 枚举实例
     */
    public static QuestionTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (QuestionTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据描述获取枚举实例
     *
     * @param description 类型描述
     * @return 枚举实例
     */
    public static QuestionTypeEnum getByDescription(String description) {
        if (description == null || description.trim().isEmpty()) {
            return null;
        }
        for (QuestionTypeEnum type : values()) {
            if (type.getDescription().equals(description.trim())) {
                return type;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
