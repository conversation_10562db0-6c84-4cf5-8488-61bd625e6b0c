# MongoDB配置
spring:
  data:
    mongodb:
      # MongoDB连接配置（单机模式）
      uri: mongodb://localhost:27017/dromara
      database: dromara
      host: localhost
      port: 27017
      # 认证配置（如果需要）
      # username: dromara
      # password: ruoyi123
      # authentication-database: admin

      # 连接池配置
      pool:
        # 最大连接数
        max-size: 100
        # 最小连接数
        min-size: 10
        # 连接超时时间(毫秒)
        max-connection-timeout-ms: 30000
        # 读取超时时间(毫秒)
        max-read-timeout-ms: 15000
        # 最大等待时间(毫秒)
        max-wait-time-ms: 30000
        # 连接最大空闲时间(毫秒)
        max-connection-idle-time-ms: 600000
        # 连接最大生存时间(毫秒)
        max-connection-life-time-ms: 0

# 日志配置
logging:
  level:
    org.springframework.data.mongodb: INFO
    org.dromara.common.mongodb: DEBUG
