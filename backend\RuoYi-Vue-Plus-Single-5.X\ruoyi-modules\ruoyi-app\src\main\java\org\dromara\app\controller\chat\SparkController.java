package org.dromara.app.controller.chat;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.service.IXunfeiService;
import org.dromara.app.service.impl.XunfeiServiceImpl.AgentContext;
import org.dromara.app.service.impl.XunfeiServiceImpl.StreamingChatResponseHandler;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 讯飞星火大模型聊天控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/spark")
public class SparkController extends BaseController {

    private final IXunfeiService xunfeiService;

    /**
     * 聊天接口
     */
    @PostMapping("/chat")
    public R<String> chat(@RequestBody ChatRequest request) {
        try {
            Map<String, Object> context = buildContext(request);
            String response = xunfeiService.sparkChat(request.getMessage(), context);
            return R.ok(response);
        } catch (Exception e) {
            log.error("星火大模型聊天异常", e);
            return R.fail("处理请求时发生错误: " + e.getMessage());
        }
    }

    /**
     * 流式聊天接口
     */
    @PostMapping("/chat/stream")
    public SseEmitter chatStream(@RequestBody ChatRequest request) {
        SseEmitter emitter = new SseEmitter(TimeUnit.MINUTES.toMillis(5));
        try {
            Map<String, Object> context = buildContext(request);

            xunfeiService.sparkChatStream(request.getMessage(), context, new IXunfeiService.StreamCallback() {
                @Override
                public void onToken(String token) {
                    try {
                        emitter.send(token);
                    } catch (IOException e) {
                        log.error("发送流式数据失败", e);
                    }
                }

                @Override
                public void onComplete(String fullResponse) {
                    try {
                        emitter.send("[DONE]");
                        emitter.complete();
                    } catch (IOException e) {
                        log.error("完成流式响应失败", e);
                    }
                }

                @Override
                public void onError(Throwable throwable) {
                    log.error("流式聊天错误", throwable);
                    emitter.completeWithError(throwable);
                }
            });
        } catch (Exception e) {
            log.error("创建流式聊天失败", e);
            emitter.completeWithError(e);
        }
        return emitter;
    }

    /**
     * 智能体聊天接口
     */
    @PostMapping("/agent/chat")
    public R<String> agentChat(@RequestBody AgentChatRequest request) {
        try {
            Map<String, Object> context = buildContext(request);
            String response = xunfeiService.sparkChatAgentSync(request.getMessage(), request.getAgentType(), context);
            return R.ok(response);
        } catch (Exception e) {
            log.error("星火智能体聊天异常", e);
            return R.fail("处理请求时发生错误: " + e.getMessage());
        }
    }

    /**
     * 智能体流式聊天接口
     */
    @PostMapping("/agent/chat/stream")
    public SseEmitter agentChatStream(@RequestBody AgentChatRequest request) {
        SseEmitter emitter = new SseEmitter(TimeUnit.MINUTES.toMillis(5));

        try {
            AgentContext context = new AgentContext();
            if (request.getSystemPrompt() != null) {
                context.setSystemPrompt(request.getSystemPrompt());
            }
            if (request.getHistory() != null) {
                context.setHistory(request.getHistory());
            }
            if (request.getUserId() != null) {
                context.setUserId(request.getUserId());
            }

            // 创建响应处理器
            StreamingChatResponseHandler handler = new StreamingChatResponseHandler() {
                @Override
                public void onResponse(String token) {
                    try {
                        emitter.send(token);
                    } catch (IOException e) {
                        log.error("发送流式数据失败", e);
                    }
                }

                @Override
                public void onComplete(String fullResponse) {
                    try {
                        emitter.send("[DONE]");
                        emitter.complete();
                    } catch (IOException e) {
                        log.error("完成流式响应失败", e);
                    }
                }

                @Override
                public void onError(Throwable throwable) {
                    log.error("流式聊天错误", throwable);
                    emitter.completeWithError(throwable);
                }
            };

            // 调用智能体服务
            invokeStreamingAgentService(context, request.getMessage(), request.getAgentType(),
                                      "spark", handler);

        } catch (Exception e) {
            log.error("创建智能体流式聊天失败", e);
            emitter.completeWithError(e);
        }

        return emitter;
    }

    /**
     * 调用流式Agent服务
     */
    private void invokeStreamingAgentService(AgentContext context, String enhancedMessage, String agentType,
                                             String provider, StreamingChatResponseHandler handler) {
        // 将StreamingChatResponseHandler适配为StreamCallback
        IXunfeiService.StreamCallback callback = new IXunfeiService.StreamCallback() {
            @Override
            public void onToken(String token) {
                handler.onResponse(token);
            }

            @Override
            public void onComplete(String fullResponse) {
                handler.onComplete(fullResponse);
            }

            @Override
            public void onError(Throwable throwable) {
                handler.onError(throwable);
            }
        };

        // 构建上下文参数
        Map<String, Object> contextMap = new HashMap<>();
        if (context != null) {
            // 添加系统提示
            if (context.getSystemPrompt() != null) {
                contextMap.put("systemPrompt", context.getSystemPrompt());
            }

            // 添加对话历史
            if (context.getHistory() != null) {
                contextMap.put("history", context.getHistory());
            }

            // 添加用户ID
            if (context.getUserId() != null) {
                contextMap.put("userId", context.getUserId());
            }
        }

        // 调用星火智能体服务
        xunfeiService.sparkChatAgent(enhancedMessage, agentType, contextMap, callback);
    }

    /**
     * 构建上下文
     */
    private Map<String, Object> buildContext(ChatRequest request) {
        Map<String, Object> context = new HashMap<>();

        if (request.getSystemPrompt() != null) {
            context.put("systemPrompt", request.getSystemPrompt());
        }

        if (request.getHistory() != null) {
            context.put("history", request.getHistory());
        }

        if (request.getUserId() != null) {
            context.put("userId", request.getUserId());
        }

        return context;
    }

    /**
     * 聊天请求
     */
    public static class ChatRequest {
        private String message;
        private String systemPrompt;
        private List<Map<String, String>> history;
        private String userId;

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getSystemPrompt() {
            return systemPrompt;
        }

        public void setSystemPrompt(String systemPrompt) {
            this.systemPrompt = systemPrompt;
        }

        public List<Map<String, String>> getHistory() {
            return history;
        }

        public void setHistory(List<Map<String, String>> history) {
            this.history = history;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }
    }

    /**
     * 智能体聊天请求
     */
    public static class AgentChatRequest extends ChatRequest {
        private String agentType;

        public String getAgentType() {
            return agentType;
        }

        public void setAgentType(String agentType) {
            this.agentType = agentType;
        }
    }
}
