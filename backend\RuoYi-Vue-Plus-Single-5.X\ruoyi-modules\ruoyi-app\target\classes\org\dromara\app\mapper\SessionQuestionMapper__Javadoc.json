{"doc": "\n 面试会话问题Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectBySessionIdOrderByOrder", "paramTypes": ["java.lang.String"], "doc": "\n 根据会话ID查询所有问题（按顺序）\r\n\r\n @param sessionId 会话ID\r\n @return 问题列表\r\n"}, {"name": "selectBySessionIdAndQuestionId", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据会话ID和问题ID查询问题\r\n\r\n @param sessionId  会话ID\r\n @param questionId 问题ID\r\n @return 问题\r\n"}, {"name": "selectBySessionIdAndOrder", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 根据会话ID和顺序查询问题\r\n\r\n @param sessionId     会话ID\r\n @param questionOrder 问题顺序\r\n @return 问题\r\n"}, {"name": "selectNextQuestion", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 查询会话的下一个问题\r\n\r\n @param sessionId      会话ID\r\n @param currentOrder   当前问题顺序\r\n @return 下一个问题\r\n"}, {"name": "selectCurrentQuestion", "paramTypes": ["java.lang.String"], "doc": "\n 查询会话的当前问题\r\n\r\n @param sessionId 会话ID\r\n @return 当前问题\r\n"}, {"name": "countBySessionId", "paramTypes": ["java.lang.String"], "doc": "\n 统计会话问题总数\r\n\r\n @param sessionId 会话ID\r\n @return 问题总数\r\n"}, {"name": "countBySessionIdAndStatus", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据状态统计问题数量\r\n\r\n @param sessionId 会话ID\r\n @param status    状态\r\n @return 问题数量\r\n"}, {"name": "batchInsert", "paramTypes": ["java.util.List"], "doc": "\n 批量插入问题\r\n\r\n @param questions 问题列表\r\n @return 插入数量\r\n"}, {"name": "updateStatusBySessionIdAndQuestionId", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 更新问题状态\r\n\r\n @param sessionId  会话ID\r\n @param questionId 问题ID\r\n @param status     新状态\r\n @return 更新数量\r\n"}], "constructors": []}