{"doc": "\n AI工具服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getAvailableTools", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 获取可用工具列表\r\n\r\n @param category 工具分类（可选）\r\n @param userId   用户ID\r\n @return 工具列表\r\n"}, {"name": "getToolDetail", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 获取工具详情\r\n\r\n @param toolId 工具ID\r\n @param userId 用户ID\r\n @return 工具详情\r\n"}, {"name": "checkToolPermission", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 检查工具权限\r\n\r\n @param toolId 工具ID\r\n @param userId 用户ID\r\n @return 是否有权限\r\n"}, {"name": "registerTool", "paramTypes": ["org.dromara.app.domain.AiTool"], "doc": "\n 注册工具\r\n\r\n @param tool 工具信息\r\n @return 是否成功\r\n"}, {"name": "updateTool", "paramTypes": ["org.dromara.app.domain.AiTool"], "doc": "\n 更新工具\r\n\r\n @param tool 工具信息\r\n @return 是否成功\r\n"}, {"name": "deleteTool", "paramTypes": ["java.lang.String"], "doc": "\n 删除工具\r\n\r\n @param toolId 工具ID\r\n @return 是否成功\r\n"}, {"name": "toggleTool", "paramTypes": ["java.lang.String", "java.lang.Bo<PERSON>an"], "doc": "\n 启用/禁用工具\r\n\r\n @param toolId  工具ID\r\n @param enabled 是否启用\r\n @return 是否成功\r\n"}, {"name": "executeTool", "paramTypes": ["java.lang.String", "java.util.Map", "org.dromara.app.service.IToolService.ToolCallContext", "java.lang.Long"], "doc": "\n 执行工具调用\r\n\r\n @param toolId     工具ID\r\n @param parameters 调用参数\r\n @param context    调用上下文\r\n @param userId     用户ID\r\n @return 调用结果\r\n"}, {"name": "executeToolAsync", "paramTypes": ["java.lang.String", "java.util.Map", "org.dromara.app.service.IToolService.ToolCallContext", "java.lang.Long"], "doc": "\n 异步执行工具调用\r\n\r\n @param toolId     工具ID\r\n @param parameters 调用参数\r\n @param context    调用上下文\r\n @param userId     用户ID\r\n @return 调用记录ID\r\n"}, {"name": "executeBatchTools", "paramTypes": ["java.util.List", "java.lang.Long"], "doc": "\n 批量执行工具调用\r\n\r\n @param toolCalls 工具调用列表\r\n @param userId    用户ID\r\n @return 调用结果列表\r\n"}, {"name": "parseToolCalls", "paramTypes": ["java.lang.String"], "doc": "\n 解析AI消息中的工具调用\r\n\r\n @param aiMessage AI消息内容\r\n @return 工具调用列表\r\n"}, {"name": "validateParameters", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": "\n 验证工具调用参数\r\n\r\n @param toolId     工具ID\r\n @param parameters 参数\r\n @return 验证结果\r\n"}, {"name": "getToolCallHistory", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 获取工具调用记录\r\n\r\n @param userId   用户ID\r\n @param pageNum  页码\r\n @param pageSize 每页大小\r\n @return 调用记录分页结果\r\n"}, {"name": "getSessionToolCalls", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 获取会话的工具调用记录\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @return 调用记录列表\r\n"}, {"name": "getToolCallDetail", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 获取工具调用详情\r\n\r\n @param callId 调用记录ID\r\n @param userId 用户ID\r\n @return 调用详情\r\n"}, {"name": "retryToolCall", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 重新执行工具调用\r\n\r\n @param callId 调用记录ID\r\n @param userId 用户ID\r\n @return 新的调用结果\r\n"}, {"name": "getToolUsageStats", "paramTypes": ["java.lang.Long"], "doc": "\n 获取工具使用统计\r\n\r\n @param userId 用户ID\r\n @return 统计信息\r\n"}, {"name": "getToolPerformanceStats", "paramTypes": ["java.lang.String"], "doc": "\n 获取工具性能统计\r\n\r\n @param toolId 工具ID\r\n @return 性能统计\r\n"}, {"name": "initSystemTools", "paramTypes": [], "doc": "\n 初始化系统工具\r\n"}], "constructors": []}