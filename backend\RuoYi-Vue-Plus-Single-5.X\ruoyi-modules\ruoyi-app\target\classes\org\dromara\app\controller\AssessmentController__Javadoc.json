{"doc": "\n 能力评估控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getQuestions", "paramTypes": [], "doc": "\n 获取评估问题\r\n"}, {"name": "submitResults", "paramTypes": ["java.util.List"], "doc": "\n 提交评估结果\r\n"}, {"name": "getDetailedReport", "paramTypes": [], "doc": "\n 获取详细能力报告\r\n"}, {"name": "getUserProfile", "paramTypes": [], "doc": "\n 获取用户成长档案\r\n"}], "constructors": []}