{"doc": "\n 支付SSE服务接口\r\n 用于管理SSE连接和推送支付状态消息\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "validatePaymentToken", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 验证支付token\r\n\r\n @param orderNo  订单号\r\n @param payToken 支付token\r\n @return 验证结果\r\n"}, {"name": "createConnection", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 创建SSE连接\r\n\r\n @param orderNo  订单号\r\n @param payToken 支付token\r\n @return SSE发射器\r\n"}, {"name": "pushPaymentSuccess", "paramTypes": ["java.lang.String"], "doc": "\n 推送支付成功消息\r\n\r\n @param orderNo 订单号\r\n"}, {"name": "pushPaymentFailed", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 推送支付失败消息\r\n\r\n @param orderNo 订单号\r\n @param reason  失败原因\r\n"}, {"name": "pushPaymentCancelled", "paramTypes": ["java.lang.String"], "doc": "\n 推送支付取消消息\r\n\r\n @param orderNo 订单号\r\n"}, {"name": "manualQueryOrderStatus", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 手动查询订单状态\r\n\r\n @param orderNo  订单号\r\n @param payToken 支付token\r\n"}, {"name": "closeConnection", "paramTypes": ["java.lang.String"], "doc": "\n 关闭SSE连接\r\n\r\n @param orderNo 订单号\r\n"}, {"name": "cleanupExpiredConnections", "paramTypes": [], "doc": "\n 清理过期连接\r\n"}], "constructors": []}