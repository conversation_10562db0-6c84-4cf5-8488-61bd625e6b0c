package org.dromara.app.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户成就视图对象 app_user_achievement
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "用户成就视图对象")
@ExcelIgnoreUnannotated
public class UserAchievementVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 成就ID
     */
    @Schema(description = "成就ID")
    @ExcelProperty(value = "成就ID")
    private Long achievementId;

    /**
     * 成就代码
     */
    @Schema(description = "成就代码")
    @ExcelProperty(value = "成就代码")
    private String achievementCode;

    /**
     * 成就名称
     */
    @Schema(description = "成就名称")
    @ExcelProperty(value = "成就名称")
    private String achievementName;

    /**
     * 成就描述
     */
    @Schema(description = "成就描述")
    @ExcelProperty(value = "成就描述")
    private String achievementDesc;

    /**
     * 成就图标URL
     */
    @Schema(description = "成就图标URL")
    @ExcelProperty(value = "成就图标URL")
    private String achievementIcon;

    /**
     * 成就类型
     */
    @Schema(description = "成就类型")
    @ExcelProperty(value = "成就类型")
    private String achievementType;

    /**
     * 奖励积分
     */
    @Schema(description = "奖励积分")
    @ExcelProperty(value = "奖励积分")
    private Integer rewardPoints;

    /**
     * 解锁时间
     */
    @Schema(description = "解锁时间")
    @ExcelProperty(value = "解锁时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date unlockTime;

    /**
     * 进度百分比(0-100)
     */
    @Schema(description = "进度百分比")
    @ExcelProperty(value = "进度百分比")
    private BigDecimal progress;

    /**
     * 当前数值
     */
    @Schema(description = "当前数值")
    @ExcelProperty(value = "当前数值")
    private Long currentValue;

    /**
     * 目标数值
     */
    @Schema(description = "目标数值")
    @ExcelProperty(value = "目标数值")
    private Long targetValue;

    /**
     * 是否完成(0否 1是)
     */
    @Schema(description = "是否完成")
    @ExcelProperty(value = "是否完成")
    private String isCompleted;

    /**
     * 是否已通知(0否 1是)
     */
    @Schema(description = "是否已通知")
    @ExcelProperty(value = "是否已通知")
    private String isNotified;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @ExcelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @ExcelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 创建者名称
     */
    @Schema(description = "创建者名称")
    @ExcelProperty(value = "创建者名称")
    private String createByName;

    /**
     * 更新者名称
     */
    @Schema(description = "更新者名称")
    @ExcelProperty(value = "更新者名称")
    private String updateByName;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;

    // ==================== 扩展字段 ====================

    /**
     * 成就完成状态描述
     */
    @Schema(description = "成就完成状态描述")
    private String completionStatusDesc;

    /**
     * 进度描述
     */
    @Schema(description = "进度描述")
    private String progressDesc;

    /**
     * 成就类型描述
     */
    @Schema(description = "成就类型描述")
    private String achievementTypeDesc;

    /**
     * 距离完成还需要的数值
     */
    @Schema(description = "距离完成还需要的数值")
    private Long remainingValue;

    /**
     * 预计完成时间（基于当前进度）
     */
    @Schema(description = "预计完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date estimatedCompletionTime;

    /**
     * 成就难度等级
     */
    @Schema(description = "成就难度等级")
    private String difficultyLevel;

    /**
     * 是否为推荐成就
     */
    @Schema(description = "是否为推荐成就")
    private Boolean isRecommended;

    /**
     * 成就排序
     */
    @Schema(description = "成就排序")
    private Integer sortOrder;

    // ==================== 业务方法 ====================

    /**
     * 获取完成状态描述
     */
    public String getCompletionStatusDesc() {
        if ("1".equals(this.isCompleted)) {
            return "已完成";
        } else if (this.progress != null && this.progress.compareTo(BigDecimal.ZERO) > 0) {
            return "进行中";
        } else {
            return "未开始";
        }
    }

    /**
     * 获取进度描述
     */
    public String getProgressDesc() {
        if (this.currentValue != null && this.targetValue != null) {
            return String.format("%d/%d (%.1f%%)", 
                this.currentValue, 
                this.targetValue, 
                this.progress != null ? this.progress.doubleValue() : 0.0);
        }
        return "0/0 (0.0%)";
    }

    /**
     * 获取成就类型描述
     */
    public String getAchievementTypeDesc() {
        if (this.achievementType == null) {
            return "未知";
        }
        
        switch (this.achievementType) {
            case "LOGIN":
                return "登录类";
            case "LEARNING":
                return "学习类";
            case "SOCIAL":
                return "社交类";
            case "TIME":
                return "时长类";
            case "CUSTOM":
                return "自定义";
            default:
                return "其他";
        }
    }

    /**
     * 获取距离完成还需要的数值
     */
    public Long getRemainingValue() {
        if (this.currentValue != null && this.targetValue != null) {
            long remaining = this.targetValue - this.currentValue;
            return Math.max(0, remaining);
        }
        return 0L;
    }

    /**
     * 判断是否接近完成（进度超过80%）
     */
    public Boolean isNearCompletion() {
        return this.progress != null && this.progress.compareTo(new BigDecimal("80")) >= 0;
    }

    /**
     * 判断是否已完成
     */
    public Boolean isCompleted() {
        return "1".equals(this.isCompleted);
    }

    /**
     * 判断是否已通知
     */
    public Boolean isNotified() {
        return "1".equals(this.isNotified);
    }

    /**
     * 获取成就完成天数（如果已完成）
     */
    public Long getCompletionDays() {
        if (this.unlockTime != null) {
            long diffTime = System.currentTimeMillis() - this.unlockTime.getTime();
            return diffTime / (24 * 60 * 60 * 1000);
        }
        return null;
    }

    /**
     * 获取成就稀有度（基于完成用户数量，需要外部设置）
     */
    @Schema(description = "成就稀有度")
    private String rarity;

    public String getRarity() {
        return this.rarity;
    }

    public void setRarity(String rarity) {
        this.rarity = rarity;
    }

}
