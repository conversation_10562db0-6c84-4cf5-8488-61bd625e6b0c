{"doc": " mybatis-plus配置类(下方注释有插件介绍)\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "dataPermissionInterceptor", "paramTypes": [], "doc": " 数据权限拦截器\n"}, {"name": "dataPermissionAspect", "paramTypes": [], "doc": " 数据权限切面处理器\n"}, {"name": "paginationInnerInterceptor", "paramTypes": [], "doc": " 分页插件，自动识别数据库类型\n"}, {"name": "optimisticLockerInnerInterceptor", "paramTypes": [], "doc": " 乐观锁插件\n"}, {"name": "metaObjectHandler", "paramTypes": [], "doc": " 元对象字段填充控制器\n"}, {"name": "idGenerator", "paramTypes": [], "doc": " 使用网卡信息绑定雪花生成器\n 防止集群雪花ID重复\n"}, {"name": "mybatisExceptionHandler", "paramTypes": [], "doc": " 异常处理器\n"}, {"name": "postInitTableInfoHandler", "paramTypes": [], "doc": " 初始化表对象处理器\n"}], "constructors": []}