<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.SysRoleMapper">

    <resultMap type="org.dromara.system.domain.vo.SysRoleVo" id="SysRoleResult">
    </resultMap>

    <sql id="selectRoleVo">
        select distinct r.role_id,
                        r.role_name,
                        r.role_key,
                        r.role_sort,
                        r.data_scope,
                        r.menu_check_strictly,
                        r.dept_check_strictly,
                        r.status,
                        r.del_flag,
                        r.create_time,
                        r.remark
        from sys_role r
                 left join sys_user_role sur on sur.role_id = r.role_id
                 left join sys_user u on u.user_id = sur.user_id
                 left join sys_dept d on u.dept_id = d.dept_id
    </sql>

    <select id="selectPageRoleList" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectRoleList" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectRolePermissionByUserId" parameterType="Long" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        WHERE r.del_flag = '0' and sur.user_id = #{userId}
    </select>

    <select id="selectRolesByUserId" parameterType="Long" resultMap="SysRoleResult">
        select r.role_id,
               r.role_name,
               r.role_key,
               r.role_sort,
               r.data_scope,
               r.status
        from sys_role r
        WHERE r.del_flag = '0'
          and r.role_id in (select role_id from sys_user_role where user_id = #{userId})
    </select>

    <select id="selectRoleById" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        WHERE r.del_flag = '0' and r.role_id = #{roleId}
    </select>

</mapper>
