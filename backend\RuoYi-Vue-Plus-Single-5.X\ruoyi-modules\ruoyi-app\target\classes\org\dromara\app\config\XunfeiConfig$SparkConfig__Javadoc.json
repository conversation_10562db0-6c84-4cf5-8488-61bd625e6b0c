{"doc": "", "fields": [{"name": "appId", "doc": "\n 应用ID（已废弃，保留兼容性）\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "doc": "\n API密钥（已废弃，保留兼容性）\r\n"}, {"name": "apiSecret", "doc": "\n API密钥（已废弃，保留兼容性）\r\n"}, {"name": "apiPassword", "doc": "\n API密码（HTTP调用认证）\r\n"}, {"name": "baseUrl", "doc": "\n 服务地址\r\n"}, {"name": "model", "doc": "\n 模型版本\r\n"}, {"name": "temperature", "doc": "\n 默认温度\r\n"}, {"name": "maxTokens", "doc": "\n 最大Token数\r\n"}, {"name": "connectTimeout", "doc": "\n 连接超时时间（秒）\r\n"}, {"name": "readTimeout", "doc": "\n 读取超时时间（秒）\r\n"}], "enumConstants": [], "methods": [{"name": "getVersion", "paramTypes": [], "doc": "\n 获取模型版本（兼容旧版本配置）\r\n"}, {"name": "setVersion", "paramTypes": ["java.lang.String"], "doc": "\n 设置模型版本（兼容旧版本配置）\r\n"}], "constructors": []}