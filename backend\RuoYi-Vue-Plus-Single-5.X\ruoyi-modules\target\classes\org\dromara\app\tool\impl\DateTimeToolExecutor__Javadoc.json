{"doc": " 时间助手工具执行器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "processDateTime", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": " 处理日期时间操作\n"}, {"name": "getCurrentDateTime", "paramTypes": ["java.util.Map"], "doc": " 获取当前时间\n"}, {"name": "formatDateTime", "paramTypes": ["java.util.Map"], "doc": " 格式化日期时间\n"}, {"name": "parseDateTime", "paramTypes": ["java.util.Map"], "doc": " 解析日期时间\n"}, {"name": "calculateDateTime", "paramTypes": ["java.util.Map"], "doc": " 计算日期时间\n"}, {"name": "convertTimezone", "paramTypes": ["java.util.Map"], "doc": " 时区转换\n"}, {"name": "parseToLocalDateTime", "paramTypes": ["java.lang.String"], "doc": " 解析字符串为LocalDateTime\n"}], "constructors": []}