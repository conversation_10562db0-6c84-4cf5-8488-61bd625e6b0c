{"doc": " SSE连接管理器\n 负责管理SSE连接的生命周期，防止连接泄露\n\n <AUTHOR>\n", "fields": [{"name": "DEFAULT_TIMEOUT", "doc": " 默认连接超时时间（毫秒）\n"}, {"name": "MAX_CONNECTIONS", "doc": " 最大连接数\n"}, {"name": "CLEANUP_INTERVAL_MINUTES", "doc": " 清理任务执行间隔（分钟）\n"}, {"name": "connections", "doc": " 连接缓存，存储连接ID到连接包装器的映射\n"}, {"name": "scheduler", "doc": " 定时清理任务执行器\n"}], "enumConstants": [], "methods": [{"name": "createConnection", "paramTypes": ["java.lang.String"], "doc": " 创建SSE连接\n\n @param connectionId 连接ID\n @return SSE发射器\n"}, {"name": "createConnection", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 创建SSE连接\n\n @param connectionId 连接ID\n @param timeout      超时时间（毫秒）\n @return SSE发射器\n"}, {"name": "getConnection", "paramTypes": ["java.lang.String"], "doc": " 获取SSE连接\n\n @param connectionId 连接ID\n @return SSE发射器，如果不存在则返回null\n"}, {"name": "removeConnection", "paramTypes": ["java.lang.String"], "doc": " 移除SSE连接\n\n @param connectionId 连接ID\n"}, {"name": "sendData", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 向指定连接发送数据\n\n @param connectionId 连接ID\n @param data         数据\n @return 是否发送成功\n"}, {"name": "cleanExpiredConnections", "paramTypes": [], "doc": " 清理过期连接\n"}, {"name": "cleanOldestConnection", "paramTypes": [], "doc": " 清理最旧的连接（当连接数超限时）\n"}, {"name": "getStats", "paramTypes": [], "doc": " 获取连接统计信息\n\n @return 统计信息\n"}], "constructors": []}