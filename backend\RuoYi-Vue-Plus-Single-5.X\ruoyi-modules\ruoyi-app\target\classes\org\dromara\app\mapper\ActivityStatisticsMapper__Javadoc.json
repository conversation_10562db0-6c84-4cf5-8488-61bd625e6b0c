{"doc": "\n 用户活动统计Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-16\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByUserAndTypeAndDate", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType", "java.time.LocalDate"], "doc": "\n 根据用户ID、活动类型和日期查询统计记录\r\n\r\n @param userId       用户ID\r\n @param activityType 活动类型\r\n @param statDate     统计日期\r\n @return 统计记录\r\n"}, {"name": "selectByUserAndDateRange", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType", "java.time.LocalDate", "java.time.LocalDate"], "doc": "\n 查询用户指定日期范围内的统计记录\r\n\r\n @param userId       用户ID\r\n @param activityType 活动类型\r\n @param startDate    开始日期\r\n @param endDate      结束日期\r\n @return 统计记录列表\r\n"}, {"name": "selectTodayStatistics", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户今日各类型活动统计\r\n\r\n @param userId 用户ID\r\n @return 今日统计列表\r\n"}, {"name": "selectWeekStatistics", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户本周各类型活动统计汇总\r\n\r\n @param userId 用户ID\r\n @return 本周统计列表\r\n"}, {"name": "selectMonthStatistics", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户本月各类型活动统计汇总\r\n\r\n @param userId 用户ID\r\n @return 本月统计列表\r\n"}, {"name": "updateStatistics", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType", "java.time.LocalDate", "java.lang.Long"], "doc": "\n 更新统计记录\r\n\r\n @param userId       用户ID\r\n @param activityType 活动类型\r\n @param statDate     统计日期\r\n @param duration     新增时长\r\n @return 更新行数\r\n"}, {"name": "deleteUserStatistics", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType"], "doc": "\n 删除用户指定类型的统计记录\r\n\r\n @param userId       用户ID\r\n @param activityType 活动类型\r\n @return 删除行数\r\n"}], "constructors": []}