/**
 * 题目评论相关API服务
 */
import { httpGet, httpPost, httpPut, httpDelete } from '@/utils/http'
import type { ApiResponse } from '@/types/learning'

/**
 * 题目评论数据类型定义
 */
export interface QuestionComment {
  commentId: number
  questionId: number
  questionTitle?: string
  userId: number
  userName?: string
  userAvatar?: string
  parentId?: number
  content: string
  likeCount: number
  replyCount: number
  status: '0' | '1' // 0-正常 1-删除
  sort: number
  ipAddress?: string
  remark?: string
  createTime: string
  updateTime: string
  children?: QuestionComment[]
  isLiked?: boolean
  level?: number
}

export interface QuestionCommentQuery {
  questionId?: number
  userId?: number
  parentId?: number
  content?: string
  status?: string
  page?: number
  pageSize?: number
}

export interface QuestionCommentCreate {
  questionId: number
  userId?: number
  parentId?: number
  content: string
  sort?: number
  ipAddress?: string
  remark?: string
}

export interface QuestionCommentUpdate extends QuestionCommentCreate {
  commentId: number
}

export interface PageResult<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

/**
 * 题目评论API服务类
 */
export const questionCommentApi = {
  /**
   * 分页查询题目评论列表
   * @param params 查询参数
   * @returns 评论分页列表
   */
  getList(params: QuestionCommentQuery): Promise<ApiResponse<PageResult<QuestionComment>>> {
    return httpGet<PageResult<QuestionComment>>('/system/question/comment/list', params)
  },

  /**
   * 根据题目ID查询评论列表
   * @param questionId 题目ID
   * @param params 查询参数
   * @returns 评论分页列表
   */
  getListByQuestionId(questionId: number, params: QuestionCommentQuery): Promise<ApiResponse<PageResult<QuestionComment>>> {
    return httpGet<PageResult<QuestionComment>>(`/system/question/comment/question/${questionId}`, params)
  },

  /**
   * 获取题目评论详情
   * @param commentId 评论ID
   * @returns 评论详情
   */
  getDetail(commentId: number): Promise<ApiResponse<QuestionComment>> {
    return httpGet<QuestionComment>(`/system/question/comment/${commentId}`)
  },

  /**
   * 新增题目评论
   * @param data 评论数据
   * @returns 创建结果
   */
  create(data: QuestionCommentCreate): Promise<ApiResponse<void>> {
    return httpPost<void>('/system/question/comment', data)
  },

  /**
   * 修改题目评论
   * @param data 评论数据
   * @returns 修改结果
   */
  update(data: QuestionCommentUpdate): Promise<ApiResponse<void>> {
    return httpPut<void>('/system/question/comment', data)
  },

  /**
   * 删除题目评论
   * @param commentIds 评论ID数组
   * @returns 删除结果
   */
  delete(commentIds: number[]): Promise<ApiResponse<void>> {
    return httpDelete<void>(`/system/question/comment/${commentIds.join(',')}`)
  },

  /**
   * 根据用户ID查询评论列表
   * @param userId 用户ID
   * @returns 评论列表
   */
  getByUserId(userId: number): Promise<ApiResponse<QuestionComment[]>> {
    return httpGet<QuestionComment[]>(`/system/question/comment/user/${userId}`)
  },

  /**
   * 根据父评论ID查询子评论列表
   * @param parentId 父评论ID
   * @returns 子评论列表
   */
  getByParentId(parentId: number): Promise<ApiResponse<QuestionComment[]>> {
    return httpGet<QuestionComment[]>(`/system/question/comment/parent/${parentId}`)
  },

  /**
   * 查询评论树形结构
   * @param questionId 题目ID
   * @returns 评论树形列表
   */
  getCommentTree(questionId: number): Promise<ApiResponse<QuestionComment[]>> {
    return httpGet<QuestionComment[]>(`/system/question/comment/tree/${questionId}`)
  },

  /**
   * 查询热门评论列表
   * @param questionId 题目ID
   * @param limit 限制数量
   * @returns 热门评论列表
   */
  getHotComments(questionId: number, limit = 10): Promise<ApiResponse<QuestionComment[]>> {
    return httpGet<QuestionComment[]>(`/system/question/comment/hot/${questionId}`, { limit })
  },

  /**
   * 查询最新评论列表
   * @param questionId 题目ID
   * @param limit 限制数量
   * @returns 最新评论列表
   */
  getLatestComments(questionId: number, limit = 10): Promise<ApiResponse<QuestionComment[]>> {
    return httpGet<QuestionComment[]>(`/system/question/comment/latest/${questionId}`, { limit })
  },

  /**
   * 点赞/取消点赞评论
   * @param commentId 评论ID
   * @param userId 用户ID
   * @returns 点赞结果
   */
  likeComment(commentId: number, userId: number): Promise<ApiResponse<void>> {
    return httpPut<void>('/system/question/comment/like', null, { commentId, userId })
  },

  /**
   * 回复评论
   * @param data 评论数据
   * @returns 回复结果
   */
  replyComment(data: QuestionCommentCreate): Promise<ApiResponse<void>> {
    return httpPost<void>('/system/question/comment/reply', data)
  },

  /**
   * 统计题目下的评论数量
   * @param questionId 题目ID
   * @returns 评论数量
   */
  countByQuestionId(questionId: number): Promise<ApiResponse<number>> {
    return httpGet<number>(`/system/question/comment/count/question/${questionId}`)
  },

  /**
   * 统计用户的评论数量
   * @param userId 用户ID
   * @returns 评论数量
   */
  countByUserId(userId: number): Promise<ApiResponse<number>> {
    return httpGet<number>(`/system/question/comment/count/user/${userId}`)
  },

  /**
   * 审核评论
   * @param commentId 评论ID
   * @param status 审核状态
   * @returns 审核结果
   */
  auditComment(commentId: number, status: string): Promise<ApiResponse<void>> {
    return httpPut<void>('/system/question/comment/audit', null, { commentId, status })
  },

  /**
   * 批量审核评论
   * @param commentIds 评论ID数组
   * @param status 审核状态
   * @returns 审核结果
   */
  batchAuditComments(commentIds: number[], status: string): Promise<ApiResponse<void>> {
    return httpPut<void>('/system/question/comment/batchAudit', null, { commentIds, status })
  },

  /**
   * 导出评论数据
   * @param params 查询参数
   * @returns 导出结果
   */
  exportData(params: QuestionCommentQuery): Promise<ApiResponse<void>> {
    return httpPost<void>('/system/question/comment/export', params)
  },
}
