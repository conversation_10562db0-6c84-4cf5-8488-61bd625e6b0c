{"doc": "\n 支付订单RabbitMQ配置\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "PAYMENT_DELAY_EXCHANGE", "doc": "\n 支付订单延迟交换机\r\n"}, {"name": "PAYMENT_DELAY_QUEUE", "doc": "\n 支付订单延迟队列\r\n"}, {"name": "PAYMENT_DELAY_ROUTING_KEY", "doc": "\n 支付订单延迟路由键\r\n"}, {"name": "PAYMENT_DEAD_EXCHANGE", "doc": "\n 支付订单死信交换机\r\n"}, {"name": "PAYMENT_DEAD_QUEUE", "doc": "\n 支付订单死信队列\r\n"}, {"name": "PAYMENT_DEAD_ROUTING_KEY", "doc": "\n 支付订单死信路由键\r\n"}], "enumConstants": [], "methods": [{"name": "paymentDelayExchange", "paramTypes": [], "doc": "\n 创建延迟交换机\r\n"}, {"name": "paymentDelayQueue", "paramTypes": [], "doc": "\n 创建延迟队列\r\n 设置30分钟过期时间，过期后转发到死信队列\r\n"}, {"name": "paymentDelayBinding", "paramTypes": [], "doc": "\n 绑定延迟队列到延迟交换机\r\n"}, {"name": "paymentDeadExchange", "paramTypes": [], "doc": "\n 创建死信交换机\r\n"}, {"name": "paymentDeadQueue", "paramTypes": [], "doc": "\n 创建死信队列\r\n"}, {"name": "paymentDeadBinding", "paramTypes": [], "doc": "\n 绑定死信队列到死信交换机\r\n"}], "constructors": []}