{"doc": "\n 权限检查结果视图对象\r\n\r\n <AUTHOR>\r\n @date 2025-01-19\r\n", "fields": [{"name": "checkId", "doc": "\n 检查ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "resource", "doc": "\n 资源标识\r\n"}, {"name": "action", "doc": "\n 操作类型\r\n"}, {"name": "hasPermission", "doc": "\n 是否有权限\r\n"}, {"name": "result", "doc": "\n 检查结果：granted/denied/expired/suspended\r\n"}, {"name": "denyReason", "doc": "\n 拒绝原因\r\n"}, {"name": "permissionSource", "doc": "\n 权限来源：role/user/group\r\n"}, {"name": "roles", "doc": "\n 相关角色列表\r\n"}, {"name": "permissionExpireTime", "doc": "\n 权限过期时间\r\n"}, {"name": "checkDuration", "doc": "\n 检查耗时（毫秒）\r\n"}, {"name": "checkTime", "doc": "\n 检查时间\r\n"}, {"name": "clientIp", "doc": "\n 客户端IP\r\n"}, {"name": "userAgent", "doc": "\n 用户代理\r\n"}, {"name": "sessionId", "doc": "\n 会话ID\r\n"}, {"name": "needsAdditionalAuth", "doc": "\n 是否需要额外验证\r\n"}, {"name": "additionalAuthType", "doc": "\n 额外验证类型\r\n"}, {"name": "riskScore", "doc": "\n 风险评分（0-100）\r\n"}, {"name": "riskFactors", "doc": "\n 风险因素\r\n"}, {"name": "rateLimitStatus", "doc": "\n 访问频率限制状态\r\n"}, {"name": "remainingAccess", "doc": "\n 剩余访问次数\r\n"}, {"name": "rateLimitResetTime", "doc": "\n 限制重置时间\r\n"}, {"name": "auditInfo", "doc": "\n 审计信息\r\n"}, {"name": "logRecorded", "doc": "\n 是否记录日志\r\n"}], "enumConstants": [], "methods": [], "constructors": []}