package org.dromara.app.controller.interview;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.bo.JobQueryBo;
import org.dromara.app.domain.vo.InterviewResponseVo;
import org.dromara.app.service.IInterviewService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.ratelimiter.annotation.RateLimiter;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import cn.hutool.core.util.StrUtil;

/**
 * 面试控制器
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/interview")
@Tag(name = "面试管理", description = "面试相关接口")
public class InterviewController extends BaseController {

    private final IInterviewService interviewService;

    /**
     * 获取岗位列表
     */
    @Operation(summary = "获取岗位列表", description = "分页查询岗位列表，支持筛选和排序")
    @GetMapping("/jobs/list")
    @RateLimiter(key = "app:interview:jobs:list", count = 5)
    public R<InterviewResponseVo.JobListResponse> getJobList(JobQueryBo queryBo) {
        log.info("获取岗位列表，查询条件: {}", queryBo);

        // 调整排序和查询参数，确保前端传递的参数被正确处理
        // 默认智能排序
        if (StrUtil.isBlank(queryBo.getSortBy())) {
            queryBo.setSortBy("smart");
        }
        // 默认降序排列
        if (StrUtil.isBlank(queryBo.getSortOrder())) {
            queryBo.setSortOrder("desc");
        }

        // 设置默认分页参数
        if (queryBo.getPageNum() == null || queryBo.getPageNum() < 1) {
            queryBo.setPageNum(1);
        }
        if (queryBo.getPageSize() == null || queryBo.getPageSize() < 1) {
            queryBo.setPageSize(20);
        }

        // 调用服务获取数据
        TableDataInfo<InterviewResponseVo.JobListResponse> result = interviewService.getJobList(queryBo);

        if (result.getRows() != null && !result.getRows().isEmpty()) {
            // 返回完整的响应数据，而不仅仅是第一条
            InterviewResponseVo.JobListResponse response = result.getRows().get(0);

            // 确保分页参数正确
            response.setPage(queryBo.getPageNum());
            response.setPageSize(queryBo.getPageSize());

            // 计算是否有更多数据
            boolean hasMore = response.getTotal() > (queryBo.getPageNum() * queryBo.getPageSize());
            response.setHasMore(hasMore);

            return R.ok("获取成功", response);
        }

        // 返回空数据
        InterviewResponseVo.JobListResponse emptyResponse = new InterviewResponseVo.JobListResponse();
        emptyResponse.setJobs(List.of());
        emptyResponse.setTotal(0L);
        emptyResponse.setPage(queryBo.getPageNum());
        emptyResponse.setPageSize(queryBo.getPageSize());
        emptyResponse.setHasMore(false);
        emptyResponse.setCategories(List.of());
        emptyResponse.setRecommendedJobs(List.of());

        return R.ok("获取成功", emptyResponse);
    }



    /**
     * 获取岗位分类列表
     */
    @Operation(summary = "获取岗位分类列表", description = "获取所有岗位分类信息")
    @RateLimiter(key = "app:interview:categories", count = 5)
    @GetMapping("/categories")
    public R<InterviewResponseVo.CategoriesResponse> getCategories(
        @Parameter(description = "是否包含岗位数量") @RequestParam(required = false) Boolean includeJobCount) {
        log.info("获取岗位分类列表，includeJobCount: {}", includeJobCount);

        InterviewResponseVo.CategoriesResponse result = interviewService.getCategories(includeJobCount);
        return R.ok("获取成功", result);
    }

    /**
     * 获取面试模式列表
     */
    @Operation(summary = "获取面试模式列表", description = "获取所有可用的面试模式")
    @RateLimiter(key = "app:interview:modes", count = 5)
    @GetMapping("/modes")
    public R<InterviewResponseVo.InterviewModesResponse> getInterviewModes() {
        log.info("获取面试模式列表");

        InterviewResponseVo.InterviewModesResponse result = interviewService.getInterviewModes();
        return R.ok("获取成功", result);
    }

    /**
     * 获取搜索建议
     */
    @Operation(summary = "获取搜索建议", description = "根据关键词获取搜索建议")
    @RateLimiter(key = "app:interview:search:suggestions", count = 5)
    @GetMapping("/search/suggestions")
    public R<InterviewResponseVo.SearchSuggestionsResponse> getSearchSuggestions(
        @Parameter(description = "搜索关键词", required = true) @RequestParam String keyword,
        @Parameter(description = "返回数量限制") @RequestParam(required = false) Integer limit) {
        log.info("获取搜索建议，keyword: {}, limit: {}", keyword, limit);

        InterviewResponseVo.SearchSuggestionsResponse result = interviewService.getSearchSuggestions(keyword, limit);
        return R.ok("获取成功", result);
    }

    /**
     * 创建面试会话
     */
    @Operation(summary = "创建面试会话", description = "创建新的面试会话")
    @Log(title = "创建面试会话", businessType = BusinessType.INSERT)
    @RateLimiter(key = "app:interview:session:create", count = 5)
    @PostMapping("/session/create")
    public R<InterviewResponseVo.CreateSessionResponse> createInterviewSession(
        @RequestBody CreateSessionRequest request) {
        log.info("创建面试会话，请求参数: {}", request);

        InterviewResponseVo.CreateSessionResponse result = interviewService.createInterviewSession(
            request.getJobId(),
            request.getMode(),
            request.getResumeUrl(),
            request.getCustomizedQuestions()
        );

        return R.ok("会话创建成功", result);
    }

    /**
     * 收藏/取消收藏岗位
     */
    @Operation(summary = "收藏岗位", description = "收藏或取消收藏岗位")
    @Log(title = "收藏岗位", businessType = BusinessType.UPDATE)
    @RateLimiter(key = "app:interview:job:favorite", count = 5)
    @PostMapping("/job/favorite")
    public R<Void> favoriteJob(@RequestBody FavoriteJobRequest request) {
        log.info("收藏岗位操作，请求参数: {}", request);

        interviewService.favoriteJob(request.getJobId(), request.getIsFavorited());

        String message = request.getIsFavorited() ? "收藏成功" : "取消收藏成功";
        return R.ok(message);
    }

    /**
     * 设备检测
     */
    @Operation(summary = "设备检测", description = "检测用户设备状态")
    @GetMapping("/device/check")
    public R<InterviewResponseVo.DeviceCheckResult> checkDevice() {
        log.info("执行设备检测");

        InterviewResponseVo.DeviceCheckResult result = interviewService.checkDevice();
        return R.ok("设备检测完成", result);
    }

    /**
     * 获取统计信息
     */
    @Operation(summary = "获取统计信息", description = "获取面试系统统计信息")
    @GetMapping("/statistics")
    public R<InterviewResponseVo.StatisticsResponse> getStatistics() {
        log.info("获取统计信息");

        InterviewResponseVo.StatisticsResponse result = interviewService.getStatistics();
        return R.ok("获取成功", result);
    }

    /**
     * 获取面试历史记录
     */
    @Operation(summary = "获取面试历史记录", description = "分页获取用户的面试历史记录")
    @GetMapping("/history")
    public R<InterviewResponseVo.HistoryListResponse> getInterviewHistory(
        @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
        @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer pageSize,
        @Parameter(description = "分类筛选") @RequestParam(required = false) String category,
        @Parameter(description = "状态筛选") @RequestParam(required = false) String status) {
        log.info("获取面试历史记录，page: {}, pageSize: {}, category: {}, status: {}", page, pageSize, category, status);

        InterviewResponseVo.HistoryListResponse result = interviewService.getInterviewHistory(page, pageSize, category, status);
        return R.ok("获取成功", result);
    }

    /**
     * 获取用户统计数据（用于历史页面）
     */
    @Operation(summary = "获取用户统计数据", description = "获取用户面试相关的统计数据，用于历史页面展示")
    @GetMapping("/user/statistics")
    public R<InterviewResponseVo.Statistics> getUserStatistics() {
        log.info("获取用户统计数据");

        InterviewResponseVo.Statistics result = interviewService.getUserStatistics();
        return R.ok("获取成功", result);
    }

    /**
     * 获取岗位详情
     */
    @Operation(summary = "获取岗位详情", description = "获取指定岗位的详细信息")
    @GetMapping("/job/detail")
    public R<InterviewResponseVo.JobDetailResponse> getJobDetail(
        @Parameter(description = "岗位ID", required = true) @RequestParam Long jobId) {
        log.info("获取岗位详情，jobId: {}", jobId);

        InterviewResponseVo.JobDetailResponse result = interviewService.getJobDetail(jobId);
        return R.ok("获取成功", result);
    }

    /**
     * 获取示例问题
     */
    @Operation(summary = "获取示例问题", description = "获取指定岗位的示例问题")
    @GetMapping("/job/sample-questions")
    public R<InterviewResponseVo.SampleQuestionsResponse> getSampleQuestions(
        @Parameter(description = "岗位ID", required = true) @RequestParam Long jobId,
        @Parameter(description = "问题数量") @RequestParam(required = false) Integer count) {
        log.info("获取示例问题，jobId: {}, count: {}", jobId, count);

        InterviewResponseVo.SampleQuestionsResponse result = interviewService.getSampleQuestions(jobId, count);
        return R.ok("获取成功", result);
    }

    /**
     * 获取相关岗位
     */
    @Operation(summary = "获取相关岗位", description = "获取与指定岗位相关的其他岗位")
    @GetMapping("/job/related")
    public R<InterviewResponseVo.RelatedJobsResponse> getRelatedJobs(
        @Parameter(description = "当前岗位ID", required = true) @RequestParam Long jobId,
        @Parameter(description = "数量限制") @RequestParam(required = false) Integer limit) {
        log.info("获取相关岗位，jobId: {}, limit: {}", jobId, limit);

        InterviewResponseVo.RelatedJobsResponse result = interviewService.getRelatedJobs(jobId, limit);
        return R.ok("获取成功", result);
    }

    /**
     * 获取岗位统计数据
     */
    @Operation(summary = "获取岗位统计数据", description = "获取指定岗位的统计数据")
    @GetMapping("/job/statistics")
    public R<InterviewResponseVo.JobStatisticsResponse> getJobStatistics(
        @Parameter(description = "岗位ID", required = true) @RequestParam Long jobId) {
        log.info("获取岗位统计数据，jobId: {}", jobId);

        InterviewResponseVo.JobStatisticsResponse result = interviewService.getJobStatistics(jobId);
        return R.ok("获取成功", result);
    }

    /**
     * 获取岗位面试模式列表
     */
    @Operation(summary = "获取岗位面试模式列表", description = "获取指定岗位的面试模式列表")
    @GetMapping("/job/modes")
    public R<InterviewResponseVo.JobInterviewModesResponse> getJobInterviewModes(
        @Parameter(description = "岗位ID", required = true) @RequestParam Long jobId) {
        log.info("获取岗位面试模式列表，jobId: {}", jobId);

        InterviewResponseVo.JobInterviewModesResponse result = interviewService.getJobInterviewModes(jobId);
        return R.ok("获取成功", result);
    }

    /**
     * 检查用户准备度
     */
    @Operation(summary = "检查用户准备度", description = "检查用户对指定岗位的准备度")
    @GetMapping("/job/readiness")
    public R<InterviewResponseVo.UserReadinessResponse> checkUserReadiness(
        @Parameter(description = "岗位ID", required = true) @RequestParam Long jobId,
        @Parameter(description = "用户ID") @RequestParam(required = false) String userId) {
        log.info("检查用户准备度，jobId: {}, userId: {}", jobId, userId);

        InterviewResponseVo.UserReadinessResponse result = interviewService.checkUserReadiness(jobId, userId);
        return R.ok("获取成功", result);
    }

    /**
     * 分享岗位信息
     */
    @Operation(summary = "分享岗位信息", description = "分享指定岗位信息到指定平台")
    @PostMapping("/job/share")
    public R<InterviewResponseVo.ShareJobResponse> shareJob(@RequestBody ShareJobRequest request) {
        log.info("分享岗位信息，请求参数: {}", request);

        InterviewResponseVo.ShareJobResponse result = interviewService.shareJob(request.getJobId(), request.getPlatform());
        return R.ok("分享成功", result);
    }

//    /**
//     * 获取会话信息
//     */
//    @Operation(summary = "获取会话信息", description = "获取指定面试会话的详细信息")
//    @GetMapping("/session/info")
//    @RateLimiter(key = "app:interview:session:info", count = 10)
//    public R<InterviewResponseVo.SessionInfo> getSessionInfo(
//        @Parameter(description = "会话ID", required = true) @RequestParam String sessionId) {
//        log.info("获取会话信息，sessionId: {}", sessionId);
//
//        InterviewResponseVo.SessionInfo result = interviewService.getSessionInfo(sessionId);
//        return R.ok("获取成功", result);
//    }

//    /**
//     * 获取面试问题列表
//     */
//    @Operation(summary = "获取面试问题列表", description = "获取指定会话的面试问题列表")
//    @GetMapping("/session/questions")
//    @RateLimiter(key = "app:interview:session:questions", count = 10)
//    public R<InterviewResponseVo.QuestionListResponse> getSessionQuestions(
//        @Parameter(description = "会话ID", required = true) @RequestParam String sessionId) {
//        log.info("获取面试问题列表，sessionId: {}", sessionId);
//
//        InterviewResponseVo.QuestionListResponse result = interviewService.getSessionQuestions(sessionId);
//        return R.ok("获取成功", result);
//    }

//    /**
//     * 提交面试回答
//     */
//    @Operation(summary = "提交面试回答", description = "提交用户对特定问题的回答")
//    @PostMapping("/session/submit-answer")
//    @RateLimiter(key = "app:interview:session:submit-answer", count = 5)
//    public R<InterviewResponseVo.AnswerResponse> submitAnswer(@RequestBody SubmitAnswerRequest request) {
//        log.info("提交面试回答，请求参数: {}", request);
//
//        InterviewResponseVo.AnswerResponse result = interviewService.submitAnswer(
//            request.getSessionId(),
//            request.getQuestionId(),
//            request.getAnswer(),
//            request.getAudioUrl(),
//            request.getDuration()
//        );
//
//        return R.ok("回答已提交", result);
//    }
//
//    /**
//     * 结束面试会话
//     */
//    @Operation(summary = "结束面试会话", description = "结束指定的面试会话")
//    @PostMapping("/session/end")
//    @RateLimiter(key = "app:interview:session:end", count = 5)
//    public R<InterviewResponseVo.EndSessionResponse> endSession(@RequestBody EndInterviewRequest request) {
//        log.info("结束面试会话，请求参数: {}", request);
//
//        InterviewResponseVo.EndSessionResponse result = interviewService.endSession(
//            request.getSessionId(),
//            request.getReason()
//        );
//
//        return R.ok("面试已结束", result);
//    }

//
//    /**
//     * 提交面试反馈
//     */
//    @Operation(summary = "提交面试反馈", description = "提交用户对面试的反馈")
//    @PostMapping("/session/feedback")
//    @RateLimiter(key = "app:interview:session:feedback", count = 5)
//    public R<InterviewResponseVo.FeedbackResponse> submitFeedback(@RequestBody FeedbackRequest request) {
//        log.info("提交面试反馈，请求参数: {}", request);
//
//        InterviewResponseVo.FeedbackResponse result = interviewService.submitFeedback(
//            request.getSessionId(),
//            request.getRating(),
//            request.getComments(),
//            request.getTags()
//        );
//
//        return R.ok("反馈已提交", result);
//    }

    /**
     * 获取面试结果
     */
    @Operation(summary = "获取面试结果", description = "获取指定面试会话的结果评分和分析")
    @GetMapping("/session/result")
    public R<InterviewResponseVo.InterviewResult> getInterviewResult(
        @Parameter(description = "会话ID", required = true) @RequestParam String sessionId) {
        log.info("获取面试结果，sessionId: {}", sessionId);

        InterviewResponseVo.InterviewResult result = interviewService.getInterviewResult(sessionId);
        return R.ok("获取成功", result);
    }

//    /**
//     * 检查设备状态
//     */
//    @Operation(summary = "检查设备状态", description = "检查用户设备的麦克风和摄像头状态")
//    @GetMapping("/session/check-devices")
//    public R<Map<String, Boolean>> checkDevices() {
//        log.info("检查设备状态");
//
//        Map<String, Boolean> result = interviewService.checkDevices();
//        return R.ok("设备状态检查完成", result);
//    }

//    /**
//     * 获取会话状态
//     */
//    @Operation(summary = "获取会话状态", description = "获取指定会话的当前状态")
//    @GetMapping("/session/status")
//    public R<InterviewResponseVo.SessionStatus> getSessionStatus(
//        @Parameter(description = "会话ID", required = true) @RequestParam String sessionId) {
//        log.info("获取会话状态，sessionId: {}", sessionId);
//
//        InterviewResponseVo.SessionStatus result = interviewService.getSessionStatus(sessionId);
//        return R.ok("获取成功", result);
//    }

    /**
     * 获取AI面试官信息
     */
    @Operation(summary = "获取AI面试官信息", description = "获取AI面试官的基本信息和特性")
    @GetMapping("/interviewer/info")
    public R<InterviewResponseVo.InterviewerInfo> getInterviewerInfo(
        @Parameter(description = "面试官ID") @RequestParam(required = false) String interviewerId) {
        log.info("获取AI面试官信息，interviewerId: {}", interviewerId);

        InterviewResponseVo.InterviewerInfo result = interviewService.getInterviewerInfo(interviewerId);
        return R.ok("获取成功", result);
    }

//    /**
//     * 获取面试问题
//     */
//    @Operation(summary = "获取面试问题", description = "获取指定会话的面试问题")
//    @GetMapping("/session/question")
//    @RateLimiter(key = "app:interview:session:question", count = 10)
//    public R<InterviewResponseVo.InterviewQuestion> getQuestion(
//        @Parameter(description = "会话ID", required = true) @RequestParam String sessionId,
//        @Parameter(description = "问题索引") @RequestParam(required = false) Integer index) {
//        log.info("获取面试问题，sessionId: {}, index: {}", sessionId, index);
//
//        // 获取问题列表
//        InterviewResponseVo.QuestionListResponse questionList = interviewService.getSessionQuestions(sessionId);
//
//        if (questionList.getQuestions() == null || questionList.getQuestions().isEmpty()) {
//            throw new ServiceException("没有找到面试问题");
//        }
//
//        // 根据索引获取问题，如果没有指定索引则返回第一个问题
//        int questionIndex = index != null ? index : 0;
//        if (questionIndex >= questionList.getQuestions().size()) {
//            throw new ServiceException("问题索引超出范围");
//        }
//
//        InterviewResponseVo.InterviewQuestion question = questionList.getQuestions().get(questionIndex);
//        return R.ok("获取成功", question);
//    }

    /**
     * 提交答案
     */
    @Operation(summary = "提交答案", description = "提交用户对面试问题的回答")
    @PostMapping("/session/answer")
    public R<InterviewResponseVo.AnswerResponse> submitAnswerForRoom(@RequestBody SubmitAnswerForRoomRequest request) {
        log.info("提交面试答案，请求参数: {}", request);

        InterviewResponseVo.AnswerResponse result = interviewService.submitAnswer(
            request.getSessionId(),
            request.getQuestionId(),
            request.getAnswer().getText(),
            request.getAnswer().getAudioUrl(),
            request.getAnswer().getDuration()
        );

        return R.ok("答案已提交", result);
    }

    /**
     * 创建会话请求对象
     */
    @Setter
    @Getter
    public static class CreateSessionRequest {
        // getters and setters
        @NotNull(message = "岗位ID不能为空")
        private Long jobId;

        @NotNull(message = "面试模式不能为空")
        private String mode;

        private String resumeUrl;
        private List<String> customizedQuestions;

        @Override
        public String toString() {
            return "CreateSessionRequest{jobId=" + jobId + ", mode='" + mode + "', resumeUrl='" + resumeUrl + "', customizedQuestions=" + customizedQuestions + "}";
        }
    }

    /**
     * 收藏岗位请求对象
     */
    @Setter
    @Getter
    public static class FavoriteJobRequest {
        // getters and setters
        @NotNull(message = "岗位ID不能为空")
        private Long jobId;

        @NotNull(message = "收藏状态不能为空")
        private Boolean isFavorited;

        @Override
        public String toString() {
            return "FavoriteJobRequest{jobId=" + jobId + ", isFavorited=" + isFavorited + "}";
        }
    }

    /**
     * 分享岗位请求对象
     */
    @Setter
    @Getter
    public static class ShareJobRequest {
        // getters and setters
        @NotNull(message = "岗位ID不能为空")
        private Long jobId;

        @NotNull(message = "分享平台不能为空")
        private String platform; // wechat, qq, weibo, link

        @Override
        public String toString() {
            return "ShareJobRequest{jobId=" + jobId + ", platform='" + platform + "'}";
        }
    }

    /**
     * 提交回答请求对象
     */
    @Setter
    @Getter
    public static class SubmitAnswerRequest {
        // getters and setters
        @NotBlank(message = "会话ID不能为空")
        private String sessionId;

        @NotBlank(message = "问题ID不能为空")
        private String questionId;

        @NotBlank(message = "回答内容不能为空")
        private String answer;

        private String audioUrl;
        private Integer duration;

        @Override
        public String toString() {
            return "SubmitAnswerRequest{sessionId='" + sessionId + "', questionId='" + questionId + "', answer='" + (answer != null ? (answer.length() > 20 ? answer.substring(0, 20) + "..." : answer) : null) + "', audioUrl='" + audioUrl + "', duration=" + duration + "}";
        }
    }

    /**
     * 结束面试请求对象
     */
    @Setter
    @Getter
    public static class EndInterviewRequest {
        // getters and setters
        @NotBlank(message = "会话ID不能为空")
        private String sessionId;
        private String reason;

        @Override
        public String toString() {
            return "EndInterviewRequest{sessionId='" + sessionId + "', reason='" + reason + "'}";
        }
    }

    /**
     * 提交反馈请求对象
     */
    @Setter
    @Getter
    public static class FeedbackRequest {
        // getters and setters
        @NotBlank(message = "会话ID不能为空")
        private String sessionId;

        @NotNull(message = "评分不能为空")
        private Integer rating;

        private String comments;
        private List<String> tags;

        @Override
        public String toString() {
            return "FeedbackRequest{sessionId='" + sessionId + "', rating=" + rating + ", comments='" + comments + "', tags=" + tags + "}";
        }
    }

    /**
     * 面试房间提交答案请求对象
     */
    @Setter
    @Getter
    public static class SubmitAnswerForRoomRequest {
        // getters and setters
        @NotBlank(message = "会话ID不能为空")
        private String sessionId;

        @NotBlank(message = "问题ID不能为空")
        private String questionId;

        @NotNull(message = "答案不能为空")
        private AnswerData answer;

        public static class AnswerData {
            private String text;
            private String audioUrl;
            private String videoUrl;
            private Integer duration;
            private List<String> options;

            public String getText() {
                return text;
            }

            public void setText(String text) {
                this.text = text;
            }

            public String getAudioUrl() {
                return audioUrl;
            }

            public void setAudioUrl(String audioUrl) {
                this.audioUrl = audioUrl;
            }

            public String getVideoUrl() {
                return videoUrl;
            }

            public void setVideoUrl(String videoUrl) {
                this.videoUrl = videoUrl;
            }

            public Integer getDuration() {
                return duration;
            }

            public void setDuration(Integer duration) {
                this.duration = duration;
            }

            public List<String> getOptions() {
                return options;
            }

            public void setOptions(List<String> options) {
                this.options = options;
            }

            @Override
            public String toString() {
                return "AnswerData{text='" + (text != null ? (text.length() > 20 ? text.substring(0, 20) + "..." : text) : null) +
                    "', audioUrl='" + audioUrl + "', videoUrl='" + videoUrl +
                    "', duration=" + duration + ", options=" + options + "}";
            }
        }

        @Override
        public String toString() {
            return "SubmitAnswerForRoomRequest{sessionId='" + sessionId + "', questionId='" + questionId + "', answer=" + answer + "}";
        }
    }

    /**
     * 结束面试会话请求对象
     */
    public static class EndSessionRequest {
        @NotBlank(message = "会话ID不能为空")
        private String sessionId;

        private String reason; // 结束原因：completed, timeout, user_exit, error

        // getters and setters
        public String getSessionId() {
            return sessionId;
        }

        public void setSessionId(String sessionId) {
            this.sessionId = sessionId;
        }

        public String getReason() {
            return reason;
        }

        public void setReason(String reason) {
            this.reason = reason;
        }

        @Override
        public String toString() {
            return "EndSessionRequest{sessionId='" + sessionId + "', reason='" + reason + "'}";
        }
    }
}
