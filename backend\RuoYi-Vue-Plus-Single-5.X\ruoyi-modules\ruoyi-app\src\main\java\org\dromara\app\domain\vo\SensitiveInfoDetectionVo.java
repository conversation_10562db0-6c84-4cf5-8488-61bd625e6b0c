package org.dromara.app.domain.vo;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 敏感信息检测结果视图对象
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SensitiveInfoDetectionVo {

    /**
     * 检测ID
     */
    private String detectionId;

    /**
     * 原始文本长度
     */
    private Integer originalTextLength;

    /**
     * 是否包含敏感信息
     */
    private Boolean hasSensitiveInfo;

    /**
     * 敏感信息总数
     */
    private Integer sensitiveInfoCount;

    /**
     * 风险等级：low/medium/high/critical
     */
    private String riskLevel;

    /**
     * 风险评分（0-100）
     */
    private Integer riskScore;

    /**
     * 检测到的敏感词列表
     */
    private List<SensitiveWordInfo> sensitiveWords;

    /**
     * 检测到的个人信息
     */
    private Map<String, List<PersonalInfoItem>> personalInfo;

    /**
     * 过滤后的文本
     */
    private String filteredText;

    /**
     * 脱敏后的文本
     */
    private String maskedText;

    /**
     * 检测耗时（毫秒）
     */
    private Long detectionTime;

    /**
     * 检测时间
     */
    private LocalDateTime detectionDateTime;

    /**
     * 建议处理方式
     */
    private List<String> recommendations;

    /**
     * 是否需要人工审核
     */
    private Boolean needsManualReview;

    /**
     * 敏感词信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SensitiveWordInfo {
        /**
         * 敏感词
         */
        private String word;

        /**
         * 分类
         */
        private String category;

        /**
         * 敏感级别
         */
        private Integer level;

        /**
         * 在文本中的位置
         */
        private List<Integer> positions;

        /**
         * 出现次数
         */
        private Integer count;

        /**
         * 替换建议
         */
        private String replacement;
    }

    /**
     * 个人信息项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PersonalInfoItem {
        /**
         * 信息类型：phone/email/idcard/address等
         */
        private String type;

        /**
         * 原始值
         */
        private String originalValue;

        /**
         * 脱敏值
         */
        private String maskedValue;

        /**
         * 在文本中的位置
         */
        private Integer position;

        /**
         * 长度
         */
        private Integer length;

        /**
         * 置信度（0-1）
         */
        private Double confidence;
    }
}
