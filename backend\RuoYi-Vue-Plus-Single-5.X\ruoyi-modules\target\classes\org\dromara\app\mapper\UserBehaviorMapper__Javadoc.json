{"doc": " 用户行为记录Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByUserIdAndBehaviorType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 根据用户ID和行为类型查询行为记录\n\n @param userId       用户ID\n @param behaviorType 行为类型\n @return 行为记录列表\n"}, {"name": "selectByUserIdAndTimeRange", "paramTypes": ["java.lang.Long", "java.util.Date", "java.util.Date"], "doc": " 根据用户ID和时间范围查询行为记录\n\n @param userId    用户ID\n @param startTime 开始时间\n @param endTime   结束时间\n @return 行为记录列表\n"}, {"name": "countByUserIdAndBehaviorType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 统计用户某种行为的次数\n\n @param userId       用户ID\n @param behaviorType 行为类型\n @return 行为次数\n"}, {"name": "countByUserIdAndBehaviorTypeAndTimeRange", "paramTypes": ["java.lang.Long", "java.lang.String", "java.util.Date", "java.util.Date"], "doc": " 统计用户某种行为在指定时间范围内的次数\n\n @param userId       用户ID\n @param behaviorType 行为类型\n @param startTime    开始时间\n @param endTime      结束时间\n @return 行为次数\n"}, {"name": "selectConsecutiveLoginDays", "paramTypes": ["java.lang.Long"], "doc": " 查询用户连续登录天数\n\n @param userId 用户ID\n @return 连续登录天数\n"}, {"name": "selectTotalStudyMinutes", "paramTypes": ["java.lang.Long"], "doc": " 查询用户累计学习时长（分钟）\n\n @param userId 用户ID\n @return 累计学习时长\n"}, {"name": "batchInsert", "paramTypes": ["java.util.List"], "doc": " 批量插入用户行为记录\n\n @param behaviors 行为记录列表\n @return 插入数量\n"}], "constructors": []}