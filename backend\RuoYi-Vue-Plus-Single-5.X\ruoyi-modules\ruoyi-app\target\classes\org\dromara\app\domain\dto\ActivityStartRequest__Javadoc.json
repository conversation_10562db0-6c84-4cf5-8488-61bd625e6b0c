{"doc": "\n 开始活动请求DTO\r\n\r\n <AUTHOR>\r\n @date 2025-07-16\r\n", "fields": [{"name": "sessionId", "doc": "\n 会话ID\r\n"}, {"name": "type", "doc": "\n 活动类型\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "activityId", "doc": "\n 活动对象ID(如题目ID、课程ID等)\r\n"}, {"name": "activityName", "doc": "\n 活动名称\r\n"}, {"name": "categoryId", "doc": "\n 分类ID(如题库ID、课程分类ID等)\r\n"}, {"name": "categoryName", "doc": "\n 分类名称\r\n"}, {"name": "metadata", "doc": "\n 额外元数据\r\n"}], "enumConstants": [], "methods": [], "constructors": []}