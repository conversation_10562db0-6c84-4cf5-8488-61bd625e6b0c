package org.dromara.app.domain.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 同步活动会话请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class ActivitySyncRequest {

    /**
     * 会话ID
     */
    @NotBlank(message = "会话ID不能为空")
    private String sessionId;

    /**
     * 当前持续时长(毫秒)
     */
    @NotNull(message = "持续时长不能为空")
    private Long duration;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
}
