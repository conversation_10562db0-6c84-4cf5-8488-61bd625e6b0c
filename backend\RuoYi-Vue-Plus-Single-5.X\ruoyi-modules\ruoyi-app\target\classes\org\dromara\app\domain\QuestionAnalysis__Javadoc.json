{"doc": "\n 问题分析对象 app_question_analysis\r\n\r\n <AUTHOR>\r\n @date 2025-07-17\r\n", "fields": [{"name": "id", "doc": "\n 分析ID\r\n"}, {"name": "resultId", "doc": "\n 结果ID\r\n"}, {"name": "questionId", "doc": "\n 问题ID\r\n"}, {"name": "question", "doc": "\n 问题内容\r\n"}, {"name": "category", "doc": "\n 问题分类\r\n"}, {"name": "difficulty", "doc": "\n 难度等级（1-5）\r\n"}, {"name": "answer", "doc": "\n 用户答案\r\n"}, {"name": "score", "doc": "\n 得分\r\n"}, {"name": "audioScore", "doc": "\n 音频得分\r\n"}, {"name": "videoScore", "doc": "\n 视频得分\r\n"}, {"name": "textScore", "doc": "\n 文本得分\r\n"}, {"name": "feedback", "doc": "\n 反馈\r\n"}, {"name": "strengths", "doc": "\n 优势点（JSON数组）\r\n"}, {"name": "weaknesses", "doc": "\n 劣势点（JSON数组）\r\n"}, {"name": "keywordMatches", "doc": "\n 关键词匹配（JSON数组）\r\n"}, {"name": "idealAnswer", "doc": "\n 理想答案\r\n"}, {"name": "timeSpent", "doc": "\n 用时（秒）\r\n"}, {"name": "timeLimit", "doc": "\n 时间限制（秒）\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}], "enumConstants": [], "methods": [], "constructors": []}