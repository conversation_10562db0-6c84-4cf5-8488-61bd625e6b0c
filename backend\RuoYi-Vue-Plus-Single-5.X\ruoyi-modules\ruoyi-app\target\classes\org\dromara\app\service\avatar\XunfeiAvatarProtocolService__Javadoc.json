{"doc": "\n 讯飞数字人协议构建服务\r\n\r\n <AUTHOR>\r\n @date 2025-07-20\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "buildStartRequest", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": "\n 构建启动协议\r\n\r\n @param avatarId   形象ID\r\n @param sceneId    场景ID\r\n @param vcn        声音ID\r\n @param width      视频宽度\r\n @param height     视频高度\r\n @param background 背景数据（可选）\r\n @return 启动协议JSON\r\n"}, {"name": "buildTextRequest", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 构建文本驱动协议\r\n\r\n @param text   文本内容\r\n @param vcn    声音ID（可选）\r\n @param speed  语速（可选）\r\n @param pitch  语调（可选）\r\n @param volume 音量（可选）\r\n @return 文本驱动协议JSON\r\n"}, {"name": "buildTextInteractRequest", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 构建文本交互协议\r\n\r\n @param text   文本内容\r\n @param vcn    声音ID（可选）\r\n @param speed  语速（可选）\r\n @param pitch  语调（可选）\r\n @param volume 音量（可选）\r\n @return 文本交互协议JSON\r\n"}, {"name": "buildAudioRequest", "paramTypes": ["java.lang.String", "int", "java.lang.String"], "doc": "\n 构建音频驱动协议\r\n\r\n @param requestId  请求ID\r\n @param status     数据状态（0开始，1过渡，2结束）\r\n @param audioData  音频数据（Base64编码）\r\n @return 音频驱动协议JSON\r\n"}, {"name": "buildPingRequest", "paramTypes": [], "doc": "\n 构建心跳协议\r\n\r\n @return 心跳协议JSON\r\n"}, {"name": "buildResetRequest", "paramTypes": [], "doc": "\n 构建重置（打断）协议\r\n\r\n @return 重置协议JSON\r\n"}, {"name": "buildStopRequest", "paramTypes": [], "doc": "\n 构建停止协议\r\n\r\n @return 停止协议JSON\r\n"}, {"name": "buildCmdRequest", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 构建单独指令协议\r\n\r\n @param actionType  动作类型\r\n @param actionValue 动作值\r\n @return 指令协议JSON\r\n"}], "constructors": []}