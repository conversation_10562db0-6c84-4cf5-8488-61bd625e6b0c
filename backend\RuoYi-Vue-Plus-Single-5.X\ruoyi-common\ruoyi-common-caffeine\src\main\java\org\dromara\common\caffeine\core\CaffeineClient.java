package org.dromara.common.caffeine.core;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.caffeine.entity.CacheValueWrapper;
import org.dromara.common.caffeine.properties.CaffeineProperties;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;

/**
 * Caffeine缓存客户端
 *
 * <AUTHOR>
 */
@Slf4j
public class CaffeineClient {

    private final Cache<String, Object> cache;
    private final CaffeineProperties properties;

    public CaffeineClient(Cache<String, Object> cache, CaffeineProperties properties) {
        this.cache = cache;
        this.properties = properties;
    }

    /**
     * 获取缓存值
     *
     * @param key 缓存键
     * @return 缓存值
     */
    public Object get(String key) {
        try {
            Object cachedValue = cache.getIfPresent(key);
            if (cachedValue == null) {
                return null;
            }

            // 检查是否为包装对象
            if (cachedValue instanceof CacheValueWrapper) {
                CacheValueWrapper wrapper = (CacheValueWrapper) cachedValue;
                if (wrapper.isExpired()) {
                    // 已过期，删除并返回null
                    cache.invalidate(key);
                    if (properties.getDebugEnabled()) {
                        log.debug("缓存已过期并清除: key={}", key);
                    }
                    return null;
                }

                Object value = wrapper.getValue();
                if (properties.getDebugEnabled()) {
                    log.debug("缓存命中(TTL): key={}, value={}, remainingTtl={}s",
                        key, value, wrapper.getRemainingTtl());
                }
                return value;
            } else {
                // 普通缓存值
                if (properties.getDebugEnabled()) {
                    log.debug("缓存命中: key={}, value={}", key, cachedValue);
                }
                return cachedValue;
            }
        } catch (Exception e) {
            log.error("获取缓存失败: key={}", key, e);
            return null;
        }
    }

    /**
     * 获取缓存值，如果不存在则使用loader加载
     *
     * @param key    缓存键
     * @param loader 加载器
     * @return 缓存值
     */
    public Object get(String key, Function<String, Object> loader) {
        try {
            return cache.get(key, loader);
        } catch (Exception e) {
            log.error("获取缓存失败: key={}", key, e);
            return null;
        }
    }

    /**
     * 设置缓存值
     *
     * @param key   缓存键
     * @param value 缓存值
     */
    public void put(String key, Object value) {
        try {
            cache.put(key, value);
            if (properties.getDebugEnabled()) {
                log.debug("设置缓存: key={}, value={}", key, value);
            }
        } catch (Exception e) {
            log.error("设置缓存失败: key={}, value={}", key, value, e);
        }
    }

    /**
     * 设置带过期时间的缓存值
     *
     * @param key        缓存键
     * @param value      缓存值
     * @param ttlSeconds 过期时间（秒）
     */
    public void put(String key, Object value, long ttlSeconds) {
        try {
            CacheValueWrapper wrapper = CacheValueWrapper.of(value, ttlSeconds);
            cache.put(key, wrapper);
            if (properties.getDebugEnabled()) {
                log.debug("设置缓存(TTL): key={}, value={}, ttl={}s", key, value, ttlSeconds);
            }
        } catch (Exception e) {
            log.error("设置缓存失败: key={}, value={}, ttl={}", key, value, ttlSeconds, e);
        }
    }

    /**
     * 批量设置缓存
     *
     * @param map 缓存键值对
     */
    public void putAll(Map<String, Object> map) {
        try {
            if (!CollectionUtils.isEmpty(map)) {
                cache.putAll(map);
                if (properties.getDebugEnabled()) {
                    log.debug("批量设置缓存: size={}", map.size());
                }
            }
        } catch (Exception e) {
            log.error("批量设置缓存失败", e);
        }
    }

    /**
     * 删除缓存
     *
     * @param key 缓存键
     */
    public void evict(String key) {
        try {
            cache.invalidate(key);
            if (properties.getDebugEnabled()) {
                log.debug("删除缓存: key={}", key);
            }
        } catch (Exception e) {
            log.error("删除缓存失败: key={}", key, e);
        }
    }

    /**
     * 批量删除缓存
     *
     * @param keys 缓存键集合
     */
    public void evictAll(Collection<String> keys) {
        try {
            if (!CollectionUtils.isEmpty(keys)) {
                cache.invalidateAll(keys);
                if (properties.getDebugEnabled()) {
                    log.debug("批量删除缓存: size={}", keys.size());
                }
            }
        } catch (Exception e) {
            log.error("批量删除缓存失败", e);
        }
    }

    /**
     * 清空所有缓存
     */
    public void clear() {
        try {
            cache.invalidateAll();
            if (properties.getDebugEnabled()) {
                log.debug("清空所有缓存");
            }
        } catch (Exception e) {
            log.error("清空缓存失败", e);
        }
    }

    /**
     * 检查缓存是否存在
     *
     * @param key 缓存键
     * @return 是否存在
     */
    public boolean hasKey(String key) {
        try {
            return cache.getIfPresent(key) != null;
        } catch (Exception e) {
            log.error("检查缓存存在性失败: key={}", key, e);
            return false;
        }
    }

    /**
     * 获取缓存大小
     *
     * @return 缓存大小
     */
    public long size() {
        try {
            return cache.estimatedSize();
        } catch (Exception e) {
            log.error("获取缓存大小失败", e);
            return 0;
        }
    }

    /**
     * 获取所有缓存键
     *
     * @return 缓存键集合
     */
    public Set<String> keys() {
        try {
            ConcurrentMap<String, Object> map = cache.asMap();
            return map.keySet();
        } catch (Exception e) {
            log.error("获取缓存键失败", e);
            return Set.of();
        }
    }

    /**
     * 获取缓存统计信息
     *
     * @return 统计信息
     */
    public CacheStats stats() {
        try {
            return cache.stats();
        } catch (Exception e) {
            log.error("获取缓存统计信息失败", e);
            return null;
        }
    }

    /**
     * 执行缓存清理
     */
    public void cleanUp() {
        try {
            cache.cleanUp();
            if (properties.getDebugEnabled()) {
                log.debug("执行缓存清理");
            }
        } catch (Exception e) {
            log.error("缓存清理失败", e);
        }
    }

    /**
     * 获取缓存剩余过期时间
     *
     * @param key 缓存键
     * @return 剩余秒数，-1表示永不过期，0表示已过期或不存在
     */
    public long getTtl(String key) {
        try {
            Object cachedValue = cache.getIfPresent(key);
            if (cachedValue instanceof CacheValueWrapper) {
                CacheValueWrapper wrapper = (CacheValueWrapper) cachedValue;
                return wrapper.getRemainingTtl();
            }
            return -1; // 普通缓存值视为永不过期
        } catch (Exception e) {
            log.error("获取缓存TTL失败: key={}", key, e);
            return 0;
        }
    }

    /**
     * 设置缓存过期时间
     *
     * @param key        缓存键
     * @param ttlSeconds 过期时间（秒）
     * @return 是否设置成功
     */
    public boolean expire(String key, long ttlSeconds) {
        try {
            Object cachedValue = cache.getIfPresent(key);
            if (cachedValue == null) {
                return false;
            }

            Object actualValue;
            if (cachedValue instanceof CacheValueWrapper) {
                actualValue = ((CacheValueWrapper) cachedValue).getValue();
            } else {
                actualValue = cachedValue;
            }

            // 重新设置带过期时间的缓存
            put(key, actualValue, ttlSeconds);
            return true;
        } catch (Exception e) {
            log.error("设置缓存过期时间失败: key={}, ttl={}", key, ttlSeconds, e);
            return false;
        }
    }

    /**
     * 获取原始缓存对象
     *
     * @return 原始缓存
     */
    public Cache<String, Object> getNativeCache() {
        return cache;
    }

}
