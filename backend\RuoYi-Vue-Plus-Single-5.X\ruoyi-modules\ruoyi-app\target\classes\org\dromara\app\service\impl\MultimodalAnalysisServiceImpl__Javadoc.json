{"doc": "\n 多模态分析服务实现类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "calculateClarity", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "org.dromara.app.service.IXunfeiService.SpeechRecognitionResult"], "doc": "\n 计算语音清晰度\r\n"}, {"name": "calculateFluency", "paramTypes": ["org.dromara.app.service.IXunfeiService.SpeechRecognitionResult"], "doc": "\n 计算语音流利度\r\n"}, {"name": "calculateConfidence", "paramTypes": ["org.dromara.app.service.IXunfeiService.VoiceEmotionResult"], "doc": "\n 计算自信度\r\n"}, {"name": "calculatePace", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "org.dromara.app.service.IXunfeiService.SpeechRecognitionResult"], "doc": "\n 计算语速\r\n"}, {"name": "calculateAudioOverallScore", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.AudioAnalysisResult"], "doc": "\n 计算音频总体评分\r\n"}, {"name": "generateAudioInsights", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.AudioAnalysisResult"], "doc": "\n 生成音频分析洞察\r\n"}, {"name": "extractTechnicalMetrics", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 提取技术指标\r\n"}, {"name": "calculateSignalToNoiseRatio", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 计算信噪比\r\n"}, {"name": "count<PERSON>auses", "paramTypes": ["java.lang.String"], "doc": "\n 统计停顿次数\r\n"}, {"name": "countRepetitions", "paramTypes": ["java.lang.String"], "doc": "\n 统计重复次数\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": "\n 统计填充词数量\r\n"}, {"name": "analyzeAudioQuality", "paramTypes": ["byte[]"], "doc": "\n 分析音频质量\r\n"}, {"name": "analyzeEyeContact", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 分析眼神交流\r\n"}, {"name": "analyzePosture", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 分析姿态\r\n"}, {"name": "analyzeExpressions", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 分析表情\r\n"}, {"name": "analyzeGestures", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 分析手势\r\n"}, {"name": "calculateVideoOverallScore", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.VideoAnalysisResult"], "doc": "\n 计算视频总体评分\r\n"}, {"name": "detectEmotions", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 检测情绪\r\n"}, {"name": "countGestures", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 统计手势\r\n"}, {"name": "identifyPostureIssues", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.VideoAnalysisResult"], "doc": "\n 识别姿态问题\r\n"}, {"name": "extractFaceMetrics", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 提取面部指标\r\n"}, {"name": "assessProfessionalKnowledge", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 评估专业知识水平\r\n"}, {"name": "assessProfessionalKnowledgeByKeywords", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 基于关键词评估专业知识\r\n"}, {"name": "analyzeLogicalThinking", "paramTypes": ["java.lang.String"], "doc": "\n 分析逻辑思维能力\r\n"}, {"name": "assessInnovation", "paramTypes": ["java.lang.String"], "doc": "\n 评估创新能力\r\n"}, {"name": "calculateSkillMatching", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 计算技能匹配度\r\n"}, {"name": "analyzeStarStructure", "paramTypes": ["java.lang.String"], "doc": "\n 分析STAR结构\r\n"}, {"name": "extractKeywords", "paramTypes": ["java.lang.String"], "doc": "\n 提取关键词\r\n"}, {"name": "identifySkills", "paramTypes": ["java.lang.String"], "doc": "\n 识别技能\r\n"}, {"name": "calculateTopicRelevance", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 计算话题相关性\r\n"}, {"name": "generateTextImprovementSuggestions", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.TextAnalysisResult"], "doc": "\n 生成文本改进建议\r\n"}, {"name": "getProfessionalKeywords", "paramTypes": ["java.lang.String"], "doc": "\n 获取专业关键词\r\n"}, {"name": "getRequiredSkills", "paramTypes": ["java.lang.String"], "doc": "\n 获取必需技能\r\n"}, {"name": "getTopicKeywords", "paramTypes": ["java.lang.String"], "doc": "\n 获取话题关键词\r\n"}, {"name": "generateOverallAssessment", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.AudioAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.VideoAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.TextAnalysisResult", "java.lang.String"], "doc": "\n 生成综合评估\r\n"}, {"name": "calculateTotalScore", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.AudioAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.VideoAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.TextAnalysisResult"], "doc": "\n 计算总分\r\n"}, {"name": "determineLevel", "paramTypes": ["int"], "doc": "\n 确定等级\r\n"}, {"name": "calculatePercentile", "paramTypes": ["int"], "doc": "\n 计算百分位数\r\n"}, {"name": "identifyStrengths", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.AudioAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.VideoAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.TextAnalysisResult"], "doc": "\n 识别优势\r\n"}, {"name": "identifyWeaknesses", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.AudioAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.VideoAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.TextAnalysisResult"], "doc": "\n 识别劣势\r\n"}, {"name": "generateDimensionScores", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.AudioAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.VideoAnalysisResult", "org.dromara.app.service.IMultimodalAnalysisService.TextAnalysisResult"], "doc": "\n 生成维度评分\r\n"}, {"name": "calculateDimensionPercentile", "paramTypes": ["int"], "doc": "\n 计算维度百分位数\r\n"}, {"name": "generateOverallFeedback", "paramTypes": ["int", "java.lang.String", "java.lang.String"], "doc": "\n 生成总体反馈\r\n"}, {"name": "generateComparisonData", "paramTypes": ["int", "java.lang.String"], "doc": "\n 生成对比数据\r\n"}], "constructors": []}