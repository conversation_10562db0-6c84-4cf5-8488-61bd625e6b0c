{"doc": "\n AI代理Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectEnabledAgentsOrdered", "paramTypes": [], "doc": "\n 获取启用的代理列表（按排序号排序）\r\n\r\n @return 代理列表\r\n"}, {"name": "selectEnabledByType", "paramTypes": ["java.lang.String"], "doc": "\n 根据代理类型获取启用的代理\r\n\r\n @param agentType 代理类型\r\n @return 代理信息\r\n"}, {"name": "incrementUsageCount", "paramTypes": ["java.lang.String"], "doc": "\n 增加代理使用次数\r\n\r\n @param agentType 代理类型\r\n @return 影响行数\r\n"}, {"name": "updateAverageRating", "paramTypes": ["java.lang.String", "java.lang.Double"], "doc": "\n 更新代理评分\r\n\r\n @param agentType     代理类型\r\n @param averageRating 平均评分\r\n @return 影响行数\r\n"}, {"name": "batchInsertDefaultAgents", "paramTypes": ["java.util.List"], "doc": "\n 批量初始化默认代理\r\n\r\n @param agents 代理列表\r\n @return 影响行数\r\n"}, {"name": "selectEnabledById", "paramTypes": ["java.lang.String"], "doc": "\n 根据代理ID获取启用的代理\r\n\r\n @param agentTypeId 代理类型ID\r\n @return 代理信息\r\n"}], "constructors": []}