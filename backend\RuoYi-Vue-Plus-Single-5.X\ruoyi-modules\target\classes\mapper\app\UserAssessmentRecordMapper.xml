<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.UserAssessmentRecordMapper">

    <resultMap type="org.dromara.app.domain.UserAssessmentRecord" id="UserAssessmentRecordResult">
        <result property="recordId" column="record_id"/>
        <result property="userId" column="user_id"/>
        <result property="assessmentType" column="assessment_type"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="totalQuestions" column="total_questions"/>
        <result property="completedQuestions" column="completed_questions"/>
        <result property="status" column="status"/>
        <result property="overallScore" column="overall_score"/>
        <result property="professionalKnowledge" column="professional_knowledge"/>
        <result property="logicalThinking" column="logical_thinking"/>
        <result property="languageExpression" column="language_expression"/>
        <result property="stressResistance" column="stress_resistance"/>
        <result property="teamCollaboration" column="team_collaboration"/>
        <result property="innovation" column="innovation"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectUserAssessmentRecordVo">
        select record_id,
               user_id,
               assessment_type,
               start_time,
               end_time,
               total_questions,
               completed_questions,
               status,
               overall_score,
               professional_knowledge,
               logical_thinking,
               language_expression,
               stress_resistance,
               team_collaboration,
               innovation,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from user_assessment_record
    </sql>

    <select id="selectRecordsByUserId" resultMap="UserAssessmentRecordResult">
        <include refid="selectUserAssessmentRecordVo"/>
        where user_id = #{userId}
        order by create_time desc
    </select>

    <select id="selectRecordsByUserIdAndType" resultMap="UserAssessmentRecordResult">
        <include refid="selectUserAssessmentRecordVo"/>
        where user_id = #{userId} and assessment_type = #{assessmentType}
        order by create_time desc
    </select>

    <select id="selectLatestRecordByUserId" resultMap="UserAssessmentRecordResult">
        <include refid="selectUserAssessmentRecordVo"/>
        where user_id = #{userId}
        order by create_time desc
        limit 1
    </select>

    <select id="selectLatestRecordByUserIdAndType" resultMap="UserAssessmentRecordResult">
        <include refid="selectUserAssessmentRecordVo"/>
        where user_id = #{userId} and assessment_type = #{assessmentType}
        order by create_time desc
        limit 1
    </select>

    <select id="countCompletedRecordsByUserId" resultType="int">
        select count(*)
        from user_assessment_record
        where user_id = #{userId}
          and status = 'completed'
    </select>

    <select id="selectRecordsByUserIdAndStatus" resultMap="UserAssessmentRecordResult">
        <include refid="selectUserAssessmentRecordVo"/>
        where user_id = #{userId} and status = #{status}
        order by create_time desc
    </select>

</mapper>
