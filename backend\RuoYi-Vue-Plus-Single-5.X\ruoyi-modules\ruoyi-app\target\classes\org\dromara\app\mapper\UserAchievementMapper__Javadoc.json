{"doc": "\n 用户成就数据层\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "countUnlockedAchievements", "paramTypes": ["java.lang.String"], "doc": "\n 获取用户已解锁成就数\r\n\r\n @param userId 用户ID\r\n @return 已解锁成就数\r\n"}, {"name": "countUnlockedAchievementsByCategory", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取用户某类别已解锁成就数\r\n\r\n @param userId   用户ID\r\n @param category 类别\r\n @return 该类别已解锁成就数\r\n"}, {"name": "countUnlockedAchievementsByRarity", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取用户某稀有度已解锁成就数\r\n\r\n @param userId 用户ID\r\n @param rarity 稀有度\r\n @return 该稀有度已解锁成就数\r\n"}, {"name": "countUnlockedAchievementsByDate", "paramTypes": ["java.lang.String", "java.time.LocalDateTime"], "doc": "\n 获取用户某日期解锁的成就数\r\n\r\n @param userId 用户ID\r\n @param date   日期\r\n @return 该日期解锁的成就数\r\n"}, {"name": "sumEarnedPoints", "paramTypes": ["java.lang.String"], "doc": "\n 获取用户获得的成就点数总和\r\n\r\n @param userId 用户ID\r\n @return 成就点数总和\r\n"}, {"name": "selectRecentAchievements", "paramTypes": ["java.lang.String", "int"], "doc": "\n 获取用户最近解锁的成就\r\n\r\n @param userId 用户ID\r\n @param limit  数量限制\r\n @return 最近解锁的成就\r\n"}, {"name": "selectInProgressAchievements", "paramTypes": ["java.lang.String"], "doc": "\n 获取用户进行中的成就\r\n\r\n @param userId 用户ID\r\n @return 进行中的成就\r\n"}, {"name": "selectUserAchievementDetail", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取用户成就详情\r\n\r\n @param userId        用户ID\r\n @param achievementId 成就ID\r\n @return 用户成就详情\r\n"}, {"name": "selectRecommendedAchievements", "paramTypes": ["java.lang.String", "int"], "doc": "\n 获取用户推荐的成就\r\n\r\n @param userId 用户ID\r\n @param limit  数量限制\r\n @return 推荐的成就\r\n"}, {"name": "selectLeaderboard", "paramTypes": ["java.lang.String", "int"], "doc": "\n 获取成就排行榜\r\n\r\n @param category 类别（可选）\r\n @param limit    数量限制\r\n @return 排行榜列表\r\n"}, {"name": "getUserRanking", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取用户排名\r\n\r\n @param userId   用户ID\r\n @param category 类别（可选）\r\n @return 用户排名\r\n"}, {"name": "selectByUserIdAndAchievementId", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 根据用户ID和成就ID获取用户成就\r\n\r\n @param userId        用户ID\r\n @param achievementId 成就ID\r\n @return 用户成就\r\n"}, {"name": "sumEventValues", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取用户某事件类型的累计事件值\r\n\r\n @param userId    用户ID\r\n @param eventType 事件类型\r\n @return 累计事件值\r\n"}, {"name": "updateProgress", "paramTypes": ["org.dromara.app.domain.UserAchievement"], "doc": "\n 更新用户成就进度\r\n\r\n @param userAchievement 用户成就\r\n @return 更新行数\r\n"}, {"name": "selectUnlockedCandidates", "paramTypes": ["java.lang.String"], "doc": "\n 查询未解锁且符合条件的成就\r\n\r\n @param userId 用户ID\r\n @return 符合条件的成就列表\r\n"}, {"name": "updateNotificationStatus", "paramTypes": ["java.lang.String", "int"], "doc": "\n 更新通知状态\r\n\r\n @param userId 用户ID\r\n @param status 状态（0=未通知，1=已通知）\r\n @return 更新行数\r\n"}, {"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 标记成就为已查看\r\n\r\n @param userId        用户ID\r\n @param achievementId 成就ID\r\n @return 更新行数\r\n"}, {"name": "selectByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询用户成就列表\r\n\r\n @param userId 用户ID\r\n @return 用户成就列表\r\n"}, {"name": "selectCompletedByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户已完成的成就列表\r\n\r\n @param userId 用户ID\r\n @return 已完成的成就列表\r\n"}, {"name": "selectInProgressByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户进行中的成就列表\r\n\r\n @param userId 用户ID\r\n @return 进行中的成就列表\r\n"}, {"name": "countCompletedByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 统计用户已完成的成就数量\r\n\r\n @param userId 用户ID\r\n @return 已完成的成就数量\r\n"}, {"name": "sumRewardPointsByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 统计用户获得的总积分\r\n\r\n @param userId 用户ID\r\n @return 总积分\r\n"}, {"name": "updateProgress", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Long", "java.math.BigDecimal"], "doc": "\n 更新用户成就进度\r\n\r\n @param userId        用户ID\r\n @param achievementId 成就ID\r\n @param currentValue  当前值\r\n @param progress      进度\r\n @return 更新行数\r\n"}, {"name": "completeAchievement", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 完成用户成就\r\n\r\n @param userId        用户ID\r\n @param achievementId 成就ID\r\n @return 更新行数\r\n"}, {"name": "selectStatsByUserIdAndType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 根据成就类型查询用户成就统计\r\n\r\n @param userId          用户ID\r\n @param achievementType 成就类型\r\n @return 用户成就统计列表\r\n"}, {"name": "selectRecentByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询用户最近的成就列表\r\n\r\n @param aLong 用户ID\r\n @param limit  数量限制\r\n @return 最近的成就列表\r\n"}], "constructors": []}