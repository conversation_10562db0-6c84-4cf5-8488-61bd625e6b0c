{"doc": "\n 维度评分Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-17\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByResultId", "paramTypes": ["java.lang.String"], "doc": "\n 根据结果ID查询维度评分列表\r\n\r\n @param resultId 结果ID\r\n @return 维度评分列表\r\n"}, {"name": "selectByResultIdAndDimension", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据结果ID和维度名称查询维度评分\r\n\r\n @param resultId  结果ID\r\n @param dimension 维度名称\r\n @return 维度评分\r\n"}, {"name": "batchInsert", "paramTypes": ["java.util.List"], "doc": "\n 批量插入维度评分\r\n\r\n @param dimensionScores 维度评分列表\r\n @return 插入数量\r\n"}, {"name": "deleteByResultId", "paramTypes": ["java.lang.String"], "doc": "\n 根据结果ID删除维度评分\r\n\r\n @param resultId 结果ID\r\n @return 删除数量\r\n"}, {"name": "selectAvgScoreByDimension", "paramTypes": ["java.lang.String"], "doc": "\n 查询维度平均分数\r\n\r\n @param dimension 维度名称\r\n @return 平均分数\r\n"}, {"name": "selectUserDimensionHistory", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer"], "doc": "\n 查询用户在某维度的历史分数\r\n\r\n @param userId    用户ID\r\n @param dimension 维度名称\r\n @param limit     限制数量\r\n @return 分数列表\r\n"}], "constructors": []}