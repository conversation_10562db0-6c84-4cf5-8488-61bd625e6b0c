package org.dromara.app.controller.pay;

import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.service.IPaymentSseService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 支付SSE控制器
 * 用于建立SSE连接和推送支付状态消息
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/pay/sse")
@Tag(name = "支付SSE管理", description = "支付状态实时推送接口")
public class PaymentSseController {

    private final IPaymentSseService paymentSseService;

    /**
     * 建立支付状态SSE连接
     */
    @GetMapping(value = "/connect", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @SaIgnore
    @Operation(summary = "建立支付状态SSE连接", description = "建立SSE连接用于实时推送支付状态")
    public SseEmitter connect(
        @Parameter(description = "订单号", required = true)
        @RequestParam String orderNo,
        @Parameter(description = "支付token", required = true)
        @RequestParam String payToken,
        HttpServletResponse response
    ) {
        try {
            // 设置响应头
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Cache-Control", "no-cache");
            response.setHeader("Connection", "keep-alive");
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Credentials", "true");

            log.info("建立支付SSE连接，订单号：{}", orderNo);

            // 验证支付token
            if (!paymentSseService.validatePaymentToken(orderNo, payToken)) {
                log.warn("SSE连接token验证失败，订单号：{}", orderNo);
                throw new RuntimeException("支付token无效或已过期");
            }

            // 创建SSE连接
            return paymentSseService.createConnection(orderNo, payToken);

        } catch (Exception e) {
            log.error("建立SSE连接失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
            throw new RuntimeException("建立连接失败：" + e.getMessage());
        }
    }

    /**
     * 手动查询订单状态
     */
    @PostMapping("/query-status")
    @SaIgnore
    @Operation(summary = "手动查询订单状态", description = "用户手动查询订单支付状态")
    public void queryOrderStatus(
        @Parameter(description = "订单号", required = true)
        @RequestParam String orderNo,
        @Parameter(description = "支付token", required = true)
        @RequestParam String payToken
    ) {
        try {
            log.info("手动查询订单状态，订单号：{}", orderNo);

            // 验证token并查询状态
            paymentSseService.manualQueryOrderStatus(orderNo, payToken);

        } catch (Exception e) {
            log.error("手动查询订单状态失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
            throw new RuntimeException("查询失败：" + e.getMessage());
        }
    }
}
