{"doc": " 缓存类型枚举\n\n <AUTHOR>\n", "fields": [{"name": "code", "doc": " 缓存类型代码\n"}, {"name": "description", "doc": " 缓存类型描述\n"}], "enumConstants": [{"name": "USER", "doc": " 用户缓存\n"}, {"name": "SYSTEM", "doc": " 系统缓存\n"}, {"name": "CONFIG", "doc": " 配置缓存\n"}, {"name": "DICT", "doc": " 字典缓存\n"}, {"name": "PERMISSION", "doc": " 权限缓存\n"}, {"name": "TEMP", "doc": " 临时缓存\n"}], "methods": [{"name": "getByCode", "paramTypes": ["java.lang.String"], "doc": " 根据代码获取枚举\n\n @param code 代码\n @return 枚举\n"}], "constructors": []}