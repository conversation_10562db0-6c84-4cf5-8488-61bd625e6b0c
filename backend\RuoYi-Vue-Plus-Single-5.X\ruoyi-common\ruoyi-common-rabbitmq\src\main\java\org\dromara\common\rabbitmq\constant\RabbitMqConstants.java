package org.dromara.common.rabbitmq.constant;

/**
 * RabbitMQ 常量
 *
 * <AUTHOR>
 */
public interface RabbitMqConstants {

    /**
     * 默认交换机类型
     */
    interface ExchangeType {
        String DIRECT = "direct";
        String TOPIC = "topic";
        String FANOUT = "fanout";
        String HEADERS = "headers";
    }

    /**
     * 死信队列相关
     */
    interface DeadLetter {
        /**
         * 死信交换机
         */
        String EXCHANGE = "dead.letter.exchange";

        /**
         * 死信队列
         */
        String QUEUE = "dead.letter.queue";

        /**
         * 死信路由键
         */
        String ROUTING_KEY = "dead.letter.routing.key";
    }

    /**
     * 延迟队列相关
     */
    interface Delay {
        /**
         * 延迟交换机
         */
        String EXCHANGE = "delay.exchange";

        /**
         * 延迟队列
         */
        String QUEUE = "delay.queue";

        /**
         * 延迟路由键
         */
        String ROUTING_KEY = "delay.routing.key";
    }

    /**
     * 消息头常量
     */
    interface Headers {
        /**
         * 消息ID
         */
        String MESSAGE_ID = "messageId";

        /**
         * 时间戳
         */
        String TIMESTAMP = "timestamp";

        /**
         * 重试次数
         */
        String RETRY_COUNT = "retryCount";

        /**
         * 消息来源
         */
        String SOURCE = "source";
    }

    /**
     * 默认配置
     */
    interface Default {
        /**
         * 默认消息过期时间（毫秒）
         */
        long MESSAGE_TTL = 60000;

        /**
         * 默认队列最大长度
         */
        int QUEUE_MAX_LENGTH = 10000;

        /**
         * 默认重试次数
         */
        int RETRY_COUNT = 3;
    }
}
