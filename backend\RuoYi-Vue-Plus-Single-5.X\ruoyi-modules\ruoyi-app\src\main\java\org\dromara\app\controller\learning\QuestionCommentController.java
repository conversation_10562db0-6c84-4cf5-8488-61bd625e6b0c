package org.dromara.app.controller.learning;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.vo.QuestionCommentVO;
import org.dromara.app.service.ILearningService;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 题目评论控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController("appQuestionCommentController")
@RequestMapping("/app/learning/comments")
public class QuestionCommentController extends BaseController {

    private final ILearningService learningService;

    /**
     * 删除题目评论
     *
     * @param commentId 评论ID
     * @return 删除结果响应
     */
    @PostMapping("/{commentId}/delete")
    public R<Map<String, Object>> deleteComment(@PathVariable String commentId) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            boolean success = learningService.deleteQuestionComment(userId, commentId);

            if (success) {
                return R.ok(Map.of("message", "删除成功"));
            } else {
                return R.fail("删除失败，请检查权限或评论是否存在");
            }
        } catch (Exception e) {
            log.error("删除题目评论失败：{}", e.getMessage(), e);
            return R.fail("删除失败，请稍后重试");
        }
    }

    /**
     * 点赞/取消点赞评论
     *
     * @param commentId 评论ID
     * @param request   HTTP请求对象
     * @return 点赞结果响应
     */
    @PostMapping("/{commentId}/like")
    public R<Map<String, Object>> likeComment(
        @PathVariable String commentId) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            Map<String, Object> result = learningService.likeQuestionComment(userId, commentId);
            return R.ok(result);
        } catch (Exception e) {
            log.error("点赞评论失败：{}", e.getMessage(), e);
            return R.fail("操作失败，请稍后重试");
        }
    }

    /**
     * 举报评论
     *
     * @param commentId 评论ID
     * @param request   举报请求参数
     * @return 举报结果响应
     */
    @PostMapping("/{commentId}/report")
    public R<Map<String, Object>> reportComment(
        @PathVariable String commentId,
        @RequestBody @Valid Map<String, Object> request) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            String reason = (String) request.get("reason");
            String description = (String) request.get("description");

            if (StrUtil.isBlank(reason)) {
                return R.fail("举报原因不能为空");
            }

            boolean success = learningService.reportContent(
                userId, commentId, "comment", reason, description);

            if (success) {
                return R.ok(Map.of("message", "举报成功，我们会及时处理"));
            } else {
                return R.fail("举报失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("举报评论失败：{}", e.getMessage(), e);
            return R.fail("举报失败，请稍后重试");
        }
    }

    /**
     * 获取评论详情
     *
     * @param commentId 评论ID
     * @return 评论详情响应
     */
    @GetMapping("/{commentId}")
    public R<QuestionCommentVO> getCommentDetail(@PathVariable String commentId) {
        try {
            // TODO : 这里可以添加权限检查，确保用户只能查看自己的评论或公开评论
            return R.fail("获取失败，请稍后重试");
        } catch (Exception e) {
            log.error("获取评论详情失败：{}", e.getMessage(), e);
            return R.fail("获取失败，请稍后重试");
        }
    }
}
