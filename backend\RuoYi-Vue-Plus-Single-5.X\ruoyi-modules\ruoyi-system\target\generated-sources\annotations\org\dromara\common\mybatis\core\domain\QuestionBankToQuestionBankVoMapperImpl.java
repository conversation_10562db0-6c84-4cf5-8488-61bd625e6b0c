package org.dromara.common.mybatis.core.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.QuestionBankVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T11:27:38+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.11 (Amazon.com Inc.)"
)
@Component
public class QuestionBankToQuestionBankVoMapperImpl implements QuestionBankToQuestionBankVoMapper {

    @Override
    public QuestionBankVo convert(QuestionBank arg0) {
        if ( arg0 == null ) {
            return null;
        }

        QuestionBankVo questionBankVo = new QuestionBankVo();

        questionBankVo.setBankId( arg0.getBankId() );
        questionBankVo.setBankCode( arg0.getBankCode() );
        questionBankVo.setTitle( arg0.getTitle() );
        questionBankVo.setDescription( arg0.getDescription() );
        questionBankVo.setMajorId( arg0.getMajorId() );
        questionBankVo.setIcon( arg0.getIcon() );
        questionBankVo.setColor( arg0.getColor() );
        questionBankVo.setDifficulty( arg0.getDifficulty() );
        questionBankVo.setTotalQuestions( arg0.getTotalQuestions() );
        questionBankVo.setPracticeCount( arg0.getPracticeCount() );
        questionBankVo.setCategories( arg0.getCategories() );
        questionBankVo.setSort( arg0.getSort() );
        questionBankVo.setStatus( arg0.getStatus() );
        questionBankVo.setRemark( arg0.getRemark() );
        questionBankVo.setCreateTime( arg0.getCreateTime() );
        questionBankVo.setUpdateTime( arg0.getUpdateTime() );
        questionBankVo.setIsBookmarked( arg0.getIsBookmarked() );
        questionBankVo.setProgress( arg0.getProgress() );

        return questionBankVo;
    }

    @Override
    public QuestionBankVo convert(QuestionBank arg0, QuestionBankVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setBankId( arg0.getBankId() );
        arg1.setBankCode( arg0.getBankCode() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setMajorId( arg0.getMajorId() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setColor( arg0.getColor() );
        arg1.setDifficulty( arg0.getDifficulty() );
        arg1.setTotalQuestions( arg0.getTotalQuestions() );
        arg1.setPracticeCount( arg0.getPracticeCount() );
        arg1.setCategories( arg0.getCategories() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setIsBookmarked( arg0.getIsBookmarked() );
        arg1.setProgress( arg0.getProgress() );

        return arg1;
    }
}
