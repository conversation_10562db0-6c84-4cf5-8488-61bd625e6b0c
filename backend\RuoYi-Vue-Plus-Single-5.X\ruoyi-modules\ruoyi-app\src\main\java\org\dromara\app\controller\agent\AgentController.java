package org.dromara.app.controller.agent;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.Agent;
import org.dromara.app.domain.ChatMessage;
import org.dromara.app.domain.ChatSession;
import org.dromara.app.domain.dto.ChatRequestDto;
import org.dromara.app.domain.dto.ChatResponseDto;
import org.dromara.app.service.IAgentService;
import org.dromara.app.service.IFileService;
import org.dromara.app.service.impl.LangChain4jChatServiceImpl;
import org.dromara.common.core.domain.R;
import org.dromara.common.sse.utils.SseMessageUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI聊天控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/chat")
public class AgentController {

    @Qualifier("langChain4jChatService")
    private final LangChain4jChatServiceImpl chatService;
    private final IAgentService agentService;
    private final IFileService fileService;

    /**
     * 发送聊天消息（同步响应）
     *
     * @param request 聊天请求
     * @return 聊天响应
     */
    @PostMapping("/send")
    public R<ChatResponseDto> sendMessage(@Valid @RequestBody ChatRequestDto request) {
        Long userId = StpUtil.getLoginIdAsLong();
        ChatResponseDto response = chatService.sendMessage(request, userId);
        return R.ok(response);
    }

    /**
     * 发送聊天消息（流式响应）
     *
     * @param message   消息内容
     * @param sessionId 会话ID
     * @param agentType Agent类型
     * @return SSE流
     */
    @SaIgnore
    @GetMapping(value = "/send/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter sendMessageStream(
        @RequestParam @NotBlank(message = "消息内容不能为空") @Size(max = 4000, message = "消息内容过长") String message,
        @RequestParam(required = false) String sessionId,
        @RequestParam(required = false) String agentType,
        @RequestParam @NotBlank(message = "satoken不能为空") String satoken) {
        Long userId = SseMessageUtils.getUserIdIfLogin(satoken);
        ChatRequestDto chatRequestDto = new ChatRequestDto();
        chatRequestDto.setMessage(message);
        chatRequestDto.setSessionId(sessionId);
        chatRequestDto.setAgentType(agentType != null ? agentType : "general");

        return chatService.sendMessageStream(chatRequestDto, userId);
    }

    /**
     * 创建新会话
     *
     * @param agentType 代理类型
     * @param title     会话标题（可选）
     * @return 会话信息
     */
    @PostMapping("/session/create")
    public R<ChatSession> createSession(
        @RequestParam @NotBlank(message = "Agent类型不能为空") String agentType,
        @RequestParam(required = false) @Size(max = 100, message = "会话标题过长") String title) {

        Long userId = StpUtil.getLoginIdAsLong();
        ChatSession session = chatService.createSession(userId, agentType, title);
        return R.ok(session);
    }

    /**
     * 获取用户会话列表
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 会话分页结果
     */
    @GetMapping("/sessions")
    public R<Page<ChatSession>> getUserSessions(@RequestParam(defaultValue = "1") Integer pageNum,
                                                @RequestParam(defaultValue = "20") Integer pageSize) {
        Long userId = StpUtil.getLoginIdAsLong();
        Page<ChatSession> sessions = chatService.getUserSessions(userId, pageNum, pageSize);
        return R.ok(sessions);
    }

    /**
     * 获取会话详情
     *
     * @param sessionId 会话ID
     * @return 会话详情
     */
    @GetMapping("/session/{sessionId}")
    public R<ChatSession> getSessionDetail(@PathVariable String sessionId) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            ChatSession session = chatService.getSessionDetail(sessionId, userId);
            if (session == null) {
                return R.fail("会话不存在或无权限访问");
            }
            return R.ok(session);
        } catch (Exception e) {
            log.error("获取会话详情失败", e);
            return R.fail("获取会话详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取会话消息列表
     *
     * @param sessionId 会话ID
     * @param pageNum   页码
     * @param pageSize  每页大小
     * @return 消息分页结果
     */
    @GetMapping("/session/{sessionId}/messages")
    public R<Page<ChatMessage>> getSessionMessages(@PathVariable String sessionId,
                                                   @RequestParam(defaultValue = "1") Integer pageNum,
                                                   @RequestParam(defaultValue = "50") Integer pageSize) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            Page<ChatMessage> messages = chatService.getSessionMessages(sessionId, userId, pageNum, pageSize);
            return R.ok(messages);
        } catch (Exception e) {
            log.error("获取会话消息失败", e);
            return R.fail("获取会话消息失败：" + e.getMessage());
        }
    }

    /**
     * 删除会话
     *
     * @param sessionId 会话ID
     * @return 操作结果
     */
    @DeleteMapping("/session/{sessionId}")
    public R<Void> deleteSession(@PathVariable String sessionId) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            boolean success = chatService.deleteSession(sessionId, userId);
            return success ? R.ok() : R.fail("删除会话失败");
        } catch (Exception e) {
            log.error("删除会话失败", e);
            return R.fail("删除会话失败：" + e.getMessage());
        }
    }

    /**
     * 清空会话消息
     *
     * @param sessionId 会话ID
     * @return 操作结果
     */
    @PostMapping("/session/{sessionId}/clear")
    public R<Void> clearSessionMessages(@PathVariable String sessionId) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            boolean success = chatService.clearSessionMessages(sessionId, userId);
            return success ? R.ok() : R.fail("清空会话消息失败");
        } catch (Exception e) {
            log.error("清空会话消息失败", e);
            return R.fail("清空会话消息失败：" + e.getMessage());
        }
    }

    /**
     * 更新会话标题
     *
     * @param sessionId 会话ID
     * @param title     新标题
     * @return 操作结果
     */
    @PutMapping("/session/{sessionId}/title")
    public R<Void> updateSessionTitle(@PathVariable String sessionId,
                                      @RequestParam String title) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            boolean success = chatService.updateSessionTitle(sessionId, userId, title);
            return success ? R.ok() : R.fail("更新会话标题失败");
        } catch (Exception e) {
            log.error("更新会话标题失败", e);
            return R.fail("更新会话标题失败：" + e.getMessage());
        }
    }

    /**
     * 归档/取消归档会话
     *
     * @param sessionId 会话ID
     * @param archived  是否归档
     * @return 操作结果
     */
    @PutMapping("/session/{sessionId}/archive")
    public R<Void> archiveSession(@PathVariable String sessionId,
                                  @RequestParam Boolean archived) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            boolean success = chatService.archiveSession(sessionId, userId, archived);
            return success ? R.ok() : R.fail("归档操作失败");
        } catch (Exception e) {
            log.error("归档会话失败", e);
            return R.fail("归档操作失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户聊天统计信息
     *
     * @return 统计信息
     */
    @GetMapping("/stats")
    public R<Map<String, Object>> getUserChatStats() {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            Map<String, Object> stats = chatService.getUserChatStats(userId);
            return R.ok(stats);
        } catch (Exception e) {
            log.error("获取聊天统计信息失败", e);
            return R.fail("获取统计信息失败：" + e.getMessage());
        }
    }

    // ========== Agent 相关接口 ==========

    /**
     * 获取所有启用的代理列表
     *
     * @return 代理列表
     */
    @GetMapping("/agents")
    public R<List<Agent>> getEnabledAgents() {
        try {
            List<Agent> agents = agentService.getEnabledAgents();
            return R.ok(agents);
        } catch (Exception e) {
            log.error("获取代理列表失败", e);
            return R.fail("获取代理列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据类型获取代理信息
     *
     * @param agentType 代理类型
     * @return 代理信息
     */
    @GetMapping("/agent/{agentType}")
    public R<Agent> getAgentByType(@PathVariable String agentType) {
        try {
            Agent agent = agentService.getAgentByType(agentType);
            if (agent == null) {
                return R.fail("代理不存在");
            }
            return R.ok(agent);
        } catch (Exception e) {
            log.error("获取代理信息失败", e);
            return R.fail("获取代理信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取代理的快速操作列表
     *
     * @param agentType 代理类型
     * @return 快速操作列表
     */
    @GetMapping("/agent/{agentType}/quick-actions")
    public R<List<Agent.QuickAction>> getQuickActions(@PathVariable String agentType) {
        try {
            List<Agent.QuickAction> actions = agentService.getQuickActions(agentType);
            return R.ok(actions);
        } catch (Exception e) {
            log.error("获取快速操作列表失败", e);
            return R.fail("获取快速操作列表失败：" + e.getMessage());
        }
    }

    /**
     * 调用特定类型的Agent进行处理（同步响应）
     *
     * @param agentType 代理类型
     * @param message   用户消息
     * @param params    额外参数
     * @return 处理结果
     */
    @PostMapping("/agent/{agentType}")
    public R<String> callAgent(
        @PathVariable String agentType,
        @RequestParam String message,
        @RequestParam(required = false) Map<String, String> params) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            String response = chatService.callAgentService(agentType, message, params, userId);
            return R.ok(response);
        } catch (Exception e) {
            log.error("调用Agent失败: {}", agentType, e);
            return R.fail("调用Agent失败：" + e.getMessage());
        }
    }

    /**
     * 调用特定类型的Agent进行处理（流式响应）
     *
     * @param agentType 代理类型
     * @param message   用户消息
     * @param params    额外参数
     * @return SSE流
     */
    @SaIgnore
    @GetMapping(value = "/agent/{agentType}/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter callAgentStream(
        @PathVariable @NotBlank(message = "Agent类型不能为空") String agentType,
        @RequestParam @NotBlank(message = "消息内容不能为空") @Size(max = 4000, message = "消息内容过长") String message,
        @RequestParam(required = false) Map<String, String> params,
        @RequestParam @NotBlank(message = "satoken不能为空") String satoken) {
        Long userId = SseMessageUtils.getUserIdIfLogin(satoken);

        return chatService.callAgentServiceStream(agentType, message, params, userId);
    }

    // ========== 文件上传相关接口 ==========

    /**
     * 上传聊天附件
     *
     * @param file 文件
     * @param type 文件类型：image/file/voice
     * @return 上传结果
     */
    @PostMapping("/upload")
    public R<Map<String, Object>> uploadFile(@RequestParam("file") MultipartFile file,
                                             @RequestParam(defaultValue = "file") String type) {
        Long userId = StpUtil.getLoginIdAsLong();
        IFileService.FileUploadResult result = fileService.uploadChatFile(file, type, userId);

        if (result.isSuccess()) {
            Map<String, Object> data = new HashMap<>();
            data.put("url", result.getFileUrl());
            data.put("name", result.getFileName());
            data.put("size", result.getFileSize());
            data.put("type", result.getMimeType());
            data.put("metadata", result.getMetadata());

            return R.ok(data);
        } else {
            return R.fail(result.getErrorMessage());
        }
    }

    /**
     * 语音转文字
     *
     * @param audioFile 音频文件
     * @return 转换结果
     */
    @PostMapping("/speech-to-text")
    public R<Map<String, Object>> speechToText(@RequestParam("audio") MultipartFile audioFile) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            IFileService.SpeechToTextResult result = fileService.speechToText(audioFile, userId);

            if (result.isSuccess()) {
                Map<String, Object> data = new HashMap<>();
                data.put("text", result.getText());
                data.put("language", result.getLanguage());
                data.put("confidence", result.getConfidence());
                data.put("duration", result.getDuration());
                data.put("metadata", result.getMetadata());

                return R.ok(data);
            } else {
                return R.fail(result.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("语音转文字失败", e);
            return R.fail("语音转文字失败：" + e.getMessage());
        }
    }

    // ========== AI模型和代理相关信息接口 ==========

    /**
     * 获取可用的AI提供商列表
     *
     * @return 提供商列表
     */
    @GetMapping("/providers")
    public R<List<String>> getAvailableProviders() {
        try {
            // 返回支持的AI提供商
            List<String> providers = List.of("ollama", "openai", "dashscope");
            return R.ok(providers);
        } catch (Exception e) {
            log.error("获取提供商列表失败", e);
            return R.fail("获取提供商列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取Agent使用统计
     *
     * @return 使用统计
     */
    @GetMapping("/agent-stats")
    public R<Map<String, Object>> getAgentStats() {
        try {
            Map<String, Object> stats = new HashMap<>();

            // 获取所有Agent的使用统计
            List<Agent> agents = agentService.getEnabledAgents();
            for (Agent agent : agents) {
                Map<String, Object> agentStat = new HashMap<>();
                agentStat.put("usageCount", agent.getUsageCount());
                agentStat.put("averageRating", agent.getAverageRating());
                stats.put(agent.getAgentType(), agentStat);
            }

            return R.ok(stats);
        } catch (Exception e) {
            log.error("获取Agent统计失败", e);
            return R.fail("获取Agent统计失败：" + e.getMessage());
        }
    }
}
