{"doc": " 学习进度Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询学习进度\n\n @param userId 用户ID\n @return 学习进度列表\n"}, {"name": "selectByUserIdAndStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 根据用户ID和状态查询学习进度\n\n @param userId 用户ID\n @param status 状态\n @return 学习进度列表\n"}, {"name": "selectByLearningPathAndUser", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 根据学习路径ID查询进度\n\n @param learningPathId 学习路径ID\n @param userId 用户ID\n @return 学习进度\n"}, {"name": "selectByResourceAndUser", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 根据资源ID查询进度\n\n @param resourceId 资源ID\n @param userId 用户ID\n @return 学习进度\n"}, {"name": "selectUserLearningStatistics", "paramTypes": ["java.lang.Long"], "doc": " 查询用户学习统计\n\n @param userId 用户ID\n @return 统计信息\n"}, {"name": "selectRecentLearningProgress", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询最近的学习进度\n\n @param userId 用户ID\n @param limit 限制数量\n @return 学习进度列表\n"}, {"name": "selectOngoingLearning", "paramTypes": ["java.lang.Long"], "doc": " 查询正在进行的学习\n\n @param userId 用户ID\n @return 学习进度列表\n"}, {"name": "selectCompletedLearning", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询已完成的学习\n\n @param userId 用户ID\n @param limit 限制数量\n @return 学习进度列表\n"}, {"name": "selectLearningEffectivenessStats", "paramTypes": ["java.lang.Long"], "doc": " 查询学习效果统计\n\n @param userId 用户ID\n @return 效果统计\n"}, {"name": "selectStudyTimeStatistics", "paramTypes": ["java.lang.Long"], "doc": " 查询学习时间统计\n\n @param userId 用户ID\n @return 时间统计\n"}, {"name": "selectLearningTrend", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询学习趋势数据\n\n @param userId 用户ID\n @param days 天数\n @return 趋势数据\n"}, {"name": "selectItemsNeedingReminder", "paramTypes": [], "doc": " 查询需要提醒的学习项目\n\n @return 需要提醒的学习进度列表\n"}, {"name": "selectOverdueLearning", "paramTypes": ["java.lang.Long"], "doc": " 查询超期的学习项目\n\n @param userId 用户ID\n @return 超期学习进度列表\n"}, {"name": "updateLearningStatistics", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 更新学习统计\n\n @param progressId 进度ID\n @param statisticsJson 统计JSON\n @return 更新行数\n"}], "constructors": []}