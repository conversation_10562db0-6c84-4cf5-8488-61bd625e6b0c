2025-08-02 09:40:45 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 09:40:45 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.11 with PID 3080 (C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend)
2025-08-02 09:40:45 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-02 09:44:55 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 09:44:55 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.11 with PID 15256 (C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend)
2025-08-02 09:44:55 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-02 09:53:49 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 09:53:49 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.11 with PID 8864 (C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend)
2025-08-02 09:53:49 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-02 09:59:21 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 09:59:21 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.11 with PID 8496 (C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend)
2025-08-02 09:59:21 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-02 10:04:49 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 10:04:49 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.11 with PID 15108 (C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend)
2025-08-02 10:04:49 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-02 10:05:14 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-02 10:05:15 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-02 10:05:15 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-02 10:05:34 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@2c40e456
2025-08-02 10:05:34 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-02 10:05:34 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-02 10:05:34 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-02 10:05:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-02 10:05:36 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-02 10:05:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-02 10:05:55 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-02 10:05:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-02 10:11:26 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 10:11:26 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.11 with PID 28460 (C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend)
2025-08-02 10:11:26 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-02 10:11:50 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-02 10:11:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-02 10:11:52 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-02 10:12:10 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@6b4376e1
2025-08-02 10:12:10 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-02 10:12:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-02 10:12:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-02 10:12:12 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-02 10:12:12 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-02 10:12:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-02 10:12:26 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-02 10:12:26 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-02 10:24:01 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 10:24:01 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.11 with PID 2856 (C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend)
2025-08-02 10:24:01 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-02 10:27:56 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 10:27:57 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.11 with PID 30696 (C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend)
2025-08-02 10:27:57 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-02 10:30:44 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 10:30:44 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.11 with PID 27424 (C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend)
2025-08-02 10:30:44 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-02 10:33:21 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 10:33:21 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.11 with PID 30656 (C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend)
2025-08-02 10:33:21 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-02 10:33:45 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-02 10:33:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-02 10:33:47 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-02 10:34:03 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@7df994ec
2025-08-02 10:34:03 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-02 10:34:03 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-02 10:34:03 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-02 10:34:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-02 10:34:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-02 10:34:21 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-02 10:34:21 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-02 10:34:21 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-02 11:28:59 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 11:28:59 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.11 with PID 29948 (C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend)
2025-08-02 11:28:59 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-02 11:29:07 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
