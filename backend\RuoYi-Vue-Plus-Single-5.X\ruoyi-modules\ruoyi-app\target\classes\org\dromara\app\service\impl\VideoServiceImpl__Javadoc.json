{"doc": "\n 视频课程Service业务层处理\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "convertToVideoItemVo", "paramTypes": ["org.dromara.app.domain.Video"], "doc": "\n 转换为视频项VO\r\n"}, {"name": "convertToVideoDetailVo", "paramTypes": ["org.dromara.app.domain.Video", "java.lang.Long"], "doc": "\n 转换为视频详情VO\r\n"}, {"name": "convertToRelatedVideoVo", "paramTypes": ["org.dromara.app.domain.Video"], "doc": "\n 转换为相关视频VO\r\n"}, {"name": "convertToVideoCommentVo", "paramTypes": ["org.dromara.app.domain.VideoComment", "java.lang.Long"], "doc": "\n 转换为视频评论VO\r\n"}, {"name": "updateDatabaseRecord", "paramTypes": ["java.lang.Long", "java.lang.Long", "org.dromara.app.domain.VideoPlayRecord"], "doc": "\n 更新数据库记录（从缓存数据同步到数据库）\r\n"}], "constructors": []}