{"doc": "\n 用户徽章数据层\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectUserBadges", "paramTypes": ["java.lang.String"], "doc": "\n 获取用户所有徽章\r\n\r\n @param userId 用户ID\r\n @return 徽章列表\r\n"}, {"name": "selectUserBadgeById", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取用户徽章详情\r\n\r\n @param userId  用户ID\r\n @param badgeId 徽章ID\r\n @return 徽章详情\r\n"}, {"name": "countPinnedBadges", "paramTypes": ["java.lang.String"], "doc": "\n 获取用户置顶徽章数量\r\n\r\n @param userId 用户ID\r\n @return 置顶徽章数量\r\n"}, {"name": "updatePinStatus", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Bo<PERSON>an", "java.time.LocalDateTime"], "doc": "\n 更新徽章置顶状态\r\n\r\n @param userId   用户ID\r\n @param badgeId  徽章ID\r\n @param isPinned 是否置顶\r\n @param pinnedAt 置顶时间\r\n @return 更新行数\r\n"}, {"name": "selectPinnedBadges", "paramTypes": ["java.lang.String"], "doc": "\n 获取用户置顶徽章\r\n\r\n @param userId 用户ID\r\n @return 置顶徽章列表\r\n"}, {"name": "countTotalBadges", "paramTypes": [], "doc": "\n 获取总徽章数\r\n\r\n @return 总徽章数\r\n"}, {"name": "countUnlockedBadges", "paramTypes": ["java.lang.String"], "doc": "\n 获取用户已解锁徽章数\r\n\r\n @param userId 用户ID\r\n @return 已解锁徽章数\r\n"}, {"name": "selectByUserIdAndBadgeId", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据用户ID和徽章ID获取用户徽章\r\n\r\n @param userId  用户ID\r\n @param badgeId 徽章ID\r\n @return 用户徽章\r\n"}, {"name": "selectRecentUnlockedBadges", "paramTypes": ["java.lang.String", "int"], "doc": "\n 获取用户最近解锁的徽章\r\n\r\n @param userId 用户ID\r\n @param limit  数量限制\r\n @return 最近解锁的徽章\r\n"}, {"name": "updateNotificationStatus", "paramTypes": ["java.lang.String", "int"], "doc": "\n 更新徽章通知状态\r\n\r\n @param userId 用户ID\r\n @param status 状态（0=未通知，1=已通知）\r\n @return 更新行数\r\n"}, {"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 标记徽章为已查看\r\n\r\n @param userId  用户ID\r\n @param badgeId 徽章ID\r\n @return 更新行数\r\n"}, {"name": "countUnlockedBadgesByCategory", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取用户某类别已解锁徽章数\r\n\r\n @param userId   用户ID\r\n @param category 类别\r\n @return 已解锁徽章数\r\n"}, {"name": "countUnlockedBadgesByRarity", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取用户某稀有度已解锁徽章数\r\n\r\n @param userId 用户ID\r\n @param rarity 稀有度\r\n @return 已解锁徽章数\r\n"}], "constructors": []}