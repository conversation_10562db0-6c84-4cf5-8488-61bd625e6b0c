{"doc": "\n 工具注册表\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "registerExecutor", "paramTypes": ["org.dromara.app.tool.ToolExecutor"], "doc": "\n 注册工具执行器\r\n\r\n @param executor 执行器\r\n"}, {"name": "registerTool", "paramTypes": ["org.dromara.app.domain.AiTool"], "doc": "\n 注册工具配置\r\n\r\n @param tool 工具配置\r\n"}, {"name": "updateTool", "paramTypes": ["org.dromara.app.domain.AiTool"], "doc": "\n 更新工具配置\r\n\r\n @param tool 工具配置\r\n"}, {"name": "unregisterTool", "paramTypes": ["java.lang.String"], "doc": "\n 移除工具\r\n\r\n @param toolId 工具ID\r\n"}, {"name": "getExecutor", "paramTypes": ["java.lang.String"], "doc": "\n 获取工具执行器\r\n\r\n @param toolId 工具ID\r\n @return 执行器\r\n"}, {"name": "getTool", "paramTypes": ["java.lang.String"], "doc": "\n 获取工具配置\r\n\r\n @param toolId 工具ID\r\n @return 工具配置\r\n"}, {"name": "isRegistered", "paramTypes": ["java.lang.String"], "doc": "\n 检查工具是否已注册\r\n\r\n @param toolId 工具ID\r\n @return 是否已注册\r\n"}, {"name": "getRegisteredToolIds", "paramTypes": [], "doc": "\n 获取所有已注册的工具ID\r\n\r\n @return 工具ID集合\r\n"}, {"name": "getAllExecutors", "paramTypes": [], "doc": "\n 获取所有执行器\r\n\r\n @return 执行器映射\r\n"}], "constructors": []}