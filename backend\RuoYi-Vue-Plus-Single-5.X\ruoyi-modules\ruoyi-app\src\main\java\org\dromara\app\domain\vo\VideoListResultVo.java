package org.dromara.app.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 视频列表结果视图对象
 *
 * <AUTHOR>
 */
@Data
public class VideoListResultVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 视频列表
     */
    private List<VideoItemVo> videos;

    /**
     * 总数
     */
    private Long total;

    /**
     * 是否还有更多
     */
    private Boolean hasMore;

    /**
     * 视频项视图对象
     */
    @Data
    public static class VideoItemVo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 视频ID
         */
        private Long id;

        /**
         * 视频标题
         */
        private String title;

        /**
         * 视频简介
         */
        private String description;

        /**
         * 讲师名称
         */
        private String instructor;

        /**
         * 视频时长
         */
        private String duration;

        /**
         * 缩略图
         */
        private String thumbnail;

        /**
         * 分类
         */
        private String category;

        /**
         * 难度
         */
        private String difficulty;

        /**
         * 评分
         */
        private BigDecimal rating;

        /**
         * 学习人数
         */
        private Integer studentCount;

        /**
         * 价格
         */
        private BigDecimal price;

        /**
         * 是否免费
         */
        private Boolean free;

        /**
         * 播放次数
         */
        private Integer viewCount;

        /**
         * 标签列表
         */
        private List<String> tags;

        /**
         * 是否已收藏
         */
        private Boolean isBookmarked;

        /**
         * 播放进度百分比
         */
        private BigDecimal completionRate;

        /**
         * 创建时间
         */
        private LocalDateTime createTime;

    }
}
