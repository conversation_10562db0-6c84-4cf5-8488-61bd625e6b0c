{"doc": "\n Prompt工程服务实现类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "buildRagEnhancedPrompt", "paramTypes": ["java.lang.String", "java.util.List", "java.lang.String", "int"], "doc": "\n 构建RAG增强提示词\r\n\r\n @param userQuery        用户查询\r\n @param retrievalResults 检索结果\r\n @param agentType        Agent类型\r\n @param contextWindow    上下文窗口大小\r\n @return 增强后的提示词\r\n"}, {"name": "buildToolCallPrompt", "paramTypes": ["java.lang.String", "java.util.List", "java.util.List"], "doc": "\n 构建工具调用提示词\r\n\r\n @param userQuery           用户查询\r\n @param availableTools      可用工具列表\r\n @param conversationHistory 对话历史\r\n @return 工具调用提示词\r\n"}, {"name": "optimizeSystemPrompt", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map"], "doc": "\n 优化系统提示词\r\n\r\n @param originalPrompt 原始提示词\r\n @param agentType      Agent类型\r\n @param userContext    用户上下文\r\n @return 优化后的提示词\r\n"}, {"name": "buildConversationalPrompt", "paramTypes": ["java.lang.String", "java.util.List", "int"], "doc": "\n 构建多轮对话提示词\r\n\r\n @param currentQuery        当前查询\r\n @param conversationHistory 对话历史\r\n @param maxHistoryLength    最大历史长度\r\n @return 多轮对话提示词\r\n"}, {"name": "buildChainOfThoughtPrompt", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 构建思维链提示词\r\n\r\n @param userQuery 用户查询\r\n @param taskType  任务类型\r\n @return 思维链提示词\r\n"}, {"name": "buildFewShotPrompt", "paramTypes": ["java.lang.String", "java.util.List", "java.lang.String"], "doc": "\n 构建少样本学习提示词\r\n\r\n @param userQuery       用户查询\r\n @param examples        示例列表\r\n @param taskDescription 任务描述\r\n @return 少样本学习提示词\r\n"}, {"name": "adjust<PERSON>rompt<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "int", "org.dromara.app.service.PromptEngineeringService.LengthAdjustmentStrategy"], "doc": "\n 动态调整提示词长度\r\n\r\n @param prompt    原始提示词\r\n @param maxTokens 最大token数\r\n @param priority  优先级策略\r\n @return 调整后的提示词\r\n"}, {"name": "extractKeyInformation", "paramTypes": ["java.lang.String", "int"], "doc": "\n 提取关键信息\r\n\r\n @param content   内容\r\n @param maxLength 最大长度\r\n @return 关键信息\r\n"}, {"name": "estimateTokenCount", "paramTypes": ["java.lang.String"], "doc": "\n 估计文本的token数量（简化版本）\r\n 注意：这是一个简化的估算方法，实际token数会因模型和分词器而异\r\n\r\n @param text 文本\r\n @return 估计的token数量\r\n"}, {"name": "truncateEnd", "paramTypes": ["java.lang.String", "int"], "doc": "\n 截断提示词末尾\r\n\r\n @param prompt    提示词\r\n @param maxTokens 最大token数\r\n @return 调整后的提示词\r\n"}, {"name": "truncateMiddle", "paramTypes": ["java.lang.String", "int"], "doc": "\n 截断提示词中间部分\r\n\r\n @param prompt    提示词\r\n @param maxTokens 最大token数\r\n @return 调整后的提示词\r\n"}, {"name": "compressContent", "paramTypes": ["java.lang.String", "int"], "doc": "\n 压缩内容（通过删除冗余信息）\r\n\r\n @param prompt    提示词\r\n @param maxTokens 最大token数\r\n @return 调整后的提示词\r\n"}, {"name": "prioritizeRecent", "paramTypes": ["java.lang.String", "int"], "doc": "\n 优先保留最近的内容\r\n\r\n @param prompt    提示词\r\n @param maxTokens 最大token数\r\n @return 调整后的提示词\r\n"}, {"name": "prioritizeRelevant", "paramTypes": ["java.lang.String", "int"], "doc": "\n 优先保留相关的内容\r\n\r\n @param prompt    提示词\r\n @param maxTokens 最大token数\r\n @return 调整后的提示词\r\n"}, {"name": "calculateParagraphRelevance", "paramTypes": ["java.lang.String"], "doc": "\n 计算段落相关性（简化版本）\r\n\r\n @param paragraph 段落\r\n @return 相关性分数\r\n"}, {"name": "calculateSentenceImportance", "paramTypes": ["java.lang.String"], "doc": "\n 计算句子重要性（简化版本）\r\n\r\n @param sentence 句子\r\n @return 重要性分数\r\n"}, {"name": "trimConversationHistory", "paramTypes": ["java.util.List", "int"], "doc": "\n 裁剪对话历史，保留最近和最相关的对话\r\n\r\n @param history   完整对话历史\r\n @param maxLength 最大保留长度\r\n @return 裁剪后的对话历史\r\n"}], "constructors": []}