{"doc": "\n 敏感信息检测结果视图对象\r\n\r\n <AUTHOR>\r\n @date 2025-01-19\r\n", "fields": [{"name": "detectionId", "doc": "\n 检测ID\r\n"}, {"name": "originalTextLength", "doc": "\n 原始文本长度\r\n"}, {"name": "hasSensitiveInfo", "doc": "\n 是否包含敏感信息\r\n"}, {"name": "sensitiveInfoCount", "doc": "\n 敏感信息总数\r\n"}, {"name": "riskLevel", "doc": "\n 风险等级：low/medium/high/critical\r\n"}, {"name": "riskScore", "doc": "\n 风险评分（0-100）\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "doc": "\n 检测到的敏感词列表\r\n"}, {"name": "personalInfo", "doc": "\n 检测到的个人信息\r\n"}, {"name": "filteredText", "doc": "\n 过滤后的文本\r\n"}, {"name": "maskedText", "doc": "\n 脱敏后的文本\r\n"}, {"name": "detectionTime", "doc": "\n 检测耗时（毫秒）\r\n"}, {"name": "detectionDateTime", "doc": "\n 检测时间\r\n"}, {"name": "recommendations", "doc": "\n 建议处理方式\r\n"}, {"name": "needs<PERSON><PERSON><PERSON><PERSON><PERSON>iew", "doc": "\n 是否需要人工审核\r\n"}], "enumConstants": [], "methods": [], "constructors": []}