package org.dromara.common.caffeine.utils;

import com.github.benmanes.caffeine.cache.stats.CacheStats;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.caffeine.core.CaffeineClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

/**
 * Caffeine缓存工具类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CaffeineUtils {

    private static CaffeineClient caffeineClient;

    /**
     * 获取缓存值
     *
     * @param key 缓存键
     * @return 缓存值
     */
    public static Object get(String key) {
        return caffeineClient.get(key);
    }

    /**
     * 获取缓存值并转换类型
     *
     * @param key   缓存键
     * @param clazz 目标类型
     * @param <T>   泛型类型
     * @return 转换后的缓存值
     */
    @SuppressWarnings("unchecked")
    public static <T> T get(String key, Class<T> clazz) {
        Object value = caffeineClient.get(key);
        if (value != null && clazz.isInstance(value)) {
            return (T) value;
        }
        return null;
    }

    /**
     * 获取缓存值，如果不存在则使用loader加载
     *
     * @param key    缓存键
     * @param loader 加载器
     * @return 缓存值
     */
    public static Object get(String key, Function<String, Object> loader) {
        return caffeineClient.get(key, loader);
    }

    /**
     * 获取缓存值并转换类型，如果不存在则使用loader加载
     *
     * @param key    缓存键
     * @param loader 加载器
     * @param clazz  目标类型
     * @param <T>    泛型类型
     * @return 转换后的缓存值
     */
    @SuppressWarnings("unchecked")
    public static <T> T get(String key, Function<String, Object> loader, Class<T> clazz) {
        Object value = caffeineClient.get(key, loader);
        if (value != null && clazz.isInstance(value)) {
            return (T) value;
        }
        return null;
    }

    /**
     * 设置缓存值
     *
     * @param key   缓存键
     * @param value 缓存值
     */
    public static void put(String key, Object value) {
        caffeineClient.put(key, value);
    }

    /**
     * 设置带过期时间的缓存值
     *
     * @param key        缓存键
     * @param value      缓存值
     * @param ttlSeconds 过期时间（秒）
     */
    public static void put(String key, Object value, long ttlSeconds) {
        caffeineClient.put(key, value, ttlSeconds);
    }

    /**
     * 批量设置缓存
     *
     * @param map 缓存键值对
     */
    public static void putAll(Map<String, Object> map) {
        caffeineClient.putAll(map);
    }

    /**
     * 删除缓存
     *
     * @param key 缓存键
     */
    public static void evict(String key) {
        caffeineClient.evict(key);
    }

    /**
     * 批量删除缓存
     *
     * @param keys 缓存键集合
     */
    public static void evictAll(Collection<String> keys) {
        caffeineClient.evictAll(keys);
    }

    /**
     * 清空所有缓存
     */
    public static void clear() {
        caffeineClient.clear();
    }

    /**
     * 检查缓存是否存在
     *
     * @param key 缓存键
     * @return 是否存在
     */
    public static boolean hasKey(String key) {
        return caffeineClient.hasKey(key);
    }

    /**
     * 获取缓存大小
     *
     * @return 缓存大小
     */
    public static long size() {
        return caffeineClient.size();
    }

    /**
     * 获取所有缓存键
     *
     * @return 缓存键集合
     */
    public static Set<String> keys() {
        return caffeineClient.keys();
    }

    /**
     * 获取缓存统计信息
     *
     * @return 统计信息
     */
    public static CacheStats stats() {
        return caffeineClient.stats();
    }

    /**
     * 执行缓存清理
     */
    public static void cleanUp() {
        caffeineClient.cleanUp();
    }

    /**
     * 生成缓存键
     *
     * @param prefix 前缀
     * @param parts  键的组成部分
     * @return 完整的缓存键
     */
    public static String buildKey(String prefix, Object... parts) {
        StringBuilder sb = new StringBuilder(prefix);
        for (Object part : parts) {
            sb.append(":").append(part);
        }
        return sb.toString();
    }

    /**
     * 生成缓存键（不带前缀）
     *
     * @param parts 键的组成部分
     * @return 完整的缓存键
     */
    public static String buildKey(Object... parts) {
        if (parts.length == 0) {
            throw new IllegalArgumentException("缓存键组成部分不能为空");
        }

        StringBuilder sb = new StringBuilder(String.valueOf(parts[0]));
        for (int i = 1; i < parts.length; i++) {
            sb.append(":").append(parts[i]);
        }
        return sb.toString();
    }

    /**
     * 获取缓存剩余过期时间
     *
     * @param key 缓存键
     * @return 剩余秒数，-1表示永不过期，0表示已过期或不存在
     */
    public static long getTtl(String key) {
        return caffeineClient.getTtl(key);
    }

    /**
     * 设置缓存过期时间
     *
     * @param key        缓存键
     * @param ttlSeconds 过期时间（秒）
     * @return 是否设置成功
     */
    public static boolean expire(String key, long ttlSeconds) {
        return caffeineClient.expire(key, ttlSeconds);
    }

    /**
     * 打印缓存统计信息
     */
    public static void printStats() {
        CacheStats stats = stats();
        if (stats != null) {
            log.info("Caffeine缓存统计信息: " +
                    "命中率={}, " +
                    "命中次数={}, " +
                    "未命中次数={}, " +
                    "加载次数={}, " +
                    "驱逐次数={}, " +
                    "平均加载时间={}ns",
                String.format("%.2f%%", stats.hitRate() * 100),
                stats.hitCount(),
                stats.missCount(),
                stats.loadCount(),
                stats.evictionCount(),
                String.format("%.2f", stats.averageLoadPenalty()));
        }
    }

    @Autowired
    public void setCaffeineClient(CaffeineClient caffeineClient) {
        CaffeineUtils.caffeineClient = caffeineClient;
    }

}
