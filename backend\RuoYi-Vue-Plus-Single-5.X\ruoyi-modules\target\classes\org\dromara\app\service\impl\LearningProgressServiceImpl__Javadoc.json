{"doc": " 学习进度服务实现类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "updateLearningStatistics", "paramTypes": ["org.dromara.app.domain.LearningProgress", "java.lang.Integer"], "doc": " 更新学习统计\n"}, {"name": "updateLearningStatisticsOnCompletion", "paramTypes": ["org.dromara.app.domain.LearningProgress"], "doc": " 完成时更新学习统计\n"}, {"name": "calculateEfficiencyScore", "paramTypes": ["org.dromara.app.domain.LearningProgress"], "doc": " 计算学习效率评分\n"}, {"name": "calculateFinalEfficiencyScore", "paramTypes": ["org.dromara.app.domain.LearningProgress"], "doc": " 计算最终效率评分\n"}, {"name": "analyzeLearningPatterns", "paramTypes": ["java.lang.Long"], "doc": " 分析学习模式\n"}, {"name": "analyzeEfficiency", "paramTypes": ["java.lang.Long"], "doc": " 分析学习效率\n"}, {"name": "generateImprovementSuggestions", "paramTypes": ["java.lang.Long"], "doc": " 生成改进建议\n"}, {"name": "analyzeAchievements", "paramTypes": ["java.lang.Long"], "doc": " 分析学习成就\n"}], "constructors": []}