{"doc": " <PERSON><PERSON><PERSON>异常处理器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleDuplicateKeyException", "paramTypes": ["org.springframework.dao.DuplicateKeyException", "jakarta.servlet.http.HttpServletRequest"], "doc": " 主键或UNIQUE索引，数据重复异常\n"}, {"name": "handleCannotFindDataSourceException", "paramTypes": ["org.mybatis.spring.MyBatisSystemException", "jakarta.servlet.http.HttpServletRequest"], "doc": " Mybat<PERSON>系统异常 通用处理\n"}], "constructors": []}