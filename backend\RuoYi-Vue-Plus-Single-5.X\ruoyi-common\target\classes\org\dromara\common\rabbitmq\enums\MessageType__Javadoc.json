{"doc": " 消息类型枚举\n\n <AUTHOR>\n", "fields": [{"name": "code", "doc": " 类型编码\n"}, {"name": "desc", "doc": " 类型描述\n"}], "enumConstants": [{"name": "NORMAL", "doc": " 普通消息\n"}, {"name": "DELAY", "doc": " 延迟消息\n"}, {"name": "BROADCAST", "doc": " 广播消息\n"}, {"name": "RPC", "doc": " RPC消息\n"}, {"name": "TRANSACTION", "doc": " 事务消息\n"}], "methods": [{"name": "getByCode", "paramTypes": ["java.lang.String"], "doc": " 根据编码获取枚举\n\n @param code 编码\n @return 枚举对象\n"}], "constructors": []}