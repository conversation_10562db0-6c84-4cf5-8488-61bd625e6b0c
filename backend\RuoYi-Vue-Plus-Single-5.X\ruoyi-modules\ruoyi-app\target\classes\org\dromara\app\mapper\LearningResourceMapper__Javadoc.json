{"doc": "\n 学习资源Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectBySkillAreaAndDifficulty", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": "\n 根据技能领域和难度查询资源\r\n\r\n @param skillArea 技能领域\r\n @param difficulty 难度等级\r\n @param limit 限制数量\r\n @return 资源列表\r\n"}, {"name": "selectByType", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 根据资源类型查询资源\r\n\r\n @param type 资源类型\r\n @param limit 限制数量\r\n @return 资源列表\r\n"}, {"name": "selectPopularResources", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询热门资源\r\n\r\n @param limit 限制数量\r\n @return 资源列表\r\n"}, {"name": "selectFreeResources", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 查询免费资源\r\n\r\n @param skillArea 技能领域\r\n @param limit 限制数量\r\n @return 资源列表\r\n"}, {"name": "selectByTag", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 根据标签查询资源\r\n\r\n @param tag 标签\r\n @param limit 限制数量\r\n @return 资源列表\r\n"}, {"name": "selectResourceStatistics", "paramTypes": [], "doc": "\n 查询资源统计信息\r\n\r\n @return 统计信息\r\n"}, {"name": "selectSkillAreaDistribution", "paramTypes": [], "doc": "\n 查询技能领域分布\r\n\r\n @return 技能领域分布\r\n"}, {"name": "selectResourceTypeDistribution", "paramTypes": [], "doc": "\n 查询资源类型分布\r\n\r\n @return 资源类型分布\r\n"}, {"name": "selectTopProviders", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询提供者排行\r\n\r\n @param limit 限制数量\r\n @return 提供者排行\r\n"}, {"name": "searchResources", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 全文搜索资源\r\n\r\n @param keyword 关键词\r\n @param limit 限制数量\r\n @return 资源列表\r\n"}, {"name": "updateUsageStatistics", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 更新资源使用统计\r\n\r\n @param resourceId 资源ID\r\n @param statisticsJson 统计信息JSON\r\n @return 更新行数\r\n"}, {"name": "selectRecommendedResources", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询推荐学习资源\r\n\r\n @param userId 用户ID（可为null，表示通用推荐）\r\n @param limit 限制数量\r\n @return 推荐资源列表\r\n"}, {"name": "selectResourcesBySkillGaps", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Integer"], "doc": "\n 根据技能差距推荐资源\r\n\r\n @param skillAreas 技能领域列表\r\n @param difficulty 难度等级\r\n @param limit 限制数量\r\n @return 推荐资源列表\r\n"}, {"name": "selectCollaborativeFilteringResources", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询用户相似度推荐资源\r\n\r\n @param userId 用户ID\r\n @param limit 限制数量\r\n @return 推荐资源列表\r\n"}, {"name": "selectTrendingResources", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": "\n 查询趋势资源（最近热门）\r\n\r\n @param days 天数\r\n @param limit 限制数量\r\n @return 趋势资源列表\r\n"}], "constructors": []}