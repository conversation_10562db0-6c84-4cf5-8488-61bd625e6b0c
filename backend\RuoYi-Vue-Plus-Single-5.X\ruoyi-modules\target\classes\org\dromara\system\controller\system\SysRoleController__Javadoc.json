{"doc": " 角色信息\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取角色信息列表\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出角色信息列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 根据角色编号获取详细信息\n\n @param roleId 角色ID\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": " 新增角色\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": " 修改保存角色\n"}, {"name": "dataScope", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": " 修改保存数据权限\n"}, {"name": "changeStatus", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": " 状态修改\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除角色\n\n @param roleIds 角色ID串\n"}, {"name": "optionselect", "paramTypes": ["java.lang.Long[]"], "doc": " 获取角色选择框列表\n\n @param roleIds 角色ID串\n"}, {"name": "allocatedList", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询已分配用户角色列表\n"}, {"name": "unallocatedList", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询未分配用户角色列表\n"}, {"name": "cancelAuthUser", "paramTypes": ["org.dromara.system.domain.SysUserRole"], "doc": " 取消授权用户\n"}, {"name": "cancelAuthUserAll", "paramTypes": ["java.lang.Long", "java.lang.Long[]"], "doc": " 批量取消授权用户\n\n @param roleId  角色ID\n @param userIds 用户ID串\n"}, {"name": "selectAuthUserAll", "paramTypes": ["java.lang.Long", "java.lang.Long[]"], "doc": " 批量选择用户授权\n\n @param roleId  角色ID\n @param userIds 用户ID串\n"}, {"name": "roleDeptTreeselect", "paramTypes": ["java.lang.Long"], "doc": " 获取对应角色部门树列表\n\n @param roleId 角色ID\n"}], "constructors": []}