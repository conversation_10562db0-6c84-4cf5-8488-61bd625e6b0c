{"doc": " 慢查询视图对象\n\n <AUTHOR>\n @date 2025-01-19\n", "fields": [{"name": "queryId", "doc": " 查询ID\n"}, {"name": "sqlText", "doc": " SQL语句\n"}, {"name": "executionTime", "doc": " 执行时间（秒）\n"}, {"name": "lockTime", "doc": " 锁定时间（秒）\n"}, {"name": "rowsExamined", "doc": " 扫描行数\n"}, {"name": "rowsSent", "doc": " 返回行数\n"}, {"name": "executionCount", "doc": " 执行次数\n"}, {"name": "averageExecutionTime", "doc": " 平均执行时间（秒）\n"}, {"name": "maxExecutionTime", "doc": " 最大执行时间（秒）\n"}, {"name": "minExecutionTime", "doc": " 最小执行时间（秒）\n"}, {"name": "databaseName", "doc": " 数据库名\n"}, {"name": "userName", "doc": " 用户名\n"}, {"name": "clientIp", "doc": " 客户端IP\n"}, {"name": "queryType", "doc": " 查询类型：SELECT/INSERT/UPDATE/DELETE\n"}, {"name": "tablesUsed", "doc": " 涉及的表\n"}, {"name": "indexUsed", "doc": " 是否使用索引\n"}, {"name": "tempTableUsed", "doc": " 是否使用临时表\n"}, {"name": "fileSortUsed", "doc": " 是否使用文件排序\n"}, {"name": "executionPlan", "doc": " 查询计划\n"}, {"name": "optimizationSuggestion", "doc": " 优化建议\n"}, {"name": "firstExecutionTime", "doc": " 首次执行时间\n"}, {"name": "lastExecutionTime", "doc": " 最后执行时间\n"}, {"name": "frequency", "doc": " 查询频率：high/medium/low\n"}, {"name": "impactLevel", "doc": " 影响级别：critical/high/medium/low\n"}, {"name": "isOptimized", "doc": " 是否已优化\n"}], "enumConstants": [], "methods": [], "constructors": []}