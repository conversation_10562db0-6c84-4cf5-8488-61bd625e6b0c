package org.dromara.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.domain.bo.QuestionBo;
import org.dromara.system.domain.vo.QuestionVo;
import org.dromara.system.mapper.QuestionMapper;
import org.dromara.system.service.IQuestionService;
import org.dromara.common.mybatis.core.domain.Question;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 题目Service业务层处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class QuestionServiceImpl implements IQuestionService {

    private final QuestionMapper baseMapper;

    /**
     * 查询题目
     */
    @Override
    public QuestionVo queryById(Long questionId) {
        return baseMapper.selectVoById(questionId);
    }

    /**
     * 查询题目列表
     */
    @Override
    public List<QuestionVo> queryList(QuestionBo bo) {
        LambdaQueryWrapper<Question> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(bo);
    }

    /**
     * 分页查询题目列表
     */
    @Override
    public TableDataInfo<QuestionVo> queryPageList(QuestionBo bo, PageQuery pageQuery) {
        Page<QuestionVo> result = baseMapper.selectVoPage(pageQuery.build(), bo);
        return TableDataInfo.build(result);
    }

    /**
     * 根据题库ID分页查询题目列表
     */
    @Override
    public TableDataInfo<QuestionVo> queryPageListByBankId(Long bankId, QuestionBo bo, PageQuery pageQuery) {
        Page<QuestionVo> result = baseMapper.selectPageQuestionByBankId(pageQuery, bankId, bo);
        return TableDataInfo.build(result);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<Question> buildQueryWrapper(QuestionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Question> lqw = Wrappers.<Question>lambdaQuery();
        lqw.eq(ObjectUtil.isNotNull(bo.getBankId()), Question::getBankId, bo.getBankId());
        lqw.like(StringUtils.isNotBlank(bo.getQuestionCode()), Question::getQuestionCode, bo.getQuestionCode());
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), Question::getTitle, bo.getTitle());
        lqw.like(StringUtils.isNotBlank(bo.getCategory()), Question::getCategory, bo.getCategory());
        lqw.eq(ObjectUtil.isNotNull(bo.getDifficulty()), Question::getDifficulty, bo.getDifficulty());
        lqw.eq(ObjectUtil.isNotNull(bo.getType()), Question::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Question::getStatus, bo.getStatus());
        lqw.orderByAsc(Question::getSort).orderByDesc(Question::getCreateTime);
        return lqw;
    }

    /**
     * 新增题目
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(QuestionBo bo) {
        Question add = MapstructUtils.convert(bo, Question.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setQuestionId(add.getQuestionId());
        }
        return flag;
    }

    /**
     * 修改题目
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(QuestionBo bo) {
        Question update = MapstructUtils.convert(bo, Question.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Question entity) {
        // 可以在此处添加业务校验逻辑
    }

    /**
     * 批量删除题目
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids) {
        return baseMapper.deleteQuestionByIds(ids) > 0;
    }

    /**
     * 根据题目编码查询题目
     */
    @Override
    public QuestionVo queryByQuestionCode(String questionCode) {
        return baseMapper.selectVoByQuestionCode(questionCode);
    }

    /**
     * 根据题库ID查询题目列表
     */
    @Override
    public List<QuestionVo> queryByBankId(Long bankId) {
        return baseMapper.selectVoByBankId(bankId);
    }

    /**
     * 根据分类查询题目列表
     */
    @Override
    public List<QuestionVo> queryByCategory(String category) {
        return baseMapper.selectVoByCategory(category);
    }

    /**
     * 根据难度查询题目列表
     */
    @Override
    public List<QuestionVo> queryByDifficulty(Integer difficulty) {
        return baseMapper.selectVoByDifficulty(difficulty);
    }

    /**
     * 根据题目类型查询题目列表
     */
    @Override
    public List<QuestionVo> queryByType(Integer type) {
        return baseMapper.selectVoByType(type);
    }

    /**
     * 查询用户收藏的题目列表
     */
    @Override
    public List<QuestionVo> queryBookmarkedQuestions(Long userId) {
        return baseMapper.selectBookmarkedQuestionsByUserId(userId);
    }

    /**
     * 查询热门题目列表
     */
    @Override
    public List<QuestionVo> queryHotQuestions(Integer limit) {
        return baseMapper.selectHotQuestions(limit);
    }

    /**
     * 随机查询题目列表
     */
    @Override
    public List<QuestionVo> queryRandomQuestions(Long bankId, Integer limit) {
        return baseMapper.selectRandomQuestions(bankId, limit);
    }

    /**
     * 更新题目练习次数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePracticeCount(Long questionId) {
        return baseMapper.updatePracticeCount(questionId) > 0;
    }

    /**
     * 更新题目评论数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCommentCount(Long questionId) {
        return baseMapper.updateCommentCount(questionId) > 0;
    }

    /**
     * 更新题目正确率
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCorrectRate(Long questionId, Integer correctRate) {
        return baseMapper.updateCorrectRate(questionId, correctRate) > 0;
    }

    /**
     * 检查题目编码是否唯一
     */
    @Override
    public Boolean checkQuestionCodeUnique(QuestionBo bo) {
        Long questionId = ObjectUtil.isNull(bo.getQuestionId()) ? -1L : bo.getQuestionId();
        Question question = baseMapper.selectOne(Wrappers.lambdaQuery(Question.class)
            .eq(Question::getQuestionCode, bo.getQuestionCode())
            .ne(Question::getQuestionId, questionId));
        return ObjectUtil.isNull(question);
    }

    /**
     * 批量导入题目
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importQuestion(List<QuestionBo> list) {
        if (CollUtil.isEmpty(list)) {
            return "导入数据不能为空";
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (QuestionBo bo : list) {
            try {
                // 验证是否存在这个题目
                if (!checkQuestionCodeUnique(bo)) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、题目编码 ").append(bo.getQuestionCode()).append(" 已存在");
                } else {
                    insertByBo(bo);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、题目 ").append(bo.getTitle()).append(" 导入成功");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、题目 " + bo.getTitle() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 导出题目数据
     */
    @Override
    public List<QuestionVo> exportQuestion(QuestionBo bo) {
        return queryList(bo);
    }

    /**
     * 启用/停用题目
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeStatus(Long questionId, String status) {
        Question question = new Question();
        question.setQuestionId(questionId);
        question.setStatus(status);
        return baseMapper.updateById(question) > 0;
    }

    /**
     * 复制题目
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean copyQuestion(Long questionId, String title) {
        Question original = baseMapper.selectById(questionId);
        if (ObjectUtil.isNull(original)) {
            return false;
        }
        Question copy = BeanUtil.copyProperties(original, Question.class);
        copy.setQuestionId(null);
        copy.setTitle(title);
        copy.setQuestionCode(original.getQuestionCode() + "_copy");
        copy.setPracticeCount(0);
        copy.setCommentCount(0);
        return baseMapper.insert(copy) > 0;
    }

    /**
     * 根据题库ID删除题目
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByBankId(Long bankId) {
        return baseMapper.deleteQuestionByBankId(bankId) > 0;
    }

    /**
     * 统计题库下的题目数量
     */
    @Override
    public Integer countByBankId(Long bankId) {
        return baseMapper.countQuestionsByBankId(bankId);
    }
}
