{"doc": " 聊天内存管理器\n 负责管理聊天会话的内存，防止内存泄露\n\n <AUTHOR>\n", "fields": [{"name": "MEMORY_EXPIRE_HOURS", "doc": " 内存过期时间（小时）\n"}, {"name": "MAX_MEMORY_COUNT", "doc": " 最大内存数量\n"}, {"name": "CLEANUP_INTERVAL_MINUTES", "doc": " 清理任务执行间隔（分钟）\n"}, {"name": "memoryCache", "doc": " 内存缓存，存储会话ID到内存的映射\n"}, {"name": "scheduler", "doc": " 定时清理任务执行器\n"}], "enumConstants": [], "methods": [{"name": "getOrCreateMemory", "paramTypes": ["java.lang.String"], "doc": " 获取或创建会话内存\n\n @param sessionId 会话ID\n @return 聊天内存\n"}, {"name": "getOrCreateMemory", "paramTypes": ["java.lang.String", "int"], "doc": " 获取或创建会话内存\n\n @param sessionId   会话ID\n @param maxMessages 最大消息数\n @return 聊天内存\n"}, {"name": "remove<PERSON><PERSON>ory", "paramTypes": ["java.lang.String"], "doc": " 移除会话内存\n\n @param sessionId 会话ID\n"}, {"name": "cleanExpiredMemory", "paramTypes": [], "doc": " 清理过期内存\n"}, {"name": "cleanOldestMemory", "paramTypes": [], "doc": " 清理最旧的内存（当内存数量超限时）\n"}, {"name": "getStats", "paramTypes": [], "doc": " 获取内存统计信息\n\n @return 统计信息\n"}], "constructors": []}