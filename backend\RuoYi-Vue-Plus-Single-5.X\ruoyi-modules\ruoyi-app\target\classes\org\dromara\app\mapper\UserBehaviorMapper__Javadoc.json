{"doc": "\n 用户行为记录Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByUserIdAndBehaviorType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 根据用户ID和行为类型查询行为记录\r\n\r\n @param userId       用户ID\r\n @param behaviorType 行为类型\r\n @return 行为记录列表\r\n"}, {"name": "selectByUserIdAndTimeRange", "paramTypes": ["java.lang.Long", "java.util.Date", "java.util.Date"], "doc": "\n 根据用户ID和时间范围查询行为记录\r\n\r\n @param userId    用户ID\r\n @param startTime 开始时间\r\n @param endTime   结束时间\r\n @return 行为记录列表\r\n"}, {"name": "countByUserIdAndBehaviorType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 统计用户某种行为的次数\r\n\r\n @param userId       用户ID\r\n @param behaviorType 行为类型\r\n @return 行为次数\r\n"}, {"name": "countByUserIdAndBehaviorTypeAndTimeRange", "paramTypes": ["java.lang.Long", "java.lang.String", "java.util.Date", "java.util.Date"], "doc": "\n 统计用户某种行为在指定时间范围内的次数\r\n\r\n @param userId       用户ID\r\n @param behaviorType 行为类型\r\n @param startTime    开始时间\r\n @param endTime      结束时间\r\n @return 行为次数\r\n"}, {"name": "selectConsecutiveLoginDays", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户连续登录天数\r\n\r\n @param userId 用户ID\r\n @return 连续登录天数\r\n"}, {"name": "selectTotalStudyMinutes", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户累计学习时长（分钟）\r\n\r\n @param userId 用户ID\r\n @return 累计学习时长\r\n"}, {"name": "batchInsert", "paramTypes": ["java.util.List"], "doc": "\n 批量插入用户行为记录\r\n\r\n @param behaviors 行为记录列表\r\n @return 插入数量\r\n"}], "constructors": []}