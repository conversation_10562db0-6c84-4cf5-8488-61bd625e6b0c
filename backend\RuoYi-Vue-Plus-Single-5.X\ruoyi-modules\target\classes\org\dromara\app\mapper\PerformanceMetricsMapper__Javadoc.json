{"doc": " 性能指标Mapper接口\n\n <AUTHOR>\n @date 2025-07-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByResultId", "paramTypes": ["java.lang.String"], "doc": " 根据结果ID查询性能指标\n\n @param resultId 结果ID\n @return 性能指标\n"}, {"name": "deleteByResultId", "paramTypes": ["java.lang.String"], "doc": " 根据结果ID删除性能指标\n\n @param resultId 结果ID\n @return 删除数量\n"}, {"name": "selectHistoryByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询用户性能指标历史记录\n\n @param userId 用户ID\n @param limit  限制数量\n @return 性能指标列表\n"}, {"name": "selectIndustryAverage", "paramTypes": ["java.lang.String"], "doc": " 查询行业平均性能指标\n\n @param industry 行业\n @return 性能指标\n"}, {"name": "selectAverageMetricsByUserId", "paramTypes": ["java.lang.Long"], "doc": " 查询用户性能指标平均值\n\n @param userId 用户ID\n @return 用户性能指标平均值\n"}, {"name": "selectRankingByResultId", "paramTypes": ["java.lang.String"], "doc": " 查询性能指标排名\n\n @param resultId 结果ID\n @return 排名信息\n"}], "constructors": []}