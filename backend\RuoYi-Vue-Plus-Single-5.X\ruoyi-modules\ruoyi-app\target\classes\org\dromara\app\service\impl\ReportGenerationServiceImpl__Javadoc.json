{"doc": "\n 报告生成服务实现类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "createMockAnalysisResult", "paramTypes": ["java.lang.String"], "doc": "\n 创建模拟分析结果\r\n"}, {"name": "createMockDimensionScore", "paramTypes": ["java.lang.String", "int", "java.lang.String"], "doc": "\n 创建模拟维度评分\r\n"}, {"name": "convertDimensionScores", "paramTypes": ["java.util.List"], "doc": "\n 转换维度评分格式\r\n"}, {"name": "generatePdfContent", "paramTypes": ["org.dromara.app.service.IReportGenerationService.InterviewReportData"], "doc": "\n 生成PDF内容\r\n"}, {"name": "createImprovementSuggestion", "paramTypes": ["java.lang.String"], "doc": "\n 创建改进建议\r\n"}, {"name": "createDimensionImprovementSuggestion", "paramTypes": ["org.dromara.app.service.IMultimodalAnalysisService.DimensionScore"], "doc": "\n 基于维度评分创建改进建议\r\n"}, {"name": "createTechnicalLearningPath", "paramTypes": [], "doc": "\n 创建技术学习路径\r\n"}, {"name": "createCommunicationLearningPath", "paramTypes": [], "doc": "\n 创建沟通表达学习路径\r\n"}, {"name": "createLogicalThinkingLearningPath", "paramTypes": [], "doc": "\n 创建逻辑思维学习路径\r\n"}, {"name": "createInterviewSkillsLearningPath", "paramTypes": [], "doc": "\n 创建面试技巧学习路径\r\n"}, {"name": "createResource", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 创建学习资源\r\n"}, {"name": "getPriorityValue", "paramTypes": ["java.lang.String"], "doc": "\n 获取优先级数值\r\n"}], "constructors": []}