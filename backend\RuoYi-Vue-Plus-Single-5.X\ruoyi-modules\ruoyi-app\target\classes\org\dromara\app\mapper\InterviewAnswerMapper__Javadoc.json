{"doc": "\n 面试答案Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectBySessionId", "paramTypes": ["java.lang.String"], "doc": "\n 根据会话ID查询所有答案\r\n\r\n @param sessionId 会话ID\r\n @return 答案列表\r\n"}, {"name": "selectBySessionIdAndQuestionId", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据会话ID和问题ID查询答案\r\n\r\n @param sessionId  会话ID\r\n @param questionId 问题ID\r\n @return 答案\r\n"}, {"name": "selectRecentByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 根据用户ID查询最近的答案\r\n\r\n @param userId 用户ID\r\n @param limit  限制数量\r\n @return 答案列表\r\n"}, {"name": "countAnsweredBySessionId", "paramTypes": ["java.lang.String"], "doc": "\n 统计会话已回答问题数量\r\n\r\n @param sessionId 会话ID\r\n @return 已回答数量\r\n"}, {"name": "countSkippedBySessionId", "paramTypes": ["java.lang.String"], "doc": "\n 统计会话跳过问题数量\r\n\r\n @param sessionId 会话ID\r\n @return 跳过数量\r\n"}, {"name": "selectAvgScoreBySessionId", "paramTypes": ["java.lang.String"], "doc": "\n 查询会话的平均分数\r\n\r\n @param sessionId 会话ID\r\n @return 平均分数\r\n"}, {"name": "selectBySessionIdAndStatus", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据状态查询答案列表\r\n\r\n @param sessionId 会话ID\r\n @param status    状态\r\n @return 答案列表\r\n"}], "constructors": []}