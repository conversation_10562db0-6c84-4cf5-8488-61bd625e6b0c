{"doc": "\n 工具执行器接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "execute", "paramTypes": ["java.util.Map", "org.dromara.app.service.IToolService.ToolCallContext"], "doc": "\n 执行工具\r\n\r\n @param parameters 参数\r\n @param context    上下文\r\n @return 执行结果\r\n"}, {"name": "getToolId", "paramTypes": [], "doc": "\n 获取工具ID\r\n\r\n @return 工具ID\r\n"}, {"name": "getToolName", "paramTypes": [], "doc": "\n 获取工具名称\r\n\r\n @return 工具名称\r\n"}, {"name": "getDescription", "paramTypes": [], "doc": "\n 获取工具描述\r\n\r\n @return 工具描述\r\n"}, {"name": "isAvailable", "paramTypes": [], "doc": "\n 检查工具是否可用\r\n\r\n @return 是否可用\r\n"}, {"name": "getConfig", "paramTypes": [], "doc": "\n 获取工具配置\r\n\r\n @return 工具配置\r\n"}], "constructors": []}