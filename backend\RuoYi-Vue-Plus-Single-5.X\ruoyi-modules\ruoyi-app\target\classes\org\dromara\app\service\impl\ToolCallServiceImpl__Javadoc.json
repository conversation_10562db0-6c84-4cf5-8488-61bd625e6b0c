{"doc": "\n 工具调用服务实现\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "toolExecutors", "doc": "\n 工具执行器注册表\r\n"}], "enumConstants": [], "methods": [{"name": "initToolExecutors", "paramTypes": [], "doc": "\n 初始化方法，自动注册所有工具执行器\r\n"}, {"name": "registerToolExecutor", "paramTypes": ["org.dromara.app.service.tool.ToolExecutor"], "doc": "\n 注册工具执行器\r\n"}, {"name": "getRegisteredExecutors", "paramTypes": [], "doc": "\n 获取已注册的工具执行器列表\r\n"}, {"name": "getToolExecutor", "paramTypes": ["java.lang.String"], "doc": "\n 获取工具执行器\r\n"}, {"name": "isExecutorRegistered", "paramTypes": ["java.lang.String"], "doc": "\n 检查工具执行器是否已注册\r\n"}, {"name": "getToolExecutionStats", "paramTypes": [], "doc": "\n 获取工具执行统计信息\r\n"}, {"name": "reloadToolExecutors", "paramTypes": [], "doc": "\n 重新加载工具执行器\r\n"}], "constructors": []}