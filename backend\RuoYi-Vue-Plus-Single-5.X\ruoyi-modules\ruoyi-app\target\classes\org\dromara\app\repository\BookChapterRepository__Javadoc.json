{"doc": "\n 书籍章节Repository接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "findByBookIdAndStatusTrueOrderByChapterOrderAsc", "paramTypes": ["java.lang.Long"], "doc": "\n 根据书籍ID查询章节列表\r\n\r\n @param bookId 书籍ID\r\n @return 章节列表（按顺序排序）\r\n"}, {"name": "findByBookIdAndChapterOrder", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 根据书籍ID和章节序号查询章节\r\n\r\n @param bookId       书籍ID\r\n @param chapterOrder 章节序号\r\n @return 章节信息\r\n"}, {"name": "findPreviewChaptersByBookId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据书籍ID查询试读章节列表\r\n\r\n @param bookId 书籍ID\r\n @return 试读章节列表\r\n"}, {"name": "findUnlockedChaptersByBookId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据书籍ID查询已解锁章节列表\r\n\r\n @param bookId 书籍ID\r\n @return 已解锁章节列表\r\n"}, {"name": "countByBookIdAndStatusTrue", "paramTypes": ["java.lang.Long"], "doc": "\n 根据书籍ID统计章节数量\r\n\r\n @param bookId 书籍ID\r\n @return 章节数量\r\n"}, {"name": "deleteByBookId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据书籍ID删除所有章节\r\n\r\n @param bookId 书籍ID\r\n"}, {"name": "findMaxChapterOrderByBookId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据书籍ID查询最大章节序号\r\n\r\n @param bookId 书籍ID\r\n @return 最大章节序号\r\n"}], "constructors": []}