package org.dromara.common.json.handler;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.dromara.common.json.utils.JsonUtils;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * JSON字符串列表类型处理器
 * 用于处理数据库JSON字段与Java List<String>类型的转换
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@MappedTypes({List.class})
@MappedJdbcTypes({JdbcType.VARCHAR, JdbcType.LONGVARCHAR})
public class JsonStringListTypeHandler extends BaseTypeHandler<List<String>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<String> parameter, JdbcType jdbcType) throws SQLException {
        if (ObjectUtil.isEmpty(parameter)) {
            ps.setString(i, null);
        } else {
            ps.setString(i, JsonUtils.toJsonString(parameter));
        }
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJsonToList(json);
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJsonToList(json);
    }

    @Override
    public List<String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJsonToList(json);
    }

    /**
     * 解析JSON字符串为List<String>
     *
     * @param json JSON字符串
     * @return List<String>
     */
    private List<String> parseJsonToList(String json) {
        if (StrUtil.isBlank(json)) {
            return new ArrayList<>();
        }

        try {
            return JsonUtils.parseObject(json, new TypeReference<List<String>>() {});
        } catch (Exception e) {
            // 如果解析失败，返回空列表
            return new ArrayList<>();
        }
    }
}
