{"doc": " 用户活动时长记录控制器\n 统计用户的学习时长(面试时长,资源学习时长)\n\n <AUTHOR>\n @version 1.0\n @date 2025-07-16\n", "fields": [], "enumConstants": [], "methods": [{"name": "startActivity", "paramTypes": ["org.dromara.app.domain.dto.ActivityStartRequest"], "doc": " 开始活动记录\n\n @param request 开始活动请求\n @return 操作结果\n"}, {"name": "pauseActivity", "paramTypes": ["org.dromara.app.domain.dto.ActivityPauseRequest"], "doc": " 暂停活动记录\n\n @param request 暂停活动请求\n @return 操作结果\n"}, {"name": "endActivity", "paramTypes": ["org.dromara.app.domain.dto.ActivityEndRequest"], "doc": " 结束活动记录\n\n @param request 结束活动请求\n @return 操作结果\n"}, {"name": "syncSession", "paramTypes": ["org.dromara.app.domain.dto.ActivitySyncRequest"], "doc": " 同步活动会话\n\n @param request 同步请求\n @return 操作结果\n"}, {"name": "getStatistics", "paramTypes": ["org.dromara.app.domain.enums.ActivityType"], "doc": " 获取活动统计数据\n\n @param type 活动类型(可选)\n @return 统计数据\n"}, {"name": "getHistory", "paramTypes": ["org.dromara.app.domain.enums.ActivityType", "java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.lang.Integer"], "doc": " 获取活动历史记录\n\n @param type      活动类型(可选)\n @param startDate 开始时间(可选)\n @param endDate   结束时间(可选)\n @param page      页码(默认1)\n @param pageSize  每页大小(默认10)\n @param limit     限制数量(可选)\n @return 历史记录\n"}, {"name": "clearRecords", "paramTypes": ["org.dromara.app.domain.enums.ActivityType"], "doc": " 清除活动记录\n\n @param type 活动类型(可选，为空时清除所有类型)\n @return 操作结果\n"}, {"name": "getStatsByType", "paramTypes": ["org.dromara.app.domain.enums.ActivityType"], "doc": " 获取指定类型的活动统计\n\n @param type 活动类型\n @return 统计数据\n"}, {"name": "initializeUserSummary", "paramTypes": [], "doc": " 初始化用户活动总览\n\n @return 操作结果\n"}, {"name": "health", "paramTypes": [], "doc": " 健康检查接口\n\n @return 服务状态\n"}], "constructors": []}