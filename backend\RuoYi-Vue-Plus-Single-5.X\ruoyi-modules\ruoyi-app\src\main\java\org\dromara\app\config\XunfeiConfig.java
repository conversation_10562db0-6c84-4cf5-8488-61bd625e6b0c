package org.dromara.app.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 讯飞AI服务配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "xunfei")
public class XunfeiConfig {

    /**
     * 讯飞星火大模型配置
     */
    private SparkConfig spark = new SparkConfig();

    /**
     * 语音识别配置
     */
    private SpeechConfig speech = new SpeechConfig();

    /**
     * 情感分析配置
     */
    private EmotionConfig emotion = new EmotionConfig();

    /**
     * 语音合成配置
     */
    private TtsConfig tts = new TtsConfig();

    @Data
    public static class SparkConfig {
        /**
         * 应用ID（已废弃，保留兼容性）
         */
        @Deprecated
        private String appId;

        /**
         * API密钥（已废弃，保留兼容性）
         */
        @Deprecated
        private String apiKey;

        /**
         * API密钥（已废弃，保留兼容性）
         */
        @Deprecated
        private String apiSecret;

        /**
         * API密码（HTTP调用认证）
         */
        private String apiPassword = "LUyfIRXanMIDPXCDdYMy:kIrhmhjMUNjjzYihNOfN";

        /**
         * 服务地址
         */
        private String baseUrl = "https://spark-api-open.xf-yun.com/v1/chat/completions";

        /**
         * 模型版本
         */
        private String model = "generalv3.5";

        /**
         * 默认温度
         */
        private Double temperature = 0.7;

        /**
         * 最大Token数
         */
        private Integer maxTokens = 4096;

        /**
         * 连接超时时间（秒）
         */
        private Integer connectTimeout = 30;

        /**
         * 读取超时时间（秒）
         */
        private Integer readTimeout = 60;

        /**
         * 获取模型版本（兼容旧版本配置）
         */
        public String getVersion() {
            return this.model;
        }

        /**
         * 设置模型版本（兼容旧版本配置）
         */
        public void setVersion(String version) {
            this.model = version;
        }
    }

    @Data
    public static class SpeechConfig {
        /**
         * 应用ID
         */
        private String appId;

        /**
         * API密钥
         */
        private String apiKey;

        /**
         * API密钥
         */
        private String apiSecret;

        /**
         * 语音识别服务地址
         */
        private String baseUrl = "https://iat-api.xfyun.cn/v2/iat";

        /**
         * 语言类型
         */
        private String language = "zh_cn";

        /**
         * 音频格式
         */
        private String audioFormat = "audio/L16;rate=16000";

        /**
         * 是否启用标点符号
         */
        private Boolean enablePunctuation = true;

        /**
         * 是否启用数字转换
         */
        private Boolean enableNumberConvert = true;
    }

    @Data
    public static class EmotionConfig {
        /**
         * 应用ID
         */
        private String appId;

        /**
         * API密钥
         */
        private String apiKey;

        /**
         * API密钥
         */
        private String apiSecret;

        /**
         * 情感分析服务地址
         */
        private String baseUrl = "https://api.xf-yun.com/v1/private/s9a3d6d6c";

        /**
         * 分析类型
         */
        private String analysisType = "emotion";

        /**
         * 语言类型
         */
        private String language = "zh";
    }

    @Data
    public static class TtsConfig {
        /**
         * 应用ID
         */
        private String appId;

        /**
         * API密钥
         */
        private String apiKey;

        /**
         * API密钥
         */
        private String apiSecret;

        /**
         * 语音合成服务地址
         */
        private String baseUrl = "https://tts-api.xfyun.cn/v2/tts";

        /**
         * 发音人
         */
        private String voiceName = "xiaoyan";

        /**
         * 语速
         */
        private Integer speed = 50;

        /**
         * 音量
         */
        private Integer volume = 50;

        /**
         * 音调
         */
        private Integer pitch = 50;

        /**
         * 音频格式
         */
        private String audioFormat = "lame";
    }
}
