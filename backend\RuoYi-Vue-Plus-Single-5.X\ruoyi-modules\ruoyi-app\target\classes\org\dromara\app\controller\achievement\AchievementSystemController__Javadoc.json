{"doc": "\n 成就系统状态控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getSystemHealth", "paramTypes": [], "doc": "\n 获取系统健康状态\r\n"}, {"name": "checkSystemIntegrity", "paramTypes": [], "doc": "\n 检查系统完整性\r\n"}, {"name": "checkDatabase", "paramTypes": [], "doc": "\n 检查数据库表\r\n"}, {"name": "checkRabbitMQ", "paramTypes": [], "doc": "\n 检查RabbitMQ配置\r\n"}, {"name": "logSystemStatus", "paramTypes": [], "doc": "\n 打印系统状态到日志\r\n"}, {"name": "getSystemInfo", "paramTypes": [], "doc": "\n 获取系统信息概览\r\n"}], "constructors": []}