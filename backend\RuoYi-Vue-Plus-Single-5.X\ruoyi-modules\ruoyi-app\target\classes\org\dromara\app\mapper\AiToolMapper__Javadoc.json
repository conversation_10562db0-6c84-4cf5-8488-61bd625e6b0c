{"doc": "\n AI工具Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectAvailableTools", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.String", "java.lang.Bo<PERSON>an"], "doc": "\n 分页查询可用工具列表\r\n\r\n @param page     分页对象\r\n @param category 工具分类（可选）\r\n @param enabled  是否启用\r\n @return 工具分页结果\r\n"}, {"name": "selectEnabledByCategory", "paramTypes": ["java.lang.String"], "doc": "\n 根据分类查询启用的工具\r\n\r\n @param category 工具分类\r\n @return 工具列表\r\n"}, {"name": "selectByPermissionLevel", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据权限级别查询工具\r\n\r\n @param maxPermissionLevel 最大权限级别\r\n @return 工具列表\r\n"}, {"name": "selectSystemTools", "paramTypes": [], "doc": "\n 查询系统内置工具\r\n\r\n @return 系统工具列表\r\n"}, {"name": "selectPopularTools", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询热门工具\r\n\r\n @param limit 返回数量限制\r\n @return 热门工具列表\r\n"}, {"name": "selectToolsByUsage", "paramTypes": ["java.lang.Long"], "doc": "\n 根据使用次数查询工具\r\n\r\n @param minUsageCount 最小使用次数\r\n @return 工具列表\r\n"}, {"name": "selectToolsByCategory", "paramTypes": ["java.util.List"], "doc": "\n 根据分类列表查询工具\r\n\r\n @param categories 分类列表\r\n @return 工具列表\r\n"}, {"name": "searchTools", "paramTypes": ["java.lang.String"], "doc": "\n 搜索工具\r\n\r\n @param keyword 关键词\r\n @return 工具列表\r\n"}, {"name": "getToolStats", "paramTypes": [], "doc": "\n 获取工具统计信息\r\n\r\n @return 统计信息\r\n"}, {"name": "getCategoryStats", "paramTypes": [], "doc": "\n 获取分类统计信息\r\n\r\n @return 分类统计列表\r\n"}, {"name": "incrementUsageCount", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 增加工具使用次数\r\n\r\n @param toolId   工具ID\r\n @param lastUsed 最后使用时间\r\n @return 影响行数\r\n"}, {"name": "updateToolStats", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.Long", "java.lang.Double", "java.lang.Long"], "doc": "\n 更新工具统计信息\r\n\r\n @param toolId           工具ID\r\n @param usageCount       使用次数\r\n @param avgExecutionTime 平均执行时间\r\n @param successRate      成功率\r\n @param lastUsed         最后使用时间\r\n @return 影响行数\r\n"}, {"name": "batchUpdateToolStatus", "paramTypes": ["java.util.List", "java.lang.Bo<PERSON>an"], "doc": "\n 批量更新工具状态\r\n\r\n @param toolIds 工具ID列表\r\n @param enabled 是否启用\r\n @return 影响行数\r\n"}, {"name": "batchDeleteTools", "paramTypes": ["java.util.List"], "doc": "\n 批量删除工具\r\n\r\n @param toolIds 工具ID列表\r\n @return 影响行数\r\n"}, {"name": "batchInsertDefaultTools", "paramTypes": ["java.util.List"], "doc": "\n 批量插入默认工具\r\n\r\n @param tools 工具列表\r\n @return 影响行数\r\n"}, {"name": "selectEnabledTools", "paramTypes": [], "doc": "\n 查询所有启用的工具\r\n\r\n @return 启用的工具列表\r\n"}, {"name": "selectByName", "paramTypes": ["java.lang.String"], "doc": "\n 根据工具名称查询工具\r\n\r\n @param toolName 工具名称\r\n @return 工具对象\r\n"}], "constructors": []}