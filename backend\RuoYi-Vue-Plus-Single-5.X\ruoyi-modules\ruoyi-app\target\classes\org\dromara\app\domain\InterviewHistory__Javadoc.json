{"doc": "\n 面试历史记录对象 app_interview_history\r\n\r\n <AUTHOR>\r\n @date 2025-07-17\r\n", "fields": [{"name": "id", "doc": "\n 历史记录ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "resultId", "doc": "\n 结果ID\r\n"}, {"name": "title", "doc": "\n 自定义标题\r\n"}, {"name": "isFavorite", "doc": "\n 是否收藏（0否 1是）\r\n"}, {"name": "tags", "doc": "\n 标签（JSON数组）\r\n"}, {"name": "notes", "doc": "\n 用户备注\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}], "enumConstants": [], "methods": [], "constructors": []}