{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "assembleRequestHeader", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "byte[]"], "doc": " 计算签名所需要的header参数 （http 接口）\n @param requestUrl like 'http://rest-api.xfyun.cn/v2/iat'\n @param apiKey\n @param apiSecret\n @method request method  POST/GET/PATCH/DELETE etc....\n @param body   http request body\n @return header map ，contains all headers should be set when access api\n"}], "constructors": []}