{"doc": "\n 知识库Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectEnabledKnowledgeBases", "paramTypes": [], "doc": "\n 查询启用的知识库列表\r\n\r\n @return 知识库列表\r\n"}, {"name": "selectByType", "paramTypes": ["java.lang.String"], "doc": "\n 根据类型查询知识库\r\n\r\n @param type 知识库类型\r\n @return 知识库列表\r\n"}, {"name": "updateDocumentCount", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 更新文档数量\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @param documentCount   文档数量\r\n @return 更新结果\r\n"}, {"name": "updateVectorCount", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 更新向量数量\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @param vectorCount     向量数量\r\n @return 更新结果\r\n"}, {"name": "selectByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询知识库\r\n\r\n @param userId 用户ID\r\n @return 知识库列表\r\n"}, {"name": "updateStatusByIds", "paramTypes": ["java.util.List", "java.lang.Integer"], "doc": "\n 批量更新知识库状态\r\n\r\n @param ids    知识库ID列表\r\n @param status 状态\r\n @return 更新结果\r\n"}], "constructors": []}