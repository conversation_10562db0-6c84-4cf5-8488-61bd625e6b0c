{"doc": "\n 用户活动统计对象 app_activity_statistics\r\n\r\n <AUTHOR>\r\n @date 2025-07-16\r\n", "fields": [{"name": "id", "doc": "\n 统计ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "activityType", "doc": "\n 活动类型\r\n"}, {"name": "statDate", "doc": "\n 统计日期\r\n"}, {"name": "totalDuration", "doc": "\n 总时长(毫秒)\r\n"}, {"name": "sessionCount", "doc": "\n 会话次数\r\n"}, {"name": "avgDuration", "doc": "\n 平均时长(毫秒)\r\n"}, {"name": "maxDuration", "doc": "\n 最长时长(毫秒)\r\n"}], "enumConstants": [], "methods": [{"name": "initialize", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType", "java.time.LocalDate", "java.lang.Long"], "doc": "\n 初始化统计数据\r\n\r\n @param userId          用户ID\r\n @param activityType    活动类型\r\n @param statDate        统计日期\r\n @param sessionDuration 首次会话时长\r\n @return 初始化的统计对象\r\n"}, {"name": "updateStatistics", "paramTypes": ["java.lang.Long"], "doc": "\n 更新统计数据\r\n\r\n @param sessionDuration 新增的会话时长\r\n"}], "constructors": []}