{"doc": " 支付订单RabbitMQ配置\n\n <AUTHOR>\n", "fields": [{"name": "PAYMENT_DELAY_EXCHANGE", "doc": " 支付订单延迟交换机\n"}, {"name": "PAYMENT_DELAY_QUEUE", "doc": " 支付订单延迟队列\n"}, {"name": "PAYMENT_DELAY_ROUTING_KEY", "doc": " 支付订单延迟路由键\n"}, {"name": "PAYMENT_DEAD_EXCHANGE", "doc": " 支付订单死信交换机\n"}, {"name": "PAYMENT_DEAD_QUEUE", "doc": " 支付订单死信队列\n"}, {"name": "PAYMENT_DEAD_ROUTING_KEY", "doc": " 支付订单死信路由键\n"}], "enumConstants": [], "methods": [{"name": "paymentDelayExchange", "paramTypes": [], "doc": " 创建延迟交换机\n"}, {"name": "paymentDelayQueue", "paramTypes": [], "doc": " 创建延迟队列\n 设置30分钟过期时间，过期后转发到死信队列\n"}, {"name": "paymentDelayBinding", "paramTypes": [], "doc": " 绑定延迟队列到延迟交换机\n"}, {"name": "paymentDeadExchange", "paramTypes": [], "doc": " 创建死信交换机\n"}, {"name": "paymentDeadQueue", "paramTypes": [], "doc": " 创建死信队列\n"}, {"name": "paymentDeadBinding", "paramTypes": [], "doc": " 绑定死信队列到死信交换机\n"}], "constructors": []}