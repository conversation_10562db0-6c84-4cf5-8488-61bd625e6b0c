package org.dromara.system.controller.question;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.bo.QuestionBo;
import org.dromara.system.domain.vo.QuestionVo;
import org.dromara.system.service.IQuestionService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 题目管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/question")
public class QuestionController extends BaseController {

    private final IQuestionService questionService;

    /**
     * 查询题目列表
     */
    @SaCheckPermission("system:question:list")
    @GetMapping("/list")
    public TableDataInfo<QuestionVo> list(QuestionBo bo, PageQuery pageQuery) {
        return questionService.queryPageList(bo, pageQuery);
    }

    /**
     * 根据题库ID查询题目列表
     */
    @SaCheckPermission("system:question:list")
    @GetMapping("/bank/{bankId}")
    public TableDataInfo<QuestionVo> listByBankId(@PathVariable Long bankId, QuestionBo bo, PageQuery pageQuery) {
        return questionService.queryPageListByBankId(bankId, bo, pageQuery);
    }

    /**
     * 导出题目列表
     */
    @SaCheckPermission("system:question:export")
    @Log(title = "题目管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QuestionBo bo, HttpServletResponse response) {
        List<QuestionVo> list = questionService.exportQuestion(bo);
        ExcelUtil.exportExcel(list, "题目数据", QuestionVo.class, response);
    }

    /**
     * 获取题目详细信息
     *
     * @param questionId 题目主键
     */
    @SaCheckPermission("system:question:query")
    @GetMapping("/{questionId}")
    public R<QuestionVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long questionId) {
        return R.ok(questionService.queryById(questionId));
    }

    /**
     * 新增题目
     */
    @SaCheckPermission("system:question:add")
    @Log(title = "题目管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QuestionBo bo) {
        if (!questionService.checkQuestionCodeUnique(bo)) {
            return R.fail("新增题目'" + bo.getTitle() + "'失败，题目编码已存在");
        }
        return toAjax(questionService.insertByBo(bo));
    }

    /**
     * 修改题目
     */
    @SaCheckPermission("system:question:edit")
    @Log(title = "题目管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QuestionBo bo) {
        if (!questionService.checkQuestionCodeUnique(bo)) {
            return R.fail("修改题目'" + bo.getTitle() + "'失败，题目编码已存在");
        }
        return toAjax(questionService.updateByBo(bo));
    }

    /**
     * 删除题目
     *
     * @param questionIds 题目主键串
     */
    @SaCheckPermission("system:question:remove")
    @Log(title = "题目管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{questionIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] questionIds) {
        return toAjax(questionService.deleteWithValidByIds(List.of(questionIds)));
    }

    /**
     * 根据题目编码查询题目
     *
     * @param questionCode 题目编码
     */
    @SaCheckPermission("system:question:query")
    @GetMapping("/code/{questionCode}")
    public R<QuestionVo> getByCode(@PathVariable String questionCode) {
        return R.ok(questionService.queryByQuestionCode(questionCode));
    }

    /**
     * 根据分类查询题目列表
     *
     * @param category 分类
     */
    @SaCheckPermission("system:question:list")
    @GetMapping("/category/{category}")
    public R<List<QuestionVo>> getByCategory(@PathVariable String category) {
        return R.ok(questionService.queryByCategory(category));
    }

    /**
     * 根据难度查询题目列表
     *
     * @param difficulty 难度
     */
    @SaCheckPermission("system:question:list")
    @GetMapping("/difficulty/{difficulty}")
    public R<List<QuestionVo>> getByDifficulty(@PathVariable Integer difficulty) {
        return R.ok(questionService.queryByDifficulty(difficulty));
    }

    /**
     * 根据题目类型查询题目列表
     *
     * @param type 题目类型
     */
    @SaCheckPermission("system:question:list")
    @GetMapping("/type/{type}")
    public R<List<QuestionVo>> getByType(@PathVariable Integer type) {
        return R.ok(questionService.queryByType(type));
    }

    /**
     * 查询用户收藏的题目列表
     *
     * @param userId 用户ID
     */
    @SaCheckPermission("system:question:list")
    @GetMapping("/bookmarked/{userId}")
    public R<List<QuestionVo>> getBookmarkedQuestions(@PathVariable Long userId) {
        return R.ok(questionService.queryBookmarkedQuestions(userId));
    }

    /**
     * 查询热门题目列表
     *
     * @param limit 限制数量
     */
    @GetMapping("/hot")
    public R<List<QuestionVo>> getHotQuestions(@RequestParam(defaultValue = "10") Integer limit) {
        return R.ok(questionService.queryHotQuestions(limit));
    }

    /**
     * 随机查询题目列表
     *
     * @param bankId 题库ID
     * @param limit  限制数量
     */
    @SaCheckPermission("system:question:list")
    @GetMapping("/random")
    public R<List<QuestionVo>> getRandomQuestions(@RequestParam Long bankId, @RequestParam(defaultValue = "10") Integer limit) {
        return R.ok(questionService.queryRandomQuestions(bankId, limit));
    }

    /**
     * 更新题目练习次数
     *
     * @param questionId 题目ID
     */
    @SaCheckPermission("system:question:edit")
    @Log(title = "题目管理", businessType = BusinessType.UPDATE)
    @PutMapping("/practice/{questionId}")
    public R<Void> updatePracticeCount(@PathVariable Long questionId) {
        return toAjax(questionService.updatePracticeCount(questionId));
    }

    /**
     * 更新题目评论数
     *
     * @param questionId 题目ID
     */
    @SaCheckPermission("system:question:edit")
    @Log(title = "题目管理", businessType = BusinessType.UPDATE)
    @PutMapping("/comment/{questionId}")
    public R<Void> updateCommentCount(@PathVariable Long questionId) {
        return toAjax(questionService.updateCommentCount(questionId));
    }

    /**
     * 更新题目正确率
     *
     * @param questionId  题目ID
     * @param correctRate 正确率
     */
    @SaCheckPermission("system:question:edit")
    @Log(title = "题目管理", businessType = BusinessType.UPDATE)
    @PutMapping("/correctRate")
    public R<Void> updateCorrectRate(@RequestParam Long questionId, @RequestParam Integer correctRate) {
        return toAjax(questionService.updateCorrectRate(questionId, correctRate));
    }

    /**
     * 启用/停用题目
     *
     * @param questionId 题目ID
     * @param status     状态
     */
    @SaCheckPermission("system:question:edit")
    @Log(title = "题目管理", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public R<Void> changeStatus(@RequestParam Long questionId, @RequestParam String status) {
        return toAjax(questionService.changeStatus(questionId, status));
    }

    /**
     * 复制题目
     *
     * @param questionId 源题目ID
     * @param title      新题目标题
     */
    @SaCheckPermission("system:question:add")
    @Log(title = "题目管理", businessType = BusinessType.INSERT)
    @PostMapping("/copy")
    public R<Void> copyQuestion(@RequestParam Long questionId, @RequestParam String title) {
        return toAjax(questionService.copyQuestion(questionId, title));
    }

    /**
     * 统计题库下的题目数量
     *
     * @param bankId 题库ID
     */
    @SaCheckPermission("system:question:list")
    @GetMapping("/count/{bankId}")
    public R<Integer> countByBankId(@PathVariable Long bankId) {
        return R.ok(questionService.countByBankId(bankId));
    }

    /**
     * 导入题目数据
     *
     * @param file 导入文件
     */
    @SaCheckPermission("system:question:import")
    @Log(title = "题目管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public R<Void> importData(MultipartFile file) throws Exception {
        List<QuestionBo> list = ExcelUtil.importExcel(file.getInputStream(), QuestionBo.class);
        String message = questionService.importQuestion(list);
        return R.ok(message);
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(List.of(), "题目数据", QuestionBo.class, response);
    }
}
