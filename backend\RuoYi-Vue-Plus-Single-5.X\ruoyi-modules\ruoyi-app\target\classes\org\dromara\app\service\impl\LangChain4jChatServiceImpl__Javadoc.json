{"doc": "\n 基于LangChain4j的聊天服务实现类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "callAgentService", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map", "java.lang.Long"], "doc": "\n 调用特定类型的Agent服务（同步）\r\n\r\n @param agentType Agent类型\r\n @param message   用户消息\r\n @param params    额外参数\r\n @param userId    用户ID\r\n @return 处理结果\r\n"}, {"name": "callAgentServiceStream", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map", "java.lang.Long"], "doc": "\n 调用特定类型的Agent服务（流式）\r\n\r\n @param agentType Agent类型\r\n @param message   用户消息\r\n @param params    额外参数\r\n @param userId    用户ID\r\n @return SSE流\r\n"}, {"name": "invokeAgentService", "paramTypes": ["AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 根据Agent类型调用相应的服务\r\n"}, {"name": "invokeXunfeiStreamingService", "paramTypes": ["AgentContext", "java.lang.String", "java.lang.String", "dev.langchain4j.model.chat.response.StreamingChatResponseHandler"], "doc": "\n 调用讯飞流式Agent服务\r\n"}, {"name": "buildXunfeiContext", "paramTypes": ["AgentContext", "java.lang.String"], "doc": "\n 构建讯飞大模型需要的上下文信息\r\n"}, {"name": "buildSystemPromptForAgent", "paramTypes": ["java.lang.String"], "doc": "\n 根据Agent类型构建系统提示词\r\n"}, {"name": "buildChatHistoryForXunfei", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 构建讯飞大模型需要的历史对话记录\r\n"}, {"name": "invokeStreamingAgentService", "paramTypes": ["AgentContext", "java.lang.String", "java.lang.String", "java.lang.String", "dev.langchain4j.model.chat.response.StreamingChatResponseHandler"], "doc": "\n 调用流式Agent服务\r\n"}, {"name": "createAgentContext", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.String"], "doc": "\n 创建Agent上下文\r\n"}, {"name": "getOrCreateMemory", "paramTypes": ["java.lang.String"], "doc": "\n 获取或创建内存\r\n"}, {"name": "loadHistoryToMemory", "paramTypes": ["java.lang.String", "dev.langchain4j.memory.chat.MessageWindowChatMemory"], "doc": "\n 从数据库加载历史消息到内存\r\n"}, {"name": "enhanceMessageWithRAG", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 使用RAG增强消息\r\n"}, {"name": "buildMetadata", "paramTypes": ["org.dromara.app.domain.dto.ChatRequestDto", "org.dromara.app.domain.Agent"], "doc": "\n 构建消息元数据\r\n"}, {"name": "convertHistoryToConversationTurns", "paramTypes": ["java.lang.String", "java.lang.Long", "int"], "doc": "\n 将聊天历史转换为ConversationTurn对象列表\r\n\r\n @param sessionId  会话ID\r\n @param userId     用户ID\r\n @param maxHistory 最大历史消息数量\r\n @return 对话历史转换结果\r\n"}, {"name": "buildChainOfThoughtPrompt", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 构建思维链提示词\r\n\r\n @param agentType Agent类型\r\n @param message   用户消息\r\n @return 增强后的提示词\r\n"}, {"name": "buildOptimizedSystemPrompt", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Long"], "doc": "\n 构建优化的系统提示词\r\n\r\n @param originalPrompt 原始系统提示词\r\n @param agentType      Agent类型\r\n @param userId         用户ID\r\n @return 优化后的系统提示词\r\n"}, {"name": "buildConversationalPrompt", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Long", "int"], "doc": "\n 构建对话提示词\r\n\r\n @param currentQuery     当前查询\r\n @param sessionId        会话ID\r\n @param userId           用户ID\r\n @param maxHistoryLength 最大历史长度\r\n @return 构建的对话提示词\r\n"}, {"name": "adaptPrompt<PERSON>ength", "paramTypes": ["java.lang.String", "int"], "doc": "\n 适应提示词长度，避免超出模型限制\r\n\r\n @param prompt    提示词\r\n @param maxTokens 最大token数量\r\n @return 调整后的提示词\r\n"}], "constructors": []}