package org.dromara.common.es.core;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.*;
import co.elastic.clients.elasticsearch._types.aggregations.*;
import co.elastic.clients.elasticsearch._types.mapping.TypeMapping;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import co.elastic.clients.elasticsearch.core.*;
import co.elastic.clients.elasticsearch.core.bulk.BulkOperation;
import co.elastic.clients.elasticsearch.core.bulk.IndexOperation;
import co.elastic.clients.elasticsearch.core.search.Highlight;
import co.elastic.clients.elasticsearch.core.search.HighlightField;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.indices.ExistsRequest;
import co.elastic.clients.elasticsearch.indices.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.es.constant.EsConstant;
import org.dromara.common.es.entity.EsPage;
import org.dromara.common.es.entity.SearchRequest;
import org.dromara.common.es.exception.EsException;
import org.dromara.common.es.properties.EsProperties;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ES客户端
 *
 * <AUTHOR>
 */
@Slf4j
public class EsClient {

    private final ObjectMapper objectMapper = new ObjectMapper();
    @Autowired
    private ElasticsearchClient client;
    @Autowired
    private EsProperties esProperties;

    /**
     * 创建索引
     *
     * @param indexName 索引名称
     * @param mapping   映射配置
     * @return 是否成功
     */
    public boolean createIndex(String indexName, String mapping) {
        try {
            CreateIndexRequest.Builder builder = new CreateIndexRequest.Builder()
                .index(indexName);

            // 设置分片和副本
            Map<String, Object> settings = new HashMap<>();
            settings.put("number_of_shards", esProperties.getDefaultShards());
            settings.put("number_of_replicas", esProperties.getDefaultReplicas());
            builder.settings(s -> s.index(IndexSettings.of(is -> is
                .numberOfShards(String.valueOf(esProperties.getDefaultShards()))
                .numberOfReplicas(String.valueOf(esProperties.getDefaultReplicas())))));

            // 设置映射
            if (StrUtil.isNotBlank(mapping)) {
                builder.mappings(TypeMapping.of(tm -> tm.withJson(new StringReader(mapping))));
            }

            CreateIndexResponse response = client.indices().create(builder.build());
            return response.acknowledged();
        } catch (IOException e) {
            log.error("创建索引失败: {}", e.getMessage(), e);
            throw new EsException("创建索引失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除索引
     *
     * @param indexName 索引名称
     * @return 是否成功
     */
    public boolean deleteIndex(String indexName) {
        try {
            DeleteIndexRequest request = DeleteIndexRequest.of(d -> d.index(indexName));
            DeleteIndexResponse response = client.indices().delete(request);
            return response.acknowledged();
        } catch (IOException e) {
            log.error("删除索引失败: {}", e.getMessage(), e);
            throw new EsException("删除索引失败: " + e.getMessage(), e);
        }
    }

    /**
     * 判断索引是否存在
     *
     * @param indexName 索引名称
     * @return 是否存在
     */
    public boolean existsIndex(String indexName) {
        try {
            ExistsRequest request = ExistsRequest.of(e -> e.index(indexName));
            return client.indices().exists(request).value();
        } catch (IOException e) {
            log.error("检查索引是否存在失败: {}", e.getMessage(), e);
            throw new EsException("检查索引是否存在失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加文档
     *
     * @param indexName 索引名称
     * @param id        文档ID
     * @param document  文档内容
     * @return 是否成功
     */
    public boolean addDocument(String indexName, String id, Object document) {
        try {
            IndexRequest.Builder<Object> builder = new IndexRequest.Builder<Object>()
                .index(indexName)
                .document(document);

            if (StrUtil.isNotBlank(id)) {
                builder.id(id);
            }

            IndexResponse response = client.index(builder.build());
            return response.result() != null;
        } catch (IOException e) {
            log.error("添加文档失败: {}", e.getMessage(), e);
            throw new EsException("添加文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量添加文档
     *
     * @param indexName 索引名称
     * @param documents 文档列表
     * @return 是否成功
     */
    public boolean batchAddDocuments(String indexName, List<Map<String, Object>> documents) {
        try {
            List<BulkOperation> operations = new ArrayList<>();

            for (Map<String, Object> document : documents) {
                IndexOperation.Builder<Map<String, Object>> indexOp = new IndexOperation.Builder<Map<String, Object>>()
                    .index(indexName)
                    .document(document);

                Object docId = document.get("id");
                if (docId != null) {
                    indexOp.id(String.valueOf(docId));
                }

                operations.add(BulkOperation.of(b -> b.index(indexOp.build())));
            }

            BulkRequest bulkRequest = BulkRequest.of(b -> b.operations(operations));
            BulkResponse bulkResponse = client.bulk(bulkRequest);
            return !bulkResponse.errors();
        } catch (IOException e) {
            log.error("批量添加文档失败: {}", e.getMessage(), e);
            throw new EsException("批量添加文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新文档
     *
     * @param indexName 索引名称
     * @param id        文档ID
     * @param document  更新内容
     * @return 是否成功
     */
    public boolean updateDocument(String indexName, String id, Object document) {
        try {
            UpdateRequest<Object, Object> request = UpdateRequest.of(u -> u
                .index(indexName)
                .id(id)
                .doc(document));

            UpdateResponse<Object> response = client.update(request, Object.class);
            return response.result() != null;
        } catch (IOException e) {
            log.error("更新文档失败: {}", e.getMessage(), e);
            throw new EsException("更新文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除文档
     *
     * @param indexName 索引名称
     * @param id        文档ID
     * @return 是否成功
     */
    public boolean deleteDocument(String indexName, String id) {
        try {
            DeleteRequest request = DeleteRequest.of(d -> d.index(indexName).id(id));
            DeleteResponse response = client.delete(request);
            return response.result() == Result.Deleted;
        } catch (IOException e) {
            log.error("删除文档失败: {}", e.getMessage(), e);
            throw new EsException("删除文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据ID获取文档
     *
     * @param indexName 索引名称
     * @param id        文档ID
     * @return 文档内容
     */
    public Map<String, Object> getDocument(String indexName, String id) {
        try {
            GetRequest request = GetRequest.of(g -> g.index(indexName).id(id));
            GetResponse<Map> response = client.get(request, Map.class);

            if (response.found()) {
                return response.source();
            }
            return null;
        } catch (IOException e) {
            log.error("获取文档失败: {}", e.getMessage(), e);
            throw new EsException("获取文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 搜索文档
     *
     * @param searchRequest 搜索请求
     * @return 搜索结果
     */
    public EsPage<Map<String, Object>> search(SearchRequest searchRequest) {
        try {
            co.elastic.clients.elasticsearch.core.SearchRequest.Builder builder =
                new co.elastic.clients.elasticsearch.core.SearchRequest.Builder()
                    .index(searchRequest.getIndexName());

            // 构建查询
            Query query = buildQuery(searchRequest);
            builder.query(query);

            // 分页
            int from = (searchRequest.getPageNum() - 1) * searchRequest.getPageSize();
            builder.from(from).size(searchRequest.getPageSize());

            // 排序
            if (CollUtil.isNotEmpty(searchRequest.getSorts())) {
                List<SortOptions> sorts = new ArrayList<>();
                for (SearchRequest.SortField sortField : searchRequest.getSorts()) {
                    SortOrder order = "desc".equalsIgnoreCase(sortField.getOrder()) ?
                        SortOrder.Desc : SortOrder.Asc;
                    sorts.add(SortOptions.of(s -> s.field(FieldSort.of(f -> f
                        .field(sortField.getField())
                        .order(order)))));
                }
                builder.sort(sorts);
            }

            // 高亮
            if (CollUtil.isNotEmpty(searchRequest.getHighlightFields())) {
                Map<String, HighlightField> highlightFields = new HashMap<>();
                for (String field : searchRequest.getHighlightFields()) {
                    highlightFields.put(field, HighlightField.of(h -> h));
                }
                builder.highlight(Highlight.of(h -> h
                    .fields(highlightFields)
                    .preTags(EsConstant.DEFAULT_HIGHLIGHT_PRE_TAG)
                    .postTags(EsConstant.DEFAULT_HIGHLIGHT_POST_TAG)));
            }

            // 聚合
            if (CollUtil.isNotEmpty(searchRequest.getAggregations())) {
                Map<String, Aggregation> aggregations = new HashMap<>();
                for (SearchRequest.AggregationConfig aggConfig : searchRequest.getAggregations()) {
                    aggregations.put(aggConfig.getName(), buildAggregation(aggConfig));
                }
                builder.aggregations(aggregations);
            }

            co.elastic.clients.elasticsearch.core.SearchResponse<Map> response =
                client.search(builder.build(), Map.class);

            return buildSearchResult(response, searchRequest.getPageNum(), searchRequest.getPageSize());
        } catch (IOException e) {
            log.error("搜索文档失败: {}", e.getMessage(), e);
            throw new EsException("搜索文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建查询
     */
    private Query buildQuery(SearchRequest searchRequest) {
        BoolQuery.Builder boolQuery = new BoolQuery.Builder();

        // 关键字查询
        if (StrUtil.isNotBlank(searchRequest.getKeyword())) {
            if (CollUtil.isNotEmpty(searchRequest.getFields())) {
                // 多字段查询
                MultiMatchQuery multiMatchQuery = MultiMatchQuery.of(m -> m
                    .query(searchRequest.getKeyword())
                    .fields(searchRequest.getFields()));
                boolQuery.must(Query.of(q -> q.multiMatch(multiMatchQuery)));
            } else {
                // 全文搜索
                QueryStringQuery queryStringQuery = QueryStringQuery.of(q -> q
                    .query(searchRequest.getKeyword()));
                boolQuery.must(Query.of(q -> q.queryString(queryStringQuery)));
            }
        }

        // 过滤条件
        if (CollUtil.isNotEmpty(searchRequest.getFilters())) {
            for (Map.Entry<String, Object> entry : searchRequest.getFilters().entrySet()) {
                TermQuery termQuery = TermQuery.of(t -> t
                    .field(entry.getKey())
                    .value(FieldValue.of(entry.getValue().toString())));
                boolQuery.filter(Query.of(q -> q.term(termQuery)));
            }
        }

        return Query.of(q -> q.bool(boolQuery.build()));
    }

    /**
     * 构建聚合
     */
    private Aggregation buildAggregation(SearchRequest.AggregationConfig aggConfig) {
        switch (aggConfig.getType().toLowerCase()) {
            case "terms":
                return Aggregation.of(a -> a.terms(TermsAggregation.of(t -> t
                    .field(aggConfig.getField())
                    .size(aggConfig.getSize()))));
            case "avg":
                return Aggregation.of(a -> a.avg(AverageAggregation.of(av -> av
                    .field(aggConfig.getField()))));
            case "sum":
                return Aggregation.of(a -> a.sum(SumAggregation.of(s -> s
                    .field(aggConfig.getField()))));
            case "max":
                return Aggregation.of(a -> a.max(MaxAggregation.of(m -> m
                    .field(aggConfig.getField()))));
            case "min":
                return Aggregation.of(a -> a.min(MinAggregation.of(m -> m
                    .field(aggConfig.getField()))));
            default:
                throw new EsException("不支持的聚合类型: " + aggConfig.getType());
        }
    }

    /**
     * 构建搜索结果
     */
    private EsPage<Map<String, Object>> buildSearchResult(
        co.elastic.clients.elasticsearch.core.SearchResponse<Map> response,
        Integer pageNum, Integer pageSize) {

        EsPage<Map<String, Object>> esPage = new EsPage<>();
        esPage.setPageNum(pageNum);
        esPage.setPageSize(pageSize);
        esPage.setTotal(response.hits().total().value());
        esPage.setPages((int) Math.ceil((double) esPage.getTotal() / pageSize));

        List<Map<String, Object>> records = new ArrayList<>();
        for (Hit<Map> hit : response.hits().hits()) {
            Map<String, Object> record = new HashMap<>(hit.source());
            record.put("_id", hit.id());
            record.put("_score", hit.score());

            // 处理高亮
            if (hit.highlight() != null && !hit.highlight().isEmpty()) {
                Map<String, Object> highlight = new HashMap<>();
                for (Map.Entry<String, List<String>> entry : hit.highlight().entrySet()) {
                    highlight.put(entry.getKey(), entry.getValue());
                }
                record.put("_highlight", highlight);
            }

            records.add(record);
        }

        esPage.setRecords(records);

        // 处理聚合结果
        if (response.aggregations() != null && !response.aggregations().isEmpty()) {
            Map<String, Object> aggregations = new HashMap<>();
            for (Map.Entry<String, Aggregate> entry : response.aggregations().entrySet()) {
                aggregations.put(entry.getKey(), parseAggregationResult(entry.getValue()));
            }
            esPage.setAggregations(aggregations);
        }

        return esPage;
    }

    /**
     * 解析聚合结果
     */
    private Object parseAggregationResult(Aggregate aggregate) {
        if (aggregate.isSterms()) {
            StringTermsAggregate terms = aggregate.sterms();
            List<Map<String, Object>> buckets = new ArrayList<>();
            for (StringTermsBucket bucket : terms.buckets().array()) {
                Map<String, Object> bucketMap = new HashMap<>();
                bucketMap.put("key", bucket.key());
                bucketMap.put("doc_count", bucket.docCount());
                buckets.add(bucketMap);
            }
            return buckets;
        } else if (aggregate.isAvg()) {
            return aggregate.avg().value();
        } else if (aggregate.isSum()) {
            return aggregate.sum().value();
        } else if (aggregate.isMax()) {
            return aggregate.max().value();
        } else if (aggregate.isMin()) {
            return aggregate.min().value();
        }
        return null;
    }
}
