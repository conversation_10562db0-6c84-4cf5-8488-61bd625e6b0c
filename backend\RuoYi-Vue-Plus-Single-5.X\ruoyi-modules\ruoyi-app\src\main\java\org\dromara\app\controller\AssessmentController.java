package org.dromara.app.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.dromara.app.domain.bo.AssessmentResultBo;
import org.dromara.app.domain.vo.AssessmentQuestionVo;
import org.dromara.app.domain.vo.DetailedAbilityReportVo;
import org.dromara.app.domain.vo.InitialAbilityAssessmentVo;
import org.dromara.app.domain.vo.UserGrowthProfileVo;
import org.dromara.app.service.IAssessmentService;
import org.dromara.common.core.domain.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 能力评估控制器
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/assessment")
@Tag(name = "能力评估接口")
public class AssessmentController {

    private final IAssessmentService assessmentService;

    /**
     * 获取评估问题
     */
    @GetMapping("/questions")
    @Operation(summary = "获取评估问题")
    public R<List<AssessmentQuestionVo>> getQuestions() {
        return R.ok(assessmentService.getAssessmentQuestions());
    }

    /**
     * 提交评估结果
     */
    @PostMapping("/submit")
    @Operation(summary = "提交评估结果")
    public R<InitialAbilityAssessmentVo> submitResults(@RequestBody List<AssessmentResultBo> results) {
        return R.ok(assessmentService.submitAssessmentResults(results));
    }

    /**
     * 获取详细能力报告
     */
    @GetMapping("/report")
    @Operation(summary = "获取详细能力报告")
    public R<DetailedAbilityReportVo> getDetailedReport() {
        return R.ok(assessmentService.getDetailedAbilityReport());
    }

    /**
     * 获取用户成长档案
     */
    @GetMapping("/profile")
    @Operation(summary = "获取用户成长档案")
    public R<UserGrowthProfileVo> getUserProfile() {
        return R.ok(assessmentService.getUserGrowthProfile());
    }
}
