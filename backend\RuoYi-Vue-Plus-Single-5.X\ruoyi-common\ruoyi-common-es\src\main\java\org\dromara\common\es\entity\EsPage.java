package org.dromara.common.es.entity;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * ES分页结果
 *
 * <AUTHOR>
 */
@Data
public class EsPage<T> {

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 总页数
     */
    private Integer pages;

    /**
     * 数据列表
     */
    private List<T> records;

    /**
     * 聚合结果
     */
    private Map<String, Object> aggregations;

    /**
     * 是否为第一页
     */
    private Boolean isFirstPage;

    /**
     * 是否为最后一页
     */
    private Boolean isLastPage;

    /**
     * 是否有前一页
     */
    private Boolean hasPrevious;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 构造分页结果
     */
    public static <T> EsPage<T> of(Integer pageNum, Integer pageSize, Long total, List<T> records) {
        EsPage<T> page = new EsPage<>();
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        page.setTotal(total);
        page.setRecords(records);
        page.calculatePageInfo();
        return page;
    }

    /**
     * 计算分页信息
     */
    public void calculatePageInfo() {
        if (total == null || pageNum == null || pageSize == null) {
            return;
        }

        this.pages = (int) Math.ceil((double) total / pageSize);
        this.isFirstPage = pageNum == 1;
        this.isLastPage = pageNum.equals(pages);
        this.hasPrevious = pageNum > 1;
        this.hasNext = pageNum < pages;
    }

}
