{"doc": "\n 缓存统计信息视图对象\r\n\r\n <AUTHOR>\r\n @date 2025-01-19\r\n", "fields": [{"name": "totalCacheCount", "doc": "\n 总缓存数量\r\n"}, {"name": "hitCount", "doc": "\n 缓存命中次数\r\n"}, {"name": "missCount", "doc": "\n 缓存未命中次数\r\n"}, {"name": "hitRate", "doc": "\n 缓存命中率（百分比）\r\n"}, {"name": "totalCacheSize", "doc": "\n 缓存总大小（字节）\r\n"}, {"name": "usedCacheSize", "doc": "\n 已使用缓存大小（字节）\r\n"}, {"name": "cacheUsageRate", "doc": "\n 缓存使用率（百分比）\r\n"}, {"name": "expiredCacheCount", "doc": "\n 过期缓存数量\r\n"}, {"name": "hotCacheCount", "doc": "\n 热点缓存数量\r\n"}, {"name": "coldCacheCount", "doc": "\n 冷缓存数量\r\n"}, {"name": "averageAccessTime", "doc": "\n 平均缓存访问时间（毫秒）\r\n"}, {"name": "writeCount", "doc": "\n 缓存写入次数\r\n"}, {"name": "deleteCount", "doc": "\n 缓存删除次数\r\n"}, {"name": "updateCount", "doc": "\n 缓存更新次数\r\n"}, {"name": "evictionCount", "doc": "\n 缓存驱逐次数\r\n"}, {"name": "lastStatisticsTime", "doc": "\n 最后统计时间\r\n"}, {"name": "categoryStatistics", "doc": "\n 缓存分类统计\r\n"}, {"name": "sizeDistribution", "doc": "\n 缓存大小分布\r\n"}, {"name": "accessFrequencyDistribution", "doc": "\n 缓存访问频率分布\r\n"}, {"name": "memoryUsage", "doc": "\n 内存使用情况\r\n"}, {"name": "healthStatus", "doc": "\n 缓存健康状态：healthy/warning/critical\r\n"}, {"name": "performanceMetrics", "doc": "\n 性能指标\r\n"}], "enumConstants": [], "methods": [], "constructors": []}