{"doc": " AI工具Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectAvailableTools", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.String", "java.lang.Bo<PERSON>an"], "doc": " 分页查询可用工具列表\n\n @param page     分页对象\n @param category 工具分类（可选）\n @param enabled  是否启用\n @return 工具分页结果\n"}, {"name": "selectEnabledByCategory", "paramTypes": ["java.lang.String"], "doc": " 根据分类查询启用的工具\n\n @param category 工具分类\n @return 工具列表\n"}, {"name": "selectByPermissionLevel", "paramTypes": ["java.lang.Integer"], "doc": " 根据权限级别查询工具\n\n @param maxPermissionLevel 最大权限级别\n @return 工具列表\n"}, {"name": "selectSystemTools", "paramTypes": [], "doc": " 查询系统内置工具\n\n @return 系统工具列表\n"}, {"name": "selectPopularTools", "paramTypes": ["java.lang.Integer"], "doc": " 查询热门工具\n\n @param limit 返回数量限制\n @return 热门工具列表\n"}, {"name": "selectToolsByUsage", "paramTypes": ["java.lang.Long"], "doc": " 根据使用次数查询工具\n\n @param minUsageCount 最小使用次数\n @return 工具列表\n"}, {"name": "selectToolsByCategory", "paramTypes": ["java.util.List"], "doc": " 根据分类列表查询工具\n\n @param categories 分类列表\n @return 工具列表\n"}, {"name": "searchTools", "paramTypes": ["java.lang.String"], "doc": " 搜索工具\n\n @param keyword 关键词\n @return 工具列表\n"}, {"name": "getToolStats", "paramTypes": [], "doc": " 获取工具统计信息\n\n @return 统计信息\n"}, {"name": "getCategoryStats", "paramTypes": [], "doc": " 获取分类统计信息\n\n @return 分类统计列表\n"}, {"name": "incrementUsageCount", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 增加工具使用次数\n\n @param toolId   工具ID\n @param lastUsed 最后使用时间\n @return 影响行数\n"}, {"name": "updateToolStats", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.Long", "java.lang.Double", "java.lang.Long"], "doc": " 更新工具统计信息\n\n @param toolId           工具ID\n @param usageCount       使用次数\n @param avgExecutionTime 平均执行时间\n @param successRate      成功率\n @param lastUsed         最后使用时间\n @return 影响行数\n"}, {"name": "batchUpdateToolStatus", "paramTypes": ["java.util.List", "java.lang.Bo<PERSON>an"], "doc": " 批量更新工具状态\n\n @param toolIds 工具ID列表\n @param enabled 是否启用\n @return 影响行数\n"}, {"name": "batchDeleteTools", "paramTypes": ["java.util.List"], "doc": " 批量删除工具\n\n @param toolIds 工具ID列表\n @return 影响行数\n"}, {"name": "batchInsertDefaultTools", "paramTypes": ["java.util.List"], "doc": " 批量插入默认工具\n\n @param tools 工具列表\n @return 影响行数\n"}, {"name": "selectEnabledTools", "paramTypes": [], "doc": " 查询所有启用的工具\n\n @return 启用的工具列表\n"}, {"name": "selectByName", "paramTypes": ["java.lang.String"], "doc": " 根据工具名称查询工具\n\n @param toolName 工具名称\n @return 工具对象\n"}], "constructors": []}