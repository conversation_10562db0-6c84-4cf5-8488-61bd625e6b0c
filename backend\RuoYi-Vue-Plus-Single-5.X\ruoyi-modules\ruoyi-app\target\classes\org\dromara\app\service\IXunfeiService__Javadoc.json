{"doc": "\n 讯飞服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "sparkChat", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": "\n 星火大模型聊天\r\n\r\n @param message 消息内容\r\n @param context 上下文信息\r\n @return 响应内容\r\n"}, {"name": "sparkChatStream", "paramTypes": ["java.lang.String", "java.util.Map", "org.dromara.app.service.IXunfeiService.StreamCallback"], "doc": "\n 星火大模型流式聊天\r\n\r\n @param message  消息内容\r\n @param context  上下文信息\r\n @param callback 回调处理器\r\n"}, {"name": "sparkChatAgent", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map", "org.dromara.app.service.IXunfeiService.StreamCallback"], "doc": "\n 智能体星火大模型流式聊天\r\n\r\n @param message   消息内容\r\n @param agentType 智能体类型\r\n @param context   上下文信息\r\n @param callback  回调处理器\r\n"}, {"name": "sparkChatAgentSync", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map"], "doc": "\n 智能体星火大模型聊天(非流式)\r\n\r\n @param message   消息内容\r\n @param agentType 智能体类型\r\n @param context   上下文信息\r\n @return 响应内容\r\n"}, {"name": "speechRecognition", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 语音识别\r\n\r\n @param audioFile 音频文件\r\n @return 语音识别结果\r\n"}, {"name": "speechRecognition", "paramTypes": ["java.lang.String"], "doc": "\n 通过URL进行语音识别\r\n\r\n @param audioUrl 音频URL\r\n @return 语音识别结果\r\n"}, {"name": "emotionAnalysis", "paramTypes": ["java.lang.String"], "doc": "\n 情感分析\r\n\r\n @param text 待分析文本\r\n @return 情感分析结果\r\n"}, {"name": "voiceEmotionAnalysis", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 语音情感分析\r\n\r\n @param audioFile 音频文件\r\n @return 语音情感分析结果\r\n"}, {"name": "imageEmotionAnalysis", "paramTypes": ["java.lang.String"], "doc": "\n 图像情感分析\r\n\r\n @param imageData base64编码的图像数据\r\n @return 图像情感分析结果\r\n"}, {"name": "generateInterviewSuggestion", "paramTypes": ["java.util.Map", "java.util.Map"], "doc": "\n 生成面试智能建议\r\n\r\n @param context 面试上下文\r\n @param analysisData 分析数据\r\n @return 智能建议\r\n"}, {"name": "textToSpeech", "paramTypes": ["java.lang.String", "org.dromara.app.service.IXunfeiService.VoiceConfig"], "doc": "\n 文本转语音\r\n\r\n @param text        待合成文本\r\n @param voiceConfig 语音配置\r\n @return 语音数据\r\n"}], "constructors": []}