package org.dromara.common.mybatis.core.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.QuestionVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T11:27:37+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.11 (Amazon.com Inc.)"
)
@Component
public class QuestionToQuestionVoMapperImpl implements QuestionToQuestionVoMapper {

    @Override
    public QuestionVo convert(Question arg0) {
        if ( arg0 == null ) {
            return null;
        }

        QuestionVo questionVo = new QuestionVo();

        questionVo.setQuestionId( arg0.getQuestionId() );
        questionVo.setBankId( arg0.getBankId() );
        questionVo.setQuestionCode( arg0.getQuestionCode() );
        questionVo.setTitle( arg0.getTitle() );
        questionVo.setDescription( arg0.getDescription() );
        questionVo.setContent( arg0.getContent() );
        questionVo.setAnswer( arg0.getAnswer() );
        questionVo.setAnalysis( arg0.getAnalysis() );
        questionVo.setDifficulty( arg0.getDifficulty() );
        questionVo.setCategory( arg0.getCategory() );
        questionVo.setType( arg0.getType() );
        questionVo.setPracticeCount( arg0.getPracticeCount() );
        questionVo.setCorrectRate( arg0.getCorrectRate() );
        questionVo.setAcceptanceRate( arg0.getAcceptanceRate() );
        questionVo.setCommentCount( arg0.getCommentCount() );
        questionVo.setTags( arg0.getTags() );
        questionVo.setSort( arg0.getSort() );
        questionVo.setStatus( arg0.getStatus() );
        questionVo.setRemark( arg0.getRemark() );
        questionVo.setCreateTime( arg0.getCreateTime() );
        questionVo.setUpdateTime( arg0.getUpdateTime() );

        return questionVo;
    }

    @Override
    public QuestionVo convert(Question arg0, QuestionVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setQuestionId( arg0.getQuestionId() );
        arg1.setBankId( arg0.getBankId() );
        arg1.setQuestionCode( arg0.getQuestionCode() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setContent( arg0.getContent() );
        arg1.setAnswer( arg0.getAnswer() );
        arg1.setAnalysis( arg0.getAnalysis() );
        arg1.setDifficulty( arg0.getDifficulty() );
        arg1.setCategory( arg0.getCategory() );
        arg1.setType( arg0.getType() );
        arg1.setPracticeCount( arg0.getPracticeCount() );
        arg1.setCorrectRate( arg0.getCorrectRate() );
        arg1.setAcceptanceRate( arg0.getAcceptanceRate() );
        arg1.setCommentCount( arg0.getCommentCount() );
        arg1.setTags( arg0.getTags() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateTime( arg0.getUpdateTime() );

        return arg1;
    }
}
