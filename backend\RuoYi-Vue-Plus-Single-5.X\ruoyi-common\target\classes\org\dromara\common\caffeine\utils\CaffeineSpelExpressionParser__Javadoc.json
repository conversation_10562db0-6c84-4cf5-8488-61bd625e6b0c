{"doc": " Caffeine缓存专用的SpEL表达式解析工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "parseExpression", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.reflect.Method", "java.lang.Object[]"], "doc": " 解析SpEL表达式并返回字符串结果\n\n @param expression SpEL表达式\n @param method     方法\n @param args       参数\n @return 解析结果\n"}, {"name": "parseExpression", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.reflect.Method", "java.lang.Object[]", "java.lang.Object"], "doc": " 解析SpEL表达式并返回字符串结果\n\n @param expression SpEL表达式\n @param method     方法\n @param args       参数\n @param result     方法返回值\n @return 解析结果\n"}, {"name": "parseCondition", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.reflect.Method", "java.lang.Object[]", "java.lang.Object"], "doc": " 解析条件表达式并返回布尔结果\n\n @param condition 条件表达式\n @param method    方法\n @param args      参数\n @param result    方法返回值（可为null）\n @return 条件结果\n"}, {"name": "createEvaluationContext", "paramTypes": ["java.lang.Object", "java.lang.reflect.Method", "java.lang.Object[]", "java.lang.Object"], "doc": " 创建SpEL评估上下文\n\n @param method 方法\n @param args   参数\n @param result 返回值\n @return 评估上下文\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": [], "doc": " 获取原始的SpEL表达式解析器\n\n @return SpEL表达式解析器\n"}, {"name": "parseExpression", "paramTypes": ["java.lang.String"], "doc": " 解析表达式字符串\n\n @param expressionString 表达式字符串\n @return 表达式对象\n @throws ParseException 解析异常\n"}, {"name": "parseExpression", "paramTypes": ["java.lang.String", "org.springframework.expression.ParserContext"], "doc": " 解析表达式字符串（带上下文）\n\n @param expressionString 表达式字符串\n @param context          解析上下文\n @return 表达式对象\n @throws ParseException 解析异常\n"}], "constructors": []}