{"doc": " 支付SSE服务实现类\n 管理SSE连接和推送支付状态消息\n\n <AUTHOR>\n", "fields": [{"name": "SSE_TIMEOUT", "doc": " SSE连接超时时间（毫秒）- 5分钟\n"}, {"name": "sseConnections", "doc": " SSE连接存储Map\n Key: 订单号, Value: SSE连接信息\n"}], "enumConstants": [], "methods": [{"name": "validatePaymentToken", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 验证支付token\n"}, {"name": "createConnection", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 创建SSE连接\n"}, {"name": "pushPaymentSuccess", "paramTypes": ["java.lang.String"], "doc": " 推送支付成功消息\n"}, {"name": "pushPaymentFailed", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 推送支付失败消息\n"}, {"name": "pushPaymentCancelled", "paramTypes": ["java.lang.String"], "doc": " 推送支付取消消息\n"}, {"name": "manualQueryOrderStatus", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 手动查询订单状态\n"}, {"name": "closeConnection", "paramTypes": ["java.lang.String"], "doc": " 关闭SSE连接\n"}, {"name": "cleanupExpiredConnections", "paramTypes": [], "doc": " 清理过期连接\n"}, {"name": "sendMessage", "paramTypes": ["org.springframework.web.servlet.mvc.method.annotation.SseEmitter", "java.lang.String", "java.lang.String", "java.lang.Object"], "doc": " 发送SSE消息\n"}, {"name": "sendHeartbeat", "paramTypes": ["org.springframework.web.servlet.mvc.method.annotation.SseEmitter"], "doc": " 发送心跳消息\n"}, {"name": "setTimeout", "paramTypes": ["java.lang.Runnable", "long"], "doc": " 延迟执行任务\n"}, {"name": "handlePaymentSuccessEvent", "paramTypes": ["org.dromara.app.event.PaymentEvent.PaymentSuccessEvent"], "doc": " 监听支付成功事件\n"}, {"name": "handlePaymentFailedEvent", "paramTypes": ["org.dromara.app.event.PaymentEvent.PaymentFailedEvent"], "doc": " 监听支付失败事件\n"}, {"name": "handlePaymentCancelledEvent", "paramTypes": ["org.dromara.app.event.PaymentEvent.PaymentCancelledEvent"], "doc": " 监听支付取消事件\n"}], "constructors": []}