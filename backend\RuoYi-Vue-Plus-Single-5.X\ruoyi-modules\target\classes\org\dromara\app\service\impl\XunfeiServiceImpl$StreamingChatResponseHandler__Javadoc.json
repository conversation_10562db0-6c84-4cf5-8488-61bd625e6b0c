{"doc": " 流式聊天响应处理器\n", "fields": [], "enumConstants": [], "methods": [{"name": "onResponse", "paramTypes": ["java.lang.String"], "doc": " 收到响应片段\n\n @param token 文本片段\n"}, {"name": "onComplete", "paramTypes": ["java.lang.String"], "doc": " 完成回调\n\n @param fullResponse 完整响应\n"}, {"name": "onError", "paramTypes": ["java.lang.Throwable"], "doc": " 错误回调\n\n @param throwable 异常信息\n"}], "constructors": []}