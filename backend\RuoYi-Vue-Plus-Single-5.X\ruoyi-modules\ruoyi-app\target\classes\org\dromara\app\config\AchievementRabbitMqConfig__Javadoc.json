{"doc": "\n 成就系统RabbitMQ配置\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "achievementExchange", "paramTypes": [], "doc": "\n 成就系统主交换机\r\n"}, {"name": "achievement<PERSON><PERSON><PERSON><PERSON><PERSON>ue", "paramTypes": [], "doc": "\n 成就检查队列\r\n"}, {"name": "achievementNotificationQueue", "paramTypes": [], "doc": "\n 成就通知队列\r\n"}, {"name": "achievementDlxExchange", "paramTypes": [], "doc": "\n 死信交换机\r\n"}, {"name": "achievementDlxQueue", "paramTypes": [], "doc": "\n 死信队列\r\n"}, {"name": "achievement<PERSON><PERSON>ck<PERSON><PERSON>ing", "paramTypes": [], "doc": "\n 绑定成就检查队列到交换机\r\n"}, {"name": "achievementNotificationBinding", "paramTypes": [], "doc": "\n 绑定成就通知队列到交换机\r\n"}, {"name": "achievementDlxBinding", "paramTypes": [], "doc": "\n 绑定死信队列到死信交换机\r\n"}], "constructors": []}