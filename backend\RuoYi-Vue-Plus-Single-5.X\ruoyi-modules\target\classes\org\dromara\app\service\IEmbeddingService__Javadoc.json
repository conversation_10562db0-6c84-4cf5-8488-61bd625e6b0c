{"doc": " 文本嵌入服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "generateEmbedding", "paramTypes": ["java.lang.String"], "doc": " 生成文本向量\n\n @param text 文本内容\n @return 向量数组\n"}, {"name": "generateEmbeddings", "paramTypes": ["java.util.List"], "doc": " 批量生成文本向量\n\n @param texts 文本列表\n @return 向量列表\n"}, {"name": "calculateSimilarity", "paramTypes": ["float[]", "float[]"], "doc": " 计算向量相似度\n\n @param vector1 向量1\n @param vector2 向量2\n @return 相似度分数 (0-1)\n"}, {"name": "getVectorDimension", "paramTypes": [], "doc": " 获取向量维度\n\n @return 向量维度\n"}, {"name": "getModelName", "paramTypes": [], "doc": " 获取嵌入模型名称\n\n @return 模型名称\n"}, {"name": "isAvailable", "paramTypes": [], "doc": " 检查嵌入服务是否可用\n\n @return 是否可用\n"}], "constructors": []}