package org.dromara.app.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.service.ConfigurationService;
import org.dromara.app.service.impl.ToolCallServiceImpl;
import org.dromara.app.service.tool.impl.CurrentTimeToolExecutor;
import org.dromara.app.tool.impl.CalculatorToolExecutor;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 应用启动配置
 * 在应用启动时执行初始化操作
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AppStartupConfiguration implements ApplicationRunner {

    private final ConfigurationService configurationService;
    private final ToolCallServiceImpl toolCallService;
    private final CurrentTimeToolExecutor currentTimeToolExecutor;
    private final CalculatorToolExecutor calculatorToolExecutor;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始初始化Agent应用...");

        try {
            // 初始化配置服务
            initConfigurationService();

            // 注册工具执行器
            registerToolExecutors();

            // 打印启动信息
            printStartupInfo();

            log.info("Agent应用初始化完成");

        } catch (Exception e) {
            log.error("Agent应用初始化失败", e);
            throw e;
        }
    }

    private void initConfigurationService() {
        try {
            configurationService.init();
            log.info("配置服务初始化完成");
        } catch (Exception e) {
            log.error("配置服务初始化失败", e);
            throw e;
        }
    }

    private void registerToolExecutors() {
        try {
            // 注册系统工具
            toolCallService.registerToolExecutor(currentTimeToolExecutor);
            toolCallService.registerToolExecutor(calculatorToolExecutor);

            log.info("工具执行器注册完成");
        } catch (Exception e) {
            log.error("工具执行器注册失败", e);
            throw e;
        }
    }

    private void printStartupInfo() {
        log.info("========================================");
        log.info("Agent应用启动信息:");
        log.info("- 聊天功能: {}", configurationService.isFeatureEnabled("chat_enabled") ? "启用" : "禁用");
        log.info("- 流式聊天: {}", configurationService.isFeatureEnabled("stream_chat_enabled") ? "启用" : "禁用");
        log.info("- RAG功能: {}", configurationService.isRagEnabled() ? "启用" : "禁用");
        log.info("- 高级RAG: {}", configurationService.isAdvancedRagEnabled() ? "启用" : "禁用");
        log.info("- 工具调用: {}", configurationService.isToolsEnabled() ? "启用" : "禁用");
        log.info("- 限流功能: {}", configurationService.isRateLimitEnabled() ? "启用" : "禁用");
        log.info("- 监控功能: {}", configurationService.isMonitoringEnabled() ? "启用" : "禁用");
        log.info("- 审计日志: {}", configurationService.isAuditLogEnabled() ? "启用" : "禁用");
        log.info("========================================");
    }
}
