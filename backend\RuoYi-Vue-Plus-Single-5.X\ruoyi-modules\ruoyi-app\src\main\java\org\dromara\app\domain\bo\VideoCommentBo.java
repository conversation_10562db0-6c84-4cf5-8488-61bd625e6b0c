package org.dromara.app.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.dromara.app.domain.VideoComment;
import org.dromara.common.core.validate.AddGroup;

/**
 * 视频评论业务对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = VideoComment.class, reverseConvertGenerate = false)
public class VideoCommentBo {

    /**
     * 评论ID
     */
    private Long id;

    /**
     * 视频ID
     */
    @NotNull(message = "视频ID不能为空", groups = {AddGroup.class})
    private Long videoId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 父评论ID
     */
    private Long parentId;

    /**
     * 评论内容
     */
    @NotBlank(message = "评论内容不能为空", groups = {AddGroup.class})
    @Size(max = 1000, message = "评论内容长度不能超过1000个字符", groups = {AddGroup.class})
    private String content;
}
