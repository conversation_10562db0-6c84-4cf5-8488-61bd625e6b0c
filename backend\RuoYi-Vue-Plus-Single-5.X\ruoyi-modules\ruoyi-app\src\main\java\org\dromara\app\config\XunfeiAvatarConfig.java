package org.dromara.app.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 讯飞数字人配置类
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
@Component
@ConfigurationProperties(prefix = "xunfei.avatar")
public class XunfeiAvatarConfig {

    /**
     * 接口地址
     */
    private String avatarUrl = "wss://avatar.cn-huadong-1.xf-yun.com/v1/interact";

    /**
     * API Key
     */
    private String apiKey;

    /**
     * API Secret
     */
    private String apiSecret;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 默认形象ID
     */
    private String defaultAvatarId = "110117005";

    /**
     * 默认声音ID
     */
    private String defaultVcn = "x4_lingxiaoying_assist";

    /**
     * 默认场景ID
     */
    private String defaultSceneId = "77213753883627520";

    /**
     * 默认视频宽度
     */
    private Integer defaultWidth = 720;

    /**
     * 默认视频高度
     */
    private Integer defaultHeight = 1280;

    /**
     * 默认语速
     */
    private Integer defaultSpeed = 50;

    /**
     * 默认音量
     */
    private Integer defaultVolume = 50;

    /**
     * 默认语调
     */
    private Integer defaultPitch = 50;

    /**
     * 心跳间隔（毫秒）
     */
    private Long heartbeatInterval = 5000L;

    /**
     * 连接超时时间（毫秒）
     */
    private Long connectTimeout = 30000L;

    /**
     * 是否开启透明背景
     */
    private Boolean enableAlpha = false;

    /**
     * 视频协议
     */
    private String protocol = "xrtc";

    /**
     * 视频帧率
     */
    private Integer fps = 25;

    /**
     * 视频码率
     */
    private Integer bitrate = 5000;
}
