{"doc": " 面试答案对象 app_interview_answer\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [{"name": "id", "doc": " 答案ID\n"}, {"name": "sessionId", "doc": " 会话ID\n"}, {"name": "questionId", "doc": " 问题ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "content", "doc": " 答案内容（文本）\n"}, {"name": "audioUrl", "doc": " 音频URL\n"}, {"name": "videoUrl", "doc": " 视频URL\n"}, {"name": "duration", "doc": " 回答时长（秒）\n"}, {"name": "submittedTime", "doc": " 提交时间\n"}, {"name": "score", "doc": " AI评估分数\n"}, {"name": "feedback", "doc": " AI评估反馈\n"}, {"name": "evaluationDetails", "doc": " 评估详情（JSON格式）\n"}, {"name": "status", "doc": " 状态（submitted, evaluated, reviewed）\n"}, {"name": "skipped", "doc": " 是否跳过\n"}, {"name": "skipR<PERSON>on", "doc": " 跳过原因\n"}], "enumConstants": [], "methods": [], "constructors": []}