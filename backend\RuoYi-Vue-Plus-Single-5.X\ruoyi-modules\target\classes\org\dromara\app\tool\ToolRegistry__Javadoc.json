{"doc": " 工具注册表\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "registerExecutor", "paramTypes": ["org.dromara.app.tool.ToolExecutor"], "doc": " 注册工具执行器\n\n @param executor 执行器\n"}, {"name": "registerTool", "paramTypes": ["org.dromara.app.domain.AiTool"], "doc": " 注册工具配置\n\n @param tool 工具配置\n"}, {"name": "updateTool", "paramTypes": ["org.dromara.app.domain.AiTool"], "doc": " 更新工具配置\n\n @param tool 工具配置\n"}, {"name": "unregisterTool", "paramTypes": ["java.lang.String"], "doc": " 移除工具\n\n @param toolId 工具ID\n"}, {"name": "getExecutor", "paramTypes": ["java.lang.String"], "doc": " 获取工具执行器\n\n @param toolId 工具ID\n @return 执行器\n"}, {"name": "getTool", "paramTypes": ["java.lang.String"], "doc": " 获取工具配置\n\n @param toolId 工具ID\n @return 工具配置\n"}, {"name": "isRegistered", "paramTypes": ["java.lang.String"], "doc": " 检查工具是否已注册\n\n @param toolId 工具ID\n @return 是否已注册\n"}, {"name": "getRegisteredToolIds", "paramTypes": [], "doc": " 获取所有已注册的工具ID\n\n @return 工具ID集合\n"}, {"name": "getAllExecutors", "paramTypes": [], "doc": " 获取所有执行器\n\n @return 执行器映射\n"}], "constructors": []}