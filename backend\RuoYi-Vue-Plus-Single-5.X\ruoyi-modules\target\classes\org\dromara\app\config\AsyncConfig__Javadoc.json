{"doc": " 异步处理配置\n 配置多模态数据分析的异步处理线程池\n\n <AUTHOR>\n @date 2025-01-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "analysisTaskExecutor", "paramTypes": [], "doc": " 分析任务执行器\n 用于执行多模态分析任务\n"}, {"name": "reportGenerationExecutor", "paramTypes": [], "doc": " 报告生成执行器\n 用于异步生成报告\n"}, {"name": "cacheOptimizationExecutor", "paramTypes": [], "doc": " 缓存优化执行器\n 用于异步执行缓存优化任务\n"}, {"name": "dataCleanupExecutor", "paramTypes": [], "doc": " 数据清理执行器\n 用于异步执行数据清理任务\n"}], "constructors": []}