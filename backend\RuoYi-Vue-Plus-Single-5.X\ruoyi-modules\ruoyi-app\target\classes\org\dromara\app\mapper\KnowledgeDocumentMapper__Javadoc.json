{"doc": "\n 知识库文档Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByKnowledgeBaseId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据知识库ID查询文档列表\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @return 文档列表\r\n"}, {"name": "selectByStatus", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据状态查询文档列表\r\n\r\n @param status 状态\r\n @return 文档列表\r\n"}, {"name": "selectByProcessStatus", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据处理状态查询文档列表\r\n\r\n @param processStatus 处理状态\r\n @return 文档列表\r\n"}, {"name": "updateStatus", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.String"], "doc": "\n 更新文档状态\r\n\r\n @param id           文档ID\r\n @param status       状态\r\n @param errorMessage 错误信息\r\n @return 更新结果\r\n"}, {"name": "updateProcessStatus", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 更新处理状态\r\n\r\n @param id            文档ID\r\n @param processStatus 处理状态\r\n @return 更新结果\r\n"}, {"name": "updateVectorCount", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 更新向量数量\r\n\r\n @param id          文档ID\r\n @param vectorCount 向量数量\r\n @return 更新结果\r\n"}, {"name": "countByKnowledgeBaseId", "paramTypes": ["java.lang.Long"], "doc": "\n 统计知识库文档数量\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @return 文档数量\r\n"}, {"name": "selectByDocType", "paramTypes": ["java.lang.String"], "doc": "\n 根据文档类型查询文档\r\n\r\n @param docType 文档类型\r\n @return 文档列表\r\n"}, {"name": "updateStatusByIds", "paramTypes": ["java.util.List", "java.lang.Integer"], "doc": "\n 批量更新文档状态\r\n\r\n @param ids    文档ID列表\r\n @param status 状态\r\n @return 更新结果\r\n"}], "constructors": []}