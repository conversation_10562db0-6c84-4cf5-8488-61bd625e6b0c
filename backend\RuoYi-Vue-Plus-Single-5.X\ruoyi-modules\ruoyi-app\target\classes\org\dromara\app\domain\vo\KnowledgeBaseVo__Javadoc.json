{"doc": "\n 知识库视图对象 app_knowledge_base\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 知识库ID\r\n"}, {"name": "name", "doc": "\n 知识库名称\r\n"}, {"name": "description", "doc": "\n 知识库描述\r\n"}, {"name": "type", "doc": "\n 知识库类型 (general/technical/business/etc.)\r\n"}, {"name": "status", "doc": "\n 知识库状态 (0=禁用 1=启用)\r\n"}, {"name": "vectorDimension", "doc": "\n 向量维度 (默认1024)\r\n"}, {"name": "documentCount", "doc": "\n 文档数量\r\n"}, {"name": "vectorCount", "doc": "\n 向量数量\r\n"}, {"name": "indexConfig", "doc": "\n 索引配置 (JSON格式)\r\n"}, {"name": "extendConfig", "doc": "\n 扩展配置 (JSON格式)\r\n"}, {"name": "lastSyncTime", "doc": "\n 最后更新时间\r\n"}, {"name": "sortOrder", "doc": "\n 排序字段\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}, {"name": "createDept", "doc": "\n 创建部门\r\n"}, {"name": "createBy", "doc": "\n 创建者\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateBy", "doc": "\n 更新者\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}, {"name": "typeName", "doc": "\n 知识库类型名称（用于显示）\r\n"}, {"name": "statusName", "doc": "\n 状态名称（用于显示）\r\n"}, {"name": "createByName", "doc": "\n 创建者名称\r\n"}, {"name": "updateByName", "doc": "\n 更新者名称\r\n"}, {"name": "deptName", "doc": "\n 部门名称\r\n"}, {"name": "avgSimilarity", "doc": "\n 平均向量相似度（统计信息）\r\n"}, {"name": "recentProcessedCount", "doc": "\n 最近处理文档数量\r\n"}, {"name": "indexHealth", "doc": "\n 索引健康状态\r\n"}], "enumConstants": [], "methods": [], "constructors": []}