{"doc": "\n 应用用户对象 app_user\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "phone", "doc": "\n 用户手机号\r\n"}, {"name": "email", "doc": "\n 用户邮箱\r\n"}, {"name": "realName", "doc": "\n 用户姓名\r\n"}, {"name": "gender", "doc": "\n 用户性别（男/女）\r\n"}, {"name": "studentId", "doc": "\n 学生学号\r\n"}, {"name": "major", "doc": "\n 专业\r\n"}, {"name": "grade", "doc": "\n 年级\r\n"}, {"name": "school", "doc": "\n 学校名称\r\n"}, {"name": "introduction", "doc": "\n 个人简介\r\n"}, {"name": "password", "doc": "\n 密码\r\n"}, {"name": "avatar", "doc": "\n 用户头像\r\n"}, {"name": "status", "doc": "\n 帐号状态（0正常 1停用）\r\n"}, {"name": "delFlag", "doc": "\n 删除标志（0代表存在 1代表删除）\r\n"}, {"name": "loginIp", "doc": "\n 最后登录IP\r\n"}, {"name": "loginDate", "doc": "\n 最后登录时间\r\n"}, {"name": "registeredAt", "doc": "\n 注册时间\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}], "enumConstants": [], "methods": [], "constructors": []}