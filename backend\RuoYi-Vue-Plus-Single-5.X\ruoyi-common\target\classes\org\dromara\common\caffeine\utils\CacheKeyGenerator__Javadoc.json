{"doc": " 缓存键生成器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "generate", "paramTypes": ["java.lang.reflect.Method", "java.lang.Object[]"], "doc": " 生成缓存键\n\n @param method 方法\n @param args   参数\n @return 缓存键\n"}, {"name": "getArgString", "paramTypes": ["java.lang.Object"], "doc": " 获取参数字符串表示\n"}, {"name": "generateSimpleKey", "paramTypes": ["java.lang.Object[]"], "doc": " 简单键生成（仅使用参数）\n\n @param args 参数\n @return 缓存键\n"}], "constructors": []}