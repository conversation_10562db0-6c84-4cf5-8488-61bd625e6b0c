package org.dromara.app.controller.user;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.io.FileUtil;
import lombok.RequiredArgsConstructor;
import org.dromara.app.domain.dto.AppUserProfileDto;
import org.dromara.app.domain.vo.AppUserProfileVo;
import org.dromara.app.service.IAppUserProfileService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.utils.file.MimeTypeUtils;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.service.ISysOssService;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;

/**
 * 用户个人信息Controller
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/user/profile")
public class UserProfileController extends BaseController {

    private final IAppUserProfileService appUserProfileService;
    private final ISysOssService ossService;

    /**
     * 获取个人信息
     */
    @GetMapping("/profile")
    public R<AppUserProfileVo> profile() {
        Long userId = StpUtil.getLoginIdAsLong();
        AppUserProfileVo userProfile = appUserProfileService.getUserProfile(userId);
        return R.ok(userProfile);
    }

    /**
     * 修改个人信息
     */
    @RepeatSubmit
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/profile")
    public R<Void> updateProfile(@Validated @RequestBody AppUserProfileDto profileDto) {
        Long userId = StpUtil.getLoginIdAsLong();
        boolean updated = appUserProfileService.updateUserProfile(userId, profileDto);
        if (updated) {
            return R.ok();
        }
        return R.fail("修改个人信息异常，请联系管理员");
    }

    /**
     * 头像上传
     */
    @Log(title = "用户头像", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/avatar", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<SysOssVo> avatar(@RequestPart("avatarfile") MultipartFile file) {
        if (file.isEmpty()) {
            return R.fail("上传文件不能为空");
        }
        // 判断是否登录
        if (!StpUtil.isLogin()) {
            return R.fail("请先登录");
        }

        // 校验文件类型
        String extension = FileUtil.extName(file.getOriginalFilename());
        if (!StringUtils.equalsAnyIgnoreCase(extension, MimeTypeUtils.IMAGE_EXTENSION)) {
            return R.fail("文件格式不正确，请上传" + Arrays.toString(MimeTypeUtils.IMAGE_EXTENSION) + "格式");
        }

        try {
            // 上传文件到OSS
            SysOssVo ossVo = ossService.upload(file);

            // 更新用户头像
            AppUserProfileDto profileDto = new AppUserProfileDto();
            profileDto.setAvatar(ossVo.getUrl());

            Long userId = StpUtil.getLoginIdAsLong();
            appUserProfileService.updateUserProfile(userId, profileDto);

            return R.ok("上传成功", ossVo);
        } catch (Exception e) {
            return R.fail("上传失败：" + e.getMessage());
        }
    }

    /**
     * 重置个人信息
     */
    @RepeatSubmit
    @Log(title = "重置个人信息", businessType = BusinessType.UPDATE)
    @PostMapping("/profile/reset")
    public R<Void> resetProfile() {
        Long userId = StpUtil.getLoginIdAsLong();

        // 创建一个空的DTO对象，只保留必要字段
        AppUserProfileDto profileDto = new AppUserProfileDto();
        profileDto.setName("");
        profileDto.setGender("男");
        profileDto.setSchool("");
        profileDto.setMajor("");
        profileDto.setGrade("大一");
        profileDto.setIntroduction("");
        profileDto.setAvatar("");

        boolean updated = appUserProfileService.updateUserProfile(userId, profileDto);
        if (updated) {
            return R.ok("重置成功");
        }
        return R.fail("重置失败，请联系管理员");
    }
}
