{"doc": "\n 面试统计服务接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getUserStats", "paramTypes": ["java.lang.Long"], "doc": "\n 获取用户面试统计\r\n\r\n @param userId 用户ID\r\n @return 统计信息\r\n"}, {"name": "getJobStats", "paramTypes": ["java.lang.Long"], "doc": "\n 获取岗位面试统计\r\n\r\n @param jobId 岗位ID\r\n @return 统计信息\r\n"}, {"name": "getSystemStats", "paramTypes": [], "doc": "\n 获取系统整体统计\r\n\r\n @return 系统统计\r\n"}, {"name": "getInterviewTrends", "paramTypes": ["java.time.LocalDateTime", "java.time.LocalDateTime", "java.lang.String"], "doc": "\n 获取面试趋势数据\r\n\r\n @param startTime 开始时间\r\n @param endTime   结束时间\r\n @param granularity 粒度（day, week, month）\r\n @return 趋势数据\r\n"}, {"name": "getPopularQuestions", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取热门问题统计\r\n\r\n @param limit 限制数量\r\n @return 热门问题列表\r\n"}, {"name": "getUserRankings", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取用户表现排行\r\n\r\n @param limit 限制数量\r\n @return 排行榜\r\n"}, {"name": "getCompletionRates", "paramTypes": ["java.lang.Long"], "doc": "\n 获取面试完成率统计\r\n\r\n @param jobId 岗位ID（可选）\r\n @return 完成率统计\r\n"}, {"name": "getAverageScores", "paramTypes": ["java.lang.Long"], "doc": "\n 获取平均分数统计\r\n\r\n @param jobId 岗位ID（可选）\r\n @return 平均分数统计\r\n"}, {"name": "recordInterviewEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map"], "doc": "\n 记录面试事件\r\n\r\n @param sessionId 会话ID\r\n @param eventType 事件类型\r\n @param eventData 事件数据\r\n"}, {"name": "cleanExpiredStats", "paramTypes": ["java.time.LocalDateTime"], "doc": "\n 清理过期统计数据\r\n\r\n @param beforeTime 清理时间点之前的数据\r\n @return 清理的记录数\r\n"}], "constructors": []}