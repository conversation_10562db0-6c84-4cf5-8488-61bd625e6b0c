<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.InterviewQuestionMapper">

    <resultMap type="org.dromara.app.domain.InterviewQuestion" id="InterviewQuestionResult">
        <result property="id" column="id"/>
        <result property="jobId" column="job_id"/>
        <result property="categoryId" column="category_id"/>
        <result property="question" column="question"/>
        <result property="questionType" column="question_type"/>
        <result property="difficulty" column="difficulty"/>
        <result property="category" column="category"/>
        <result property="tags" column="tags" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="hint" column="hint"/>
        <result property="answer" column="answer"/>
        <result property="explanation" column="explanation"/>
        <result property="timeLimit" column="time_limit"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="multimodalRequirements" column="multimodal_requirements" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="evaluationCriteria" column="evaluation_criteria" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectInterviewQuestionVo">
        SELECT id, job_id, category_id, question, question_type, difficulty, category, tags, hint, answer, 
               explanation, time_limit, sort_order, multimodal_requirements, evaluation_criteria, status,
               create_by, create_time, update_by, update_time, del_flag
        FROM app_interview_question
    </sql>

    <!-- 根据岗位ID查询问题列表 -->
    <select id="selectByJobId" parameterType="map" resultMap="InterviewQuestionResult">
        <include refid="selectInterviewQuestionVo"/>
        WHERE del_flag = '0' AND status = '0'
        AND (job_id = #{jobId} OR job_id IS NULL)
        <if test="difficulty != null">
            AND difficulty = #{difficulty}
        </if>
        ORDER BY job_id DESC, difficulty ASC, sort_order ASC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据技术领域查询问题 -->
    <select id="selectByTechnicalDomain" parameterType="map" resultMap="InterviewQuestionResult">
        SELECT q.id, q.job_id, q.category_id, q.question, q.question_type, q.difficulty, q.category, q.tags, 
               q.hint, q.answer, q.explanation, q.time_limit, q.sort_order, q.multimodal_requirements, 
               q.evaluation_criteria, q.status, q.create_by, q.create_time, q.update_by, q.update_time, q.del_flag
        FROM app_interview_question q
        LEFT JOIN app_job j ON q.job_id = j.id
        WHERE q.del_flag = '0' AND q.status = '0'
        AND (j.technical_domain = #{technicalDomain} OR q.job_id IS NULL)
        <if test="questionType != null and questionType != ''">
            AND q.question_type = #{questionType}
        </if>
        ORDER BY q.difficulty ASC, q.sort_order ASC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询多模态问题 -->
    <select id="selectMultimodalQuestions" parameterType="map" resultMap="InterviewQuestionResult">
        <include refid="selectInterviewQuestionVo"/>
        WHERE del_flag = '0' AND status = '0'
        AND question_type = 'multimodal'
        <if test="jobId != null">
            AND (job_id = #{jobId} OR job_id IS NULL)
        </if>
        ORDER BY difficulty ASC, sort_order ASC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据标签查询问题 -->
    <select id="selectByTags" parameterType="map" resultMap="InterviewQuestionResult">
        <include refid="selectInterviewQuestionVo"/>
        WHERE del_flag = '0' AND status = '0'
        <if test="tags != null and tags.size() > 0">
            AND (
            <foreach collection="tags" item="tag" separator=" OR ">
                JSON_CONTAINS(tags, JSON_QUOTE(#{tag}))
            </foreach>
            )
        </if>
        ORDER BY difficulty ASC, sort_order ASC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 分页查询问题列表 -->
    <select id="selectQuestionPage" parameterType="map" resultMap="InterviewQuestionResult">
        SELECT q.id, q.job_id, q.category_id, q.question, q.question_type, q.difficulty, q.category, q.tags, 
               q.hint, q.answer, q.explanation, q.time_limit, q.sort_order, q.multimodal_requirements, 
               q.evaluation_criteria, q.status, q.create_by, q.create_time, q.update_by, q.update_time, q.del_flag,
               j.name as job_name
        FROM app_interview_question q
        LEFT JOIN app_job j ON q.job_id = j.id
        WHERE q.del_flag = '0' AND q.status = '0'
        <if test="jobId != null">
            AND q.job_id = #{jobId}
        </if>
        <if test="questionType != null and questionType != ''">
            AND q.question_type = #{questionType}
        </if>
        <if test="difficulty != null">
            AND q.difficulty = #{difficulty}
        </if>
        <if test="category != null and category != ''">
            AND q.category = #{category}
        </if>
        ORDER BY q.sort_order ASC, q.create_time DESC
    </select>

    <!-- 根据问题ID批量查询 -->
    <select id="selectByIds" parameterType="list" resultMap="InterviewQuestionResult">
        <include refid="selectInterviewQuestionVo"/>
        WHERE del_flag = '0' AND status = '0'
        AND id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY sort_order ASC
    </select>

    <!-- 根据分类查询问题数量 -->
    <select id="countByCategory" parameterType="string" resultType="int">
        SELECT COUNT(*)
        FROM app_interview_question
        WHERE del_flag = '0' AND status = '0'
        AND category = #{category}
    </select>

    <!-- 根据岗位ID查询问题数量 -->
    <select id="countByJobId" parameterType="long" resultType="int">
        SELECT COUNT(*)
        FROM app_interview_question
        WHERE del_flag = '0' AND status = '0'
        AND (job_id = #{jobId} OR job_id IS NULL)
    </select>

    <!-- 查询随机问题 -->
    <select id="selectRandomQuestions" parameterType="map" resultMap="InterviewQuestionResult">
        <include refid="selectInterviewQuestionVo"/>
        WHERE del_flag = '0' AND status = '0'
        <if test="jobId != null">
            AND (job_id = #{jobId} OR job_id IS NULL)
        </if>
        <if test="difficulty != null">
            AND difficulty = #{difficulty}
        </if>
        <if test="questionType != null and questionType != ''">
            AND question_type = #{questionType}
        </if>
        ORDER BY RAND()
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询问题统计信息 -->
    <select id="selectQuestionStatistics" resultType="map">
        SELECT 
            COUNT(*) as total_count,
            COUNT(CASE WHEN difficulty = 1 THEN 1 END) as easy_count,
            COUNT(CASE WHEN difficulty = 2 THEN 1 END) as normal_count,
            COUNT(CASE WHEN difficulty = 3 THEN 1 END) as medium_count,
            COUNT(CASE WHEN difficulty = 4 THEN 1 END) as hard_count,
            COUNT(CASE WHEN difficulty = 5 THEN 1 END) as expert_count,
            COUNT(CASE WHEN job_id IS NULL THEN 1 END) as general_count,
            COUNT(CASE WHEN job_id IS NOT NULL THEN 1 END) as specific_count,
            AVG(time_limit) as avg_time_limit
        FROM app_interview_question
        WHERE del_flag = '0' AND status = '0'
    </select>

    <!-- 批量插入问题 -->
    <insert id="batchInsert" parameterType="list">
        INSERT INTO app_interview_question (
            job_id, category_id, question, question_type, difficulty, category, tags, hint, answer, 
            explanation, time_limit, sort_order, multimodal_requirements, evaluation_criteria, 
            status, create_by, create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.jobId}, #{item.categoryId}, #{item.question}, #{item.questionType}, #{item.difficulty},
                #{item.category}, #{item.tags,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
                #{item.hint}, #{item.answer}, #{item.explanation}, #{item.timeLimit}, #{item.sortOrder},
                #{item.multimodalRequirements,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
                #{item.evaluationCriteria,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
                #{item.status}, #{item.createBy}, #{item.createTime}
            )
        </foreach>
    </insert>

    <!-- 批量更新问题状态 -->
    <update id="batchUpdateStatus" parameterType="map">
        UPDATE app_interview_question 
        SET status = #{status}, update_by = #{updateBy}, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
