{"doc": "\n 文件服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "uploadChatFile", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String", "java.lang.Long"], "doc": "\n 上传聊天附件\r\n\r\n @param file   文件\r\n @param type   文件类型：image/file/voice\r\n @param userId 用户ID\r\n @return 上传结果\r\n"}, {"name": "speechToText", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.Long"], "doc": "\n 语音转文字\r\n\r\n @param audioFile 音频文件\r\n @param userId    用户ID\r\n @return 转换结果\r\n"}, {"name": "deleteFile", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 删除文件\r\n\r\n @param fileUrl 文件URL\r\n @param userId  用户ID\r\n @return 是否成功\r\n"}, {"name": "getFileInfo", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 获取文件信息\r\n\r\n @param fileUrl 文件URL\r\n @param userId  用户ID\r\n @return 文件信息\r\n"}, {"name": "batchDeleteFiles", "paramTypes": ["java.util.List", "java.lang.Long"], "doc": "\n 批量删除文件\r\n\r\n @param fileUrls 文件URL列表\r\n @param userId   用户ID\r\n @return 删除结果\r\n"}, {"name": "getFileUsageStats", "paramTypes": ["java.lang.Long"], "doc": "\n 获取用户文件使用统计\r\n\r\n @param userId 用户ID\r\n @return 统计信息\r\n"}], "constructors": []}