{"doc": "\n 工具调用服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getAvailableTools", "paramTypes": ["java.lang.Long"], "doc": "\n 获取可用的工具列表\r\n\r\n @param userId 用户ID\r\n @return 工具列表\r\n"}, {"name": "getToolById", "paramTypes": ["java.lang.String"], "doc": "\n 根据ID获取工具\r\n\r\n @param toolId 工具ID\r\n @return 工具信息\r\n"}, {"name": "getToolByName", "paramTypes": ["java.lang.String"], "doc": "\n 根据名称获取工具\r\n\r\n @param toolName 工具名称\r\n @return 工具信息\r\n"}, {"name": "executeToolCall", "paramTypes": ["java.lang.String", "java.util.Map", "java.lang.Long", "java.lang.String", "java.lang.String"], "doc": "\n 执行工具调用\r\n\r\n @param toolId     工具ID\r\n @param parameters 调用参数\r\n @param userId     用户ID\r\n @param sessionId  会话ID\r\n @param messageId  消息ID\r\n @return 调用结果\r\n"}, {"name": "executeToolCallAsync", "paramTypes": ["java.lang.String", "java.util.Map", "java.lang.Long", "java.lang.String", "java.lang.String"], "doc": "\n 异步执行工具调用\r\n\r\n @param toolId     工具ID\r\n @param parameters 调用参数\r\n @param userId     用户ID\r\n @param sessionId  会话ID\r\n @param messageId  消息ID\r\n @return 调用记录ID\r\n"}, {"name": "getToolCallRecord", "paramTypes": ["java.lang.String"], "doc": "\n 获取工具调用记录\r\n\r\n @param callId 调用记录ID\r\n @return 调用记录\r\n"}, {"name": "getUserToolCallHistory", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 获取用户的工具调用历史\r\n\r\n @param userId   用户ID\r\n @param pageNum  页码\r\n @param pageSize 每页大小\r\n @return 调用历史\r\n"}, {"name": "getSessionToolCalls", "paramTypes": ["java.lang.String"], "doc": "\n 获取会话的工具调用记录\r\n\r\n @param sessionId 会话ID\r\n @return 调用记录列表\r\n"}, {"name": "hasToolPermission", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 验证工具调用权限\r\n\r\n @param toolId 工具ID\r\n @param userId 用户ID\r\n @return 是否有权限\r\n"}, {"name": "validateParameters", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": "\n 验证工具参数\r\n\r\n @param toolId     工具ID\r\n @param parameters 参数\r\n @return 验证结果\r\n"}, {"name": "getToolFunctionDefinitions", "paramTypes": ["java.util.List"], "doc": "\n 获取工具的函数定义（用于AI模型）\r\n\r\n @param toolIds 工具ID列表\r\n @return 函数定义列表\r\n"}, {"name": "processAiToolCalls", "paramTypes": ["java.util.List", "java.lang.Long", "java.lang.String", "java.lang.String"], "doc": "\n 解析AI模型的工具调用请求\r\n\r\n @param toolCalls AI模型返回的工具调用信息\r\n @param userId    用户ID\r\n @param sessionId 会话ID\r\n @param messageId 消息ID\r\n @return 工具调用结果列表\r\n"}], "constructors": []}