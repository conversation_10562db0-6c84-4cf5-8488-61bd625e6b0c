package org.dromara.system.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.mybatis.core.domain.Question;

import java.io.Serial;
import java.io.Serializable;

/**
 * 题目业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Question.class, reverseConvertGenerate = false)
public class QuestionBo extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 题目ID
     */
    @NotNull(message = "题目ID不能为空", groups = { EditGroup.class })
    private Long questionId;

    /**
     * 题库ID
     */
    @NotNull(message = "题库ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long bankId;

    /**
     * 题目编码
     */
    @NotBlank(message = "题目编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionCode;

    /**
     * 题目标题
     */
    @NotBlank(message = "题目标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String title;

    /**
     * 题目描述
     */
    private String description;

    /**
     * 题目内容
     */
    @NotBlank(message = "题目内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String content;

    /**
     * 参考答案
     */
    private String answer;

    /**
     * 答案解析
     */
    private String analysis;

    /**
     * 难度（1-简单 2-中等 3-困难）
     */
    private Integer difficulty;

    /**
     * 分类
     */
    private String category;

    /**
     * 题目类型（1-单选题 2-多选题 3-判断题 4-简答题 5-编程题）
     */
    @NotNull(message = "题目类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer type;

    /**
     * 练习次数
     */
    private Integer practiceCount;

    /**
     * 正确率
     */
    private Integer correctRate;

    /**
     * 通过率（百分比）
     */
    private Double acceptanceRate;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 标签（JSON格式）
     */
    private String tags;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;
}
