{"doc": "\n 活动时长记录服务实现类\r\n\r\n <AUTHOR>\r\n @date 2025-07-16\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "updateStatisticsAfterSession", "paramTypes": ["org.dromara.app.domain.ActivitySession"], "doc": "\n 更新会话结束后的统计数据\r\n\r\n @param session 活动会话\r\n"}, {"name": "ensureUserSummaryExists", "paramTypes": ["java.lang.Long"], "doc": "\n 确保用户活动总览记录存在\r\n\r\n @param userId 用户ID\r\n"}, {"name": "convertToSessionVO", "paramTypes": ["org.dromara.app.domain.ActivitySession"], "doc": "\n 转换ActivitySession为ActivitySessionVO\r\n\r\n @param session 活动会话\r\n @return 会话VO\r\n"}], "constructors": []}