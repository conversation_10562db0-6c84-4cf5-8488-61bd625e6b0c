{"doc": " MongoDB配置属性\n\n <AUTHOR>\n", "fields": [{"name": "uri", "doc": " MongoDB连接字符串\n"}, {"name": "database", "doc": " 数据库名称\n"}, {"name": "username", "doc": " 用户名\n"}, {"name": "password", "doc": " 密码\n"}, {"name": "host", "doc": " 主机地址\n"}, {"name": "port", "doc": " 端口号\n"}, {"name": "authenticationDatabase", "doc": " 认证数据库\n"}, {"name": "pool", "doc": " 连接池配置\n"}], "enumConstants": [], "methods": [], "constructors": []}