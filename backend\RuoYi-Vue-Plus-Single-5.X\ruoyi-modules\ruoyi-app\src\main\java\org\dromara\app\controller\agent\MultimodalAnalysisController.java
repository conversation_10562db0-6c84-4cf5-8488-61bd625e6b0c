package org.dromara.app.controller.agent;

import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.service.IMultimodalAnalysisService;
import org.dromara.common.core.domain.R;
import org.dromara.common.sse.utils.SseMessageUtils;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import jakarta.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 多模态分析控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/multimodal")
public class MultimodalAnalysisController {

    private final IMultimodalAnalysisService multimodalAnalysisService;

    /**
     * 综合多模态分析
     *
     * @param sessionId 面试会话ID
     * @param audioFile 音频文件
     * @param videoFile 视频文件
     * @param textContent 文本内容
     * @param jobPosition 岗位信息
     * @return 分析结果
     */
    @PostMapping("/analyze/comprehensive")
    public R<IMultimodalAnalysisService.MultimodalAnalysisResult> comprehensiveAnalysis(
            @RequestParam @NotBlank(message = "会话ID不能为空") String sessionId,
            @RequestParam(required = false) MultipartFile audioFile,
            @RequestParam(required = false) MultipartFile videoFile,
            @RequestParam(required = false) String textContent,
            @RequestParam(required = false) String jobPosition) {

        try {
            IMultimodalAnalysisService.MultimodalAnalysisResult result =
                multimodalAnalysisService.comprehensiveAnalysis(sessionId, audioFile, videoFile, textContent, jobPosition);

            return R.ok(result);

        } catch (Exception e) {
            log.error("综合多模态分析失败", e);
            return R.fail("分析失败: " + e.getMessage());
        }
    }

    /**
     * 音频分析
     *
     * @param audioFile 音频文件
     * @param sessionId 会话ID
     * @return 音频分析结果
     */
    @PostMapping("/analyze/audio")
    public R<IMultimodalAnalysisService.AudioAnalysisResult> analyzeAudio(
            @RequestParam("file") MultipartFile audioFile,
            @RequestParam @NotBlank(message = "会话ID不能为空") String sessionId) {
        try {
            if (audioFile == null || audioFile.isEmpty()) {
                return R.fail("音频文件不能为空");
            }

            IMultimodalAnalysisService.AudioAnalysisResult result =
                multimodalAnalysisService.analyzeAudio(audioFile, sessionId);

            return R.ok(result);

        } catch (Exception e) {
            log.error("音频分析失败", e);
            return R.fail("音频分析失败: " + e.getMessage());
        }
    }

    /**
     * 视频分析
     *
     * @param videoFile 视频文件
     * @param sessionId 会话ID
     * @return 视频分析结果
     */
    @PostMapping("/analyze/video")
    public R<IMultimodalAnalysisService.VideoAnalysisResult> analyzeVideo(
            @RequestParam("file") MultipartFile videoFile,
            @RequestParam @NotBlank(message = "会话ID不能为空") String sessionId) {

        try {
            if (videoFile == null || videoFile.isEmpty()) {
                return R.fail("视频文件不能为空");
            }

            IMultimodalAnalysisService.VideoAnalysisResult result =
                multimodalAnalysisService.analyzeVideo(videoFile, sessionId);

            return R.ok(result);

        } catch (Exception e) {
            log.error("视频分析失败", e);
            return R.fail("视频分析失败: " + e.getMessage());
        }
    }

    /**
     * 文本分析
     *
     * @param textContent 文本内容
     * @param jobPosition 岗位信息
     * @param sessionId 会话ID
     * @return 文本分析结果
     */
    @PostMapping("/analyze/text")
    public R<IMultimodalAnalysisService.TextAnalysisResult> analyzeText(
            @RequestParam @NotBlank(message = "文本内容不能为空") String textContent,
            @RequestParam(required = false) String jobPosition,
            @RequestParam @NotBlank(message = "会话ID不能为空") String sessionId) {

        try {
            IMultimodalAnalysisService.TextAnalysisResult result =
                multimodalAnalysisService.analyzeText(textContent, jobPosition, sessionId);

            return R.ok(result);

        } catch (Exception e) {
            log.error("文本分析失败", e);
            return R.fail("文本分析失败: " + e.getMessage());
        }
    }

    /**
     * 实时音频分析（流式）
     *
     * @param sessionId 会话ID
     * @param satoken 认证token
     * @return SSE流
     */
    @GetMapping(value = "/analyze/audio/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter analyzeAudioStream(
            @RequestParam @NotBlank(message = "会话ID不能为空") String sessionId,
            @RequestParam @NotBlank(message = "satoken不能为空") String satoken) {

        Long userId = SseMessageUtils.getUserIdIfLogin(satoken);
        SseEmitter emitter = new SseEmitter(300000L);

        CompletableFuture.runAsync(() -> {
            try {
                // 模拟实时音频流分析
                byte[] mockAudioData = new byte[1024]; // 模拟音频数据

                multimodalAnalysisService.analyzeAudioStream(mockAudioData, sessionId,
                    new IMultimodalAnalysisService.AnalysisCallback() {
                        @Override
                        public void onProgress(int progress, String stage) {
                            try {
                                emitter.send(SseEmitter.event()
                                    .name("progress")
                                    .data(Map.of("progress", progress, "stage", stage)));
                            } catch (Exception e) {
                                log.error("发送进度事件失败", e);
                            }
                        }

                        @Override
                        public void onComplete(Object result) {
                            try {
                                emitter.send(SseEmitter.event()
                                    .name("complete")
                                    .data(result));
                                emitter.complete();
                            } catch (Exception e) {
                                log.error("发送完成事件失败", e);
                            }
                        }

                        @Override
                        public void onError(Throwable error) {
                            try {
                                emitter.send(SseEmitter.event()
                                    .name("error")
                                    .data("分析失败: " + error.getMessage()));
                                emitter.completeWithError(error);
                            } catch (Exception e) {
                                log.error("发送错误事件失败", e);
                            }
                        }
                    });

            } catch (Exception e) {
                log.error("实时音频分析失败", e);
                try {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data("实时分析失败: " + e.getMessage()));
                    emitter.completeWithError(e);
                } catch (Exception ex) {
                    log.error("发送错误事件失败", ex);
                }
            }
        });

        return emitter;
    }

    /**
     * 实时视频分析（流式）
     *
     * @param sessionId 会话ID
     * @param satoken 认证token
     * @return SSE流
     */
    @GetMapping(value = "/analyze/video/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter analyzeVideoStream(
            @RequestParam @NotBlank(message = "会话ID不能为空") String sessionId,
            @RequestParam @NotBlank(message = "satoken不能为空") String satoken) {

        Long userId = SseMessageUtils.getUserIdIfLogin(satoken);
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时

        CompletableFuture.runAsync(() -> {
            try {
                log.info("用户{}开始实时视频分析，会话ID: {}", userId, sessionId);

                // 模拟实时视频帧分析
                byte[] mockVideoFrame = new byte[2048]; // 模拟视频帧数据

                multimodalAnalysisService.analyzeVideoFrame(mockVideoFrame, sessionId,
                    new IMultimodalAnalysisService.AnalysisCallback() {
                        @Override
                        public void onProgress(int progress, String stage) {
                            try {
                                emitter.send(SseEmitter.event()
                                    .name("progress")
                                    .data(Map.of("progress", progress, "stage", stage)));
                            } catch (Exception e) {
                                log.error("发送进度事件失败", e);
                            }
                        }

                        @Override
                        public void onComplete(Object result) {
                            try {
                                emitter.send(SseEmitter.event()
                                    .name("complete")
                                    .data(result));
                                emitter.complete();
                            } catch (Exception e) {
                                log.error("发送完成事件失败", e);
                            }
                        }

                        @Override
                        public void onError(Throwable error) {
                            try {
                                emitter.send(SseEmitter.event()
                                    .name("error")
                                    .data("分析失败: " + error.getMessage()));
                                emitter.completeWithError(error);
                            } catch (Exception e) {
                                log.error("发送错误事件失败", e);
                            }
                        }
                    });

            } catch (Exception e) {
                log.error("实时视频分析失败", e);
                try {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data("实时分析失败: " + e.getMessage()));
                    emitter.completeWithError(e);
                } catch (Exception ex) {
                    log.error("发送错误事件失败", ex);
                }
            }
        });

        return emitter;
    }

    /**
     * 获取分析历史
     *
     * @param sessionId 会话ID
     * @return 分析历史记录
     */
    @GetMapping("/history/{sessionId}")
    public R<List<IMultimodalAnalysisService.MultimodalAnalysisResult>> getAnalysisHistory(
            @PathVariable @NotBlank(message = "会话ID不能为空") String sessionId) {

        try {
            Long userId = StpUtil.getLoginIdAsLong();
            log.info("用户{}获取分析历史，会话ID: {}", userId, sessionId);

            List<IMultimodalAnalysisService.MultimodalAnalysisResult> history =
                multimodalAnalysisService.getAnalysisHistory(sessionId);

            return R.ok(history);

        } catch (Exception e) {
            log.error("获取分析历史失败", e);
            return R.fail("获取历史记录失败: " + e.getMessage());
        }
    }

    /**
     * 批量上传文件进行分析
     *
     * @param sessionId 会话ID
     * @param files 文件列表
     * @param jobPosition 岗位信息
     * @return 分析结果
     */
    @PostMapping("/analyze/batch")
    public R<IMultimodalAnalysisService.MultimodalAnalysisResult> batchAnalysis(
            @RequestParam @NotBlank(message = "会话ID不能为空") String sessionId,
            @RequestParam("files") MultipartFile[] files,
            @RequestParam(required = false) String jobPosition) {

        try {
            Long userId = StpUtil.getLoginIdAsLong();
            log.info("用户{}开始批量分析，会话ID: {}, 文件数量: {}", userId, sessionId, files.length);

            if (files.length == 0) {
                return R.fail("请至少上传一个文件");
            }

            MultipartFile audioFile = null;
            MultipartFile videoFile = null;
            StringBuilder textContent = new StringBuilder();

            // 分类处理不同类型的文件
            for (MultipartFile file : files) {
                String contentType = file.getContentType();
                if (contentType != null) {
                    if (contentType.startsWith("audio/")) {
                        audioFile = file;
                    } else if (contentType.startsWith("video/")) {
                        videoFile = file;
                    } else if (contentType.startsWith("text/")) {
                        // 读取文本文件内容
                        try {
                            String content = new String(file.getBytes());
                            textContent.append(content).append("\n");
                        } catch (Exception e) {
                            log.warn("读取文本文件失败: {}", file.getOriginalFilename(), e);
                        }
                    }
                }
            }

            IMultimodalAnalysisService.MultimodalAnalysisResult result =
                multimodalAnalysisService.comprehensiveAnalysis(
                    sessionId, audioFile, videoFile, textContent.toString(), jobPosition);

            return R.ok(result);

        } catch (Exception e) {
            log.error("批量分析失败", e);
            return R.fail("批量分析失败: " + e.getMessage());
        }
    }
}
