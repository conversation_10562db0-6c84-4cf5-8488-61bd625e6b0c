{"doc": " RAG知识库服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryPageList", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeBaseBo"], "doc": " 分页查询知识库列表\n\n @param bo 查询条件\n @return 分页结果\n"}, {"name": "queryList", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeBaseBo"], "doc": " 查询知识库列表\n\n @param bo 查询条件\n @return 知识库列表\n"}, {"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 根据ID查询知识库详情\n\n @param id 知识库ID\n @return 知识库详情\n"}, {"name": "queryDocumentPageList", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeDocumentBo"], "doc": " 分页查询知识库文档列表\n\n @param bo 查询条件\n @return 分页结果\n"}, {"name": "queryDocumentList", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeDocumentBo"], "doc": " 查询知识库文档列表\n\n @param bo 查询条件\n @return 文档列表\n"}, {"name": "queryDocumentById", "paramTypes": ["java.lang.Long"], "doc": " 根据ID查询文档详情\n\n @param id 文档ID\n @return 文档详情\n"}, {"name": "createKnowledgeBase", "paramTypes": ["org.dromara.app.domain.KnowledgeBase"], "doc": " 创建知识库\n\n @param knowledgeBase 知识库信息\n @return 创建结果\n"}, {"name": "addDocument", "paramTypes": ["org.dromara.app.domain.KnowledgeDocument"], "doc": " 添加文档到知识库\n\n @param document 文档信息\n @return 添加结果\n"}, {"name": "processDocument", "paramTypes": ["java.lang.Long"], "doc": " 处理文档（分块、向量化）\n\n @param documentId 文档ID\n @return 处理结果\n"}, {"name": "searchKnowledge", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer"], "doc": " 搜索相关知识\n\n @param knowledgeBaseId 知识库ID\n @param query           查询文本\n @param topK            返回数量\n @return 相关文档列表\n"}, {"name": "hybridSearch", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer"], "doc": " 混合搜索（向量 + 关键词）\n\n @param knowledgeBaseId 知识库ID\n @param query           查询文本\n @param topK            返回数量\n @return 相关文档列表\n"}, {"name": "getKnowledgeBases", "paramTypes": [], "doc": " 获取知识库列表\n\n @return 知识库列表\n"}, {"name": "getKnowledgeBase", "paramTypes": ["java.lang.Long"], "doc": " 获取知识库详情\n\n @param knowledgeBaseId 知识库ID\n @return 知识库信息\n"}, {"name": "getDocuments", "paramTypes": ["java.lang.Long"], "doc": " 获取知识库文档列表\n\n @param knowledgeBaseId 知识库ID\n @return 文档列表\n"}, {"name": "deleteDocument", "paramTypes": ["java.lang.Long"], "doc": " 删除文档\n\n @param documentId 文档ID\n @return 删除结果\n"}, {"name": "deleteKnowledgeBase", "paramTypes": ["java.lang.Long"], "doc": " 删除知识库\n\n @param knowledgeBaseId 知识库ID\n @return 删除结果\n"}, {"name": "updateKnowledgeBase", "paramTypes": ["org.dromara.app.domain.KnowledgeBase"], "doc": " 更新知识库\n\n @param knowledgeBase 知识库信息\n @return 更新结果\n"}, {"name": "rebuildIndex", "paramTypes": ["java.lang.Long"], "doc": " 重建知识库索引\n\n @param knowledgeBaseId 知识库ID\n @return 重建结果\n"}, {"name": "getKnowledgeBaseStats", "paramTypes": ["java.lang.Long"], "doc": " 获取知识库统计信息\n\n @param knowledgeBaseId 知识库ID\n @return 统计信息\n"}], "constructors": []}