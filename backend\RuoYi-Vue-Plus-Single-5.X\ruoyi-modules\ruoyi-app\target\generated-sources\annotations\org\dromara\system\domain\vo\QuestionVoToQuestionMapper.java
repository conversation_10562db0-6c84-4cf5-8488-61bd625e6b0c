package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__162;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.mybatis.core.domain.Question;
import org.dromara.common.mybatis.core.domain.QuestionToQuestionVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__162.class,
    uses = {QuestionToQuestionVoMapper.class},
    imports = {}
)
public interface QuestionVoToQuestionMapper extends BaseMapper<QuestionVo, Question> {
}
