{"doc": "\n 支付订单实体类\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "orderId", "doc": "\n 订单ID\r\n"}, {"name": "orderNo", "doc": "\n 订单号\r\n"}, {"name": "productId", "doc": "\n 商品ID\r\n"}, {"name": "productType", "doc": "\n 商品类型\r\n"}, {"name": "productTitle", "doc": "\n 商品标题\r\n"}, {"name": "amount", "doc": "\n 支付金额\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "paymentMethod", "doc": "\n 支付方式\r\n"}, {"name": "status", "doc": "\n 订单状态：pending-待支付，paid-已支付，cancelled-已取消，expired-已过期\r\n"}, {"name": "alipayTradeNo", "doc": "\n 支付宝交易号\r\n"}, {"name": "payTime", "doc": "\n 支付时间\r\n"}, {"name": "expireTime", "doc": "\n 过期时间\r\n"}, {"name": "clientIp", "doc": "\n 客户端IP\r\n"}, {"name": "userAgent", "doc": "\n 用户代理\r\n"}, {"name": "notifyCount", "doc": "\n 回调通知次数\r\n"}, {"name": "lastNotifyTime", "doc": "\n 最后通知时间\r\n"}, {"name": "notifyR<PERSON>ult", "doc": "\n 通知结果\r\n"}, {"name": "remark", "doc": "\n 备注信息\r\n"}, {"name": "payToken", "doc": "\n 支付token\r\n"}, {"name": "payTokenExpireTime", "doc": "\n 支付token过期时间\r\n"}, {"name": "payTokenUsed", "doc": "\n 支付token是否已使用\r\n"}], "enumConstants": [], "methods": [], "constructors": []}