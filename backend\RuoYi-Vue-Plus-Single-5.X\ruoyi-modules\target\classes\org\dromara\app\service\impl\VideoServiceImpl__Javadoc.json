{"doc": " 视频课程Service业务层处理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "convertToVideoItemVo", "paramTypes": ["org.dromara.app.domain.Video"], "doc": " 转换为视频项VO\n"}, {"name": "convertToVideoDetailVo", "paramTypes": ["org.dromara.app.domain.Video", "java.lang.Long"], "doc": " 转换为视频详情VO\n"}, {"name": "convertToRelatedVideoVo", "paramTypes": ["org.dromara.app.domain.Video"], "doc": " 转换为相关视频VO\n"}, {"name": "convertToVideoCommentVo", "paramTypes": ["org.dromara.app.domain.VideoComment", "java.lang.Long"], "doc": " 转换为视频评论VO\n"}, {"name": "updateDatabaseRecord", "paramTypes": ["java.lang.Long", "java.lang.Long", "org.dromara.app.domain.VideoPlayRecord"], "doc": " 更新数据库记录（从缓存数据同步到数据库）\n"}], "constructors": []}