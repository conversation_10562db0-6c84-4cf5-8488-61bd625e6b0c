{"doc": "\n 用户活动时长记录控制器\r\n 统计用户的学习时长(面试时长,资源学习时长)\r\n\r\n <AUTHOR>\r\n @version 1.0\r\n @date 2025-07-16\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "startActivity", "paramTypes": ["org.dromara.app.domain.dto.ActivityStartRequest"], "doc": "\n 开始活动记录\r\n\r\n @param request 开始活动请求\r\n @return 操作结果\r\n"}, {"name": "pauseActivity", "paramTypes": ["org.dromara.app.domain.dto.ActivityPauseRequest"], "doc": "\n 暂停活动记录\r\n\r\n @param request 暂停活动请求\r\n @return 操作结果\r\n"}, {"name": "endActivity", "paramTypes": ["org.dromara.app.domain.dto.ActivityEndRequest"], "doc": "\n 结束活动记录\r\n\r\n @param request 结束活动请求\r\n @return 操作结果\r\n"}, {"name": "syncSession", "paramTypes": ["org.dromara.app.domain.dto.ActivitySyncRequest"], "doc": "\n 同步活动会话\r\n\r\n @param request 同步请求\r\n @return 操作结果\r\n"}, {"name": "getStatistics", "paramTypes": ["org.dromara.app.domain.enums.ActivityType"], "doc": "\n 获取活动统计数据\r\n\r\n @param type 活动类型(可选)\r\n @return 统计数据\r\n"}, {"name": "getHistory", "paramTypes": ["org.dromara.app.domain.enums.ActivityType", "java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 获取活动历史记录\r\n\r\n @param type      活动类型(可选)\r\n @param startDate 开始时间(可选)\r\n @param endDate   结束时间(可选)\r\n @param page      页码(默认1)\r\n @param pageSize  每页大小(默认10)\r\n @param limit     限制数量(可选)\r\n @return 历史记录\r\n"}, {"name": "clearRecords", "paramTypes": ["org.dromara.app.domain.enums.ActivityType"], "doc": "\n 清除活动记录\r\n\r\n @param type 活动类型(可选，为空时清除所有类型)\r\n @return 操作结果\r\n"}, {"name": "getStatsByType", "paramTypes": ["org.dromara.app.domain.enums.ActivityType"], "doc": "\n 获取指定类型的活动统计\r\n\r\n @param type 活动类型\r\n @return 统计数据\r\n"}, {"name": "initializeUserSummary", "paramTypes": [], "doc": "\n 初始化用户活动总览\r\n\r\n @return 操作结果\r\n"}, {"name": "health", "paramTypes": [], "doc": "\n 健康检查接口\r\n\r\n @return 服务状态\r\n"}], "constructors": []}