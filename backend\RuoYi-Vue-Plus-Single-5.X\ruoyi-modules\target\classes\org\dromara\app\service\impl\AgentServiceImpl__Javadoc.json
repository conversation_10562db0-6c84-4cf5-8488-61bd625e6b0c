{"doc": " AI代理服务实现类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "processAgentData", "paramTypes": ["org.dromara.app.domain.Agent"], "doc": " 处理Agent数据，解析JSON字段\n"}, {"name": "createDefaultAgents", "paramTypes": [], "doc": " 创建默认代理列表\n"}, {"name": "createAgent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.util.List", "java.util.List", "java.lang.Integer"], "doc": " 创建Agent对象\n"}, {"name": "createDefaultModelConfig", "paramTypes": [], "doc": " 创建默认模型配置\n"}, {"name": "createQuickAction", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 创建通用的快速操作\n"}, {"name": "getKnowledgeBaseIdsByType", "paramTypes": ["java.lang.String"], "doc": " 根据类型获取知识库ID列表\n"}], "constructors": []}