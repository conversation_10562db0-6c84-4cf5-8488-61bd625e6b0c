{"doc": " 题目管理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.QuestionBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询题目列表\n"}, {"name": "listByBankId", "paramTypes": ["java.lang.Long", "org.dromara.system.domain.bo.QuestionBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 根据题库ID查询题目列表\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.QuestionBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出题目列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取题目详细信息\n\n @param questionId 题目主键\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.QuestionBo"], "doc": " 新增题目\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.QuestionBo"], "doc": " 修改题目\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除题目\n\n @param questionIds 题目主键串\n"}, {"name": "getByCode", "paramTypes": ["java.lang.String"], "doc": " 根据题目编码查询题目\n\n @param questionCode 题目编码\n"}, {"name": "getByCategory", "paramTypes": ["java.lang.String"], "doc": " 根据分类查询题目列表\n\n @param category 分类\n"}, {"name": "getByDifficulty", "paramTypes": ["java.lang.Integer"], "doc": " 根据难度查询题目列表\n\n @param difficulty 难度\n"}, {"name": "getByType", "paramTypes": ["java.lang.Integer"], "doc": " 根据题目类型查询题目列表\n\n @param type 题目类型\n"}, {"name": "getBookmarkedQuestions", "paramTypes": ["java.lang.Long"], "doc": " 查询用户收藏的题目列表\n\n @param userId 用户ID\n"}, {"name": "getHotQuestions", "paramTypes": ["java.lang.Integer"], "doc": " 查询热门题目列表\n\n @param limit 限制数量\n"}, {"name": "getRandomQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 随机查询题目列表\n\n @param bankId 题库ID\n @param limit  限制数量\n"}, {"name": "updatePracticeCount", "paramTypes": ["java.lang.Long"], "doc": " 更新题目练习次数\n\n @param questionId 题目ID\n"}, {"name": "updateCommentCount", "paramTypes": ["java.lang.Long"], "doc": " 更新题目评论数\n\n @param questionId 题目ID\n"}, {"name": "updateCorrectRate", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 更新题目正确率\n\n @param questionId  题目ID\n @param correctRate 正确率\n"}, {"name": "changeStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 启用/停用题目\n\n @param questionId 题目ID\n @param status     状态\n"}, {"name": "copyQuestion", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 复制题目\n\n @param questionId 源题目ID\n @param title      新题目标题\n"}, {"name": "countByBankId", "paramTypes": ["java.lang.Long"], "doc": " 统计题库下的题目数量\n\n @param bankId 题库ID\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 导入题目数据\n\n @param file 导入文件\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 获取导入模板\n"}], "constructors": []}