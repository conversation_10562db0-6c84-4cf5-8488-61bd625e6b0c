{"doc": " 文档分块对象 app_document_chunk\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 分块ID\n"}, {"name": "documentId", "doc": " 文档ID\n"}, {"name": "knowledgeBaseId", "doc": " 知识库ID\n"}, {"name": "chunkIndex", "doc": " 分块序号\n"}, {"name": "content", "doc": " 分块内容\n"}, {"name": "title", "doc": " 分块标题（可选）\n"}, {"name": "contentLength", "doc": " 内容长度\n"}, {"name": "vector", "doc": " 向量数据（JSON格式存储浮点数组）\n"}, {"name": "vectorDimension", "doc": " 向量维度\n"}, {"name": "embeddingModel", "doc": " 嵌入模型名称\n"}, {"name": "chunkType", "doc": " 分块类型：text/code/table/image_caption\n"}, {"name": "startPosition", "doc": " 开始位置（在原文档中的字符位置）\n"}, {"name": "endPosition", "doc": " 结束位置\n"}, {"name": "metadata", "doc": " 分块元数据（JSON格式）\n"}, {"name": "weight", "doc": " 分块权重\n"}, {"name": "enabled", "doc": " 是否启用：0-禁用，1-启用\n"}, {"name": "contentHash", "doc": " 哈希值（用于去重）\n"}, {"name": "vectorizedAt", "doc": " 向量化时间\n"}, {"name": "metadataObject", "doc": " 分块元数据对象（不存储到数据库）\n"}, {"name": "vectorArray", "doc": " 向量数组（不存储到数据库，用于计算）\n"}, {"name": "similarityScore", "doc": " 相似度分数（搜索时使用）\n"}, {"name": "rerankScore", "doc": " 重排序分数（搜索时使用）\n"}], "enumConstants": [], "methods": [], "constructors": []}