{"doc": "\n 聊天请求DTO\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "message", "doc": "\n 消息内容\r\n"}, {"name": "sessionId", "doc": "\n 会话ID（可选，新会话时为空）\r\n"}, {"name": "agentType", "doc": "\n Agent类型\r\n"}, {"name": "attachments", "doc": "\n 附件列表\r\n"}, {"name": "messageType", "doc": "\n 消息类型：text/image/voice\r\n"}, {"name": "provider", "doc": "\n 模型提供商：ollama/openai/rjb-sias\r\n"}, {"name": "modelName", "doc": "\n 模型名称\r\n"}, {"name": "temperature", "doc": "\n 温度参数\r\n"}, {"name": "enableStreaming", "doc": "\n 是否启用流式响应\r\n"}, {"name": "contextLimit", "doc": "\n 上下文消息数量限制\r\n"}], "enumConstants": [], "methods": [], "constructors": []}