package org.dromara.app.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 视频播放记录视图对象
 *
 * <AUTHOR>
 */
@Data
public class VideoPlayRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 视频ID
     */
    private Long videoId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 上次播放时间
     */
    private Integer lastPlayTime;

    /**
     * 视频总时长
     */
    private Integer duration;

    /**
     * 完成率
     */
    private BigDecimal completionRate;

    /**
     * 是否完成
     */
    private Boolean isCompleted;
}
