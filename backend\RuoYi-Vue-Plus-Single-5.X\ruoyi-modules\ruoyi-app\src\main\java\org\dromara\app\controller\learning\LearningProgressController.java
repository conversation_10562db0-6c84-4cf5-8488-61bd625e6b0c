package org.dromara.app.controller.learning;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.LearningProgress;
import org.dromara.app.service.ILearningProgressService;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 学习进度控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/learning/progress")
@Tag(name = "学习进度管理", description = "学习进度跟踪和管理相关接口")
public class LearningProgressController extends BaseController {

    private final ILearningProgressService learningProgressService;

    @PostMapping("/start")
    @Operation(summary = "开始学习", description = "开始一个新的学习项目或继续已有的学习进度")
    @Log(title = "开始学习", businessType = BusinessType.INSERT)
    public R<LearningProgress> startLearning(
            @Parameter(description = "用户ID", required = true) @RequestParam Long userId,
            @Parameter(description = "学习路径ID") @RequestParam(required = false) String learningPathId,
            @Parameter(description = "资源ID") @RequestParam(required = false) Long resourceId) {

        try {
            LearningProgress progress = learningProgressService.startLearning(userId, learningPathId, resourceId);
            return R.ok("开始学习成功");
        } catch (Exception e) {
            log.error("开始学习失败", e);
            return R.fail("开始学习失败: " + e.getMessage());
        }
    }

    @PutMapping("/update/{progressId}")
    @Operation(summary = "更新学习进度", description = "更新学习进度和学习时长")
    @Log(title = "更新学习进度", businessType = BusinessType.UPDATE)
    public R<Boolean> updateProgress(
            @Parameter(description = "进度ID", required = true) @PathVariable Long progressId,
            @Parameter(description = "完成百分比", required = true) @RequestParam Integer completionPercentage,
            @Parameter(description = "本次学习时长(分钟)", required = true) @RequestParam Integer studyMinutes) {

        try {
            boolean result = learningProgressService.updateProgress(progressId, completionPercentage, studyMinutes);
            return result ? R.ok("学习进度更新成功") : R.fail("学习进度更新失败");
        } catch (Exception e) {
            log.error("更新学习进度失败", e);
            return R.fail("更新学习进度失败: " + e.getMessage());
        }
    }

    @PutMapping("/complete/{progressId}")
    @Operation(summary = "完成学习", description = "标记学习项目为完成状态")
    @Log(title = "完成学习", businessType = BusinessType.UPDATE)
    public R<Boolean> completeLearning(
            @Parameter(description = "进度ID", required = true) @PathVariable Long progressId,
            @Parameter(description = "效果评分(1-5)") @RequestParam(required = false) Double effectivenessRating,
            @Parameter(description = "满意度评分(1-5)") @RequestParam(required = false) Double satisfactionRating,
            @Parameter(description = "学习笔记") @RequestParam(required = false) String notes) {

        try {
            boolean result = learningProgressService.completeLearning(progressId, effectivenessRating, satisfactionRating, notes);
            return result ? R.ok("学习完成记录成功") : R.fail("学习完成记录失败");
        } catch (Exception e) {
            log.error("完成学习失败", e);
            return R.fail("完成学习失败: " + e.getMessage());
        }
    }

    @PutMapping("/pause/{progressId}")
    @Operation(summary = "暂停学习", description = "暂停学习项目")
    @Log(title = "暂停学习", businessType = BusinessType.UPDATE)
    public R<Boolean> pauseLearning(
            @Parameter(description = "进度ID", required = true) @PathVariable Long progressId,
            @Parameter(description = "暂停原因") @RequestParam(required = false) String reason) {

        try {
            boolean result = learningProgressService.pauseLearning(progressId, reason);
            return result ? R.ok("学习暂停成功") : R.fail("学习暂停失败");
        } catch (Exception e) {
            log.error("暂停学习失败", e);
            return R.fail("暂停学习失败: " + e.getMessage());
        }
    }

    @PutMapping("/resume/{progressId}")
    @Operation(summary = "恢复学习", description = "恢复暂停的学习项目")
    @Log(title = "恢复学习", businessType = BusinessType.UPDATE)
    public R<Boolean> resumeLearning(
            @Parameter(description = "进度ID", required = true) @PathVariable Long progressId) {

        try {
            boolean result = learningProgressService.resumeLearning(progressId);
            return result ? R.ok("学习恢复成功") : R.fail("学习恢复失败");
        } catch (Exception e) {
            log.error("恢复学习失败", e);
            return R.fail("恢复学习失败: " + e.getMessage());
        }
    }

    @GetMapping("/user/{userId}")
    @Operation(summary = "获取用户学习进度", description = "获取指定用户的所有学习进度")
    public R<List<LearningProgress>> getUserLearningProgress(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "状态筛选") @RequestParam(required = false) String status) {

        try {
            List<LearningProgress> progressList = learningProgressService.getUserLearningProgress(userId, status);
            return R.ok(progressList);
        } catch (Exception e) {
            log.error("获取用户学习进度失败", e);
            return R.fail("获取用户学习进度失败: " + e.getMessage());
        }
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询学习进度", description = "分页查询用户的学习进度")
    public R<Page<LearningProgress>> getLearningProgressPage(
            @Parameter(description = "用户ID", required = true) @RequestParam Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @Parameter(description = "状态筛选") @RequestParam(required = false) String status) {

        try {
            Page<LearningProgress> page = learningProgressService.getLearningProgressPage(userId, pageNum, pageSize, status);
            return R.ok(page);
        } catch (Exception e) {
            log.error("分页查询学习进度失败", e);
            return R.fail("分页查询学习进度失败: " + e.getMessage());
        }
    }

    @GetMapping("/detail/{progressId}")
    @Operation(summary = "获取学习进度详情", description = "获取指定学习进度的详细信息")
    public R<LearningProgress> getLearningProgressDetail(
            @Parameter(description = "进度ID", required = true) @PathVariable Long progressId,
            @Parameter(description = "用户ID", required = true) @RequestParam Long userId) {

        try {
            LearningProgress progress = learningProgressService.getLearningProgressDetail(progressId, userId);
            return progress != null ? R.ok(progress) : R.fail("学习进度不存在");
        } catch (Exception e) {
            log.error("获取学习进度详情失败", e);
            return R.fail("获取学习进度详情失败: " + e.getMessage());
        }
    }

    @GetMapping("/path/{learningPathId}")
    @Operation(summary = "根据学习路径获取进度", description = "获取指定学习路径的学习进度")
    public R<LearningProgress> getProgressByLearningPath(
            @Parameter(description = "学习路径ID", required = true) @PathVariable String learningPathId,
            @Parameter(description = "用户ID", required = true) @RequestParam Long userId) {

        try {
            LearningProgress progress = learningProgressService.getProgressByLearningPath(learningPathId, userId);
            return progress != null ? R.ok(progress) : R.fail("学习进度不存在");
        } catch (Exception e) {
            log.error("根据学习路径获取进度失败", e);
            return R.fail("根据学习路径获取进度失败: " + e.getMessage());
        }
    }

    @GetMapping("/resource/{resourceId}")
    @Operation(summary = "根据资源获取进度", description = "获取指定资源的学习进度")
    public R<LearningProgress> getProgressByResource(
            @Parameter(description = "资源ID", required = true) @PathVariable Long resourceId,
            @Parameter(description = "用户ID", required = true) @RequestParam Long userId) {

        try {
            LearningProgress progress = learningProgressService.getProgressByResource(resourceId, userId);
            return progress != null ? R.ok(progress) : R.fail("学习进度不存在");
        } catch (Exception e) {
            log.error("根据资源获取进度失败", e);
            return R.fail("根据资源获取进度失败: " + e.getMessage());
        }
    }

    @GetMapping("/statistics/{userId}")
    @Operation(summary = "获取用户学习统计", description = "获取用户的学习统计数据")
    public R<Map<String, Object>> getUserLearningStatistics(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {

        try {
            Map<String, Object> statistics = learningProgressService.getUserLearningStatistics(userId);
            return R.ok(statistics);
        } catch (Exception e) {
            log.error("获取用户学习统计失败", e);
            return R.fail("获取用户学习统计失败: " + e.getMessage());
        }
    }

    @GetMapping("/effectiveness/{userId}")
    @Operation(summary = "获取学习效果评估", description = "获取用户的学习效果评估数据")
    public R<Map<String, Object>> getLearningEffectivenessAssessment(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {

        try {
            Map<String, Object> effectiveness = learningProgressService.getLearningEffectivenessAssessment(userId);
            return R.ok(effectiveness);
        } catch (Exception e) {
            log.error("获取学习效果评估失败", e);
            return R.fail("获取学习效果评估失败: " + e.getMessage());
        }
    }

    @GetMapping("/time-statistics/{userId}")
    @Operation(summary = "获取学习时间统计", description = "获取用户的学习时间统计数据")
    public R<Map<String, Object>> getStudyTimeStatistics(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {

        try {
            Map<String, Object> timeStats = learningProgressService.getStudyTimeStatistics(userId);
            return R.ok(timeStats);
        } catch (Exception e) {
            log.error("获取学习时间统计失败", e);
            return R.fail("获取学习时间统计失败: " + e.getMessage());
        }
    }

    @GetMapping("/trend/{userId}")
    @Operation(summary = "获取学习趋势", description = "获取用户的学习趋势数据")
    public R<List<Map<String, Object>>> getLearningTrend(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "天数") @RequestParam(defaultValue = "30") Integer days) {

        try {
            List<Map<String, Object>> trend = learningProgressService.getLearningTrend(userId, days);
            return R.ok(trend);
        } catch (Exception e) {
            log.error("获取学习趋势失败", e);
            return R.fail("获取学习趋势失败: " + e.getMessage());
        }
    }

    @GetMapping("/recent/{userId}")
    @Operation(summary = "获取最近学习记录", description = "获取用户最近的学习记录")
    public R<List<LearningProgress>> getRecentLearningRecords(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "记录数量") @RequestParam(defaultValue = "10") Integer limit) {

        try {
            List<LearningProgress> records = learningProgressService.getRecentLearningRecords(userId, limit);
            return R.ok(records);
        } catch (Exception e) {
            log.error("获取最近学习记录失败", e);
            return R.fail("获取最近学习记录失败: " + e.getMessage());
        }
    }

    @GetMapping("/ongoing/{userId}")
    @Operation(summary = "获取正在进行的学习", description = "获取用户正在进行的学习项目")
    public R<List<LearningProgress>> getOngoingLearning(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {

        try {
            List<LearningProgress> ongoing = learningProgressService.getOngoingLearning(userId);
            return R.ok(ongoing);
        } catch (Exception e) {
            log.error("获取正在进行的学习失败", e);
            return R.fail("获取正在进行的学习失败: " + e.getMessage());
        }
    }

    @GetMapping("/completed/{userId}")
    @Operation(summary = "获取已完成的学习", description = "获取用户已完成的学习项目")
    public R<List<LearningProgress>> getCompletedLearning(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "记录数量") @RequestParam(defaultValue = "20") Integer limit) {

        try {
            List<LearningProgress> completed = learningProgressService.getCompletedLearning(userId, limit);
            return R.ok(completed);
        } catch (Exception e) {
            log.error("获取已完成的学习失败", e);
            return R.fail("获取已完成的学习失败: " + e.getMessage());
        }
    }

    @GetMapping("/overdue/{userId}")
    @Operation(summary = "获取超期学习项目", description = "获取用户超期的学习项目")
    public R<List<LearningProgress>> getOverdueLearning(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {

        try {
            List<LearningProgress> overdue = learningProgressService.getOverdueLearning(userId);
            return R.ok(overdue);
        } catch (Exception e) {
            log.error("获取超期学习项目失败", e);
            return R.fail("获取超期学习项目失败: " + e.getMessage());
        }
    }

    @PostMapping("/plan/{progressId}")
    @Operation(summary = "创建学习计划", description = "为学习进度创建学习计划")
    @Log(title = "创建学习计划", businessType = BusinessType.INSERT)
    public R<Boolean> createLearningPlan(
            @Parameter(description = "进度ID", required = true) @PathVariable Long progressId,
            @RequestBody LearningProgress.LearningPlan learningPlan) {

        try {
            boolean result = learningProgressService.createLearningPlan(progressId, learningPlan);
            return result ? R.ok("学习计划创建成功") : R.fail("学习计划创建失败");
        } catch (Exception e) {
            log.error("创建学习计划失败", e);
            return R.fail("创建学习计划失败: " + e.getMessage());
        }
    }

    @PutMapping("/plan/{progressId}")
    @Operation(summary = "更新学习计划", description = "更新学习进度的学习计划")
    @Log(title = "更新学习计划", businessType = BusinessType.UPDATE)
    public R<Boolean> updateLearningPlan(
            @Parameter(description = "进度ID", required = true) @PathVariable Long progressId,
            @RequestBody LearningProgress.LearningPlan learningPlan) {

        try {
            boolean result = learningProgressService.updateLearningPlan(progressId, learningPlan);
            return result ? R.ok("学习计划更新成功") : R.fail("学习计划更新失败");
        } catch (Exception e) {
            log.error("更新学习计划失败", e);
            return R.fail("更新学习计划失败: " + e.getMessage());
        }
    }

    @PostMapping("/feedback/{progressId}")
    @Operation(summary = "添加学习反馈", description = "为学习进度添加反馈")
    @Log(title = "添加学习反馈", businessType = BusinessType.INSERT)
    public R<Boolean> addLearningFeedback(
            @Parameter(description = "进度ID", required = true) @PathVariable Long progressId,
            @RequestBody LearningProgress.LearningFeedback feedback) {

        try {
            boolean result = learningProgressService.addLearningFeedback(progressId, feedback);
            return result ? R.ok("学习反馈添加成功") : R.fail("学习反馈添加失败");
        } catch (Exception e) {
            log.error("添加学习反馈失败", e);
            return R.fail("添加学习反馈失败: " + e.getMessage());
        }
    }

    @PutMapping("/milestone/{progressId}")
    @Operation(summary = "更新里程碑状态", description = "更新学习进度的里程碑完成状态")
    @Log(title = "更新里程碑状态", businessType = BusinessType.UPDATE)
    public R<Boolean> updateMilestoneStatus(
            @Parameter(description = "进度ID", required = true) @PathVariable Long progressId,
            @Parameter(description = "里程碑名称", required = true) @RequestParam String milestone,
            @Parameter(description = "是否完成", required = true) @RequestParam Boolean completed) {

        try {
            // 处理Boolean到boolean的转换，避免空指针异常
            boolean completedValue = completed != null ? completed : false;
            boolean result = learningProgressService.updateMilestoneStatus(progressId, milestone, completedValue);
            return result ? R.ok("里程碑状态更新成功") : R.fail("里程碑状态更新失败");
        } catch (Exception e) {
            log.error("更新里程碑状态失败", e);
            return R.fail("更新里程碑状态失败: " + e.getMessage());
        }
    }

    @GetMapping("/efficiency/{progressId}")
    @Operation(summary = "计算学习效率", description = "计算指定学习进度的学习效率")
    public R<Double> calculateLearningEfficiency(
            @Parameter(description = "进度ID", required = true) @PathVariable Long progressId) {

        try {
            Double efficiency = learningProgressService.calculateLearningEfficiency(progressId);
            return R.ok(efficiency);
        } catch (Exception e) {
            log.error("计算学习效率失败", e);
            return R.fail("计算学习效率失败: " + e.getMessage());
        }
    }

    @GetMapping("/report/{userId}")
    @Operation(summary = "生成学习报告", description = "生成用户的学习报告")
    public R<Map<String, Object>> generateLearningReport(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "开始日期") @RequestParam(required = false) String startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) String endDate) {

        try {
            Map<String, Object> report = learningProgressService.generateLearningReport(userId, startDate, endDate);
            return R.ok(report);
        } catch (Exception e) {
            log.error("生成学习报告失败", e);
            return R.fail("生成学习报告失败: " + e.getMessage());
        }
    }

    @GetMapping("/recommendations/{userId}")
    @Operation(summary = "推荐学习调整", description = "基于学习数据推荐学习内容调整")
    public R<List<Map<String, Object>>> recommendLearningAdjustments(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {

        try {
            List<Map<String, Object>> recommendations = learningProgressService.recommendLearningAdjustments(userId);
            return R.ok(recommendations);
        } catch (Exception e) {
            log.error("推荐学习调整失败", e);
            return R.fail("推荐学习调整失败: " + e.getMessage());
        }
    }

    @GetMapping("/reminders/{userId}")
    @Operation(summary = "获取学习提醒", description = "获取用户的学习提醒")
    public R<List<Map<String, Object>>> getLearningReminders(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {

        try {
            List<Map<String, Object>> reminders = learningProgressService.getLearningReminders(userId);
            return R.ok(reminders);
        } catch (Exception e) {
            log.error("获取学习提醒失败", e);
            return R.fail("获取学习提醒失败: " + e.getMessage());
        }
    }

    @PostMapping("/reminder/{progressId}")
    @Operation(summary = "设置学习提醒", description = "为学习进度设置提醒")
    @Log(title = "设置学习提醒", businessType = BusinessType.UPDATE)
    public R<Boolean> setLearningReminder(
            @Parameter(description = "进度ID", required = true) @PathVariable Long progressId,
            @RequestBody LearningProgress.ReminderSettings reminderSettings) {

        try {
            boolean result = learningProgressService.setLearningReminder(progressId, reminderSettings);
            return result ? R.ok("学习提醒设置成功") : R.fail("学习提醒设置失败");
        } catch (Exception e) {
            log.error("设置学习提醒失败", e);
            return R.fail("设置学习提醒失败: " + e.getMessage());
        }
    }

    @GetMapping("/personalized-recommendations/{userId}")
    @Operation(summary = "获取个性化推荐", description = "获取基于用户学习历史的个性化推荐")
    public R<List<Map<String, Object>>> getPersonalizedRecommendations(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {

        try {
            List<Map<String, Object>> recommendations = learningProgressService.getPersonalizedRecommendations(userId);
            return R.ok(recommendations);
        } catch (Exception e) {
            log.error("获取个性化推荐失败", e);
            return R.fail("获取个性化推荐失败: " + e.getMessage());
        }
    }

    @PutMapping("/batch-update")
    @Operation(summary = "批量更新学习进度", description = "批量更新多个学习进度")
    @Log(title = "批量更新学习进度", businessType = BusinessType.UPDATE)
    public R<Boolean> batchUpdateProgress(
            @Parameter(description = "进度ID列表", required = true) @RequestParam List<Long> progressIds,
            @RequestBody Map<String, Object> updateData) {

        try {
            boolean result = learningProgressService.batchUpdateProgress(progressIds, updateData);
            return result ? R.ok("批量更新成功") : R.fail("批量更新失败");
        } catch (Exception e) {
            log.error("批量更新学习进度失败", e);
            return R.fail("批量更新学习进度失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{progressId}")
    @Operation(summary = "删除学习进度", description = "删除指定的学习进度记录")
    @Log(title = "删除学习进度", businessType = BusinessType.DELETE)
    public R<Boolean> deleteProgress(
            @Parameter(description = "进度ID", required = true) @PathVariable Long progressId,
            @Parameter(description = "用户ID", required = true) @RequestParam Long userId) {

        try {
            boolean result = learningProgressService.deleteLearningProgress(progressId, userId);
            return result ? R.ok("学习进度删除成功") : R.fail("学习进度删除失败");
        } catch (Exception e) {
            log.error("删除学习进度失败", e);
            return R.fail("删除学习进度失败: " + e.getMessage());
        }
    }

    @GetMapping("/export/{userId}")
    @Operation(summary = "导出学习数据", description = "导出用户的学习数据")
    public R<String> exportLearningData(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "导出格式") @RequestParam(defaultValue = "json") String format) {

        try {
            String filePath = learningProgressService.exportLearningData(userId, format);
            return R.ok(filePath);
        } catch (Exception e) {
            log.error("导出学习数据失败", e);
            return R.fail("导出学习数据失败: " + e.getMessage());
        }
    }

    @PostMapping("/import/{userId}")
    @Operation(summary = "导入学习数据", description = "导入用户的学习数据")
    @Log(title = "导入学习数据", businessType = BusinessType.IMPORT)
    public R<Boolean> importLearningData(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @RequestBody Map<String, Object> importData) {

        try {
            boolean result = learningProgressService.importLearningData(userId, importData);
            return result ? R.ok("学习数据导入成功") : R.fail("学习数据导入失败");
        } catch (Exception e) {
            log.error("导入学习数据失败", e);
            return R.fail("导入学习数据失败: " + e.getMessage());
        }
    }

    @GetMapping("/comparison/{userId}")
    @Operation(summary = "获取学习进度对比", description = "获取用户与其他用户的学习进度对比")
    public R<List<Map<String, Object>>> getProgressComparison(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "对比用户ID列表", required = true) @RequestParam List<Long> compareUserIds) {

        try {
            List<Map<String, Object>> comparison = learningProgressService.getProgressComparison(userId, compareUserIds);
            return R.ok(comparison);
        } catch (Exception e) {
            log.error("获取学习进度对比失败", e);
            return R.fail("获取学习进度对比失败: " + e.getMessage());
        }
    }

    @GetMapping("/insights/{userId}")
    @Operation(summary = "获取学习洞察", description = "获取用户的学习洞察和分析")
    public R<Map<String, Object>> getLearningInsights(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {

        try {
            Map<String, Object> insights = learningProgressService.getLearningInsights(userId);
            return R.ok(insights);
        } catch (Exception e) {
            log.error("获取学习洞察失败", e);
            return R.fail("获取学习洞察失败: " + e.getMessage());
        }
    }
}
