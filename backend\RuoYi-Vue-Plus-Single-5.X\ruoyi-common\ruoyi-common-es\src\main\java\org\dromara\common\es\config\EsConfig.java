package org.dromara.common.es.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.ssl.SSLContexts;
import org.dromara.common.es.core.EsClient;
import org.dromara.common.es.properties.EsProperties;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;

/**
 * ES配置
 *
 * <AUTHOR>
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(EsProperties.class)
@ConditionalOnProperty(prefix = "elasticsearch", name = "hosts")
public class EsConfig {

    /**
     * ES客户端
     */
    @Bean
    @Primary
    public ElasticsearchClient elasticsearchClient(EsProperties esProperties) {
        // 解析主机地址
        List<HttpHost> hosts = parseHosts(esProperties.getHosts());

        // 创建RestClient构建器
        RestClientBuilder builder = RestClient.builder(hosts.toArray(new HttpHost[0]));

        // 设置连接超时等参数
        builder.setRequestConfigCallback(requestConfigBuilder -> {
            requestConfigBuilder.setConnectTimeout(esProperties.getConnectTimeout());
            requestConfigBuilder.setSocketTimeout(esProperties.getSocketTimeout());
            requestConfigBuilder.setConnectionRequestTimeout(esProperties.getConnectionRequestTimeout());
            return requestConfigBuilder;
        });

        // 设置连接池参数
        builder.setHttpClientConfigCallback(httpClientBuilder -> {
            httpClientBuilder.setMaxConnTotal(esProperties.getMaxConnections());
            httpClientBuilder.setMaxConnPerRoute(esProperties.getMaxConnectionsPerRoute());

            // 设置认证信息
            if (StrUtil.isNotBlank(esProperties.getUsername()) && StrUtil.isNotBlank(esProperties.getPassword())) {
                CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                credentialsProvider.setCredentials(AuthScope.ANY,
                    new UsernamePasswordCredentials(esProperties.getUsername(), esProperties.getPassword()));
                httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
            }

            // 设置SSL
            if (Boolean.TRUE.equals(esProperties.getSslEnabled())) {
                try {
                    SSLContext sslContext = SSLContexts.custom()
                        .loadTrustMaterial(null, (chain, authType) -> true)
                        .build();
                    httpClientBuilder.setSSLContext(sslContext);
                } catch (KeyManagementException | NoSuchAlgorithmException | KeyStoreException e) {
                    log.error("SSL配置失败: {}", e.getMessage(), e);
                }
            }

            return httpClientBuilder;
        });

        // 创建RestClient
        RestClient restClient = builder.build();

        // 创建ElasticsearchTransport
        ElasticsearchTransport transport = new RestClientTransport(
            restClient, new JacksonJsonpMapper());

        // 创建ElasticsearchClient
        ElasticsearchClient client = new ElasticsearchClient(transport);

        log.info("初始化ES客户端成功，连接地址: {}", esProperties.getHosts());
        return client;
    }

    /**
     * ES客户端封装
     */
    @Bean
    public EsClient esClient() {
        return new EsClient();
    }

    /**
     * 解析主机地址
     */
    private List<HttpHost> parseHosts(List<String> hostList) {
        List<HttpHost> hosts = new ArrayList<>();

        if (CollUtil.isEmpty(hostList)) {
            throw new IllegalArgumentException("ES主机地址不能为空");
        }

        for (String host : hostList) {
            try {
                String[] parts = host.split(":");
                String hostname = parts[0];
                int port = parts.length > 1 ? Integer.parseInt(parts[1]) : 9200;
                String scheme = parts.length > 2 ? parts[2] : "http";

                hosts.add(new HttpHost(hostname, port, scheme));
            } catch (Exception e) {
                log.error("解析ES主机地址失败: {}", host, e);
                throw new IllegalArgumentException("ES主机地址格式错误: " + host);
            }
        }

        return hosts;
    }

}
