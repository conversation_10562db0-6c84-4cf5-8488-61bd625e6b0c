package org.dromara.common.pay.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PaymentStatus {

    /**
     * 未支付
     */
    UNPAID("unpaid", "未支付"),

    /**
     * 待支付
     */
    PENDING("pending", "待支付"),

    /**
     * 支付中
     */
    PAYING("paying", "支付中"),

    /**
     * 支付成功
     */
    PAID("paid", "支付成功"),

    /**
     * 支付失败
     */
    FAILED("failed", "支付失败"),

    /**
     * 已取消
     */
    CANCELLED("cancelled", "已取消"),

    /**
     * 已过期
     */
    EXPIRED("expired", "已过期"),

    /**
     * 已退款
     */
    REFUNDED("refunded", "已退款"),

    /**
     * 部分退款
     */
    PARTIAL_REFUNDED("partial_refunded", "部分退款");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return PaymentStatus
     */
    public static PaymentStatus fromCode(String code) {
        for (PaymentStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的支付状态: " + code);
    }

    /**
     * 判断是否为终态
     *
     * @return true-终态 false-非终态
     */
    public boolean isFinalStatus() {
        return this == PAID || this == FAILED || this == CANCELLED || this == EXPIRED;
    }

    /**
     * 判断是否为成功状态
     *
     * @return true-成功 false-非成功
     */
    public boolean isSuccess() {
        return this == PAID;
    }
}
