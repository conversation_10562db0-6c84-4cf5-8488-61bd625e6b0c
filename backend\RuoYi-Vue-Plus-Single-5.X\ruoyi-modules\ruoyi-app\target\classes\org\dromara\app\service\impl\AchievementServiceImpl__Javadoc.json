{"doc": "\n 成就系统服务实现类\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "checkSingleAchievement", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.AchievementVo", "org.dromara.app.domain.dto.TrackEventDto"], "doc": "\n 检查单个成就\r\n"}, {"name": "checkAchievementCondition", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.AchievementVo", "java.util.Map", "org.dromara.app.domain.dto.TrackEventDto"], "doc": "\n 检查成就条件是否满足\r\n"}, {"name": "checkLoginAchievement", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": "\n 检查登录类成就\r\n"}, {"name": "checkVideoWatchAchievement", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": "\n 检查视频观看类成就\r\n"}, {"name": "checkCommentAchievement", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": "\n 检查评论类成就\r\n"}, {"name": "checkLikeAchievement", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": "\n 检查点赞类成就\r\n"}, {"name": "checkStudyTimeAchievement", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": "\n 检查学习时长类成就\r\n"}, {"name": "updateAchievementProgressInternal", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.AchievementVo", "java.util.Map", "org.dromara.app.domain.dto.TrackEventDto"], "doc": "\n 更新成就进度\r\n"}, {"name": "getCurrentValue", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": "\n 获取当前数值\r\n"}, {"name": "getTargetValue", "paramTypes": ["java.util.Map"], "doc": "\n 获取目标数值\r\n"}, {"name": "completeAchievement", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 完成成就\r\n"}, {"name": "createUserAchievement", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.AchievementVo"], "doc": "\n 创建用户成就记录\r\n"}], "constructors": []}