{"doc": " 发号器工具类\n\n <AUTHOR>\n @date 2024-12-10\n", "fields": [{"name": "DEFAULT_INIT_VALUE", "doc": " 默认初始值\n"}, {"name": "DEFAULT_STEP_VALUE", "doc": " 默认步长\n"}, {"name": "DEFAULT_EXPIRE_TIME_DAY", "doc": " 默认过期时间-天\n"}, {"name": "DEFAULT_EXPIRE_TIME_MINUTE", "doc": " 默认过期时间-分钟\n"}, {"name": "REDISSON_CLIENT", "doc": " 获取Redisson客户端实例\n"}], "enumConstants": [], "methods": [{"name": "getIdGenerator", "paramTypes": ["java.lang.String", "java.time.Duration", "java.lang.Long", "java.lang.Long"], "doc": " 获取ID生成器\n\n @param key        业务key\n @param expireTime 过期时间\n @param initValue  ID初始值\n @param stepValue  ID步长\n @return ID生成器\n"}, {"name": "nextId", "paramTypes": ["java.lang.String", "java.time.Duration", "java.lang.Long", "java.lang.Long"], "doc": " 获取指定业务key的唯一id\n\n @param key        业务key\n @param expireTime 过期时间\n @param initValue  ID初始值\n @param stepValue  ID步长\n @return 唯一id\n"}, {"name": "nextIdStr", "paramTypes": ["java.lang.String", "java.time.Duration", "java.lang.Long", "java.lang.Long"], "doc": " 获取指定业务key的唯一id字符串\n\n @param key        业务key\n @param expireTime 过期时间\n @param initValue  ID初始值\n @param stepValue  ID步长\n @return 唯一id\n"}, {"name": "nextId", "paramTypes": ["java.lang.String", "java.time.Duration"], "doc": " 获取指定业务key的唯一id (ID初始值=1,ID步长=1)\n\n @param key        业务key\n @param expireTime 过期时间\n @return 唯一id\n"}, {"name": "nextIdStr", "paramTypes": ["java.lang.String", "java.time.Duration"], "doc": " 获取指定业务key的唯一id字符串 (ID初始值=1,ID步长=1)\n\n @param key        业务key\n @param expireTime 过期时间\n @return 唯一id\n"}, {"name": "nextPaddedIdStr", "paramTypes": ["java.lang.String", "java.time.Duration", "java.lang.Integer"], "doc": " 获取指定业务key的唯一id字符串 (ID初始值=1,ID步长=1)，不足位数自动补零\n\n @param key        业务key\n @param expireTime 过期时间\n @param width      位数，不足左补0\n @return 补零后的唯一id字符串\n"}, {"name": "nextIdDate", "paramTypes": [], "doc": " 获取 yyyyMMdd 开头的唯一id\n\n @return 唯一id\n"}, {"name": "nextIdDate", "paramTypes": ["java.lang.String"], "doc": " 获取 prefix + yyyyMMdd 开头的唯一id\n\n @param prefix 业务前缀\n @return 唯一id\n"}, {"name": "nextIdDateTime", "paramTypes": [], "doc": " 获取 yyyyMMddHHmmss 开头的唯一id\n\n @return 唯一id\n"}, {"name": "nextIdDateTime", "paramTypes": ["java.lang.String"], "doc": " 获取 prefix + yyyyMMddHHmmss 开头的唯一id\n\n @param prefix 业务前缀\n @return 唯一id\n"}], "constructors": []}