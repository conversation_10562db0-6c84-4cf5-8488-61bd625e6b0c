{"doc": "\n 文件服务实现类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "validateFile", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String"], "doc": "\n 验证文件\r\n"}, {"name": "generateFileName", "paramTypes": ["java.lang.String"], "doc": "\n 生成文件名\r\n"}, {"name": "generateRelativePath", "paramTypes": ["java.lang.String"], "doc": "\n 生成相对路径\r\n"}, {"name": "formatFileSize", "paramTypes": ["long"], "doc": "\n 格式化文件大小\r\n"}], "constructors": []}