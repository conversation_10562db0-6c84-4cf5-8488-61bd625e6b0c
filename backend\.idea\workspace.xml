<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e231f2c9-3c03-4727-9b0e-a28dafc3c073" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/java/org/dromara/web/service/SysLoginService.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/java/org/dromara/web/service/SysLoginService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/java/org/dromara/web/service/SysRegisterService.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/java/org/dromara/web/service/SysRegisterService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/java/org/dromara/web/service/impl/EmailAuthStrategy.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/java/org/dromara/web/service/impl/EmailAuthStrategy.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/java/org/dromara/web/service/impl/PasswordAuthStrategy.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/java/org/dromara/web/service/impl/PasswordAuthStrategy.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/java/org/dromara/web/service/impl/SmsAuthStrategy.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/java/org/dromara/web/service/impl/SmsAuthStrategy.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/java/org/dromara/web/service/impl/SocialAuthStrategy.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/java/org/dromara/web/service/impl/SocialAuthStrategy.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/config/AsyncConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/config/AsyncConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/controller/learning/LearningController.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/controller/learning/LearningController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/controller/learning/QuestionCommentController.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/controller/learning/QuestionCommentController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/DimensionScore.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/DimensionScore.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/InterviewQuestion.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/InterviewQuestion.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/InterviewReport.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/InterviewReport.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/LearningProgress.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/LearningProgress.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/Question.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/QuestionBank.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/QuestionComment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/QuestionTag.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/QuestionTag.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/dto/AnalysisTaskDto.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/dto/AnalysisTaskDto.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/dto/InterviewReportData.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/dto/InterviewReportData.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/AccessControlStatusVo.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/AccessControlStatusVo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/CacheStatisticsVo.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/CacheStatisticsVo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/DatabasePerformanceVo.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/DatabasePerformanceVo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/EncryptionStatusVo.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/EncryptionStatusVo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/JobVo.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/JobVo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/PermissionCheckResultVo.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/PermissionCheckResultVo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/QuestionBankVo.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/QuestionVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/QuestionVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/SensitiveInfoDetectionVo.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/SensitiveInfoDetectionVo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/SlowQueryVo.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/SlowQueryVo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/TaskQueueStatusVo.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/TaskQueueStatusVo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/InterviewReportMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/InterviewReportMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/LearningProgressMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/LearningProgressMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/LearningResourceMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/LearningResourceMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionBankMapper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionCommentMapper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionMapper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionTagMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionTagMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/ICacheOptimizationService.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/ICacheOptimizationService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/IChartGenerationService.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/IChartGenerationService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/IDataEncryptionService.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/IDataEncryptionService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/IDatabaseOptimizationService.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/IDatabaseOptimizationService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/IInterviewQuestionService.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/IInterviewQuestionService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/ILearningRecommendationService.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/ILearningRecommendationService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/ILearningResourceService.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/ILearningResourceService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/ILearningService.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/ILearningService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/IMultimodalAnalysisService.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/IMultimodalAnalysisService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/IPdfReportService.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/IPdfReportService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/IQuestionManagementService.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/IQuestionManagementService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/IQuestionTagService.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/IQuestionTagService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/ITaskQueueService.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/ITaskQueueService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/AiEvaluationServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/AiEvaluationServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/ChartGenerationServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/ChartGenerationServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/InterviewQuestionServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/InterviewQuestionServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/LearningRecommendServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/LearningRecommendServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/LearningRecommendationServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/LearningRecommendationServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/LearningServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/LearningServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/QuestionManagementServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/QuestionManagementServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/QuestionTagServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/QuestionTagServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/resources/mapper/app/QuestionCommentMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/resources/mapper/app/QuestionCommentMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/resources/mapper/app/QuestionMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/resources/mapper/app/QuestionMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/.eslintrc-auto-import.json" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/.eslintrc-auto-import.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/pnpm-lock.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/pnpm-lock.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/manifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/manifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/pages.json" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/pages.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/pages/learning/question-bank-detail.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/pages/learning/question-bank-detail.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/pages/learning/question-detail.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/pages/learning/question-detail.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/service/dataManager.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/service/dataManager.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/service/interview-select.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/service/interview-select.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/service/question.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/service/question.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/types/auto-import.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/types/auto-import.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/types/onboarding.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/types/onboarding.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/types/uni-pages.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/types/uni-pages.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/tsconfig.json" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/tsconfig.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/vite.config.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/vite.config.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../target/classes/META-INF/mps/autoMapper" beforeDir="false" afterPath="$PROJECT_DIR$/../target/classes/META-INF/mps/autoMapper" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../target/classes/META-INF/mps/mappers" beforeDir="false" afterPath="$PROJECT_DIR$/../target/classes/META-INF/mps/mappers" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30g6d7k07sR3ffn9XX2yhtgcZDM" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.ruoyi-vue-plus [compile].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.DromaraApplication.executor": "Run",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/softwart-xunfei-code2/backend",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="Spring Boot.DromaraApplication">
    <configuration name="DromaraApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-admin" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.DromaraApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="jdk-17.0.10-corretto-17.0.10-f644763e9732-24fca987" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="e231f2c9-3c03-4727-9b0e-a28dafc3c073" name="更改" comment="" />
      <created>1754036953775</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754036953775</updated>
      <workItem from="1754036954851" duration="476000" />
      <workItem from="1754052301428" duration="5233000" />
      <workItem from="1754098148757" duration="5889000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>