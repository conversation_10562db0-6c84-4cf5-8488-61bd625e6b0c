{"doc": "\n 面试书籍Service业务层处理\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryBookPage", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String", "java.lang.String", "java.lang.Long"], "doc": "\n 分页查询书籍列表\r\n"}, {"name": "queryBookDetail", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 根据ID查询书籍详情\r\n"}, {"name": "queryHotBooks", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询热门书籍列表\r\n"}, {"name": "queryRecommended<PERSON>ooks", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询推荐书籍列表\r\n"}, {"name": "queryCategoryStats", "paramTypes": [], "doc": "\n 查询分类统计信息\r\n"}, {"name": "incrementReadCount", "paramTypes": ["java.lang.Long"], "doc": "\n 增加书籍阅读次数\r\n"}, {"name": "insertBook", "paramTypes": ["org.dromara.app.domain.Book"], "doc": "\n 新增书籍\r\n"}, {"name": "updateBook", "paramTypes": ["org.dromara.app.domain.Book"], "doc": "\n 修改书籍\r\n"}, {"name": "deleteBooks", "paramTypes": ["java.util.List"], "doc": "\n 删除书籍\r\n"}, {"name": "updateBookStatus", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": "\n 上架/下架书籍\r\n"}, {"name": "processBookData", "paramTypes": ["org.dromara.app.domain.Book"], "doc": "\n 处理书籍数据（转换标签等）\r\n"}], "constructors": []}