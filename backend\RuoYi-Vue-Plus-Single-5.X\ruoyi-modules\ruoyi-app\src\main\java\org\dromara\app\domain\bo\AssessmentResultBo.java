package org.dromara.app.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 评估结果业务对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(description = "评估结果业务对象")
public class AssessmentResultBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 问题ID
     */
    @Schema(description = "问题ID")
    private String questionId;

    /**
     * 问题类型
     */
    @Schema(description = "问题类型")
    private String questionType;

    /**
     * 问题类别
     */
    @Schema(description = "问题类别")
    private String category;

    /**
     * 选择的选项ID（单选题）
     */
    @Schema(description = "选择的选项ID（单选题）")
    private String selectedOptionId;

    /**
     * 选择的分值（量表题）
     */
    @Schema(description = "选择的分值（量表题）")
    private Integer selectedValue;
}
