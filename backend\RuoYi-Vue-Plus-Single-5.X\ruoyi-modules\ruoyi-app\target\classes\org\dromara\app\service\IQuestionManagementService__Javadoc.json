{"doc": "\n 问题管理增强Service接口\r\n\r\n <AUTHOR> Assistant\r\n @date 2025-07-19\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "recommendQuestions", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.lang.Bo<PERSON>an"], "doc": "\n 智能推荐面试问题\r\n\r\n @param jobId           岗位ID\r\n @param technicalDomain 技术领域\r\n @param difficulty      目标难度\r\n @param questionCount   问题数量\r\n @param includeMultimodal 是否包含多模态问题\r\n @return 推荐问题列表\r\n"}, {"name": "recommendQuestionsForUser", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Integer"], "doc": "\n 根据用户技能水平推荐问题\r\n\r\n @param userId        用户ID\r\n @param jobId         岗位ID\r\n @param questionCount 问题数量\r\n @return 个性化推荐问题\r\n"}, {"name": "getDifficultyGradingSystem", "paramTypes": ["java.lang.String"], "doc": "\n 获取问题难度分级系统\r\n\r\n @param technicalDomain 技术领域\r\n @return 难度分级信息\r\n"}, {"name": "analyzeQuestionCoverage", "paramTypes": ["java.lang.Long"], "doc": "\n 分析问题覆盖度\r\n\r\n @param jobId 岗位ID\r\n @return 覆盖度分析结果\r\n"}, {"name": "getMultimodalConfig", "paramTypes": ["java.lang.Long"], "doc": "\n 获取多模态评估配置\r\n\r\n @param questionId 问题ID\r\n @return 多模态评估配置\r\n"}, {"name": "batchUpdateQuestionTags", "paramTypes": ["java.util.List", "java.util.List"], "doc": "\n 批量更新问题标签\r\n\r\n @param questionIds 问题ID列表\r\n @param tags        标签列表\r\n @return 更新结果\r\n"}], "constructors": []}