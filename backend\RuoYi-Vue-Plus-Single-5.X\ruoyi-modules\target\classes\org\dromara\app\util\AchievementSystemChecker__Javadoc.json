{"doc": " 成就系统完整性检查工具\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "checkSystemIntegrity", "paramTypes": [], "doc": " 检查成就系统完整性\n\n @return 检查结果\n"}, {"name": "checkDatabaseTables", "paramTypes": [], "doc": " 检查数据库表是否存在\n\n @return 检查结果\n"}, {"name": "checkRabbitMQConfig", "paramTypes": [], "doc": " 检查RabbitMQ配置\n\n @return 检查结果\n"}, {"name": "generateHealthReport", "paramTypes": [], "doc": " 生成系统健康报告\n\n @return 健康报告\n"}, {"name": "printSystemStatus", "paramTypes": [], "doc": " 打印系统状态报告\n"}], "constructors": []}