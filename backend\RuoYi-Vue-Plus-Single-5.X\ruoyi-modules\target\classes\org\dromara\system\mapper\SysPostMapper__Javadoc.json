{"doc": " 岗位信息 数据层\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectPagePostList", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 分页查询岗位列表\n\n @param page         分页对象\n @param queryWrapper 查询条件\n @return 包含岗位信息的分页结果\n"}, {"name": "selectPostsByUserId", "paramTypes": ["java.lang.Long"], "doc": " 查询用户所属岗位组\n\n @param userId 用户ID\n @return 结果\n"}], "constructors": []}