package org.dromara.system.controller.question;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.bo.QuestionBankBo;
import org.dromara.system.domain.vo.QuestionBankVo;
import org.dromara.system.service.IQuestionBankService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 题库管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/question/bank")
public class QuestionBankController extends BaseController {

    private final IQuestionBankService questionBankService;

    /**
     * 查询题库列表
     */
    @SaCheckPermission("system:questionBank:list")
    @GetMapping("/list")
    public TableDataInfo<QuestionBankVo> list(QuestionBankBo bo, PageQuery pageQuery) {
        return questionBankService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出题库列表
     */
    @SaCheckPermission("system:questionBank:export")
    @Log(title = "题库管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QuestionBankBo bo, HttpServletResponse response) {
        List<QuestionBankVo> list = questionBankService.exportQuestionBank(bo);
        ExcelUtil.exportExcel(list, "题库数据", QuestionBankVo.class, response);
    }

    /**
     * 获取题库详细信息
     *
     * @param bankId 题库主键
     */
    @SaCheckPermission("system:questionBank:query")
    @GetMapping("/{bankId}")
    public R<QuestionBankVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long bankId) {
        return R.ok(questionBankService.queryById(bankId));
    }

    /**
     * 新增题库
     */
    @SaCheckPermission("system:questionBank:add")
    @Log(title = "题库管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QuestionBankBo bo) {
        if (!questionBankService.checkBankCodeUnique(bo)) {
            return R.fail("新增题库'" + bo.getTitle() + "'失败，题库编码已存在");
        }
        return toAjax(questionBankService.insertByBo(bo));
    }

    /**
     * 修改题库
     */
    @SaCheckPermission("system:questionBank:edit")
    @Log(title = "题库管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QuestionBankBo bo) {
        if (!questionBankService.checkBankCodeUnique(bo)) {
            return R.fail("修改题库'" + bo.getTitle() + "'失败，题库编码已存在");
        }
        return toAjax(questionBankService.updateByBo(bo));
    }

    /**
     * 删除题库
     *
     * @param bankIds 题库主键串
     */
    @SaCheckPermission("system:questionBank:remove")
    @Log(title = "题库管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{bankIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] bankIds) {
        return toAjax(questionBankService.deleteWithValidByIds(List.of(bankIds)));
    }

    /**
     * 根据题库编码查询题库
     *
     * @param bankCode 题库编码
     */
    @SaCheckPermission("system:questionBank:query")
    @GetMapping("/code/{bankCode}")
    public R<QuestionBankVo> getByCode(@PathVariable String bankCode) {
        return R.ok(questionBankService.queryByBankCode(bankCode));
    }

    /**
     * 根据专业ID查询题库列表
     *
     * @param majorId 专业ID
     */
    @SaCheckPermission("system:questionBank:list")
    @GetMapping("/major/{majorId}")
    public R<List<QuestionBankVo>> getByMajorId(@PathVariable Long majorId) {
        return R.ok(questionBankService.queryByMajorId(majorId));
    }

    /**
     * 查询用户收藏的题库列表
     *
     * @param userId 用户ID
     */
    @SaCheckPermission("system:questionBank:list")
    @GetMapping("/bookmarked/{userId}")
    public R<List<QuestionBankVo>> getBookmarkedBanks(@PathVariable Long userId) {
        return R.ok(questionBankService.queryBookmarkedBanks(userId));
    }

    /**
     * 查询热门题库列表
     *
     * @param limit 限制数量
     */
    @GetMapping("/hot")
    public R<List<QuestionBankVo>> getHotBanks(@RequestParam(defaultValue = "10") Integer limit) {
        return R.ok(questionBankService.queryHotBanks(limit));
    }

    /**
     * 更新题库练习次数
     *
     * @param bankId 题库ID
     */
    @SaCheckPermission("system:questionBank:edit")
    @Log(title = "题库管理", businessType = BusinessType.UPDATE)
    @PutMapping("/practice/{bankId}")
    public R<Void> updatePracticeCount(@PathVariable Long bankId) {
        return toAjax(questionBankService.updatePracticeCount(bankId));
    }

    /**
     * 更新题库题目总数
     *
     * @param bankId 题库ID
     */
    @SaCheckPermission("system:questionBank:edit")
    @Log(title = "题库管理", businessType = BusinessType.UPDATE)
    @PutMapping("/total/{bankId}")
    public R<Void> updateTotalQuestions(@PathVariable Long bankId) {
        return toAjax(questionBankService.updateTotalQuestions(bankId));
    }

    /**
     * 启用/停用题库
     *
     * @param bankId 题库ID
     * @param status 状态
     */
    @SaCheckPermission("system:questionBank:edit")
    @Log(title = "题库管理", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public R<Void> changeStatus(@RequestParam Long bankId, @RequestParam String status) {
        return toAjax(questionBankService.changeStatus(bankId, status));
    }

    /**
     * 复制题库
     *
     * @param bankId 源题库ID
     * @param title  新题库标题
     */
    @SaCheckPermission("system:questionBank:add")
    @Log(title = "题库管理", businessType = BusinessType.INSERT)
    @PostMapping("/copy")
    public R<Void> copyQuestionBank(@RequestParam Long bankId, @RequestParam String title) {
        return toAjax(questionBankService.copyQuestionBank(bankId, title));
    }

    /**
     * 获取题库统计信息
     *
     * @param bankId 题库ID
     */
    @SaCheckPermission("system:questionBank:query")
    @GetMapping("/statistics/{bankId}")
    public R<QuestionBankVo> getStatistics(@PathVariable Long bankId) {
        return R.ok(questionBankService.getStatistics(bankId));
    }

    /**
     * 导入题库数据
     *
     * @param file 导入文件
     */
    @SaCheckPermission("system:questionBank:import")
    @Log(title = "题库管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public R<Void> importData(MultipartFile file) throws Exception {
        List<QuestionBankBo> list = ExcelUtil.importExcel(file.getInputStream(), QuestionBankBo.class);
        String message = questionBankService.importQuestionBank(list);
        return R.ok(message);
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(List.of(), "题库数据", QuestionBankBo.class, response);
    }
}
