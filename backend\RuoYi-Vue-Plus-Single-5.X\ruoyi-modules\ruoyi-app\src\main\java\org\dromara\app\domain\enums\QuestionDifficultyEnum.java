package org.dromara.app.domain.enums;

/**
 * 题目难度枚举
 *
 * <AUTHOR>
 */
public enum QuestionDifficultyEnum {

    /**
     * 简单
     */
    EASY(1, "简单"),

    /**
     * 中等
     */
    MEDIUM(2, "中等"),

    /**
     * 困难
     */
    HARD(3, "困难");

    private final Integer code;
    private final String description;

    QuestionDifficultyEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取描述
     *
     * @param code 难度代码
     * @return 难度描述
     */
    public static String getDescriptionByCode(Integer code) {
        if (code == null) {
            return "未知";
        }
        for (QuestionDifficultyEnum difficulty : values()) {
            if (difficulty.getCode().equals(code)) {
                return difficulty.getDescription();
            }
        }
        return "未知";
    }

    /**
     * 根据描述获取代码
     *
     * @param description 难度描述
     * @return 难度代码
     */
    public static Integer getCodeByDescription(String description) {
        if (description == null || description.trim().isEmpty()) {
            return null;
        }
        for (QuestionDifficultyEnum difficulty : values()) {
            if (difficulty.getDescription().equals(description.trim())) {
                return difficulty.getCode();
            }
        }
        return null;
    }

    /**
     * 根据代码获取枚举实例
     *
     * @param code 难度代码
     * @return 枚举实例
     */
    public static QuestionDifficultyEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (QuestionDifficultyEnum difficulty : values()) {
            if (difficulty.getCode().equals(code)) {
                return difficulty;
            }
        }
        return null;
    }

    /**
     * 根据描述获取枚举实例
     *
     * @param description 难度描述
     * @return 枚举实例
     */
    public static QuestionDifficultyEnum getByDescription(String description) {
        if (description == null || description.trim().isEmpty()) {
            return null;
        }
        for (QuestionDifficultyEnum difficulty : values()) {
            if (difficulty.getDescription().equals(description.trim())) {
                return difficulty;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
