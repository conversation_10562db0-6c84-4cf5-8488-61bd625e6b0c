<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.QuestionCommentMapper">

    <!-- 评论VO结果映射 -->
    <resultMap id="QuestionCommentVOResult" type="org.dromara.app.domain.vo.QuestionCommentVO">
        <id property="id" column="comment_id"/>
        <result property="questionId" column="question_id"/>
        <result property="userId" column="user_id"/>
        <result property="author" column="nick_name"/>
        <result property="avatar" column="avatar"/>
        <result property="content" column="content"/>
        <result property="likes" column="like_count"/>
        <result property="replyCount" column="reply_count"/>
        <result property="parentId" column="parent_id"/>
        <result property="time" column="time_display"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 查询题目评论列表（主评论，不包含回复） -->
    <select id="selectCommentListWithReplies" resultMap="QuestionCommentVOResult">
        SELECT
        c.comment_id,
        c.question_id,
        c.user_id,
        c.content,
        c.like_count,
        c.reply_count,
        c.parent_id,
        c.create_time,
        c.update_time,
        u.user_name as nick_name,
        COALESCE(oss.url, '👤') as avatar,
        CASE
        WHEN TIMESTAMPDIFF(MINUTE, c.create_time, NOW()) &lt; 1 THEN '刚刚'
        WHEN TIMESTAMPDIFF(MINUTE, c.create_time, NOW()) &lt; 60 THEN CONCAT(TIMESTAMPDIFF(MINUTE, c.create_time,
        NOW()), '分钟前')
        WHEN TIMESTAMPDIFF(HOUR, c.create_time, NOW()) &lt; 24 THEN CONCAT(TIMESTAMPDIFF(HOUR, c.create_time, NOW()),
        '小时前')
        WHEN TIMESTAMPDIFF(DAY, c.create_time, NOW()) &lt; 30 THEN CONCAT(TIMESTAMPDIFF(DAY, c.create_time, NOW()),
        '天前')
        ELSE DATE_FORMAT(c.create_time, '%Y-%m-%d')
        END as time_display
        FROM app_question_comment c
        LEFT JOIN app_user u ON c.user_id = u.user_id
        LEFT JOIN sys_oss oss ON u.avatar = oss.oss_id
        WHERE c.question_id = #{questionId}
        AND c.parent_id IS NULL
        AND c.status = '0'
        ORDER BY
        <choose>
            <when test="orderBy == 'likes' and orderDirection == 'desc'">
                c.like_count DESC, c.create_time DESC
            </when>
            <when test="orderBy == 'likes' and orderDirection == 'asc'">
                c.like_count ASC, c.create_time DESC
            </when>
            <when test="orderBy == 'createTime' and orderDirection == 'asc'">
                c.create_time ASC
            </when>
            <otherwise>
                c.create_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 查询回复列表 -->
    <select id="selectRepliesByParentId" resultMap="QuestionCommentVOResult">
        SELECT c.comment_id,
               c.question_id,
               c.user_id,
               c.content,
               c.like_count,
               c.reply_count,
               c.parent_id,
               c.create_time,
               c.update_time,
               u.user_name            as nick_name,
               COALESCE(oss.url, '👤') as avatar,
               CASE
                   WHEN TIMESTAMPDIFF(MINUTE, c.create_time, NOW()) &lt; 1 THEN '刚刚'
                   WHEN TIMESTAMPDIFF(MINUTE, c.create_time, NOW()) &lt; 60
                       THEN CONCAT(TIMESTAMPDIFF(MINUTE, c.create_time, NOW()), '分钟前')
                   WHEN TIMESTAMPDIFF(HOUR, c.create_time, NOW()) &lt; 24
                       THEN CONCAT(TIMESTAMPDIFF(HOUR, c.create_time, NOW()), '小时前')
                   WHEN TIMESTAMPDIFF(DAY, c.create_time, NOW()) &lt; 30
                       THEN CONCAT(TIMESTAMPDIFF(DAY, c.create_time, NOW()), '天前')
                   ELSE DATE_FORMAT(c.create_time, '%Y-%m-%d')
                   END                as time_display
        FROM app_question_comment c
                 LEFT JOIN app_user u ON c.user_id = u.user_id
                 LEFT JOIN sys_oss oss ON u.avatar = oss.oss_id
        WHERE c.parent_id = #{parentId}
          AND c.status = '0'
        ORDER BY c.create_time ASC
    </select>

    <!-- 查询用户对评论的点赞状态 -->
    <select id="selectLikeStatus" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM app_comment_like
        WHERE comment_id = #{commentId}
          AND user_id = #{userId}
          AND status = '1'
    </select>

    <!-- 增加评论点赞数 -->
    <update id="incrementLikeCount">
        UPDATE app_question_comment
        SET like_count  = like_count + 1,
            update_time = NOW()
        WHERE comment_id = #{commentId}
    </update>

    <!-- 减少评论点赞数 -->
    <update id="decrementLikeCount">
        UPDATE app_question_comment
        SET like_count  = GREATEST(like_count - 1, 0),
            update_time = NOW()
        WHERE comment_id = #{commentId}
    </update>

    <!-- 增加回复数 -->
    <update id="incrementReplyCount">
        UPDATE app_question_comment
        SET reply_count = reply_count + 1,
            update_time = NOW()
        WHERE comment_id = #{commentId}
    </update>

    <!-- 减少回复数 -->
    <update id="decrementReplyCount">
        UPDATE app_question_comment
        SET reply_count = GREATEST(reply_count - 1, 0),
            update_time = NOW()
        WHERE comment_id = #{commentId}
    </update>

    <!-- 插入点赞记录 -->
    <insert id="insertLikeRecord">
        INSERT INTO app_comment_like (like_id, comment_id, user_id, status, create_by, create_time, update_by,
                                      update_time)
        VALUES (#{commentId} + #{userId}, #{commentId}, #{userId}, '1', #{userId}, NOW(), #{userId}, NOW())
        ON DUPLICATE KEY UPDATE status      = '1',
                                update_time = NOW(),
                                update_by   = #{userId}
    </insert>

    <!-- 删除点赞记录 -->
    <update id="deleteLikeRecord">
        UPDATE app_comment_like
        SET status      = '0',
            update_time = NOW(),
            update_by   = #{userId}
        WHERE comment_id = #{commentId}
          AND user_id = #{userId}
    </update>

    <!-- 更新题目评论数 -->
    <update id="updateQuestionCommentCount">
        UPDATE app_question
        SET comment_count = GREATEST(comment_count + #{increment}, 0),
            update_time   = NOW()
        WHERE question_id = #{questionId}
    </update>

</mapper>
