{"doc": " 流式聊天服务\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "streamChat", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": " 流式聊天\n"}, {"name": "streamChat", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 流式聊天（使用默认提供商）\n"}, {"name": "enhanceMessageForAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentType", "java.lang.String"], "doc": " 根据Agent类型增强消息\n"}, {"name": "selectStreamingModel", "paramTypes": ["java.lang.String"], "doc": " 选择流式模型\n"}, {"name": "getOrCreateMemory", "paramTypes": ["java.lang.String"], "doc": " 获取或创建内存\n"}, {"name": "clearSessionMemory", "paramTypes": ["java.lang.String"], "doc": " 清理会话内存\n"}, {"name": "clearAllMemory", "paramTypes": [], "doc": " 清理所有内存\n"}, {"name": "getMemoryCacheSize", "paramTypes": [], "doc": " 获取会话内存统计\n"}, {"name": "hasSession", "paramTypes": ["java.lang.String"], "doc": " 检查会话是否存在\n"}], "constructors": []}