{"doc": "\n 书籍章节Service业务层处理\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryChaptersByBookId", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 根据书籍ID查询章节列表\r\n"}, {"name": "query<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 根据章节ID查询章节内容\r\n"}, {"name": "queryChapterByOrder", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Long"], "doc": "\n 根据书籍ID和章节序号查询章节\r\n"}, {"name": "queryPreviewChapters", "paramTypes": ["java.lang.Long"], "doc": "\n 查询试读章节列表\r\n"}, {"name": "insertChapter", "paramTypes": ["org.dromara.app.domain.BookChapter"], "doc": "\n 新增章节\r\n"}, {"name": "updateChapter", "paramTypes": ["org.dromara.app.domain.BookChapter"], "doc": "\n 修改章节\r\n"}, {"name": "deleteChapter", "paramTypes": ["java.lang.String"], "doc": "\n 删除章节\r\n"}, {"name": "deleteChaptersByBookId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据书籍ID删除所有章节\r\n"}, {"name": "checkChapterAccess", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 检查用户是否有权限访问章节\r\n"}, {"name": "updateChapterUnlockStatus", "paramTypes": ["java.lang.String", "java.lang.Bo<PERSON>an"], "doc": "\n 更新章节解锁状态\r\n"}, {"name": "setUserChapterStatus", "paramTypes": ["java.util.List", "java.lang.Long", "java.lang.Long"], "doc": "\n 设置用户章节状态（是否已完成等）\r\n"}, {"name": "calculateChapterStats", "paramTypes": ["org.dromara.app.domain.BookChapter"], "doc": "\n 计算章节统计信息（字数、阅读时间）\r\n"}, {"name": "updateBookChapterCount", "paramTypes": ["java.lang.Long"], "doc": "\n 更新书籍的章节数\r\n"}], "constructors": []}