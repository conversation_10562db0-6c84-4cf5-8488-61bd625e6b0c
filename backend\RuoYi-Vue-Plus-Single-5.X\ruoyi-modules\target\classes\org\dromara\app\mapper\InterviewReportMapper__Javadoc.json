{"doc": " 面试报告Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectBySessionIdAndUserId", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 根据会话ID和用户ID查询报告\n\n @param sessionId 会话ID\n @param userId 用户ID\n @return 面试报告\n"}, {"name": "selectUserReportStats", "paramTypes": ["java.lang.Long"], "doc": " 查询用户的报告统计\n\n @param userId 用户ID\n @return 统计数据\n"}, {"name": "selectRecentReports", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询用户最近的报告\n\n @param userId 用户ID\n @param limit 限制数量\n @return 报告列表\n"}, {"name": "selectJobPositionStats", "paramTypes": ["java.lang.String"], "doc": " 查询岗位相关的报告统计\n\n @param jobPosition 岗位\n @return 统计数据\n"}, {"name": "selectScoreDistribution", "paramTypes": [], "doc": " 查询分数分布统计\n\n @return 分数分布\n"}], "constructors": []}