package org.dromara.app.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket测试控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/test/websocket")
@RequiredArgsConstructor
public class WebSocketTestController {

    /**
     * 测试WebSocket服务状态
     */
    @GetMapping("/status")
    public R<Map<String, Object>> getWebSocketStatus() {
        log.info("检查WebSocket服务状态");
        
        Map<String, Object> status = new HashMap<>();
        status.put("service", "running");
        status.put("endpoint", "/interview/ws");
        status.put("timestamp", System.currentTimeMillis());
        status.put("message", "WebSocket服务正常运行");
        
        return R.ok(status);
    }

    /**
     * 获取WebSocket连接信息
     */
    @GetMapping("/info")
    public R<Map<String, Object>> getWebSocketInfo() {
        log.info("获取WebSocket连接信息");
        
        Map<String, Object> info = new HashMap<>();
        info.put("protocol", "WebSocket");
        info.put("path", "/interview/ws");
        info.put("allowedOrigins", "*");
        info.put("supportedMessageTypes", new String[]{"connect", "emotion_request", "speech_analysis", "heartbeat"});
        info.put("features", new String[]{"实时智能建议", "表情分析", "语音分析", "心跳检测"});
        
        return R.ok(info);
    }
}
