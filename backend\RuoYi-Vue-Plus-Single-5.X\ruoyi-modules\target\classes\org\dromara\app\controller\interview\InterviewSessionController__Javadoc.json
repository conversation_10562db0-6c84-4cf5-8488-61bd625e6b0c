{"doc": " 面试会话控制器\n 专门处理面试房间相关的API请求，集成多模态分析功能\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getSessionInfo", "paramTypes": ["java.lang.String"], "doc": " 获取会话信息\n"}, {"name": "getQuestion", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取面试问题\n"}, {"name": "submitAnswer", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "org.springframework.web.multipart.MultipartFile", "org.springframework.web.multipart.MultipartFile", "java.lang.Integer", "java.lang.String"], "doc": " 提交面试回答（集成多模态分析）\n"}, {"name": "endSession", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 结束面试会话\n"}, {"name": "submitFeedback", "paramTypes": ["java.lang.String", "java.lang.Integer", "java.lang.String"], "doc": " 提交面试反馈\n"}, {"name": "checkDevices", "paramTypes": [], "doc": " 检查设备状态\n"}, {"name": "getSessionStatus", "paramTypes": ["java.lang.String"], "doc": " 获取会话状态\n"}], "constructors": []}