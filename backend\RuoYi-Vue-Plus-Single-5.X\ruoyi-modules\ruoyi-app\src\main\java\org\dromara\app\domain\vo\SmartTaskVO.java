package org.dromara.app.domain.vo;

import lombok.Data;

/**
 * 智能推荐任务VO
 *
 * <AUTHOR>
 */
@Data
public class SmartTaskVO {

    /**
     * 任务ID
     */
    private Long id;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 任务类型：skill-技能, expression-表达, knowledge-知识, practice-实践
     */
    private String type;

    /**
     * 优先级：high-高, medium-中, low-低
     */
    private String priority;

    /**
     * 任务链接
     */
    private String link;

    /**
     * 预计完成时间（分钟）
     */
    private Integer estimatedTime;

    /**
     * 截止日期
     */
    private String deadline;
}
