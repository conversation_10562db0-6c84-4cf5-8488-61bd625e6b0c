package org.dromara.app.controller.learning;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import org.dromara.app.domain.bo.CommentQueryBo;
import org.dromara.app.domain.bo.VideoCommentBo;
import org.dromara.app.domain.bo.VideoQueryBo;
import org.dromara.app.domain.vo.*;
import org.dromara.app.service.IVideoService;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.service.ISysOssService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 视频课程控制器
 *
 * @Author: SevenJL
 * @CreateTime: 2025-01-05
 * @Version: 1.0
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/video")
public class VideoController extends BaseController {

    private final IVideoService videoService;
    private final ISysOssService sysOssService;

    /**
     * 查询视频课程列表
     */
    @PostMapping("/list")
    public R<VideoListResultVo> list(@RequestBody VideoQueryBo bo) {
        return R.ok(videoService.queryVideoList(bo));
    }

    /**
     * 获取热门视频
     */
    @GetMapping("/hot")
    public R<List<VideoDetailVo>> getHotVideos(@RequestParam(defaultValue = "5") Integer limit) {
        return R.ok(videoService.getHotVideos(limit));
    }

    /**
     * 获取视频详情
     */
    @SaCheckLogin
    @GetMapping("/{id}")
    public R<VideoDetailVo> getDetail(@PathVariable Long id) {
        Long userId = StpUtil.getLoginIdAsLong();
        return R.ok(videoService.getVideoDetail(id, userId));
    }

    /**
     * 获取学习统计数据
     */
    @SaCheckLogin
    @GetMapping("/stats")
    public R<VideoLearningStatsVo> getLearningStats() {
        Long userId = StpUtil.getLoginIdAsLong();
        return R.ok(videoService.getLearningStats(userId));
    }

    /**
     * 获取收藏的视频
     */
    @SaCheckLogin
    @GetMapping("/bookmarked")
    public R<VideoListResultVo> getBookmarkedVideos(VideoQueryBo bo, PageQuery pageQuery) {
        Long userId = StpUtil.getLoginIdAsLong();
        return R.ok(videoService.getBookmarkedVideos(userId, bo, pageQuery));
    }

    /**
     * 获取已购买的视频
     */
    @SaCheckLogin
    @GetMapping("/purchased")
    public R<VideoListResultVo> getPurchasedVideos(VideoQueryBo bo, PageQuery pageQuery) {
        Long userId = StpUtil.getLoginIdAsLong();
        return R.ok(videoService.getPurchasedVideos(userId, bo, pageQuery));
    }

    /**
     * 获取学习历史
     */
    @SaCheckLogin
    @GetMapping("/history")
    public R<VideoListResultVo> getLearningHistory(VideoQueryBo bo, PageQuery pageQuery) {
        Long userId = StpUtil.getLoginIdAsLong();
        return R.ok(videoService.getLearningHistory(userId, bo, pageQuery));
    }


    /**
     * 保存视频播放记录
     *
     * @param videoId 视频ID
     */
    @SaCheckLogin
    @Log(title = "保存视频播放记录", businessType = BusinessType.INSERT)
    @PostMapping("/{videoId}/save-play-record")
    public R<Void> savePlayRecord(@PathVariable Long videoId) {
        Long userId = StpUtil.getLoginIdAsLong();
        videoService.saveVideoPlayRecord(videoId, userId);
        return R.ok();
    }

    /**
     * 获取相关推荐视频
     *
     * @param id    视频ID
     * @param limit 返回数量限制
     */
    @GetMapping("/{id}/related")
    public R<List<RelatedVideoVo>> getRelatedVideos(@PathVariable Long id,
                                                    @RequestParam(defaultValue = "6") Integer limit) {
        return R.ok(videoService.getRelatedVideos(id, limit));
    }

    /**
     * 切换视频点赞状态
     */
    @SaCheckLogin
    @Log(title = "视频点赞", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/like")
    public R<Void> toggleLike(@PathVariable Long id, @RequestParam Boolean isLike) {
        Long userId = StpUtil.getLoginIdAsLong();
        videoService.toggleVideoLike(id, userId, isLike);
        return R.ok();
    }

    /**
     * 切换视频收藏状态
     */
    @SaCheckLogin
    @Log(title = "视频收藏", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/collect")
    public R<Void> toggleCollect(@PathVariable Long id, @RequestParam Boolean isCollect) {
        Long userId = StpUtil.getLoginIdAsLong();
        videoService.toggleVideoCollect(id, userId, isCollect);
        return R.ok();
    }

    /**
     * 分享视频
     */
    @SaCheckLogin
    @Log(title = "视频分享", businessType = BusinessType.INSERT)
    @PostMapping("/{id}/share")
    public R<Void> shareVideo(@PathVariable Long id, @RequestParam String platform) {
        Long userId = StpUtil.getLoginIdAsLong();
        videoService.shareVideo(id, userId, platform);
        return R.ok();
    }

    /**
     * 增加视频播放次数
     */
    @PostMapping("/{id}/view")
    public R<Void> incrementView(@PathVariable Long id) {
        videoService.incrementVideoView(id);
        return R.ok();
    }

    /**
     * 更新视频播放进度
     */
    @SaCheckLogin
    @Log(title = "更新播放进度", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/progress")
    public R<Void> updateProgress(@PathVariable Long id,
                                  @RequestParam Integer currentTime,
                                  @RequestParam Integer duration) {
        Long userId = StpUtil.getLoginIdAsLong();
        videoService.updateVideoProgress(id, userId, currentTime, duration);
        return R.ok();
    }

    /**
     * 获取视频播放记录
     */
    @SaCheckLogin
    @GetMapping("/{id}/play-record")
    public R<VideoPlayRecordVo> getPlayRecord(@PathVariable Long id) {
        Long userId = StpUtil.getLoginIdAsLong();
        return R.ok(videoService.getVideoPlayRecord(id, userId));
    }

    /**
     * 检查视频购买状态
     */
    @SaCheckLogin
    @GetMapping("/{id}/purchase-status")
    public R<VideoPurchaseStatusVo> checkPurchaseStatus(@PathVariable Long id) {
        Long userId = StpUtil.getLoginIdAsLong();
        return R.ok(videoService.checkVideoPurchaseStatus(id, userId));
    }

    /**
     * 关注/取关讲师
     */
    @SaCheckLogin
    @Log(title = "关注讲师", businessType = BusinessType.UPDATE)
    @PostMapping("/instructor/{instructorId}/follow")
    public R<Void> toggleFollow(@PathVariable Long instructorId, @RequestParam Boolean isFollow) {
        Long userId = StpUtil.getLoginIdAsLong();
        videoService.toggleInstructorFollow(instructorId, userId, isFollow);
        return R.ok();
    }

    /**
     * 获取视频评论列表
     */
    @GetMapping("/{videoId}/comments")
    public R<VideoCommentListVo> getComments(@PathVariable Long videoId,
                                             CommentQueryBo bo,
                                             PageQuery pageQuery) {
        bo.setVideoId(videoId);
        return R.ok(videoService.getVideoComments(bo, pageQuery));
    }

    /**
     * 发布视频评论
     */
    @SaCheckLogin
    @Log(title = "发布评论", businessType = BusinessType.INSERT)
    @PostMapping("/comment")
    public R<VideoCommentVo> publishComment(@Validated @RequestBody VideoCommentBo bo) {
        Long userId = StpUtil.getLoginIdAsLong();
        bo.setUserId(userId);
        return R.ok(videoService.publishVideoComment(bo));
    }

    /**
     * 切换评论点赞状态
     */
    @SaCheckLogin
    @Log(title = "评论点赞", businessType = BusinessType.UPDATE)
    @PostMapping("/comment/{commentId}/like")
    public R<Void> toggleCommentLike(@PathVariable Long commentId, @RequestParam Boolean isLike) {
        Long userId = StpUtil.getLoginIdAsLong();
        videoService.toggleCommentLike(commentId, userId, isLike);
        return R.ok();
    }

    /**
     * 上传视频文件
     */
    @SaCheckLogin
    @Log(title = "视频上传", businessType = BusinessType.INSERT)
    @PostMapping("/upload")
    public R<SysOssVo> uploadVideo(@RequestParam("file") MultipartFile file) {
        // 验证文件类型
        String fileName = file.getOriginalFilename();
        if (fileName == null || !isVideoFile(fileName)) {
            return R.fail("请上传有效的视频文件");
        }

        // 验证文件大小（100MB限制）
        if (file.getSize() > 100 * 1024 * 1024) {
            return R.fail("视频文件大小不能超过100MB");
        }

        SysOssVo ossVo = sysOssService.upload(file);
        return R.ok(ossVo);
    }

    /**
     * 上传视频缩略图
     */
    @SaCheckLogin
    @Log(title = "缩略图上传", businessType = BusinessType.INSERT)
    @PostMapping("/upload/thumbnail")
    public R<SysOssVo> uploadThumbnail(@RequestParam("file") MultipartFile file) {
        // 验证文件类型
        String fileName = file.getOriginalFilename();
        if (fileName == null || !isImageFile(fileName)) {
            return R.fail("请上传有效的图片文件");
        }

        // 验证文件大小（5MB限制）
        if (file.getSize() > 5 * 1024 * 1024) {
            return R.fail("图片文件大小不能超过5MB");
        }

        SysOssVo ossVo = sysOssService.upload(file);
        return R.ok(ossVo);
    }

    /**
     * 检查是否为视频文件
     */
    private boolean isVideoFile(String fileName) {
        String[] videoExtensions = {".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm", ".mkv"};
        String lowerFileName = fileName.toLowerCase();
        for (String ext : videoExtensions) {
            if (lowerFileName.endsWith(ext)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否为图片文件
     */
    private boolean isImageFile(String fileName) {
        String[] imageExtensions = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"};
        String lowerFileName = fileName.toLowerCase();
        for (String ext : imageExtensions) {
            if (lowerFileName.endsWith(ext)) {
                return true;
            }
        }
        return false;
    }
}
