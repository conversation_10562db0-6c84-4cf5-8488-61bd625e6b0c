package org.dromara.app.domain.dto.avatar;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 数字人启动请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
@Schema(description = "数字人启动请求")
public class AvatarStartDto {

    @Schema(description = "形象ID")
    private String avatarId;

    @Schema(description = "场景ID")
    private String sceneId;

    @Schema(description = "声音ID")
    private String vcn;

    @Schema(description = "视频宽度", example = "720")
    @Min(value = 405, message = "视频宽度最小为405")
    @Max(value = 1920, message = "视频宽度最大为1920")
    private Integer width;

    @Schema(description = "视频高度", example = "1280")
    @Min(value = 405, message = "视频高度最小为405")
    @Max(value = 1080, message = "视频高度最大为1080")
    private Integer height;

    @Schema(description = "背景数据（Base64编码）")
    private String background;

    @Schema(description = "是否开启透明背景", example = "false")
    private Boolean enableAlpha;

    @Schema(description = "视频协议", example = "xrtc")
    private String protocol;

    @Schema(description = "视频帧率", example = "25")
    @Min(value = 1, message = "帧率最小为1")
    @Max(value = 25, message = "帧率最大为25")
    private Integer fps;

    @Schema(description = "视频码率", example = "5000")
    @Min(value = 1000, message = "码率最小为1000")
    @Max(value = 10000, message = "码率最大为10000")
    private Integer bitrate;

    @Schema(description = "连接超时时间（秒）", example = "30")
    @Min(value = 5, message = "超时时间最小为5秒")
    @Max(value = 300, message = "超时时间最大为300秒")
    private Long timeout;
}
