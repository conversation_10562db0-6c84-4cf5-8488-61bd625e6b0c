{"doc": " 书籍章节对象 book_chapters (MongoDB)\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 章节ID（MongoDB ObjectId）\n"}, {"name": "bookId", "doc": " 书籍ID\n"}, {"name": "title", "doc": " 章节标题\n"}, {"name": "content", "doc": " 章节内容（Markdown格式）\n"}, {"name": "chapterOrder", "doc": " 章节排序序号\n"}, {"name": "wordCount", "doc": " 字数统计\n"}, {"name": "readTime", "doc": " 预估阅读时间（分钟）\n"}, {"name": "isUnlocked", "doc": " 是否已解锁：0-未解锁，1-已解锁\n"}, {"name": "isPreview", "doc": " 是否为试读章节：0-否，1-是\n"}, {"name": "status", "doc": " 章节状态：0-草稿，1-已发布\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}, {"name": "createBy", "doc": " 创建者\n"}, {"name": "updateBy", "doc": " 更新者\n"}, {"name": "isCompleted", "doc": " 是否已完成（用户维度，不存储到数据库）\n"}], "enumConstants": [], "methods": [], "constructors": []}