{"doc": " 岗位信息Service接口\n\n <AUTHOR> Assistant\n @date 2025-07-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectJobPageWithFavorite", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "org.dromara.app.domain.bo.JobQueryBo"], "doc": " 分页查询岗位列表\n\n @param page    分页参数\n @param queryBo 查询条件\n @return 岗位列表\n"}, {"name": "selectByTechnicalDomain", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": " 根据技术领域查询岗位\n\n @param technicalDomain 技术领域\n @param level          岗位级别\n @param limit          限制数量\n @return 岗位列表\n"}, {"name": "selectRecommendedJobs", "paramTypes": ["java.lang.Integer", "java.lang.Long"], "doc": " 查询推荐岗位\n\n @param limit  限制数量\n @param userId 用户ID\n @return 推荐岗位列表\n"}, {"name": "selectHotJobs", "paramTypes": ["java.lang.Integer", "java.lang.Long"], "doc": " 查询热门岗位\n\n @param limit  限制数量\n @param userId 用户ID\n @return 热门岗位列表\n"}, {"name": "selectJobDetail", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 根据ID查询岗位详情\n\n @param jobId  岗位ID\n @param userId 用户ID\n @return 岗位详情\n"}, {"name": "incrementViewCount", "paramTypes": ["java.lang.Long"], "doc": " 增加浏览次数\n\n @param jobId 岗位ID\n @return 是否成功\n"}, {"name": "updateFavoriteCount", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 更新收藏次数\n\n @param jobId     岗位ID\n @param increment 增量（1或-1）\n @return 是否成功\n"}, {"name": "getTechnicalDomainStats", "paramTypes": [], "doc": " 查询技术领域统计\n\n @return 统计信息\n"}], "constructors": []}