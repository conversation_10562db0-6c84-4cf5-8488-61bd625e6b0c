{"doc": "\n 知识库实体\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 知识库ID\r\n"}, {"name": "name", "doc": "\n 知识库名称\r\n"}, {"name": "description", "doc": "\n 知识库描述\r\n"}, {"name": "type", "doc": "\n 知识库类型 (general/technical/business/etc.)\r\n"}, {"name": "status", "doc": "\n 知识库状态 (0=禁用 1=启用)\r\n"}, {"name": "vectorDimension", "doc": "\n 向量维度 (默认1024)\r\n"}, {"name": "documentCount", "doc": "\n 文档数量\r\n"}, {"name": "vectorCount", "doc": "\n 向量数量\r\n"}, {"name": "indexConfig", "doc": "\n 索引配置 (JSON格式)\r\n"}, {"name": "extendConfig", "doc": "\n 扩展配置 (JSON格式)\r\n"}, {"name": "lastSyncTime", "doc": "\n 最后更新时间\r\n"}, {"name": "sortOrder", "doc": "\n 排序字段\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}], "enumConstants": [], "methods": [], "constructors": []}