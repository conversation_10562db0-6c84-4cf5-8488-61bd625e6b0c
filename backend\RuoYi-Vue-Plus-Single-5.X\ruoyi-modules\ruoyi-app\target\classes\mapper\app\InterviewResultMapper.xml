<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.InterviewResultMapper">

    <resultMap type="org.dromara.app.domain.InterviewResult" id="InterviewResultResult">
        <result property="id" column="id"/>
        <result property="sessionId" column="session_id"/>
        <result property="userId" column="user_id"/>
        <result property="jobId" column="job_id"/>
        <result property="jobName" column="job_name"/>
        <result property="company" column="company"/>
        <result property="mode" column="mode"/>
        <result property="date" column="date"/>
        <result property="duration" column="duration"/>
        <result property="totalScore" column="total_score"/>
        <result property="rank" column="rank"/>
        <result property="rankText" column="rank_text"/>
        <result property="percentile" column="percentile"/>
        <result property="answeredQuestions" column="answered_questions"/>
        <result property="totalQuestions" column="total_questions"/>
        <result property="status" column="status"/>
        <result property="topStrengths" column="top_strengths" typeHandler="org.dromara.common.json.handler.JsonStringListTypeHandler"/>
        <result property="topWeaknesses" column="top_weaknesses" typeHandler="org.dromara.common.json.handler.JsonStringListTypeHandler"/>
        <result property="overallFeedback" column="overall_feedback"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectInterviewResultVo">
        select id,
               session_id,
               user_id,
               job_id,
               job_name,
               company,
               mode,
               date,
               duration,
               total_score,
               rank,
               rank_text,
               percentile,
               answered_questions,
               total_questions,
               status,
               top_strengths,
               top_weaknesses,
               overall_feedback,
               create_by,
               create_time,
               update_by,
               update_time,
               del_flag
        from app_interview_result
    </sql>

    <!-- 根据会话ID查询面试结果 -->
    <select id="selectBySessionId" parameterType="String" resultMap="InterviewResultResult">
        <include refid="selectInterviewResultVo"/>
        where session_id = #{sessionId} and del_flag = '0'
    </select>

    <!-- 根据用户ID查询面试结果列表 -->
    <select id="selectByUserId" resultMap="InterviewResultResult">
        <include refid="selectInterviewResultVo"/>
        where user_id = #{userId} and del_flag = '0'
        order by create_time desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

    <!-- 根据用户ID和状态查询面试结果列表 -->
    <select id="selectByUserIdAndStatus" resultMap="InterviewResultResult">
        <include refid="selectInterviewResultVo"/>
        where user_id = #{userId} and status = #{status} and del_flag = '0'
        order by create_time desc
    </select>

    <!-- 根据岗位ID查询面试结果统计 -->
    <select id="selectStatsByJobId" resultMap="InterviewResultResult">
        <include refid="selectInterviewResultVo"/>
        where job_id = #{jobId} and del_flag = '0'
        order by create_time desc
    </select>

    <!-- 查询用户最近的面试结果 -->
    <select id="selectRecentByUserId" resultMap="InterviewResultResult">
        <include refid="selectInterviewResultVo"/>
        where user_id = #{userId} 
        and del_flag = '0'
        and create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        order by create_time desc
    </select>

    <!-- 根据分数范围查询面试结果 -->
    <select id="selectByScoreRange" resultMap="InterviewResultResult">
        <include refid="selectInterviewResultVo"/>
        where total_score between #{minScore} and #{maxScore} 
        and del_flag = '0'
        order by total_score desc
    </select>

    <!-- 查询用户平均分数 -->
    <select id="selectAvgScoreByUserId" parameterType="Long" resultType="Double">
        select avg(total_score) 
        from app_interview_result 
        where user_id = #{userId} and del_flag = '0'
    </select>

    <!-- 查询用户面试次数 -->
    <select id="selectCountByUserId" parameterType="Long" resultType="Integer">
        select count(*) 
        from app_interview_result 
        where user_id = #{userId} and del_flag = '0'
    </select>

    <!-- 分页查询用户面试历史记录（包含岗位和分类信息） -->
    <select id="selectHistoryPageWithJobInfo" resultMap="InterviewResultResult">
        select ir.id,
               ir.session_id,
               ir.user_id,
               ir.job_id,
               ir.job_name,
               ir.company,
               ir.mode,
               ir.date,
               ir.duration,
               ir.total_score,
               ir.rank,
               ir.rank_text,
               ir.percentile,
               ir.answered_questions,
               ir.total_questions,
               ir.status,
               ir.top_strengths,
               ir.top_weaknesses,
               ir.overall_feedback,
               ir.create_by,
               ir.create_time,
               ir.update_by,
               ir.update_time,
               ir.del_flag
        from app_interview_result ir
        left join app_job j on ir.job_id = j.id
        left join app_job_category jc on j.category_id = jc.id
        where ir.user_id = #{userId} 
        and ir.del_flag = '0'
        <if test="category != null and category != ''">
            and jc.name = #{category}
            and jc.del_flag = '0' 
            and jc.status = '0'
        </if>
        <if test="status != null and status != ''">
            and ir.status = #{status}
        </if>
        order by ir.create_time desc
    </select>

</mapper>
