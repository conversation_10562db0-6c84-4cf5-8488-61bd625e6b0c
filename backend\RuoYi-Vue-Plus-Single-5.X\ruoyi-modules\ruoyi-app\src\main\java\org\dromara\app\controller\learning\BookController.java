package org.dromara.app.controller.learning;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.app.domain.Book;
import org.dromara.app.domain.BookChapter;
import org.dromara.app.domain.BookReadingRecord;
import org.dromara.app.service.IBookChapterService;
import org.dromara.app.service.IBookReadingRecordService;
import org.dromara.app.service.IBookService;
import org.dromara.common.core.domain.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 面试书籍控制器
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/learning/books")
public class BookController {

    private final IBookService bookService;
    private final IBookChapterService bookChapterService;
    private final IBookReadingRecordService bookReadingRecordService;

    /**
     * 分页查询书籍列表
     *
     * @param pageNum     页码
     * @param pageSize    每页大小
     * @param category    分类筛选
     * @param searchQuery 搜索关键词
     * @return 分页结果
     */
    @GetMapping("/list")
    public R<Page<Book>> list(@RequestParam(defaultValue = "1") Integer pageNum,
                              @RequestParam(defaultValue = "10") Integer pageSize,
                              @RequestParam(required = false) String category,
                              @RequestParam(required = false) String searchQuery) {
        Long userId = StpUtil.getLoginIdAsLong();
        Page<Book> page = bookService.queryBookPage(pageNum, pageSize, category, searchQuery, userId);
        return R.ok(page);
    }

    /**
     * 查询书籍详情
     *
     * @param id 书籍ID
     * @return 书籍详情
     */
    @GetMapping("/{id}")
    public R<Book> getInfo(@PathVariable Long id) {
        Long userId = StpUtil.getLoginIdAsLong();
        Book book = bookService.queryBookDetail(id, userId);
        if (book == null) {
            return R.fail("书籍不存在或已下架");
        }
        return R.ok(book);
    }

    /**
     * 查询热门书籍列表
     *
     * @param limit 限制数量
     * @return 热门书籍列表
     */
    @GetMapping("/hot")
    public R<List<Book>> getHotBooks(@RequestParam(defaultValue = "10") Integer limit) {
        List<Book> books = bookService.queryHotBooks(limit);
        return R.ok(books);
    }

    /**
     * 查询推荐书籍列表
     *
     * @param limit 限制数量
     * @return 推荐书籍列表
     */
    @GetMapping("/recommend")
    public R<List<Book>> getRecommendedBooks(@RequestParam(defaultValue = "10") Integer limit) {
        Long userId = StpUtil.getLoginIdAsLong();
        List<Book> books = bookService.queryRecommendedBooks(userId, limit);
        return R.ok(books);
    }

    /**
     * 查询分类统计信息
     *
     * @return 分类统计
     */
    @GetMapping("/category/stats")
    public R<Map<String, Object>> getCategoryStats() {
        Map<String, Object> stats = bookService.queryCategoryStats();
        return R.ok(stats);
    }

    /**
     * 开始阅读书籍（增加阅读次数）
     *
     * @param id 书籍ID
     * @return 操作结果
     */
    @PostMapping("/{id}/read")
    public R<Void> startReading(@PathVariable Long id) {
        boolean success = bookService.incrementReadCount(id);
        return success ? R.ok() : R.fail("操作失败");
    }

    /**
     * 查询书籍章节列表
     *
     * @param bookId 书籍ID
     * @return 章节列表
     */
    @GetMapping("/{bookId}/chapters")
    public R<List<BookChapter>> getChapters(@PathVariable Long bookId) {
        Long userId = StpUtil.getLoginIdAsLong();
        List<BookChapter> chapters = bookChapterService.queryChaptersByBookId(bookId, userId);
        return R.ok(chapters);
    }

    /**
     * 查询章节内容
     *
     * @param chapterId 章节ID
     * @return 章节内容
     */
    @GetMapping("/chapter/{chapterId}")
    public R<BookChapter> getChapterContent(@PathVariable String chapterId) {
        Long userId = StpUtil.getLoginIdAsLong();

        // 检查用户是否有权限访问该章节
        if (!bookChapterService.checkChapterAccess(chapterId, userId)) {
            return R.fail("您暂无权限访问该章节，请先购买或解锁");
        }

        BookChapter chapter = bookChapterService.queryChapterContent(chapterId, userId);
        if (chapter == null) {
            return R.fail("章节不存在");
        }

        return R.ok(chapter);
    }

    /**
     * 根据书籍ID和章节序号查询章节
     *
     * @param bookId       书籍ID
     * @param chapterOrder 章节序号
     * @return 章节信息
     */
    @GetMapping("/{bookId}/chapter/{chapterOrder}")
    public R<BookChapter> getChapterByOrder(@PathVariable Long bookId,
                                            @PathVariable Integer chapterOrder) {
        Long userId = StpUtil.getLoginIdAsLong();
        BookChapter chapter = bookChapterService.queryChapterByOrder(bookId, chapterOrder, userId);
        if (chapter == null) {
            return R.fail("章节不存在");
        }
        return R.ok(chapter);
    }

    /**
     * 查询试读章节列表
     *
     * @param bookId 书籍ID
     * @return 试读章节列表
     */
    @GetMapping("/{bookId}/preview")
    public R<List<BookChapter>> getPreviewChapters(@PathVariable Long bookId) {
        List<BookChapter> chapters = bookChapterService.queryPreviewChapters(bookId);
        return R.ok(chapters);
    }

    // ========== 管理端接口 ==========

    /**
     * 新增书籍
     *
     * @param book 书籍信息
     * @return 操作结果
     */
    @PostMapping
    public R<Void> add(@Validated @RequestBody Book book) {
        boolean success = bookService.insertBook(book);
        return success ? R.ok() : R.fail("新增失败");
    }

    /**
     * 修改书籍
     *
     * @param book 书籍信息
     * @return 操作结果
     */
    @PutMapping
    public R<Void> edit(@Validated @RequestBody Book book) {
        boolean success = bookService.updateBook(book);
        return success ? R.ok() : R.fail("修改失败");
    }

    /**
     * 删除书籍
     *
     * @param ids 书籍ID列表
     * @return 操作结果
     */
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable List<Long> ids) {
        boolean success = bookService.deleteBooks(ids);
        return success ? R.ok() : R.fail("删除失败");
    }

    /**
     * 更新书籍状态
     *
     * @param id     书籍ID
     * @param status 状态
     * @return 操作结果
     */
    @PutMapping("/{id}/status")
    public R<Void> updateStatus(@PathVariable Long id, @RequestParam Boolean status) {
        boolean success = bookService.updateBookStatus(id, status);
        return success ? R.ok() : R.fail("状态更新失败");
    }

    /**
     * 新增章节
     *
     * @param chapter 章节信息
     * @return 操作结果
     */
    @PostMapping("/chapter")
    public R<Void> addChapter(@Validated @RequestBody BookChapter chapter) {
        boolean success = bookChapterService.insertChapter(chapter);
        return success ? R.ok() : R.fail("新增章节失败");
    }

    /**
     * 修改章节
     *
     * @param chapter 章节信息
     * @return 操作结果
     */
    @PutMapping("/chapter")
    public R<Void> editChapter(@Validated @RequestBody BookChapter chapter) {
        boolean success = bookChapterService.updateChapter(chapter);
        return success ? R.ok() : R.fail("修改章节失败");
    }

    /**
     * 删除章节
     *
     * @param chapterId 章节ID
     * @return 操作结果
     */
    @DeleteMapping("/chapter/{chapterId}")
    public R<Void> removeChapter(@PathVariable String chapterId) {
        boolean success = bookChapterService.deleteChapter(chapterId);
        return success ? R.ok() : R.fail("删除章节失败");
    }

    /**
     * 更新章节解锁状态
     *
     * @param chapterId  章节ID
     * @param isUnlocked 是否解锁
     * @return 操作结果
     */
    @PutMapping("/chapter/{chapterId}/unlock")
    public R<Void> updateChapterUnlock(@PathVariable String chapterId,
                                       @RequestParam Boolean isUnlocked) {
        boolean success = bookChapterService.updateChapterUnlockStatus(chapterId, isUnlocked);
        return success ? R.ok() : R.fail("更新解锁状态失败");
    }

    // ========== 阅读记录接口 ==========

    /**
     * 获取用户的阅读记录
     *
     * @param bookId 书籍ID
     * @return 阅读记录
     */
    @GetMapping("/{bookId}/reading-record")
    public R<BookReadingRecord> getReadingRecord(@PathVariable Long bookId) {
        Long userId = StpUtil.getLoginIdAsLong();
        BookReadingRecord record = bookReadingRecordService.queryByUserIdAndBookId(userId, bookId);
        return R.ok(record);
    }

    /**
     * 保存或更新阅读记录
     *
     * @param bookId              书籍ID
     * @param currentChapterId    当前章节ID
     * @param currentChapterIndex 当前章节索引
     * @param readingProgress     阅读进度
     * @param readingSettings     阅读设置
     * @return 操作结果
     */
    @PostMapping("/{bookId}/reading-record")
    public R<Void> saveReadingRecord(@PathVariable Long bookId,
                                     @RequestParam(required = false) String currentChapterId,
                                     @RequestParam(required = false) Integer currentChapterIndex,
                                     @RequestParam(required = false) Integer readingProgress,
                                     @RequestBody(required = false) Map<String, Object> readingSettings) {
        Long userId = StpUtil.getLoginIdAsLong();
        boolean success = bookReadingRecordService.saveOrUpdateReadingRecord(
            userId, bookId, currentChapterId, currentChapterIndex, readingProgress, readingSettings);
        return success ? R.ok() : R.fail("保存阅读记录失败");
    }

    /**
     * 标记章节已完成
     *
     * @param bookId    书籍ID
     * @param chapterId 章节ID
     * @return 操作结果
     */
    @PostMapping("/{bookId}/complete-chapter")
    public R<Void> markChapterCompleted(@PathVariable Long bookId,
                                        @RequestParam String chapterId) {
        Long userId = StpUtil.getLoginIdAsLong();
        boolean success = bookReadingRecordService.markChapterCompleted(userId, bookId, chapterId);
        return success ? R.ok() : R.fail("标记章节完成失败");
    }

    /**
     * 查询用户阅读历史
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 阅读历史分页
     */
    @GetMapping("/reading-history")
    public R<Page<BookReadingRecord>> getReadingHistory(@RequestParam(defaultValue = "1") Integer pageNum,
                                                        @RequestParam(defaultValue = "10") Integer pageSize) {
        Long userId = StpUtil.getLoginIdAsLong();
        Page<BookReadingRecord> page = bookReadingRecordService.queryUserReadingHistory(pageNum, pageSize, userId);
        return R.ok(page);
    }

    /**
     * 查询最近阅读的书籍
     *
     * @param limit 限制数量
     * @return 最近阅读列表
     */
    @GetMapping("/recent-reading")
    public R<List<BookReadingRecord>> getRecentReading(@RequestParam(defaultValue = "5") Integer limit) {
        Long userId = StpUtil.getLoginIdAsLong();
        List<BookReadingRecord> records = bookReadingRecordService.queryRecentReading(userId, limit);
        return R.ok(records);
    }

    /**
     * 查询用户阅读统计
     *
     * @return 阅读统计数据
     */
    @GetMapping("/reading-stats")
    public R<Map<String, Object>> getReadingStats() {
        Long userId = StpUtil.getLoginIdAsLong();
        Map<String, Object> stats = bookReadingRecordService.queryUserReadingStats(userId);
        return R.ok(stats);
    }

    /**
     * 增加阅读时长
     *
     * @param bookId      书籍ID
     * @param readingTime 阅读时长（分钟）
     * @return 操作结果
     */
    @PostMapping("/{bookId}/add-reading-time")
    public R<Void> addReadingTime(@PathVariable Long bookId,
                                  @RequestParam Integer readingTime) {
        Long userId = StpUtil.getLoginIdAsLong();
        boolean success = bookReadingRecordService.addReadingTime(userId, bookId, readingTime);
        return success ? R.ok() : R.fail("记录阅读时长失败");
    }
}
