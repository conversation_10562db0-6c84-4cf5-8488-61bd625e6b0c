{"doc": " 用户活动总览对象 app_activity_summary\n\n <AUTHOR>\n @date 2025-07-16\n", "fields": [{"name": "id", "doc": " 总览ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "totalDuration", "doc": " 总活动时长(毫秒)\n"}, {"name": "totalSessions", "doc": " 总会话数\n"}, {"name": "courseDuration", "doc": " 课程学习时长(毫秒)\n"}, {"name": "interviewDuration", "doc": " 面试练习时长(毫秒)\n"}, {"name": "bookDuration", "doc": " 书籍阅读时长(毫秒)\n"}, {"name": "videoDuration", "doc": " 视频学习时长(毫秒)\n"}, {"name": "exerciseDuration", "doc": " 习题练习时长(毫秒)\n"}, {"name": "documentDuration", "doc": " 文档阅读时长(毫秒)\n"}, {"name": "otherDuration", "doc": " 其他活动时长(毫秒)\n"}, {"name": "lastActivityTime", "doc": " 最后活动时间\n"}], "enumConstants": [], "methods": [{"name": "initialize", "paramTypes": ["java.lang.Long"], "doc": " 初始化用户活动总览\n\n @param userId 用户ID\n @return 初始化的总览对象\n"}, {"name": "updateDuration", "paramTypes": ["org.dromara.app.domain.enums.ActivityType", "java.lang.Long"], "doc": " 更新活动时长\n\n @param activityType 活动类型\n @param duration     时长(毫秒)\n"}, {"name": "getDurationByType", "paramTypes": ["org.dromara.app.domain.enums.ActivityType"], "doc": " 获取指定类型的活动时长\n\n @param activityType 活动类型\n @return 时长(毫秒)\n"}], "constructors": []}