{"doc": "\n 用户简历服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryPageList", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo", "<PERSON><PERSON><PERSON><PERSON>"], "doc": "\n 查询用户简历分页列表\r\n\r\n @param bo        查询条件\r\n @param pageQuery 分页参数\r\n @return 分页结果\r\n"}, {"name": "selectUserResumeList", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户的所有简历列表\r\n\r\n @param userId 用户ID\r\n @return 简历列表\r\n"}, {"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": "\n 根据简历ID查询简历详情\r\n\r\n @param resumeId 简历ID\r\n @return 简历详情\r\n"}, {"name": "uploadResume", "paramTypes": ["java.lang.Long", "org.springframework.web.multipart.MultipartFile", "java.lang.String", "java.lang.String"], "doc": "\n 上传用户简历文件\r\n\r\n @param userId   用户ID\r\n @param file     简历文件\r\n @param fileName 原始文件名（可选）\r\n @param fileType 文件类型（可选）\r\n @return 上传结果\r\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo"], "doc": "\n 新增用户简历\r\n\r\n @param bo 简历信息\r\n @return 新增结果\r\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo"], "doc": "\n 修改用户简历\r\n\r\n @param bo 简历信息\r\n @return 修改结果\r\n"}, {"name": "renameResume", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 重命名简历\r\n\r\n @param resumeId   简历ID\r\n @param resumeName 新名称\r\n @return 重命名结果\r\n"}, {"name": "setDefaultResume", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 设置默认简历\r\n\r\n @param userId   用户ID\r\n @param resumeId 简历ID\r\n @return 设置结果\r\n"}, {"name": "cancelDefaultResume", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 取消默认简历\r\n\r\n @param userId   用户ID\r\n @param resumeId 简历ID\r\n @return 取消结果\r\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": "\n 校验并批量删除用户简历信息\r\n\r\n @param ids     主键集合\r\n @param isValid 是否校验,true-删除前校验,false-不校验\r\n @return 删除结果\r\n"}, {"name": "downloadResume", "paramTypes": ["java.lang.Long", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 下载用户简历文件\r\n\r\n @param resumeId 简历ID\r\n @param response 响应对象\r\n @throws IOException IO异常\r\n"}, {"name": "getDefaultResume", "paramTypes": ["java.lang.Long"], "doc": "\n 获取用户的默认简历\r\n\r\n @param userId 用户ID\r\n @return 默认简历\r\n"}, {"name": "previewResumeContent", "paramTypes": ["java.lang.Long"], "doc": "\n 预览简历文件内容\r\n\r\n @param resumeId 简历ID\r\n @return 预览内容数据（包含content和type字段）\r\n @throws Exception 预览异常\r\n"}, {"name": "getStructuredResumeContent", "paramTypes": ["java.lang.Long"], "doc": "\n 获取简历结构化预览内容\r\n\r\n @param resumeId 简历ID\r\n @return 结构化简历内容数据\r\n @throws Exception 解析异常\r\n"}, {"name": "generateResumePreviewImage", "paramTypes": ["java.lang.Long"], "doc": "\n 生成简历预览图片\r\n\r\n @param resumeId 简历ID\r\n @return 预览图片数据（包含imageUrl和thumbnailUrl字段）\r\n @throws Exception 生成异常\r\n"}], "constructors": []}