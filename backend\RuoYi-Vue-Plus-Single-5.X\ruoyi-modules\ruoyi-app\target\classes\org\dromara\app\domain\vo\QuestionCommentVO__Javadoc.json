{"doc": "\n 题目评论VO\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 评论ID\r\n"}, {"name": "questionId", "doc": "\n 题目ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "author", "doc": "\n 用户昵称\r\n"}, {"name": "avatar", "doc": "\n 用户头像\r\n"}, {"name": "content", "doc": "\n 评论内容\r\n"}, {"name": "likes", "doc": "\n 点赞数\r\n"}, {"name": "replyCount", "doc": "\n 回复数\r\n"}, {"name": "parentId", "doc": "\n 父评论ID\r\n"}, {"name": "replies", "doc": "\n 回复列表\r\n"}, {"name": "time", "doc": "\n 发表时间\r\n"}, {"name": "liked", "doc": "\n 是否点赞(true:已点赞,false:未点赞)\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}], "enumConstants": [], "methods": [], "constructors": []}