package org.dromara.common.caffeine.annotation;

/**
 * 时间单位枚举
 *
 * <AUTHOR>
 */
public enum TimeUnit {

    /**
     * 秒
     */
    SECONDS(1),

    /**
     * 分钟
     */
    MINUTES(60),

    /**
     * 小时
     */
    HOURS(3600),

    /**
     * 天
     */
    DAYS(86400);

    private final long seconds;

    TimeUnit(long seconds) {
        this.seconds = seconds;
    }

    /**
     * 转换为秒
     *
     * @param duration 时长
     * @return 秒数
     */
    public long toSeconds(long duration) {
        return duration * seconds;
    }

}
