package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__162;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.mybatis.core.domain.QuestionComment;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__162.class,
    uses = {},
    imports = {}
)
public interface QuestionCommentBoToQuestionCommentMapper extends BaseMapper<QuestionCommentBo, QuestionComment> {
}
