package org.dromara.app.controller.avatar;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.dto.avatar.AvatarStartDto;
import org.dromara.app.domain.dto.avatar.AvatarTextDto;
import org.dromara.app.domain.vo.avatar.AvatarSessionVo;
import org.dromara.app.service.IXunfeiAvatarService;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 讯飞数字人控制器
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/avatar")
@Tag(name = "讯飞数字人", description = "讯飞数字人相关接口")
public class XunfeiAvatarController extends BaseController {

    private final IXunfeiAvatarService avatarService;

    /**
     * 启动数字人会话
     */
    @Operation(summary = "启动数字人会话")
    @Log(title = "数字人", businessType = BusinessType.INSERT)
    @PostMapping("/start")
    public R<AvatarSessionVo> startSession(@Valid @RequestBody AvatarStartDto startDto) {
        try {
            AvatarSessionVo sessionVo = avatarService.startSession(startDto);
            return R.ok("数字人会话启动成功", sessionVo);
        } catch (Exception e) {
            log.error("启动数字人会话失败", e);
            return R.fail("启动数字人会话失败: " + e.getMessage());
        }
    }

    /**
     * 发送文本驱动
     */
    @Operation(summary = "发送文本驱动")
    @Log(title = "数字人", businessType = BusinessType.UPDATE)
    @PostMapping("/text-driver/{sessionId}")
    public R<Void> sendTextDriver(
            @Parameter(description = "会话ID", required = true) @PathVariable @NotBlank String sessionId,
            @Valid @RequestBody AvatarTextDto textDto) {

        boolean success = avatarService.sendTextDriver(sessionId, textDto);
        return toAjax(success);
    }

    /**
     * 发送文本交互
     */
    @Operation(summary = "发送文本交互")
    @Log(title = "数字人", businessType = BusinessType.UPDATE)
    @PostMapping("/text-interact/{sessionId}")
    public R<Void> sendTextInteract(
            @Parameter(description = "会话ID", required = true) @PathVariable @NotBlank String sessionId,
            @Valid @RequestBody AvatarTextDto textDto) {

        boolean success = avatarService.sendTextInteract(sessionId, textDto);
        return toAjax(success);
    }

    /**
     * 发送音频驱动
     */
    @Operation(summary = "发送音频驱动")
    @Log(title = "数字人", businessType = BusinessType.UPDATE)
    @PostMapping("/audio-driver/{sessionId}")
    public R<Void> sendAudioDriver(
            @Parameter(description = "会话ID", required = true) @PathVariable @NotBlank String sessionId,
            @Parameter(description = "音频数据(Base64)", required = true) @RequestParam @NotBlank String audioData,
            @Parameter(description = "数据状态(0开始,1过渡,2结束)", required = true) @RequestParam int status) {

        boolean success = avatarService.sendAudioDriver(sessionId, audioData, status);
        return toAjax(success);
    }

    /**
     * 重置数字人
     */
    @Operation(summary = "重置数字人")
    @Log(title = "数字人", businessType = BusinessType.UPDATE)
    @PostMapping("/reset/{sessionId}")
    public R<Void> resetAvatar(
            @Parameter(description = "会话ID", required = true) @PathVariable @NotBlank String sessionId) {

        boolean success = avatarService.resetAvatar(sessionId);
        return toAjax(success);
    }

    /**
     * 停止数字人会话
     */
    @Operation(summary = "停止数字人会话")
    @Log(title = "数字人", businessType = BusinessType.DELETE)
    @PostMapping("/stop/{sessionId}")
    public R<Void> stopSession(
            @Parameter(description = "会话ID", required = true) @PathVariable @NotBlank String sessionId) {

        boolean success = avatarService.stopSession(sessionId);
        return toAjax(success);
    }

    /**
     * 发送心跳
     */
    @Operation(summary = "发送心跳")
    @PostMapping("/heartbeat/{sessionId}")
    public R<Void> sendHeartbeat(
            @Parameter(description = "会话ID", required = true) @PathVariable @NotBlank String sessionId) {

        boolean success = avatarService.sendHeartbeat(sessionId);
        return toAjax(success);
    }

    /**
     * 发送动作指令
     */
    @Operation(summary = "发送动作指令")
    @Log(title = "数字人", businessType = BusinessType.UPDATE)
    @PostMapping("/action/{sessionId}")
    public R<Void> sendAction(
            @Parameter(description = "会话ID", required = true) @PathVariable @NotBlank String sessionId,
            @Parameter(description = "动作类型", required = true) @RequestParam @NotBlank String actionType,
            @Parameter(description = "动作值", required = true) @RequestParam @NotBlank String actionValue) {

        boolean success = avatarService.sendAction(sessionId, actionType, actionValue);
        return toAjax(success);
    }

    /**
     * 获取会话信息
     */
    @Operation(summary = "获取会话信息")
    @GetMapping("/session/{sessionId}")
    public R<AvatarSessionVo> getSessionInfo(
            @Parameter(description = "会话ID", required = true) @PathVariable @NotBlank String sessionId) {

        AvatarSessionVo sessionVo = avatarService.getSessionInfo(sessionId);
        if (sessionVo == null) {
            return R.fail("会话不存在");
        }
        return R.ok(sessionVo);
    }

    /**
     * 检查会话状态
     */
    @Operation(summary = "检查会话状态")
    @GetMapping("/status/{sessionId}")
    public R<Boolean> checkSessionStatus(
            @Parameter(description = "会话ID", required = true) @PathVariable @NotBlank String sessionId) {

        boolean active = avatarService.isSessionActive(sessionId);
        return R.ok("会话状态", active);
    }

    /**
     * 获取推流地址
     */
    @Operation(summary = "获取推流地址")
    @GetMapping("/stream-url/{sessionId}")
    public R<String> getStreamUrl(
            @Parameter(description = "会话ID", required = true) @PathVariable @NotBlank String sessionId) {

        // 先尝试更新推流地址
        avatarService.updateStreamUrl(sessionId);

        AvatarSessionVo sessionVo = avatarService.getSessionInfo(sessionId);
        if (sessionVo == null) {
            return R.fail("会话不存在");
        }

        String streamUrl = sessionVo.getStreamUrl();
        if (streamUrl == null || streamUrl.isEmpty()) {
            return R.fail("推流地址尚未获取，请稍后重试");
        }

        return R.ok("推流地址获取成功", streamUrl);
    }

    /**
     * 检查推流地址状态
     */
    @Operation(summary = "检查推流地址状态")
    @GetMapping("/stream-status/{sessionId}")
    public R<Map<String, Object>> getStreamStatus(
            @Parameter(description = "会话ID", required = true) @PathVariable @NotBlank String sessionId) {

        AvatarSessionVo sessionVo = avatarService.getSessionInfo(sessionId);
        if (sessionVo == null) {
            return R.fail("会话不存在");
        }

        Map<String, Object> status = new HashMap<>();
        status.put("sessionId", sessionId);
        status.put("connected", sessionVo.getConnected());
        status.put("hasStreamUrl", sessionVo.getStreamUrl() != null && !sessionVo.getStreamUrl().isEmpty());
        status.put("streamUrl", sessionVo.getStreamUrl());
        status.put("createTime", sessionVo.getCreateTime());
        status.put("lastActiveTime", sessionVo.getLastActiveTime());
        status.put("statusDesc", sessionVo.getStatusDesc());

        return R.ok("状态查询成功", status);
    }

    /**
     * 更新推流地址
     */
    @Operation(summary = "更新推流地址")
    @PostMapping("/update-stream-url/{sessionId}")
    public R<Void> updateStreamUrl(
            @Parameter(description = "会话ID", required = true) @PathVariable @NotBlank String sessionId) {

        boolean updated = avatarService.updateStreamUrl(sessionId);
        if (updated) {
            return R.ok("推流地址已更新");
        } else {
            return R.fail("推流地址更新失败或无变化");
        }
    }
}
