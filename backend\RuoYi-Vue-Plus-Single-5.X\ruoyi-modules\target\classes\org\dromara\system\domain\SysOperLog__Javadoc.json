{"doc": " 操作日志记录表 oper_log\n\n <AUTHOR>\n", "fields": [{"name": "operId", "doc": " 日志主键\n"}, {"name": "title", "doc": " 操作模块\n"}, {"name": "businessType", "doc": " 业务类型（0其它 1新增 2修改 3删除）\n"}, {"name": "method", "doc": " 请求方法\n"}, {"name": "requestMethod", "doc": " 请求方式\n"}, {"name": "operatorType", "doc": " 操作类别（0其它 1后台用户 2手机端用户）\n"}, {"name": "operName", "doc": " 操作人员\n"}, {"name": "deptName", "doc": " 部门名称\n"}, {"name": "operUrl", "doc": " 请求url\n"}, {"name": "operIp", "doc": " 操作地址\n"}, {"name": "operLocation", "doc": " 操作地点\n"}, {"name": "operParam", "doc": " 请求参数\n"}, {"name": "jsonResult", "doc": " 返回参数\n"}, {"name": "status", "doc": " 操作状态（0正常 1异常）\n"}, {"name": "errorMsg", "doc": " 错误消息\n"}, {"name": "operTime", "doc": " 操作时间\n"}, {"name": "costTime", "doc": " 消耗时间\n"}], "enumConstants": [], "methods": [], "constructors": []}