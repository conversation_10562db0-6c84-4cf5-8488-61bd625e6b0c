{"doc": " 面试问题Service接口\n\n <AUTHOR> Assistant\n @date 2025-07-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByJobId", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": " 根据岗位ID查询问题列表\n\n @param jobId      岗位ID\n @param difficulty 难度等级\n @param limit      限制数量\n @return 问题列表\n"}, {"name": "selectByTechnicalDomain", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": " 根据技术领域查询问题\n\n @param technicalDomain 技术领域\n @param questionType    问题类型\n @param limit          限制数量\n @return 问题列表\n"}, {"name": "selectMultimodalQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询多模态问题\n\n @param jobId 岗位ID\n @param limit 限制数量\n @return 多模态问题列表\n"}, {"name": "selectByTags", "paramTypes": ["java.util.List", "java.lang.Integer"], "doc": " 根据标签查询问题\n\n @param tags  标签列表\n @param limit 限制数量\n @return 问题列表\n"}, {"name": "selectQuestionPage", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.String"], "doc": " 分页查询问题列表\n\n @param page           分页参数\n @param jobId          岗位ID\n @param questionType   问题类型\n @param difficulty     难度等级\n @param category       问题分类\n @return 分页结果\n"}, {"name": "getQuestionsByDifficulty", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer", "java.lang.Integer"], "doc": " 根据难度分级获取问题\n\n @param jobId      岗位ID\n @param easyCount  简单题数量\n @param mediumCount 中等题数量\n @param hardCount  困难题数量\n @return 分级问题列表\n"}], "constructors": []}