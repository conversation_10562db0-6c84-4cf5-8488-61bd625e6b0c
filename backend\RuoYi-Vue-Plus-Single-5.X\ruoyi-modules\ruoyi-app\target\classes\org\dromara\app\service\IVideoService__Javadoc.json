{"doc": "\n 视频课程Service接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryVideoList", "paramTypes": ["org.dromara.app.domain.bo.VideoQueryBo"], "doc": "\n 查询视频课程列表\r\n\r\n @param bo        查询条件\r\n @param pageQuery 分页查询条件\r\n @return 视频列表\r\n"}, {"name": "getVideoDetail", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 获取视频详情\r\n\r\n @param videoId 视频ID\r\n @param userId  用户ID\r\n @return 视频详情\r\n"}, {"name": "getLearningStats", "paramTypes": ["java.lang.Long"], "doc": "\n 获取学习统计数据\r\n\r\n @param userId 用户ID\r\n @return 学习统计\r\n"}, {"name": "getBookmarkedVideos", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.bo.VideoQueryBo", "<PERSON><PERSON><PERSON><PERSON>"], "doc": "\n 获取收藏的视频\r\n\r\n @param userId    用户ID\r\n @param bo        查询条件\r\n @param pageQuery 分页查询条件\r\n @return 收藏视频列表\r\n"}, {"name": "getPurchasedVideos", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.bo.VideoQueryBo", "<PERSON><PERSON><PERSON><PERSON>"], "doc": "\n 获取已购买的视频\r\n\r\n @param userId    用户ID\r\n @param bo        查询条件\r\n @param pageQuery 分页查询条件\r\n @return 已购买视频列表\r\n"}, {"name": "getLearningHistory", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.bo.VideoQueryBo", "<PERSON><PERSON><PERSON><PERSON>"], "doc": "\n 获取学习历史\r\n\r\n @param userId    用户ID\r\n @param bo        查询条件\r\n @param pageQuery 分页查询条件\r\n @return 学习历史列表\r\n"}, {"name": "getRelatedVideos", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 获取相关推荐视频\r\n\r\n @param videoId 视频ID\r\n @param limit   限制数量\r\n @return 相关推荐视频列表\r\n"}, {"name": "toggleVideoLike", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": "\n 切换视频点赞状态\r\n\r\n @param videoId 视频ID\r\n @param userId  用户ID\r\n @param isLike  是否点赞\r\n"}, {"name": "toggleVideoCollect", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": "\n 切换视频收藏状态\r\n\r\n @param videoId   视频ID\r\n @param userId    用户ID\r\n @param isCollect 是否收藏\r\n"}, {"name": "shareVideo", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.String"], "doc": "\n 分享视频\r\n\r\n @param videoId  视频ID\r\n @param userId   用户ID\r\n @param platform 分享平台\r\n"}, {"name": "incrementVideoView", "paramTypes": ["java.lang.Long"], "doc": "\n 增加视频播放次数\r\n\r\n @param videoId 视频ID\r\n"}, {"name": "updateVideoProgress", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 更新视频播放进度\r\n\r\n @param videoId     视频ID\r\n @param userId      用户ID\r\n @param currentTime 当前播放时间\r\n @param duration    视频总时长\r\n"}, {"name": "getVideoPlayRecord", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 获取视频播放记录\r\n\r\n @param videoId 视频ID\r\n @param userId  用户ID\r\n @return 播放记录\r\n"}, {"name": "checkVideoPurchaseStatus", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 检查视频购买状态\r\n\r\n @param videoId 视频ID\r\n @param userId  用户ID\r\n @return 购买状态\r\n"}, {"name": "toggleInstructorFollow", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": "\n 关注/取关讲师\r\n\r\n @param instructorId 讲师ID\r\n @param userId       用户ID\r\n @param isFollow     是否关注\r\n"}, {"name": "getVideoComments", "paramTypes": ["org.dromara.app.domain.bo.CommentQueryBo", "<PERSON><PERSON><PERSON><PERSON>"], "doc": "\n 获取视频评论列表\r\n\r\n @param bo        查询条件\r\n @param pageQuery 分页查询条件\r\n @return 评论列表\r\n"}, {"name": "publishVideoComment", "paramTypes": ["org.dromara.app.domain.bo.VideoCommentBo"], "doc": "\n 发布视频评论\r\n\r\n @param bo 评论信息\r\n @return 评论详情\r\n"}, {"name": "toggleCommentLike", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": "\n 切换评论点赞状态\r\n\r\n @param commentId 评论ID\r\n @param userId    用户ID\r\n @param isLike    是否点赞\r\n"}, {"name": "saveVideoPlayRecord", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 保存视频播放记录\r\n\r\n @param videoId 视频ID\r\n @param userId  用户ID\r\n"}, {"name": "getHotVideos", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取热门视频列表\r\n\r\n @param limit 限制数量\r\n @return 热门视频列表\r\n"}], "constructors": []}