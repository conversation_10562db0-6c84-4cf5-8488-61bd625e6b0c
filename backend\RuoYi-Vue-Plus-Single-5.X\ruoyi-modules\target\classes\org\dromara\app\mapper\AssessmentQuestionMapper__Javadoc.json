{"doc": " 评估问题Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectQuestionsWithOptions", "paramTypes": [], "doc": " 查询评估问题列表（包含选项）\n\n @return 评估问题列表\n"}, {"name": "selectQuestionsWithOptionsByStatus", "paramTypes": ["java.lang.String"], "doc": " 根据状态查询评估问题列表（包含选项）\n\n @param status 状态\n @return 评估问题列表\n"}, {"name": "selectQuestionWithOptionsById", "paramTypes": ["java.lang.Long"], "doc": " 根据问题ID查询评估问题（包含选项）\n\n @param questionId 问题ID\n @return 评估问题\n"}, {"name": "selectQuestionWithOptionsByCode", "paramTypes": ["java.lang.String"], "doc": " 根据问题编码查询评估问题（包含选项）\n\n @param questionCode 问题编码\n @return 评估问题\n"}], "constructors": []}