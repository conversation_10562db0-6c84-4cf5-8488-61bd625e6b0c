package org.dromara.common.pay.constant;

/**
 * 支付相关常量
 *
 * <AUTHOR>
 */
public class PayConstants {

    /**
     * 支付宝相关常量
     */
    public static class Alipay {
        /**
         * 支付宝交易状态 - 交易创建，等待买家付款
         */
        public static final String TRADE_STATUS_WAIT_BUYER_PAY = "WAIT_BUYER_PAY";

        /**
         * 支付宝交易状态 - 未付款交易超时关闭，或支付完成后全额退款
         */
        public static final String TRADE_STATUS_TRADE_CLOSED = "TRADE_CLOSED";

        /**
         * 支付宝交易状态 - 交易支付成功
         */
        public static final String TRADE_STATUS_TRADE_SUCCESS = "TRADE_SUCCESS";

        /**
         * 支付宝交易状态 - 交易结束，不可退款
         */
        public static final String TRADE_STATUS_TRADE_FINISHED = "TRADE_FINISHED";

        /**
         * 支付宝产品码 - 快捷支付产品
         */
        public static final String PRODUCT_CODE_FAST_INSTANT_TRADE_PAY = "FAST_INSTANT_TRADE_PAY";

        /**
         * 支付宝产品码 - 手机网站支付产品
         */
        public static final String PRODUCT_CODE_QUICK_WAP_WAY = "QUICK_WAP_WAY";

        /**
         * 支付宝产品码 - APP支付产品
         */
        public static final String PRODUCT_CODE_QUICK_MSECURITY_PAY = "QUICK_MSECURITY_PAY";

        /**
         * 支付宝异步通知验证成功
         */
        public static final String NOTIFY_VERIFY_SUCCESS = "success";

        /**
         * 支付宝异步通知验证失败
         */
        public static final String NOTIFY_VERIFY_FAILURE = "failure";
    }

    /**
     * 微信支付相关常量
     */
    public static class Wechat {
        /**
         * 微信支付交易状态 - 支付成功
         */
        public static final String TRADE_STATE_SUCCESS = "SUCCESS";

        /**
         * 微信支付交易状态 - 转入退款
         */
        public static final String TRADE_STATE_REFUND = "REFUND";

        /**
         * 微信支付交易状态 - 未支付
         */
        public static final String TRADE_STATE_NOTPAY = "NOTPAY";

        /**
         * 微信支付交易状态 - 已关闭
         */
        public static final String TRADE_STATE_CLOSED = "CLOSED";

        /**
         * 微信支付交易状态 - 已撤销（付款码支付）
         */
        public static final String TRADE_STATE_REVOKED = "REVOKED";

        /**
         * 微信支付交易状态 - 用户支付中（付款码支付）
         */
        public static final String TRADE_STATE_USERPAYING = "USERPAYING";

        /**
         * 微信支付交易状态 - 支付失败（其他原因，如银行返回失败）
         */
        public static final String TRADE_STATE_PAYERROR = "PAYERROR";
    }

    /**
     * 通用支付常量
     */
    public static class Common {
        /**
         * 订单超时时间（分钟）
         */
        public static final int ORDER_TIMEOUT_MINUTES = 30;

        /**
         * 订单号前缀
         */
        public static final String ORDER_NO_PREFIX = "PAY";

        /**
         * 支付金额最小值（分）
         */
        public static final int MIN_AMOUNT_CENT = 1;

        /**
         * 支付金额最大值（分）
         */
        public static final int MAX_AMOUNT_CENT = 100000000; // 1000万分 = 10万元

        /**
         * 默认货币类型
         */
        public static final String DEFAULT_CURRENCY = "CNY";

        /**
         * 异步通知重试次数
         */
        public static final int NOTIFY_RETRY_COUNT = 3;

        /**
         * 异步通知重试间隔（秒）
         */
        public static final int NOTIFY_RETRY_INTERVAL = 60;
    }
}
