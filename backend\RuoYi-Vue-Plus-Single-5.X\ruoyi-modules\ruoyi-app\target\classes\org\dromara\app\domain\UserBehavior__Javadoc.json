{"doc": "\n 用户行为记录对象 app_user_behavior\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 主键ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "behaviorType", "doc": "\n 行为类型\r\n"}, {"name": "behaviorData", "doc": "\n 行为数据(JSON格式)\r\n"}, {"name": "ip<PERSON><PERSON><PERSON>", "doc": "\n IP地址\r\n"}, {"name": "userAgent", "doc": "\n 用户代理\r\n"}, {"name": "sessionId", "doc": "\n 会话ID\r\n"}], "enumConstants": [], "methods": [], "constructors": []}