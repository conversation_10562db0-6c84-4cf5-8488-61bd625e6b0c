package org.dromara.common.pay.config;

import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import lombok.Data;
import org.dromara.common.pay.properties.AlipayProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 支付宝支付配置类
 *
 * <AUTHOR>
 */
@Data
@Configuration
@EnableConfigurationProperties(AlipayProperties.class)
@ConditionalOnProperty(prefix = "pay.alipay", name = "enabled", havingValue = "true", matchIfMissing = false)
public class AlipayConfig {

    /**
     * 创建支付宝客户端
     *
     * @param alipayProperties 支付宝配置属性
     * @return AlipayClient 支付宝客户端
     */
    @Bean
    public AlipayClient alipayClient(AlipayProperties alipayProperties) {
        return new DefaultAlipayClient(
            alipayProperties.getGatewayUrl(),
            alipayProperties.getAppId(),
            alipayProperties.getPrivateKey(),
            alipayProperties.getFormat(),
            alipayProperties.getCharset(),
            alipayProperties.getAlipayPublicKey(),
            alipayProperties.getSignType()
        );
    }
}
