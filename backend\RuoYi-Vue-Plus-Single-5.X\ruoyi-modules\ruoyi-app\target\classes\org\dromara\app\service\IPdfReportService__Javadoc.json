{"doc": "\n PDF报告生成服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "generatePdfReport", "paramTypes": ["org.dromara.app.domain.InterviewReport"], "doc": "\n 生成PDF报告\r\n\r\n @param report 面试报告\r\n @return PDF文件路径\r\n"}, {"name": "generatePdfReportStream", "paramTypes": ["org.dromara.app.domain.InterviewReport"], "doc": "\n 生成PDF报告流\r\n\r\n @param report 面试报告\r\n @return PDF文件流\r\n"}, {"name": "generatePdfReportBytes", "paramTypes": ["org.dromara.app.domain.InterviewReport"], "doc": "\n 生成PDF报告字节数组\r\n\r\n @param report 面试报告\r\n @return PDF字节数组\r\n"}, {"name": "isPdfFileExists", "paramTypes": ["java.lang.String"], "doc": "\n 验证PDF文件是否存在\r\n\r\n @param filePath 文件路径\r\n @return 是否存在\r\n"}, {"name": "deletePdfFile", "paramTypes": ["java.lang.String"], "doc": "\n 删除PDF文件\r\n\r\n @param filePath 文件路径\r\n @return 是否删除成功\r\n"}, {"name": "getPdfFileSize", "paramTypes": ["java.lang.String"], "doc": "\n 获取PDF文件大小\r\n\r\n @param filePath 文件路径\r\n @return 文件大小（字节）\r\n"}], "constructors": []}