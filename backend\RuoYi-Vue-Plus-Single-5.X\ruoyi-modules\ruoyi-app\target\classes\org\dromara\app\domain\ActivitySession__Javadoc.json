{"doc": "\n 用户活动会话记录对象 app_activity_session\r\n\r\n <AUTHOR>\r\n @date 2025-07-16\r\n", "fields": [{"name": "id", "doc": "\n 会话ID\r\n"}, {"name": "sessionId", "doc": "\n 会话唯一标识符\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "activityType", "doc": "\n 活动类型\r\n"}, {"name": "activityId", "doc": "\n 活动对象ID(如题目ID、课程ID等)\r\n"}, {"name": "activityName", "doc": "\n 活动名称\r\n"}, {"name": "categoryId", "doc": "\n 分类ID(如题库ID、课程分类ID等)\r\n"}, {"name": "categoryName", "doc": "\n 分类名称\r\n"}, {"name": "startTime", "doc": "\n 开始时间\r\n"}, {"name": "endTime", "doc": "\n 结束时间\r\n"}, {"name": "duration", "doc": "\n 持续时长(毫秒)\r\n"}, {"name": "isActive", "doc": "\n 是否活跃(1:活跃 0:已结束)\r\n"}, {"name": "metadata", "doc": "\n 额外元数据(JSON格式)\r\n"}], "enumConstants": [], "methods": [{"name": "calculateDuration", "paramTypes": [], "doc": "\n 计算持续时长\r\n 如果会话还在进行中，计算到当前时间的时长\r\n 如果会话已结束，返回记录的时长\r\n\r\n @return 持续时长(毫秒)\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": [], "doc": "\n 检查会话是否有效\r\n\r\n @return 是否有效\r\n"}, {"name": "endSession", "paramTypes": [], "doc": "\n 结束会话\r\n"}, {"name": "pauseSession", "paramTypes": [], "doc": "\n 暂停会话\r\n"}, {"name": "resumeSession", "paramTypes": [], "doc": "\n 恢复会话\r\n"}], "constructors": []}