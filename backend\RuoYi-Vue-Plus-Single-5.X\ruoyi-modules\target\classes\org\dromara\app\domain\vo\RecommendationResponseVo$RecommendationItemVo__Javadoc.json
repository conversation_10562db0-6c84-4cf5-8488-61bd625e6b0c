{"doc": " 推荐项VO\n", "fields": [{"name": "id", "doc": " 资源ID\n"}, {"name": "title", "doc": " 资源标题\n"}, {"name": "description", "doc": " 资源描述\n"}, {"name": "type", "doc": " 资源类型（video/question-bank/book）\n"}, {"name": "difficulty", "doc": " 难度等级\n"}, {"name": "target", "doc": " 目标能力\n"}, {"name": "targetCapability", "doc": " 目标能力代码\n"}, {"name": "improvementPoints", "doc": " 预期提升点数\n"}, {"name": "priority", "doc": " 推荐优先级（high/medium/low）\n"}, {"name": "priorityScore", "doc": " 推荐优先级分数\n"}, {"name": "category", "doc": " 分类\n"}, {"name": "tags", "doc": " 标签列表\n"}, {"name": "rating", "doc": " 评分\n"}, {"name": "cover", "doc": " 封面图片\n"}, {"name": "isBookmarked", "doc": " 是否已收藏\n"}, {"name": "recommendReason", "doc": " 推荐原因\n"}, {"name": "duration", "doc": " 时长\n"}, {"name": "instructor", "doc": " 讲师\n"}, {"name": "studentCount", "doc": " 学习人数\n"}, {"name": "price", "doc": " 价格\n"}, {"name": "originalPrice", "doc": " 原价\n"}, {"name": "questionCount", "doc": " 题目数量\n"}, {"name": "completedCount", "doc": " 已完成数量\n"}, {"name": "progress", "doc": " 完成进度\n"}, {"name": "estimatedTime", "doc": " 预估时间\n"}, {"name": "author", "doc": " 作者\n"}, {"name": "pageCount", "doc": " 页数\n"}, {"name": "readingProgress", "doc": " 阅读进度\n"}, {"name": "publisher", "doc": " 出版社\n"}, {"name": "readerCount", "doc": " 阅读人数\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}], "enumConstants": [], "methods": [], "constructors": []}