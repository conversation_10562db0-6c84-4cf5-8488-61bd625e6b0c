package org.dromara.app.domain.dto;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.io.Serializable;

/**
 * 题库查询参数
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class QuestionBankQueryDto extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 专业ID
     */
    private String majorId;

    /**
     * 专业名称（用于显示）
     */
    private String majorName;

    /**
     * 筛选条件（全部/按难度/收藏状态/完成状态）
     */
    @Pattern(regexp = "^(all|easy|medium|hard|bookmarked|completed|hot|new)$", message = "筛选条件不合法")
    private String filter = "all";

    /**
     * 搜索关键词
     */
    private String keyword;

    /**
     * 排序类型
     */
    @Pattern(regexp = "^(default|difficulty|progress|popularity|latest)$", message = "排序类型不合法")
    private String sortType = "default";

    /**
     * 当前页码
     */
    @Min(value = 1, message = "页码最小值为1")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小最小值为1")
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向（asc/desc）
     */
    @Pattern(regexp = "^(asc|desc)$", message = "排序方向不合法")
    private String orderDirection = "desc";
}
