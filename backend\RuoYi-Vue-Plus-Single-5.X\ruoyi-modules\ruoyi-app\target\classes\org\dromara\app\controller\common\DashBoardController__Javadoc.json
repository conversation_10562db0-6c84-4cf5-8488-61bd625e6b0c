{"doc": "\n 首页仪表盘控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getDashboardSummary", "paramTypes": [], "doc": "\n 获取首页仪表盘汇总数据\r\n"}, {"name": "getUserAbilities", "paramTypes": [], "doc": "\n 获取用户能力评估数据\r\n"}, {"name": "getStudyStats", "paramTypes": [], "doc": "\n 获取学习统计数据\r\n"}, {"name": "getSmartTasks", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": "\n 获取智能推荐任务\r\n"}, {"name": "getRecentInterviews", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": "\n 获取最近面试记录\r\n"}, {"name": "updateTargetPosition", "paramTypes": ["java.util.Map"], "doc": "\n 更新用户目标岗位\r\n"}, {"name": "completeTask", "paramTypes": ["java.util.Map"], "doc": "\n 标记任务为已完成\r\n"}, {"name": "getDashboardData", "paramTypes": [], "doc": "\n 获取首页所有数据（聚合接口）\r\n"}], "constructors": []}