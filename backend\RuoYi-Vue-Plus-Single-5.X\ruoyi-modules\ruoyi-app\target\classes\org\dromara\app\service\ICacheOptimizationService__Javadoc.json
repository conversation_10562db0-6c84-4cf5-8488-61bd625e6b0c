{"doc": "\n 缓存优化服务接口\r\n 用于优化系统缓存策略和性能\r\n\r\n <AUTHOR>\r\n @date 2025-01-19\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "warmupCache", "paramTypes": [], "doc": "\n 预热常用数据缓存\r\n"}, {"name": "cleanExpiredCache", "paramTypes": [], "doc": "\n 清理过期缓存\r\n\r\n @return 清理的缓存数量\r\n"}, {"name": "getCacheStatistics", "paramTypes": [], "doc": "\n 获取缓存统计信息\r\n\r\n @return 缓存统计\r\n"}, {"name": "refreshCache", "paramTypes": ["java.lang.String"], "doc": "\n 刷新指定缓存\r\n\r\n @param cacheKey 缓存键\r\n @return 是否刷新成功\r\n"}, {"name": "batchRefreshCache", "paramTypes": ["java.util.List"], "doc": "\n 批量刷新缓存\r\n\r\n @param cacheKeys 缓存键列表\r\n @return 刷新成功的数量\r\n"}, {"name": "getHotData", "paramTypes": ["int"], "doc": "\n 获取热点数据\r\n\r\n @param limit 限制数量\r\n @return 热点数据键值对\r\n"}, {"name": "setCacheWarmupStrategy", "paramTypes": ["java.lang.String"], "doc": "\n 设置缓存预热策略\r\n\r\n @param strategy 预热策略\r\n"}, {"name": "optimizeCacheConfiguration", "paramTypes": [], "doc": "\n 优化缓存配置\r\n"}, {"name": "monitorCachePerformance", "paramTypes": [], "doc": "\n 监控缓存性能\r\n\r\n @return 性能指标\r\n"}, {"name": "analyzeCacheHitRate", "paramTypes": [], "doc": "\n 分析缓存命中率\r\n\r\n @return 命中率分析结果\r\n"}, {"name": "cleanLowFrequencyCache", "paramTypes": ["int"], "doc": "\n 清理低频访问缓存\r\n\r\n @param threshold 访问频率阈值\r\n @return 清理的缓存数量\r\n"}, {"name": "compressCacheData", "paramTypes": [], "doc": "\n 压缩缓存数据\r\n\r\n @return 压缩节省的空间大小（字节）\r\n"}], "constructors": []}