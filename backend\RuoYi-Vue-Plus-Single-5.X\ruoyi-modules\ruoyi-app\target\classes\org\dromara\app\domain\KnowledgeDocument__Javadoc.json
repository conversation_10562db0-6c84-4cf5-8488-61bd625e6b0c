{"doc": "\n 知识库文档实体\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 文档ID\r\n"}, {"name": "knowledgeBaseId", "doc": "\n 知识库ID\r\n"}, {"name": "title", "doc": "\n 文档标题\r\n"}, {"name": "content", "doc": "\n 文档内容\r\n"}, {"name": "docType", "doc": "\n 文档类型 (text/pdf/word/markdown/etc.)\r\n"}, {"name": "source", "doc": "\n 文档来源 (upload/url/api/etc.)\r\n"}, {"name": "originalFilename", "doc": "\n 原始文件名\r\n"}, {"name": "filePath", "doc": "\n 文件路径\r\n"}, {"name": "fileSize", "doc": "\n 文件大小 (字节)\r\n"}, {"name": "status", "doc": "\n 文档状态 (0=处理中 1=已完成 2=失败)\r\n"}, {"name": "processStatus", "doc": "\n 处理状态 (0=未处理 1=已向量化 2=已索引)\r\n"}, {"name": "vectorCount", "doc": "\n 向量数量\r\n"}, {"name": "summary", "doc": "\n 文档摘要\r\n"}, {"name": "tags", "doc": "\n 文档标签 (JSON数组)\r\n"}, {"name": "metadata", "doc": "\n 文档元数据 (JSON格式)\r\n"}, {"name": "processConfig", "doc": "\n 处理配置 (JSON格式)\r\n"}, {"name": "errorMessage", "doc": "\n 错误信息\r\n"}, {"name": "lastProcessTime", "doc": "\n 最后处理时间\r\n"}, {"name": "sortOrder", "doc": "\n 排序字段\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}], "enumConstants": [], "methods": [], "constructors": []}