{"doc": " 书籍阅读记录Service接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryByUserIdAndBookId", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 根据用户ID和书籍ID查询阅读记录\n\n @param userId 用户ID\n @param bookId 书籍ID\n @return 阅读记录\n"}, {"name": "queryUserReadingHistory", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.Long"], "doc": " 查询用户的阅读历史（分页）\n\n @param pageNum  页码\n @param pageSize 每页大小\n @param userId   用户ID\n @return 阅读记录分页\n"}, {"name": "queryRecentReading", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询用户最近阅读的书籍\n\n @param userId 用户ID\n @param limit  限制数量\n @return 阅读记录列表\n"}, {"name": "queryUserReadingStats", "paramTypes": ["java.lang.Long"], "doc": " 统计用户阅读数据\n\n @param userId 用户ID\n @return 阅读统计数据\n"}, {"name": "saveOrUpdateReadingRecord", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.util.Map"], "doc": " 保存或更新阅读记录\n\n @param userId              用户ID\n @param bookId              书籍ID\n @param currentChapterId    当前章节ID\n @param currentChapterIndex 当前章节索引\n @param readingProgress     阅读进度\n @param readingSettings     阅读设置\n @return 是否成功\n"}, {"name": "mark<PERSON><PERSON><PERSON>erCompleted", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.String"], "doc": " 标记章节已完成\n\n @param userId    用户ID\n @param bookId    书籍ID\n @param chapterId 章节ID\n @return 是否成功\n"}, {"name": "markBookFinished", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 标记书籍已读完\n\n @param userId 用户ID\n @param bookId 书籍ID\n @return 是否成功\n"}, {"name": "addReadingTime", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Integer"], "doc": " 增加阅读时长\n\n @param userId      用户ID\n @param bookId      书籍ID\n @param readingTime 阅读时长（分钟）\n @return 是否成功\n"}, {"name": "deleteReadingRecord", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 删除阅读记录\n\n @param userId 用户ID\n @param bookId 书籍ID\n @return 是否成功\n"}], "constructors": []}