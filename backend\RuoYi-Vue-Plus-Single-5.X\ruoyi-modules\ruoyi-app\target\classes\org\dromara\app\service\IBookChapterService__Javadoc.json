{"doc": "\n 书籍章节Service接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryChaptersByBookId", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 根据书籍ID查询章节列表\r\n\r\n @param bookId 书籍ID\r\n @param userId 用户ID（用于判断解锁状态和完成状态）\r\n @return 章节列表\r\n"}, {"name": "query<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 根据章节ID查询章节内容\r\n\r\n @param chapterId 章节ID\r\n @param userId    用户ID（用于权限校验）\r\n @return 章节内容\r\n"}, {"name": "queryChapterByOrder", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Long"], "doc": "\n 根据书籍ID和章节序号查询章节\r\n\r\n @param bookId       书籍ID\r\n @param chapterOrder 章节序号\r\n @param userId       用户ID\r\n @return 章节信息\r\n"}, {"name": "queryPreviewChapters", "paramTypes": ["java.lang.Long"], "doc": "\n 查询试读章节列表\r\n\r\n @param bookId 书籍ID\r\n @return 试读章节列表\r\n"}, {"name": "insertChapter", "paramTypes": ["org.dromara.app.domain.BookChapter"], "doc": "\n 新增章节\r\n\r\n @param chapter 章节信息\r\n @return 是否成功\r\n"}, {"name": "updateChapter", "paramTypes": ["org.dromara.app.domain.BookChapter"], "doc": "\n 修改章节\r\n\r\n @param chapter 章节信息\r\n @return 是否成功\r\n"}, {"name": "deleteChapter", "paramTypes": ["java.lang.String"], "doc": "\n 删除章节\r\n\r\n @param chapterId 章节ID\r\n @return 是否成功\r\n"}, {"name": "deleteChaptersByBookId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据书籍ID删除所有章节\r\n\r\n @param bookId 书籍ID\r\n @return 是否成功\r\n"}, {"name": "checkChapterAccess", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 检查用户是否有权限访问章节\r\n\r\n @param chapterId 章节ID\r\n @param userId    用户ID\r\n @return 是否有权限\r\n"}, {"name": "updateChapterUnlockStatus", "paramTypes": ["java.lang.String", "java.lang.Bo<PERSON>an"], "doc": "\n 更新章节解锁状态\r\n\r\n @param chapterId  章节ID\r\n @param isUnlocked 是否解锁\r\n @return 是否成功\r\n"}], "constructors": []}