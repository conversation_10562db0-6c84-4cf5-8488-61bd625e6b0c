package org.dromara.app.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 成就业务对象 app_achievement
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "成就业务对象")
public class AchievementBo extends BaseEntity {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 成就代码(唯一标识)
     */
    @Schema(description = "成就代码")
    @NotBlank(message = "成就代码不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 100, message = "成就代码长度不能超过100个字符")
    private String achievementCode;

    /**
     * 成就名称
     */
    @Schema(description = "成就名称")
    @NotBlank(message = "成就名称不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 200, message = "成就名称长度不能超过200个字符")
    private String achievementName;

    /**
     * 成就描述
     */
    @Schema(description = "成就描述")
    @Size(max = 500, message = "成就描述长度不能超过500个字符")
    private String achievementDesc;

    /**
     * 成就图标URL
     */
    @Schema(description = "成就图标URL")
    @Size(max = 200, message = "成就图标URL长度不能超过200个字符")
    private String achievementIcon;

    /**
     * 成就类型
     */
    @Schema(description = "成就类型")
    @NotBlank(message = "成就类型不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 50, message = "成就类型长度不能超过50个字符")
    private String achievementType;

    /**
     * 触发条件(JSON格式)
     */
    @Schema(description = "触发条件(JSON格式)")
    @NotBlank(message = "触发条件不能为空", groups = { AddGroup.class, EditGroup.class })
    private String triggerCondition;

    /**
     * 奖励积分
     */
    @Schema(description = "奖励积分")
    private Integer rewardPoints;

    /**
     * 是否激活(0否 1是)
     */
    @Schema(description = "是否激活")
    private String isActive;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortOrder;

}
