package org.dromara.app.domain.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * 聊天请求DTO
 *
 * <AUTHOR>
 */
@Data
public class ChatRequestDto {

    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    private String message;

    /**
     * 会话ID（可选，新会话时为空）
     */
    private String sessionId;

    /**
     * Agent类型
     */
    private String agentType = "general";

    /**
     * 附件列表
     */
    private List<MessageAttachment> attachments;

    /**
     * 消息类型：text/image/voice
     */
    private String messageType = "text";

    /**
     * 模型提供商：ollama/openai/rjb-sias
     */
    private String provider = "ollama";

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 温度参数
     */
    private Double temperature;

    /**
     * 是否启用流式响应
     */
    private Boolean enableStreaming = true;

    /**
     * 上下文消息数量限制
     */
    private Integer contextLimit = 10;

    /**
     * 消息附件
     */
    @Data
    public static class MessageAttachment {
        @NotBlank(message = "附件类型不能为空")
        private String type; // image/file/voice

        @NotBlank(message = "附件URL不能为空")
        private String url;

        private String name;
        private Long size;
        private String mimeType;
        private String thumbnail;
        private Integer duration;
    }
}
