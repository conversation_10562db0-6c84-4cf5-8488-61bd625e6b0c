package org.dromara.app.controller.learning;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.InterviewReport;
import org.dromara.app.service.ILearningRecommendationService;
import org.dromara.app.service.ILearningRecommendationService.LearningPathRecommendation;
import org.dromara.app.service.ILearningRecommendationService.LearningResource;
import org.dromara.app.service.ILearningRecommendationService.UserProfile;
import org.dromara.app.service.IReportGenerationService;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 学习推荐控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/learning")
public class LearningRecommendationController extends BaseController {

    private final ILearningRecommendationService learningRecommendationService;
    private final IReportGenerationService reportGenerationService;

    /**
     * 基于面试会话生成学习推荐
     *
     * @param sessionId 面试会话ID
     * @return 学习路径推荐列表
     */
    @Log(title = "生成学习推荐", businessType = BusinessType.OTHER)
    @PostMapping("/recommend/{sessionId}")
    public R<List<LearningPathRecommendation>> generateLearningRecommendations(@PathVariable String sessionId) {
        try {
            // 先生成面试报告
            IReportGenerationService.InterviewReportData reportData = reportGenerationService.generateInterviewReport(sessionId);

            // 转换为InterviewReport对象（简化处理）
            InterviewReport report = convertToInterviewReport(reportData);

            // 生成学习推荐
            List<LearningPathRecommendation> recommendations = learningRecommendationService.generateLearningPaths(report);

            return R.ok(recommendations);
        } catch (Exception e) {
            log.error("生成学习推荐失败", e);
            return R.fail("生成学习推荐失败: " + e.getMessage());
        }
    }

    /**
     * 基于弱项生成学习推荐
     *
     * @param request 请求参数
     * @return 学习路径推荐列表
     */
    @Log(title = "基于弱项生成学习推荐", businessType = BusinessType.OTHER)
    @PostMapping("/recommend/weaknesses")
    public R<List<LearningPathRecommendation>> generateRecommendationsByWeaknesses(
            @RequestBody WeaknessRecommendationRequest request) {
        try {
            List<LearningPathRecommendation> recommendations = learningRecommendationService
                .generatePathsByWeaknesses(request.getWeaknesses(), request.getJobPosition());

            return R.ok(recommendations);
        } catch (Exception e) {
            log.error("基于弱项生成学习推荐失败", e);
            return R.fail("生成学习推荐失败: " + e.getMessage());
        }
    }

    /**
     * 基于岗位生成学习推荐
     *
     * @param jobPosition 岗位信息
     * @param userLevel 用户水平
     * @return 学习路径推荐列表
     */
    @GetMapping("/recommend/job/{jobPosition}")
    public R<List<LearningPathRecommendation>> generateRecommendationsByJob(
            @PathVariable String jobPosition,
            @RequestParam(defaultValue = "初级") String userLevel) {
        try {
            List<LearningPathRecommendation> recommendations = learningRecommendationService
                .generatePathsByJobPosition(jobPosition, userLevel);

            return R.ok(recommendations);
        } catch (Exception e) {
            log.error("基于岗位生成学习推荐失败", e);
            return R.fail("生成学习推荐失败: " + e.getMessage());
        }
    }

    /**
     * 获取学习资源推荐
     *
     * @param skillArea 技能领域
     * @param difficulty 难度等级
     * @param resourceType 资源类型
     * @return 学习资源列表
     */
    @GetMapping("/resources")
    public R<List<LearningResource>> getRecommendedResources(
            @RequestParam String skillArea,
            @RequestParam(required = false) String difficulty,
            @RequestParam(required = false) String resourceType) {
        try {
            List<LearningResource> resources = learningRecommendationService
                .getRecommendedResources(skillArea, difficulty, resourceType);

            return R.ok(resources);
        } catch (Exception e) {
            log.error("获取学习资源推荐失败", e);
            return R.fail("获取学习资源推荐失败: " + e.getMessage());
        }
    }

    /**
     * 计算学习优先级
     *
     * @param request 请求参数
     * @return 优先级映射
     */
    @PostMapping("/priorities")
    public R<Map<String, Integer>> calculateLearningPriorities(@RequestBody PriorityCalculationRequest request) {
        try {
            Map<String, Integer> priorities = learningRecommendationService.calculateLearningPriorities(
                request.getWeaknesses(), request.getDimensionScores(), request.getJobPosition());

            return R.ok(priorities);
        } catch (Exception e) {
            log.error("计算学习优先级失败", e);
            return R.fail("计算学习优先级失败: " + e.getMessage());
        }
    }

    /**
     * 个性化学习路径调整
     *
     * @param request 请求参数
     * @return 调整后的学习路径
     */
    @PostMapping("/personalize")
    public R<List<LearningPathRecommendation>> personalizelearningPaths(@RequestBody PersonalizationRequest request) {
        try {
            List<LearningPathRecommendation> personalizedPaths = learningRecommendationService
                .personalizelearningPaths(request.getBasePaths(), request.getUserProfile());

            return R.ok(personalizedPaths);
        } catch (Exception e) {
            log.error("个性化学习路径调整失败", e);
            return R.fail("个性化调整失败: " + e.getMessage());
        }
    }

    /**
     * 获取学习路径详情
     *
     * @param pathId 路径ID
     * @return 学习路径详情
     */
    @GetMapping("/path/{pathId}")
    public R<LearningPathRecommendation> getLearningPathDetail(@PathVariable String pathId) {
        try {
            LearningPathRecommendation pathDetail = learningRecommendationService.getLearningPathDetail(pathId);

            if (pathDetail == null) {
                return R.fail("学习路径不存在");
            }

            return R.ok(pathDetail);
        } catch (Exception e) {
            log.error("获取学习路径详情失败", e);
            return R.fail("获取学习路径详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门学习路径
     *
     * @param category 分类
     * @param limit 限制数量
     * @return 热门学习路径列表
     */
    @GetMapping("/popular")
    public R<List<LearningPathRecommendation>> getPopularLearningPaths(
            @RequestParam(required = false) String category,
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            // 这里返回一些热门的学习路径（模拟数据）
            List<LearningPathRecommendation> popularPaths = getPopularPaths(category, limit);

            return R.ok(popularPaths);
        } catch (Exception e) {
            log.error("获取热门学习路径失败", e);
            return R.fail("获取热门学习路径失败: " + e.getMessage());
        }
    }

    // ========== 请求参数类 ==========

    /**
     * 弱项推荐请求
     */
    public static class WeaknessRecommendationRequest {
        private List<String> weaknesses;
        private String jobPosition;

        public List<String> getWeaknesses() { return weaknesses; }
        public void setWeaknesses(List<String> weaknesses) { this.weaknesses = weaknesses; }
        public String getJobPosition() { return jobPosition; }
        public void setJobPosition(String jobPosition) { this.jobPosition = jobPosition; }
    }

    /**
     * 优先级计算请求
     */
    public static class PriorityCalculationRequest {
        private List<String> weaknesses;
        private List<org.dromara.app.service.IMultimodalAnalysisService.DimensionScore> dimensionScores;
        private String jobPosition;

        public List<String> getWeaknesses() { return weaknesses; }
        public void setWeaknesses(List<String> weaknesses) { this.weaknesses = weaknesses; }
        public List<org.dromara.app.service.IMultimodalAnalysisService.DimensionScore> getDimensionScores() { return dimensionScores; }
        public void setDimensionScores(List<org.dromara.app.service.IMultimodalAnalysisService.DimensionScore> dimensionScores) { this.dimensionScores = dimensionScores; }
        public String getJobPosition() { return jobPosition; }
        public void setJobPosition(String jobPosition) { this.jobPosition = jobPosition; }
    }

    /**
     * 个性化请求
     */
    public static class PersonalizationRequest {
        private List<LearningPathRecommendation> basePaths;
        private UserProfile userProfile;

        public List<LearningPathRecommendation> getBasePaths() { return basePaths; }
        public void setBasePaths(List<LearningPathRecommendation> basePaths) { this.basePaths = basePaths; }
        public UserProfile getUserProfile() { return userProfile; }
        public void setUserProfile(UserProfile userProfile) { this.userProfile = userProfile; }
    }

    // ========== 私有辅助方法 ==========

    /**
     * 转换报告数据为InterviewReport对象
     */
    private InterviewReport convertToInterviewReport(IReportGenerationService.InterviewReportData reportData) {
        InterviewReport report = new InterviewReport();
        report.setTotalScore(reportData.getOverallScore());
        report.setLevel(reportData.getRank());
        report.setPercentile(reportData.getPercentile());
        report.setStrengths(reportData.getStrengths());
        report.setWeaknesses(reportData.getWeaknesses());

        if (reportData.getBasicInfo() != null) {
            report.setJobPosition(reportData.getBasicInfo().getJobPosition());
        }

        // 转换维度评分
        if (reportData.getDimensionScores() != null) {
            List<org.dromara.app.domain.DimensionScore> domainScores = reportData.getDimensionScores().stream()
                .map(this::convertToDomainScore)
                .collect(java.util.stream.Collectors.toList());
            report.setDimensionScores(domainScores);
        }

        return report;
    }

    /**
     * 转换维度评分
     */
    private org.dromara.app.domain.DimensionScore convertToDomainScore(IReportGenerationService.DimensionScore source) {
        org.dromara.app.domain.DimensionScore target = new org.dromara.app.domain.DimensionScore();
        target.setDimension(source.getDimension());
        target.setScore(source.getScore());
        target.setMaxScore(source.getMaxScore());
        target.setPercentile(source.getPercentile());
        target.setDescription(source.getDescription());
        return target;
    }

    /**
     * 获取热门学习路径（模拟数据）
     */
    private List<LearningPathRecommendation> getPopularPaths(String category, Integer limit) {
        // 这里应该从数据库获取热门路径，暂时返回模拟数据
        return learningRecommendationService.generatePathsByJobPosition("Java开发工程师", "中级")
            .stream()
            .limit(limit)
            .collect(java.util.stream.Collectors.toList());
    }
}
