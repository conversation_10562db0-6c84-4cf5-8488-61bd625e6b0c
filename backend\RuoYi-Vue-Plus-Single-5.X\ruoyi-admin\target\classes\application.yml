# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  # undertow 配置
  undertow:
    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的
    max-http-post-size: -1
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分
    buffer-size: 512
    # 是否分配的直接内存
    direct-buffers: true
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 8
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 256
    # 环境


# 讯飞数字人配置
xunfei:
  avatar:
    # 接口地址
    avatar-url: wss://avatar.cn-huadong-1.xf-yun.com/v1/interact
    # API Key (请到交互平台-接口服务中获取)
    api-key: 4f05752b4710749af00e4fead9513c92
    # API Secret (请到交互平台-接口服务中获取)
    api-secret: ODg0YTMwMzZkODE5YWRjYjY3ZDY3MzZj
    # 应用ID (请到交互平台-接口服务中获取)
    app-id: "55e45762"
    # 默认形象ID (请到交互平台-接口服务-形象列表中获取)
    default-avatar-id: "110117005"
    # 默认声音ID (请到交互平台-接口服务-声音列表中获取)
    default-vcn: "x4_lingxiaoying_assist"
    # 默认场景ID (请到交互平台-接口服务中获取)
    default-scene-id: "77213753883627520"
    # 默认视频宽度
    default-width: 720
    # 默认视频高度
    default-height: 1280
    # 默认语速
    default-speed: 50
    # 默认音量
    default-volume: 50
    # 默认语调
    default-pitch: 50
    # 心跳间隔（毫秒）
    heartbeat-interval: 5000
    # 连接超时时间（毫秒）
    connect-timeout: 30000
    # 是否开启透明背景
    enable-alpha: false
    # 视频协议
    protocol: xrtc
    # 视频帧率
    fps: 25
    # 视频码率
    bitrate: 5000

captcha:
  # 是否启用验证码校验
  enable: true
  # 验证码类型 math 数组计算 char 字符验证
  type: MATH
  # line 线段干扰 circle 圆圈干扰 shear 扭曲干扰
  category: CIRCLE
  # 数字验证码位数
  numberLength: 1
  # 字符验证码长度
  charLength: 4

# 日志配置
logging:
  level:
    org.dromara: info
    org.springframework: warn
    org.mybatis.spring.mapper: error
    org.apache.fury: warn
  config: classpath:logback-plus.xml

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  application:
    name: RuoYi-Vue-Plus
  threads:
    # 开启虚拟线程 仅jdk21可用
    virtual:
      enabled: false
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: dev
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  mvc:
    # 设置静态资源路径 防止所有请求都去查静态资源
    static-path-pattern: /static/**
    format:
      date-time: yyyy-MM-dd HH:mm:ss
  jackson:
    # 日期格式化
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      # 格式化输出
      indent_output: false
      # 忽略无法转换的对象
      fail_on_empty_beans: false
    deserialization:
      # 允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: Bearer
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # jwt秘钥
  jwt-secret-key: abcdefghijklmnopqrstuvwxyz

# security配置
security:
  # 排除路径
  excludes:
    - /*.html
    - /**/*.html
    - /**/*.css
    - /**/*.js
    - /favicon.ico
    - /error
    - /*/api-docs
    - /*/api-docs/**
    - /warm-flow-ui/config

# MyBatisPlus配置
# https://baomidou.com/config/
mybatis-plus:
  # 自定义配置 是否全局开启逻辑删除 关闭后 所有逻辑删除功能将失效
  enableLogicDelete: true
  # 多包名使用 例如 org.dromara.**.mapper,org.xxx.**.mapper
  mapperPackage: org.dromara.**.mapper
  # 对应的 XML 文件位置
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: org.dromara.**.domain
  global-config:
    dbConfig:
      # 主键类型
      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID
      # 如需改为自增 需要将数据库表全部设置为自增
      idType: ASSIGN_ID

# 数据加密
mybatis-encryptor:
  # 是否开启加密
  enable: false
  # 默认加密算法
  algorithm: BASE64
  # 编码方式 BASE64/HEX。默认BASE64
  encode: BASE64
  # 安全秘钥 对称算法的秘钥 如：AES，SM4
  password:
  # 公私钥 非对称算法的公私钥 如：SM2，RSA
  publicKey:
  privateKey:

# api接口加密
api-decrypt:
  # 是否开启全局接口加密
  enabled: true
  # AES 加密头标识
  headerFlag: encrypt-key
  # 响应加密公钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换
  # 对应前端解密私钥 MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAmc3CuPiGL/LcIIm7zryCEIbl1SPzBkr75E2VMtxegyZ1lYRD+7TZGAPkvIsBcaMs6Nsy0L78n2qh+lIZMpLH8wIDAQABAkEAk82Mhz0tlv6IVCyIcw/s3f0E+WLmtPFyR9/WtV3Y5aaejUkU60JpX4m5xNR2VaqOLTZAYjW8Wy0aXr3zYIhhQQIhAMfqR9oFdYw1J9SsNc+CrhugAvKTi0+BF6VoL6psWhvbAiEAxPPNTmrkmrXwdm/pQQu3UOQmc2vCZ5tiKpW10CgJi8kCIFGkL6utxw93Ncj4exE/gPLvKcT+1Emnoox+O9kRXss5AiAMtYLJDaLEzPrAWcZeeSgSIzbL+ecokmFKSDDcRske6QIgSMkHedwND1olF8vlKsJUGK3BcdtM8w4Xq7BpSBwsloE=
  publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJnNwrj4hi/y3CCJu868ghCG5dUj8wZK++RNlTLcXoMmdZWEQ/u02RgD5LyLAXGjLOjbMtC+/J9qofpSGTKSx/MCAwEAAQ==
  # 请求解密私钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换
  # 对应前端加密公钥 MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==
  privateKey: MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKNPuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gAkM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWowcSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99EcvDQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthhYhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3UP8iWi1Qw0Y=

springdoc:
  api-docs:
    # 是否开启接口文档
    enabled: true
  info:
    # 标题
    title: '标题：RuoYi-Vue-Plus管理系统_接口文档'
    # 描述
    description: '描述：用于管理集团旗下公司的人员信息,具体包括XXX,XXX模块...'
    # 版本
    version: '版本号: ${ruoyi.version}'
    # 作者信息
    contact:
      name: Lion Li
      email: <EMAIL>
      url: https://gitee.com/dromara/RuoYi-Vue-Plus
  components:
    # 鉴权方式配置
    security-schemes:
      apiKey:
        type: APIKEY
        in: HEADER
        name: ${sa-token.token-name}
  #这里定义了两个分组，可定义多个，也可以不定义
  group-configs:
    - group: 1.演示模块
      packages-to-scan: org.dromara.demo
    - group: 2.通用模块
      packages-to-scan: org.dromara.web
    - group: 3.系统模块
      packages-to-scan: org.dromara.system
    - group: 4.代码生成模块
      packages-to-scan: org.dromara.generator
    - group: 5.工作流模块
      packages-to-scan: org.dromara.workflow

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludeUrls:
    - /system/notice

# 全局线程池相关配置
# 如使用JDK21请直接使用虚拟线程 不要开启此配置
thread-pool:
  # 是否开启线程池
  enabled: false
  # 队列最大长度
  queueCapacity: 128
  # 线程池维护线程所允许的空闲时间
  keepAliveSeconds: 300

--- # 分布式锁 lock4j 全局配置
lock4j:
  # 获取分布式锁超时时间，默认为 3000 毫秒
  acquire-timeout: 3000
  # 分布式锁的超时时间，默认为 30 秒
  expire: 30000

--- # Actuator 监控端点的配置项
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
    logfile:
      external-file: ./logs/sys-console.log

--- # 默认/推荐使用sse推送
sse:
  enabled: true
  path: /resource/sse

--- # websocket
websocket:
  # 如果关闭 需要和前端开关一起关闭
  enabled: false
  # 路径
  path: /resource/websocket
  # 设置访问源地址
  allowedOrigins: '*'

--- # warm-flow工作流配置
warm-flow:
  # 是否开启工作流，默认true
  enabled: true
  # 是否开启设计器ui
  ui: true
  # 默认Authorization，如果有多个token，用逗号分隔
  token-name: ${sa-token.token-name},clientid
  # 流程状态对应的三元色
  chart-status-color:
    ## 未办理
    - 62,62,62
    ## 待办理
    - 255,205,23
    ## 已办理
    - 157,255,0


--- # ES配置示例
elasticsearch:
  enabled: false
  # ES集群节点地址 (必填)
  hosts:
    - 127.0.0.1:9200
    - 127.0.0.1:9201
    - 127.0.0.1:9202
  # 用户名 (选填)
  username: elastic
  # 密码 (选填)
  password: elastic
  # 连接超时时间（毫秒）
  connect-timeout: 5000
  # 响应超时时间（毫秒）
  socket-timeout: 30000
  # 连接请求超时时间（毫秒）
  connection-request-timeout: 1000
  # 最大连接数
  max-connections: 100
  # 每个路由的最大连接数
  max-connections-per-route: 10
  # 索引默认分片数
  default-shards: 1
  # 索引默认副本数
  default-replicas: 1
  # 是否启用SSL
  ssl-enabled: false
  # 是否启用调试日志
  debug-enabled: false

--- # 支付配置
# 支付组件配置示例
pay:
  # 启用支付功能
  enabled: true

  # 支付宝配置
  alipay:
    # 启用支付宝支付
    enabled: true
    # 环境类型：sandbox(沙箱) 或 production(生产)
    environment: sandbox
    # 应用ID（请替换为您的实际应用ID）
    app-id: 9021000150600183
    # 商户私钥（请替换为您的实际私钥）
    private-key: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDN6/X4eSc0o5pdMwKoE79zV/AkiHUNtLykRTYfo9nuJM7IJGyO//ElP+zNoK7SKU/xKVk1AWTp13p7qNSfV3S/nPexvkI8oycN4N3xtAXT8GFi3JYa6J/4WhwAyvYiAFfXOZ1gbkxE91rnaK+4mQq+N3Kkes1hKEiCVz6zeLcbc/E0VrXJMVJ01k9uYRROHvic30tZTKaRJBsQ5H6GMNmZTe5kdAAHuAmBo1a6/obRe9BaHRpFgFG83XI23aK8zpdy/PGycBLVHaRH3vsn/oPMhBUGKW+d7xlwbKmtZUwfd2bEsr+5cB3Ai1P90AhjQZWlzWsu+9ythoSiwpEsyeB3AgMBAAECggEAEsOxwlan7/AInP2M/GvwQj59L5cPs5QIrJ9x8ygnL8BC55IKIm3IN7C/j21ZAOWq1zsBWgcqfvSXwLT9yxdZGCzhlVzVBFmJsYYQM3pIHawF89BODV2tViA/9QKJs0d8uawSXzst0AsKSZQMjzHC/Npoo/xcwsG8+cOhfxE8/k9EeQCBCJYksM6fj0q/btK6jSuQMugfJfOwj5IApqZ3++GkmdeRUtFahMTAA9t0xyMvrs75t9P5GbTOHHXnDcsniL9ELE4RvfSniiTHTlqHbZ3HBz+/vEW6OCLiJGkRK9trqJk1AbTAHh8hPgSsc6E64sbNMXzasZ3e5/8ipNVsYQKBgQD06IrXuzGjUrI6IpINKfMw7dcfLoX/wDl6l8DG1umkZS7UMtiiZr4jktzBhQo6iCkmKeKfBFNyDNidrYO3GLmWaq/Kd0T3dlDce5SFqUxfjeoAjJwpSsijDmvODH5jJY73tiDWHAkhjAJDzpZaN7uUzP5LUWKfMbLdJdJ2JVWyIwKBgQDXP2isHG/wrb6PIIAJTZLFzUp4tPbqzK39yGs1r1DeVzPUQeOWy+ZLLbxC4XGc76W0j/bmpOWA90RzxmSSfcASLu7OmeS3vzunyQTz5zZEIZKB/4UWfBFvyYWlpZDEEjsaAi/vqawt+pyjQPnJ70Q5Qlw+M+4bxTC9A0fIjg1rnQKBgCI2NecLBFUfYTxTxflGye0k3G9DrX5bmOvyNQDR1tObOt5zt+V/2sHXazUxY4tnp+/n2/uAvDNrbfsg2QyDzLEheCsdXCoBgiw6qzW8v6l9hpnLCqmOA9cbVZ30CtfI0F70N0QRqIjiKRLV0hUKQg74T/GvjcpVQ/o2pV7RulXnAoGASG/PYvRi+4UM9w/B5t+TQDvZDwwfeF8PQrO62KM6PMjt6UWPOMU/qBcpHvzNhjnj8wLr3SyrC56rC6c9W0s0YyomNaL2pJuXkNyGbSpnScBelJ8QA8QW/h68ekVglYtpFlScGzRnfEW1hv8r0LhTpk1dy5UgUXon78P12VlszMUCgYA4yVw2/MHYCxM6AeG7u4jKGdkB59isqBDpgivsFIPieZtFXQ40PGQn29xaPV3DdJcb7pbZZgYO4NnfOF0W4pky5zOhmxH3k+H1+erathJW6up61oK+Uh0yuA2Jh7qwVM8welTITZkVwMTjBuZqz21mB9WGggD4AgJS9fJt4K1sqQ==
    # 支付宝公钥（请替换为您的实际公钥）
    alipay-public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlQTZJm81j1d7Um3FptvlS8P1QDUmVxDYSXqISwMiYM+NocPbXlTWnK7yVFvqgyNetQAQ4oYYG+dHqRj+hOWkziJkwsvPbxTCmc7u6j8f8DGoepZL8Uc8DTCjq4HTcMZbRMngrD72gmpGS+h5rcK1Bx1jmg9Kxw5Jec3C9NqMHy95s4e8jm63hV7w4HWNsrDypGxn2OeWDCzi1G5vp2ubylZUePlDg01ZBa4nYc7rERVrBUPuZYBkB4mtOmTW8gabTHrOD7AM9yfbQ8ItqgfITuFP9TihMRtySw5I42ZOvxVAuJL1DD4ErMiahDR5yDD9BeY9F+8l2Wo1zXamvW7t9QIDAQAB
    # 应用服务器地址
    server-url: https://openapi-sandbox.dl.alipaydev.com/gateway.do
    # 异步通知URL（可选，未配置时使用默认值）
    notify-url: http://localhost:8080/app/pay/notify
    # 同步回调URL（可选，未配置时使用默认值）
    return-url: http://localhost:8080/app/pay/return
    # 签名方式
    sign-type: RSA2
    # 字符编码
    charset: UTF-8
    # 数据格式
    format: JSON

  # 微信支付配置（待实现）
  wechat:
    # 启用微信支付
    enabled: false
    # 应用ID
    app-id: ${WECHAT_APP_ID:}
    # 商户号
    mch-id: ${WECHAT_MCH_ID:}
    # 商户密钥
    api-key: ${WECHAT_API_KEY:}
    # 异步通知URL
    notify-url: ${WECHAT_NOTIFY_URL:http://localhost:8080/app/pay/wechat/notify}
    # 同步回调URL
    return-url: ${WECHAT_RETURN_URL:http://localhost:8080/app/pay/wechat/return}


--- # Rabbitmq
spring:
  rabbitmq:
    # 是否启用RabbitMQ（默认true）
    enabled: true
    # RabbitMQ主机地址
    host: localhost
    # RabbitMQ端口
    port: 5672
    # 用户名
    username: admin
    # 密码
    password: admin
    # 虚拟主机
    virtual-host: /
    # 连接超时时间（毫秒）
    connection-timeout: 60000
    # 通道缓存大小
    channel-cache-size: 50

    # 生产者重试配置
    retry:
      # 是否启用重试
      enabled: true
      # 最大重试次数
      max-attempts: 3
      # 初始重试间隔（毫秒）
      initial-interval: 1000
      # 重试间隔倍数
      multiplier: 2.0
      # 最大重试间隔（毫秒）
      max-interval: 10000

    # 消费者监听配置
    listener:
      # 并发消费者数量
      concurrency: 1
      # 最大并发消费者数量
      max-concurrency: 10
      # 预取数量
      prefetch: 1
      # 消费者重试配置
      retry:
        # 是否启用重试
        enabled: true
        # 最大重试次数
        max-attempts: 3
        # 初始重试间隔（毫秒）
        initial-interval: 1000
        # 重试间隔倍数
        multiplier: 2.0
        # 最大重试间隔（毫秒）
        max-interval: 10000
  ai:
    openai:
      api-key: sk-xx
      base-url: https://api.pandarobot.chat/
    mcp:
      client:
        enabled: false
        name: ruoyi-ai-mcp
        sse:
          connections:
            server:
              url: http://127.0.0.1:8081
        stdio:
          servers-configuration: classpath:mcp-server.json
        request-timeout: 300s
--- # 监控中心配置
spring.boot.admin.client:
  # 增加客户端开关
  enabled: false
  url: http://localhost:9090/admin
  instance:
    service-host-type: IP
    metadata:
      username: ${spring.boot.admin.client.username}
      userpassword: ${spring.boot.admin.client.password}
  username: "ruoyi"
  password: "123456"

--- # snail-job 配置
snail-job:
  enabled: false
  # 需要在 SnailJob 后台组管理创建对应名称的组,然后创建任务的时候选择对应的组,才能正确分派任务
  group: "ruoyi_group"
  # SnailJob 接入验证令牌 详见 script/sql/ry_job.sql `sj_group_config` 表
  token: "SJ_cKqBTPzCsWA3VyuCfFoccmuIEGXjr5KT"
  server:
    host: 127.0.0.1
    port: 17888
  # 命名空间UUID 详见 script/sql/ry_job.sql `sj_namespace`表`unique_id`字段
  namespace: ${spring.profiles.active}
  # 随主应用端口漂移
  port: 2${server.port}
  # 客户端ip指定
  host:
  # RPC类型: netty, grpc
  rpc-type: grpc

--- # 数据源配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: true
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driverClassName: com.mysql.cj.jdbc.Driver
          # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562
          # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能(对数据库有性能损耗 使用批量操作应考虑性能问题)
          url: ***************************************************************************************************************************************************************************************************************************************************************
          username: rjb-dev
          password: D2weNhfLkSkYpThj
      #        # 从库数据源
      #        slave:
      #          lazy: true
      #          type: ${spring.datasource.type}
      #          driverClassName: com.mysql.cj.jdbc.Driver
      #          url: **********************************************************************************************************************************************************************************************************************************************************
      #          username:
      #          password:
      #        oracle:
      #          type: ${spring.datasource.type}
      #          driverClassName: oracle.jdbc.OracleDriver
      #          url: *************************************
      #          username: ROOT
      #          password: root
      #        postgres:
      #          type: ${spring.datasource.type}
      #          driverClassName: org.postgresql.Driver
      #          url: ******************************************************************************************************************************************
      #          username: root
      #          password: root
      #        sqlserver:
      #          type: ${spring.datasource.type}
      #          driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
      #          url: *******************************************************************************************************************
      #          username: SA
      #          password: root
      hikari:
        # 最大连接池数量
        maxPoolSize: 20
        # 最小空闲线程数量
        minIdle: 10
        # 配置获取连接等待超时的时间
        connectionTimeout: 30000
        # 校验超时时间
        validationTimeout: 5000
        # 空闲连接存活最大时间，默认10分钟
        idleTimeout: 600000
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
        maxLifetime: 1800000
        # 多久检查一次连接的活性
        keepaliveTime: 30000

--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring.data:
  redis:
    # 地址
    host: *************
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # redis 密码必须配置
    password: 123123
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl.enabled: false

# redisson 配置
redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 客户端名称 不能用中文
    clientName: RuoYi-Vue-Plus
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

--- # MongoDB 配置
spring.data:
  mongodb:
    # MongoDB连接配置（开发环境）
    uri: mongodb://**************:27017/rjb-dev
    database: ruoyi_dev
    host: **************
    port: 27017
    # 认证配置（如果需要）
    # username: dromara
    # password: ruoyi123
    # authentication-database: admin


    # 连接池配置
    pool:
      # 最大连接数
      max-size: 50
      # 最小连接数
      min-size: 5
      # 连接超时时间(毫秒)
      max-connection-timeout-ms: 30000
      # 读取超时时间(毫秒)
      max-read-timeout-ms: 15000
      # 最大等待时间(毫秒)
      max-wait-time-ms: 30000
      # 连接最大空闲时间(毫秒)
      max-connection-idle-time-ms: 600000
      # 连接最大生存时间(毫秒)
      max-connection-life-time-ms: 0

--- # RabbitMQ 配置
spring:
  rabbitmq:
    # 是否启用RabbitMQ（默认true）
    enabled: true
    # RabbitMQ主机地址
    host: **************
    # RabbitMQ端口
    port: 5672
    # 用户名
    username: itheima
    # 密码
    password: 123321
    # 虚拟主机
    virtual-host: /
    # 连接超时时间（毫秒）
    connection-timeout: 60000
    # 通道缓存大小
    channel-cache-size: 50

    # 生产者重试配置
    retry:
      # 是否启用重试
      enabled: true
      # 最大重试次数
      max-attempts: 3
      # 初始重试间隔（毫秒）
      initial-interval: 1000
      # 重试间隔倍数
      multiplier: 2.0
      # 最大重试间隔（毫秒）
      max-interval: 10000

    # 消费者监听配置
    listener:
      # 并发消费者数量
      concurrency: 1
      # 最大并发消费者数量
      max-concurrency: 10
      # 预取数量
      prefetch: 1
      # 消费者重试配置
      retry:
        # 是否启用重试
        enabled: true
        # 最大重试次数
        max-attempts: 3
        # 初始重试间隔（毫秒）
        initial-interval: 1000
        # 重试间隔倍数
        multiplier: 2.0
        # 最大重试间隔（毫秒）
        max-interval: 10000

--- # mail 邮件发送
mail:
  enabled: true
  host: smtp.qq.com
  port: 465
  # 是否需要用户名密码验证
  auth: true
  # 发送方，遵循RFC-822标准
  from: <EMAIL>
  # 用户名（注意：如果使用foxmail邮箱，此处user为qq号）
  user: <EMAIL>
  # 密码（注意，某些邮箱需要为SMTP服务单独设置密码，详情查看相关帮助）
  pass: tcgvscbmdykdebif
  # 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。
  starttlsEnable: true
  # 使用SSL安全连接
  sslEnable: true
  # SMTP超时时长，单位毫秒，缺省值不超时
  timeout: 0
  # Socket连接超时值，单位毫秒，缺省值不超时
  connectionTimeout: 0

--- # sms 短信 支持 阿里云 腾讯云 云片 等等各式各样的短信服务商
# https://sms4j.com/doc3/ 差异配置文档地址 支持单厂商多配置，可以配置多个同时使用
sms:
  # 配置源类型用于标定配置来源(interface,yaml)
  config-type: yaml
  # 用于标定yml中的配置是否开启短信拦截，接口配置不受此限制
  restricted: true
  # 短信拦截限制单手机号每分钟最大发送，只对开启了拦截的配置有效
  minute-max: 1
  # 短信拦截限制单手机号每日最大发送量，只对开启了拦截的配置有效
  account-max: 30
  # 以下配置来自于 org.dromara.sms4j.provider.config.BaseConfig类中
  blends:
    # 唯一ID 用于发送短信寻找具体配置 随便定义别用中文即可
    # 可以同时存在两个相同厂商 例如: ali1 ali2 两个不同的阿里短信账号
    config1:
      # 框架定义的厂商名称标识，标定此配置是哪个厂商，详细请看厂商标识介绍部分
      supplier: alibaba
      # 有些称为accessKey有些称之为apiKey，也有称为sdkKey或者appId。
      access-key-id: 您的accessKey
      # 称为accessSecret有些称之为apiSecret
      access-key-secret: 您的accessKeySecret
      signature: 您的短信签名
      sdk-app-id: 您的sdkAppId
    config2:
      # 厂商标识，标定此配置是哪个厂商，详细请看厂商标识介绍部分
      supplier: tencent
      access-key-id: 您的accessKey
      access-key-secret: 您的accessKeySecret
      signature: 您的短信签名
      sdk-app-id: 您的sdkAppId


--- # 三方授权
justauth:
  # 前端外网访问地址
  address: http://localhost:80
  type:
    maxkey:
      # maxkey 服务器地址
      # 注意 如下均配置均不需要修改 maxkey 已经内置好了数据
      server-url: http://sso.maxkey.top
      client-id: 876892492581044224
      client-secret: x1Y5MTMwNzIwMjMxNTM4NDc3Mzche8
      redirect-uri: ${justauth.address}/social-callback?source=maxkey
    topiam:
      # topiam 服务器地址
      server-url: http://127.0.0.1:1898/api/authorize/y0q************spq***********8ol
      client-id: 449c4*********937************759
      client-secret: ac7***********1e0************28d
      redirect-uri: ${justauth.address}/social-callback?source=topiam
      scopes: [ openid, email, phone, profile ]
    qq:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=qq
      union-id: false
    weibo:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=weibo
    gitee:
      client-id: 91436b7940090d09c72c7daf85b959cfd5f215d67eea73acbf61b6b590751a98
      client-secret: 02c6fcfd70342980cd8dd2f2c06c1a350645d76c754d7a264c4e125f9ba915ac
      redirect-uri: ${justauth.address}/social-callback?source=gitee
    dingtalk:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=dingtalk
    baidu:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=baidu
    csdn:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=csdn
    coding:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=coding
      coding-group-name: xx
    oschina:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=oschina
    alipay_wallet:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=alipay_wallet
      alipay-public-key: MIIB**************DAQAB
    wechat_open:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=wechat_open
    wechat_mp:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=wechat_mp
    wechat_enterprise:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=wechat_enterprise
      agent-id: 1000002
    gitlab:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=gitlab
    gitea:
      # 前端改动 https://gitee.com/JavaLionLi/plus-ui/pulls/204
      # gitea 服务器地址
      server-url: https://demo.gitea.com
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=gitea
--- # Caffeine 缓存配置
caffeine:
  # 是否开启缓存
  enabled: true
  # 缓存最大容量
  maximum-size: 1000
  # 缓存过期时间(秒)
  expire-after-write: 3600
  # 是否开启统计信息
  record-stats: true
  # 缓存名称
  default-cache-name: default

--- # LangChain4j AI Agent 配置示例
langchain4j:
  # 是否启用LangChain4j
  enabled: true

  # OpenAI配置
  openai:
    api-key: ${OPENAI_API_KEY:sk-your-openai-api-key}
    base-url: ${OPENAI_BASE_URL:https://api.openai.com/v1}
    model: ${OPENAI_MODEL:gpt-3.5-turbo}
    temperature: 0.7
    max-tokens: 2048
    timeout: 60

  # Ollama本地模型配置
  ollama:
    base-url: ${OLLAMA_BASE_URL:http://localhost:11434}
    model: ${OLLAMA_MODEL:llama2}
    temperature: 0.7
    timeout: 60

  # 智谱AI配置
  zhipu-ai:
    api-key: ${ZHIPU_API_KEY:your-zhipu-api-key}
    model: ${ZHIPU_MODEL:glm-4}
    temperature: 0.7
    max-tokens: 2048

  # 阿里云通义千问配置
  dashscope:
    api-key: ${DASHSCOPE_API_KEY:sk-7520b9ee487c408ab02f879768871023}
    model: ${DASHSCOPE_MODEL:qwen-turbo}
    temperature: 0.7
    max-tokens: 2048

  # Agent配置
  agent:
    # 默认AI提供商 (openai, ollama, dashscope)
    default-provider: ${AI_DEFAULT_PROVIDER:ollama}
    # 最大内存消息数
    max-memory-size: 10
    # 会话超时时间（秒）
    session-timeout: 3600

xunfei:
  emotion:
    api-key: 144e135273b23c5bddac290086e97578
    api-secret: MjBhNzAzZWYzZjVmZGY4MjQzZmM1NGUy
    app-id: 7d72d2e7
    base-url: https://api.xf-yun.com/v1/private/s9a3d6d6c
  spark:
    api-key: 144e135273b23c5bddac290086e97578
    api-secret: MjBhNzAzZWYzZjVmZGY4MjQzZmM1NGUy
    base-url: https://spark-api-open.xf-yun.com/v2/chat/completions
    model: "x1"
  avatar:
    avatar-url: wss://avatar.cn-huadong-1.xf-yun.com/v1/interact
