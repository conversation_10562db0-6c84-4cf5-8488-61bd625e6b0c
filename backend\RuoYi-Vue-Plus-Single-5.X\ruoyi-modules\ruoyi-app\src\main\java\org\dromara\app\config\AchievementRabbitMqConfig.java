package org.dromara.app.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 成就系统RabbitMQ配置
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@ConditionalOnProperty(prefix = "spring.rabbitmq", name = "enabled", havingValue = "true")
public class AchievementRabbitMqConfig {

    // 交换机名称
    public static final String ACHIEVEMENT_EXCHANGE = "achievement.exchange";
    
    // 队列名称
    public static final String ACHIEVEMENT_CHECK_QUEUE = "achievement.check.queue";
    public static final String ACHIEVEMENT_NOTIFICATION_QUEUE = "achievement.notification.queue";
    
    // 路由键
    public static final String ACHIEVEMENT_CHECK_ROUTING_KEY = "achievement.check";
    public static final String ACHIEVEMENT_NOTIFICATION_ROUTING_KEY = "achievement.notification";
    
    // 死信交换机和队列
    public static final String ACHIEVEMENT_DLX_EXCHANGE = "achievement.dlx.exchange";
    public static final String ACHIEVEMENT_DLX_QUEUE = "achievement.dlx.queue";
    public static final String ACHIEVEMENT_DLX_ROUTING_KEY = "achievement.dlx";

    /**
     * 成就系统主交换机
     */
    @Bean
    public TopicExchange achievementExchange() {
        return ExchangeBuilder
            .topicExchange(ACHIEVEMENT_EXCHANGE)
            .durable(true)
            .build();
    }

    /**
     * 成就检查队列
     */
    @Bean
    public Queue achievementCheckQueue() {
        return QueueBuilder
            .durable(ACHIEVEMENT_CHECK_QUEUE)
            .withArgument("x-dead-letter-exchange", ACHIEVEMENT_DLX_EXCHANGE)
            .withArgument("x-dead-letter-routing-key", ACHIEVEMENT_DLX_ROUTING_KEY)
            .withArgument("x-message-ttl", 300000) // 5分钟TTL
            .build();
    }

    /**
     * 成就通知队列
     */
    @Bean
    public Queue achievementNotificationQueue() {
        return QueueBuilder
            .durable(ACHIEVEMENT_NOTIFICATION_QUEUE)
            .withArgument("x-dead-letter-exchange", ACHIEVEMENT_DLX_EXCHANGE)
            .withArgument("x-dead-letter-routing-key", ACHIEVEMENT_DLX_ROUTING_KEY)
            .withArgument("x-message-ttl", 600000) // 10分钟TTL
            .build();
    }

    /**
     * 死信交换机
     */
    @Bean
    public DirectExchange achievementDlxExchange() {
        return ExchangeBuilder
            .directExchange(ACHIEVEMENT_DLX_EXCHANGE)
            .durable(true)
            .build();
    }

    /**
     * 死信队列
     */
    @Bean
    public Queue achievementDlxQueue() {
        return QueueBuilder
            .durable(ACHIEVEMENT_DLX_QUEUE)
            .build();
    }

    /**
     * 绑定成就检查队列到交换机
     */
    @Bean
    public Binding achievementCheckBinding() {
        return BindingBuilder
            .bind(achievementCheckQueue())
            .to(achievementExchange())
            .with(ACHIEVEMENT_CHECK_ROUTING_KEY);
    }

    /**
     * 绑定成就通知队列到交换机
     */
    @Bean
    public Binding achievementNotificationBinding() {
        return BindingBuilder
            .bind(achievementNotificationQueue())
            .to(achievementExchange())
            .with(ACHIEVEMENT_NOTIFICATION_ROUTING_KEY);
    }

    /**
     * 绑定死信队列到死信交换机
     */
    @Bean
    public Binding achievementDlxBinding() {
        return BindingBuilder
            .bind(achievementDlxQueue())
            .to(achievementDlxExchange())
            .with(ACHIEVEMENT_DLX_ROUTING_KEY);
    }

}
