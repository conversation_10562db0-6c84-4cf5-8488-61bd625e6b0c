{"doc": "\n 用户成长档案Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectProfileByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询成长档案\r\n\r\n @param userId 用户ID\r\n @return 成长档案\r\n"}, {"name": "updateProfileByUserId", "paramTypes": ["org.dromara.app.domain.UserGrowthProfile"], "doc": "\n 根据用户ID更新成长档案\r\n\r\n @param profile 成长档案\r\n @return 更新数量\r\n"}, {"name": "deleteByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID删除成长档案\r\n\r\n @param userId 用户ID\r\n @return 删除数量\r\n"}, {"name": "selectProfilesByCurrentStage", "paramTypes": ["java.lang.String"], "doc": "\n 根据当前阶段查询用户列表\r\n\r\n @param currentStage 当前阶段\r\n @return 用户成长档案列表\r\n"}, {"name": "selectProfilesStatistics", "paramTypes": [], "doc": "\n 查询所有用户的成长档案统计信息\r\n\r\n @return 成长档案统计信息\r\n"}, {"name": "updateLastActiveTimeByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID更新最后活跃时间\r\n\r\n @param userId 用户ID\r\n @return 更新数量\r\n"}, {"name": "incrementInterviewCountByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 根据用户ID增加面试次数\r\n\r\n @param userId    用户ID\r\n @param increment 增加数量\r\n @return 更新数量\r\n"}, {"name": "updateContinuousLearningDaysByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 根据用户ID更新连续学习天数\r\n\r\n @param userId 用户ID\r\n @param days   连续学习天数\r\n @return 更新数量\r\n"}, {"name": "incrementCompletedCoursesByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 根据用户ID增加完成课程数\r\n\r\n @param userId    用户ID\r\n @param increment 增加数量\r\n @return 更新数量\r\n"}], "constructors": []}