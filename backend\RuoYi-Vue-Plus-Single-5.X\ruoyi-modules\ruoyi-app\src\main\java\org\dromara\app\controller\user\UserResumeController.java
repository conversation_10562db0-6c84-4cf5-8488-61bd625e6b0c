package org.dromara.app.controller.user;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.bo.UserResumeBo;
import org.dromara.app.domain.vo.UserResumeUploadVo;
import org.dromara.app.domain.vo.UserResumeVo;
import org.dromara.app.service.IUserResumeService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.core.validate.QueryGroup;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 用户简历管理控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/user/resume")
public class UserResumeController extends BaseController {

    private final IUserResumeService userResumeService;

    /**
     * 查询用户简历分页列表
     */
    @SaCheckLogin
    @GetMapping("/list")
    public TableDataInfo<UserResumeVo> list(@Validated(QueryGroup.class) UserResumeBo bo, PageQuery pageQuery) {
        return userResumeService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询当前用户的所有简历列表
     */
    @SaCheckLogin
    @GetMapping("/my")
    public R<List<UserResumeVo>> getMyResumeList() {
        Long userId = StpUtil.getLoginIdAsLong();
        List<UserResumeVo> list = userResumeService.selectUserResumeList(userId);
        return R.ok(list);
    }

    /**
     * 获取用户简历详细信息
     *
     * @param resumeId 简历主键
     */
    @SaCheckLogin
    @GetMapping("/{resumeId}")
    public R<UserResumeVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable String resumeId) {
        UserResumeVo userResumeVo = userResumeService.queryById(Long.parseLong(resumeId));

        return R.ok(userResumeVo);
    }

    /**
     * 上传用户简历文件
     *
     * @param file     简历文件
     * @param fileName 原始文件名（可选）
     * @param fileType 文件类型（可选）
     */
    @SaCheckLogin
    @Log(title = "用户简历", businessType = BusinessType.INSERT)
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<UserResumeUploadVo> uploadResume(@RequestPart("file") MultipartFile file,
                                              @RequestParam(value = "fileName", required = false) String fileName,
                                              @RequestParam(value = "fileType", required = false) String fileType) {
        if (ObjectUtil.isNull(file)) {
            return R.fail("上传文件不能为空");
        }

        // 验证文件类型
        String originalFilename = file.getOriginalFilename();

        // 如果前端传递了fileName参数，优先使用
        if (ObjectUtil.isNotNull(fileName) && !fileName.trim().isEmpty()) {
            originalFilename = fileName;
        }

        if (ObjectUtil.isNull(originalFilename)) {
            return R.fail("文件名不能为空");
        }

        // 检查文件是否有扩展名
        int lastDotIndex = originalFilename.lastIndexOf(".");
        if (lastDotIndex == -1) {
            return R.fail("文件必须包含扩展名");
        }

        String suffix = originalFilename.substring(lastDotIndex).toLowerCase();
        if (!suffix.matches("\\.(pdf|doc|docx)$")) {
            return R.fail("只支持上传PDF、DOC、DOCX格式的简历文件");
        }

        // 验证文件大小(最大10MB)
        if (file.getSize() > 10 * 1024 * 1024) {
            return R.fail("简历文件大小不能超过10MB");
        }

        Long userId = StpUtil.getLoginIdAsLong();

        // 传递额外的文件信息给服务层
        UserResumeUploadVo uploadVo = userResumeService.uploadResume(userId, file, originalFilename, fileType);
        return R.ok(uploadVo);
    }

    /**
     * 新增用户简历
     */
    @SaCheckLogin
    @Log(title = "用户简历", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody UserResumeBo bo) {
        Long userId = StpUtil.getLoginIdAsLong();
        bo.setUserId(userId);
        return toAjax(userResumeService.insertByBo(bo));
    }

    /**
     * 修改用户简历
     */
    @SaCheckLogin
    @Log(title = "用户简历", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody UserResumeBo bo) {
        return toAjax(userResumeService.updateByBo(bo));
    }

    /**
     * 重命名简历
     *
     * @param resumeId 简历ID
     * @param request  重命名请求参数
     */
    @SaCheckLogin
    @Log(title = "重命名简历", businessType = BusinessType.UPDATE)
    @PutMapping("/{resumeId}/rename")
    public R<Void> renameResume(@PathVariable Long resumeId,
                                @RequestBody Map<String, String> request) {
        String resumeName = request.get("resumeName");
        return toAjax(userResumeService.renameResume(resumeId, resumeName));
    }

    /**
     * 设置默认简历
     *
     * @param resumeId 简历ID
     */
    @SaCheckLogin
    @Log(title = "设置默认简历", businessType = BusinessType.UPDATE)
    @PutMapping("/{resumeId}/default")
    public R<Void> setDefaultResume(@PathVariable Long resumeId) {
        Long userId = StpUtil.getLoginIdAsLong();
        return toAjax(userResumeService.setDefaultResume(userId, resumeId));
    }

    /**
     * 取消默认简历
     *
     * @param resumeId 简历ID
     */
    @SaCheckLogin
    @Log(title = "取消默认简历", businessType = BusinessType.UPDATE)
    @PutMapping("/{resumeId}/cancel-default")
    public R<Void> cancelDefaultResume(@PathVariable Long resumeId) {
        Long userId = StpUtil.getLoginIdAsLong();
        return toAjax(userResumeService.cancelDefaultResume(userId, resumeId));
    }

    /**
     * 获取当前用户的默认简历
     */
    @SaCheckLogin
    @GetMapping("/default")
    public R<UserResumeVo> getDefaultResume() {
        Long userId = StpUtil.getLoginIdAsLong();
        UserResumeVo defaultResume = userResumeService.getDefaultResume(userId);
        return R.ok(defaultResume);
    }

    /**
     * 删除用户简历
     *
     * @param resumeIds 简历主键串
     */
    @SaCheckLogin
    @Log(title = "用户简历", businessType = BusinessType.DELETE)
    @DeleteMapping("/{resumeIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] resumeIds) {
        return toAjax(userResumeService.deleteWithValidByIds(Arrays.asList(resumeIds), true));
    }

    /**
     * 下载用户简历文件
     *
     * @param resumeId 简历ID
     */
    @SaCheckLogin
    @GetMapping("/download/{resumeId}")
    public void downloadResume(@PathVariable Long resumeId, HttpServletResponse response) throws IOException {
        userResumeService.downloadResume(resumeId, response);
    }

    /**
     * 预览简历文件内容
     *
     * @param resumeId 简历ID
     * @return 预览内容响应
     */
    @SaCheckLogin
    @GetMapping("/preview/{resumeId}")
    public R<Map<String, Object>> previewResumeContent(@PathVariable Long resumeId) {
        try {
            Map<String, Object> previewData = userResumeService.previewResumeContent(resumeId);
            return R.ok(previewData);
        } catch (Exception e) {
            log.error("预览简历内容失败: resumeId={}, error={}", resumeId, e.getMessage(), e);
            return R.fail("预览失败: " + e.getMessage());
        }
    }

    /**
     * 获取简历结构化预览内容
     *
     * <p>将简历文件内容解析为结构化数据，包括：</p>
     * <ul>
     *   <li>基本信息：简历名称、创建时间等</li>
     *   <li>个人信息：姓名、联系方式、求职意向等</li>
     *   <li>教育经历：学校、专业、学历等</li>
     *   <li>工作经验：公司、职位、工作内容等</li>
     *   <li>技能特长：技能名称、熟练度等</li>
     *   <li>其他信息：项目经历、获奖情况、证书等</li>
     * </ul>
     *
     * @param resumeId 简历ID，必须为正整数
     * @return 结构化简历内容响应，包含完整的简历数据结构
     * @throws ServiceException 当简历不存在、已禁用或解析失败时抛出
     */
    @SaCheckLogin
    @GetMapping("/preview/{resumeId}/structured")
    public R<Map<String, Object>> getStructuredResumeContent(@PathVariable Long resumeId) {
        try {
            Map<String, Object> structuredData = userResumeService.getStructuredResumeContent(resumeId);
            return R.ok(structuredData);
        } catch (Exception e) {
            log.error("获取结构化简历内容失败: resumeId={}, error={}", resumeId, e.getMessage(), e);
            return R.fail("获取结构化内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取简历预览图片
     *
     * <p>生成简历的预览图片，用于快速预览简历内容。</p>
     * <p>返回的数据包含：</p>
     * <ul>
     *   <li>imageUrl: 完整尺寸预览图URL</li>
     *   <li>thumbnailUrl: 缩略图URL（可选）</li>
     *   <li>resumeId: 简历ID</li>
     *   <li>resumeName: 简历名称</li>
     * </ul>
     *
     * <p>注意：目前返回占位图片，后续可扩展为真实的图片生成功能。</p>
     *
     * @param resumeId 简历ID，必须为正整数
     * @return 预览图片数据响应，包含图片URL和相关信息
     * @throws ServiceException 当简历不存在、已禁用或生成失败时抛出
     */
    @SaCheckLogin
    @GetMapping("/preview/{resumeId}/image")
    public R<Map<String, Object>> getResumePreviewImage(@PathVariable Long resumeId) {
        try {
            Map<String, Object> imageData = userResumeService.generateResumePreviewImage(resumeId);
            return R.ok(imageData);
        } catch (Exception e) {
            log.error("获取简历预览图片失败: resumeId={}, error={}", resumeId, e.getMessage(), e);
            return R.fail("获取预览图片失败: " + e.getMessage());
        }
    }

}
