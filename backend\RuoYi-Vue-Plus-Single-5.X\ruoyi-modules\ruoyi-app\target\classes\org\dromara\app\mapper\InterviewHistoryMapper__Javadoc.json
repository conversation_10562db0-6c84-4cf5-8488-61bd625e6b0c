{"doc": "\n 面试历史记录Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-17\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 根据用户ID查询历史记录列表\r\n\r\n @param userId 用户ID\r\n @param limit  限制数量\r\n @return 历史记录列表\r\n"}, {"name": "selectByUserIdAndResultId", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 根据用户ID和结果ID查询历史记录\r\n\r\n @param userId   用户ID\r\n @param resultId 结果ID\r\n @return 历史记录\r\n"}, {"name": "selectFavoritesByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户收藏的历史记录\r\n\r\n @param userId 用户ID\r\n @return 历史记录列表\r\n"}, {"name": "selectByUserIdAndTags", "paramTypes": ["java.lang.Long", "java.util.List"], "doc": "\n 根据标签查询历史记录\r\n\r\n @param userId 用户ID\r\n @param tags   标签列表\r\n @return 历史记录列表\r\n"}, {"name": "updateFavoriteStatus", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": "\n 更新收藏状态\r\n\r\n @param id         历史记录ID\r\n @param isFavorite 是否收藏\r\n @return 更新数量\r\n"}, {"name": "deleteByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID删除历史记录\r\n\r\n @param userId 用户ID\r\n @return 删除数量\r\n"}, {"name": "selectCountByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户历史记录数量\r\n\r\n @param userId 用户ID\r\n @return 记录数量\r\n"}], "constructors": []}