{"doc": " 应用用户个人信息VO\n\n <AUTHOR>\n", "fields": [{"name": "userId", "doc": " 用户ID\n"}, {"name": "phone", "doc": " 手机号\n"}, {"name": "email", "doc": " 邮箱\n"}, {"name": "name", "doc": " 用户姓名\n"}, {"name": "gender", "doc": " 用户性别（男/女）\n"}, {"name": "studentId", "doc": " 学号\n"}, {"name": "school", "doc": " 学校名称\n"}, {"name": "major", "doc": " 专业\n"}, {"name": "grade", "doc": " 年级\n"}, {"name": "introduction", "doc": " 个人简介\n"}, {"name": "avatar", "doc": " 用户头像\n"}, {"name": "registeredAt", "doc": " 注册时间\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}], "enumConstants": [], "methods": [], "constructors": []}