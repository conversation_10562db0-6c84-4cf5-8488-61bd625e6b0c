{"doc": "\n 能力评估服务实现\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getAssessmentQuestions", "paramTypes": [], "doc": "\n 获取评估问题列表\r\n"}, {"name": "convertToQuestionVo", "paramTypes": ["org.dromara.app.domain.AssessmentQuestion"], "doc": "\n 转换评估问题实体为VO对象\r\n"}, {"name": "submitAssessmentResults", "paramTypes": ["java.util.List"], "doc": "\n 提交评估结果\r\n"}, {"name": "createAssessmentRecord", "paramTypes": ["java.lang.Long", "int"], "doc": "\n 创建评估记录\r\n"}, {"name": "convertToAssessmentResult", "paramTypes": ["org.dromara.app.domain.bo.AssessmentResultBo", "java.lang.Long"], "doc": "\n 转换评估结果BO为实体对象\r\n"}, {"name": "calculateQuestionScore", "paramTypes": ["org.dromara.app.domain.AssessmentQuestion", "org.dromara.app.domain.bo.AssessmentResultBo"], "doc": "\n 计算单个问题的得分\r\n"}, {"name": "calculateAbilityScores", "paramTypes": ["java.util.Map"], "doc": "\n 计算各维度能力得分\r\n"}, {"name": "calculateCategoryScore", "paramTypes": ["java.util.List"], "doc": "\n 计算单个类别的得分\r\n"}, {"name": "updateRecordScores", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.InitialAbilityAssessmentVo"], "doc": "\n 更新评估记录的得分\r\n"}, {"name": "createOrUpdateUserGrowthProfile", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.InitialAbilityAssessmentVo"], "doc": "\n 创建或更新用户成长档案\r\n"}, {"name": "determineUserStage", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据总分确定用户阶段\r\n"}, {"name": "setInitialAssessmentScores", "paramTypes": ["org.dromara.app.domain.UserGrowthProfile", "org.dromara.app.domain.vo.InitialAbilityAssessmentVo"], "doc": "\n 设置初始评估分数\r\n"}, {"name": "setCurrentAssessmentScores", "paramTypes": ["org.dromara.app.domain.UserGrowthProfile", "org.dromara.app.domain.vo.InitialAbilityAssessmentVo"], "doc": "\n 设置当前评估分数\r\n"}, {"name": "getDetailedAbilityReport", "paramTypes": [], "doc": "\n 获取详细能力报告\r\n"}, {"name": "generateStrengths", "paramTypes": ["org.dromara.app.domain.UserAssessmentRecord"], "doc": "\n 生成优势分析\r\n"}, {"name": "generateWeaknesses", "paramTypes": ["org.dromara.app.domain.UserAssessmentRecord"], "doc": "\n 生成劣势分析\r\n"}, {"name": "generateRecommendations", "paramTypes": ["org.dromara.app.domain.UserAssessmentRecord"], "doc": "\n 生成推荐内容\r\n"}, {"name": "getUserGrowthProfile", "paramTypes": [], "doc": "\n 获取用户成长档案\r\n"}, {"name": "convertToGrowthProfileVo", "paramTypes": ["org.dromara.app.domain.UserGrowthProfile"], "doc": "\n 转换用户成长档案实体为VO对象\r\n"}, {"name": "parseJsonStringToList", "paramTypes": ["java.lang.String"], "doc": "\n 解析JSON字符串为字符串列表\r\n"}], "constructors": []}