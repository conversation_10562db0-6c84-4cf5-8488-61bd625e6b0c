{"doc": " 数据库优化服务接口\n 用于数据库查询优化和性能监控\n\n <AUTHOR>\n @date 2025-01-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "analyzeSlowQueries", "paramTypes": ["int"], "doc": " 分析慢查询\n\n @param limit 限制数量\n @return 慢查询列表\n"}, {"name": "optimizeIndexes", "paramTypes": [], "doc": " 优化数据库索引\n\n @return 优化建议\n"}, {"name": "getDatabasePerformance", "paramTypes": [], "doc": " 获取数据库性能指标\n\n @return 性能指标\n"}, {"name": "analyzeTableSpaceUsage", "paramTypes": [], "doc": " 分析表空间使用情况\n\n @return 表空间使用情况\n"}, {"name": "optimizeQueryPlan", "paramTypes": ["java.lang.String"], "doc": " 优化查询计划\n\n @param sql SQL语句\n @return 优化建议\n"}, {"name": "monitorConnectionPool", "paramTypes": [], "doc": " 监控数据库连接池\n\n @return 连接池状态\n"}, {"name": "cleanupUnusedData", "paramTypes": [], "doc": " 清理无用数据\n\n @return 清理的记录数\n"}, {"name": "analyzeDataDistribution", "paramTypes": ["java.lang.String"], "doc": " 分析数据分布\n\n @param tableName 表名\n @return 数据分布分析\n"}, {"name": "suggestPartitionStrategy", "paramTypes": ["java.lang.String"], "doc": " 建议分区策略\n\n @param tableName 表名\n @return 分区建议\n"}, {"name": "monitorLockWaits", "paramTypes": [], "doc": " 监控锁等待\n\n @return 锁等待信息\n"}, {"name": "optimizeBatchSize", "paramTypes": ["int"], "doc": " 优化批量操作\n\n @param batchSize 批次大小\n @return 优化后的批次大小\n"}, {"name": "analyzeQueryFrequency", "paramTypes": [], "doc": " 分析查询频率\n\n @return 查询频率统计\n"}], "constructors": []}