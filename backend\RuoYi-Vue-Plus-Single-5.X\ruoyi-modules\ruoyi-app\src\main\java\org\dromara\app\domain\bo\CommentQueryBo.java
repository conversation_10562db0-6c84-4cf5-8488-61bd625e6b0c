package org.dromara.app.domain.bo;

import lombok.Data;

/**
 * 评论查询业务对象
 *
 * <AUTHOR>
 */
@Data
public class CommentQueryBo {

    /**
     * 视频ID
     */
    private Long videoId;

    /**
     * 父评论ID
     */
    private Long parentId;

    /**
     * 当前用户ID
     */
    private Long currentUserId;

    /**
     * 页码
     */
    private Integer page;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 排序方式
     */
    private String sortBy;

    /**
     * 排序方向
     */
    private String sortOrder;

    /**
     * 排序类型(latest-最新, hottest-最热)
     */
    private String sortType;
}
