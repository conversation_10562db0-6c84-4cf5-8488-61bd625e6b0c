package org.dromara.common.chat.config;

import cn.hutool.core.util.StrUtil;
import dev.langchain4j.community.model.dashscope.QwenChatModel;
import dev.langchain4j.community.model.dashscope.QwenStreamingChatModel;
import dev.langchain4j.http.client.jdk.JdkHttpClientBuilderFactory;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.StreamingChatModel;
import dev.langchain4j.model.ollama.OllamaChatModel;
import dev.langchain4j.model.ollama.OllamaStreamingChatModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.model.openai.OpenAiStreamingChatModel;
import org.dromara.common.chat.config.properties.LangChain4jProperties;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.time.Duration;

/**
 * LangChain4j配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration
@ConditionalOnProperty(value = "langchain4j.enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(LangChain4jProperties.class)
public class LangChain4jConfig {

    private final LangChain4jProperties properties;

    public LangChain4jConfig(LangChain4jProperties properties) {
        this.properties = properties;
    }

    // 修改 ollamaChatModel 方法
    @Bean("ollamaChatModel")
    public ChatModel ollamaChatModel() {
        LangChain4jProperties.OllamaConfig config = properties.getOllama();

        return OllamaChatModel.builder()
            .baseUrl(config.getBaseUrl())
            .modelName(config.getModel())
            .temperature(config.getTemperature())
            .timeout(Duration.ofSeconds(config.getTimeout()))
            .httpClientBuilder(new JdkHttpClientBuilderFactory().create())
            .build();
    }

    // 修改 ollamaStreamingChatModel 方法
    @Bean("ollamaStreamingChatModel")
    public StreamingChatModel ollamaStreamingChatModel() {
        LangChain4jProperties.OllamaConfig config = properties.getOllama();

        return OllamaStreamingChatModel.builder()
            .baseUrl(config.getBaseUrl())
            .modelName(config.getModel())
            .temperature(config.getTemperature())
            .timeout(Duration.ofSeconds(config.getTimeout()))
            .httpClientBuilder(new JdkHttpClientBuilderFactory().create()) // 显式指定HTTP客户端
            .build();
    }

    /**
     * OpenAI聊天模型
     */
    @Bean("openAiChatModel")
    public ChatModel openAiChatModel() {
        LangChain4jProperties.OpenAiConfig config = properties.getOpenai();
        if (StrUtil.isBlank(config.getApiKey())) {
            return null;
        }

        return OpenAiChatModel.builder()
            .apiKey(config.getApiKey())
            .baseUrl(config.getBaseUrl())
            .modelName(config.getModel())
            .temperature(config.getTemperature())
            .maxTokens(config.getMaxTokens())
            .timeout(Duration.ofSeconds(config.getTimeout()))
            .httpClientBuilder(new JdkHttpClientBuilderFactory().create()) // 显式指定HTTP客户端
            .build();
    }


    /**
     * 阿里云通义千问聊天模型
     */
    @Bean("dashscopeChatModel")
    public ChatModel dashscopeChatModel() {
        LangChain4jProperties.DashscopeConfig config = properties.getDashscope();
        if (StrUtil.isBlank(config.getApiKey())) {
            return null;
        }

        return QwenChatModel.builder()
            .apiKey(config.getApiKey())
            .modelName(config.getModel())
            .temperature(config.getTemperature().floatValue())
            .maxTokens(config.getMaxTokens())
            .build();
    }

    /**
     * OpenAI流式聊天模型
     */
    @Bean("openAiStreamingChatModel")
    public StreamingChatModel openAiStreamingChatModel() {
        LangChain4jProperties.OpenAiConfig config = properties.getOpenai();
        if (StrUtil.isBlank(config.getApiKey())) {
            return null;
        }

        return OpenAiStreamingChatModel.builder()
            .apiKey(config.getApiKey())
            .baseUrl(config.getBaseUrl())
            .modelName(config.getModel())
            .temperature(config.getTemperature())
            .maxTokens(config.getMaxTokens())
            .timeout(Duration.ofSeconds(config.getTimeout()))
            .httpClientBuilder(new JdkHttpClientBuilderFactory().create()) // 显式指定HTTP客户端
            .build();
    }


    /**
     * 阿里云通义千问流式聊天模型
     */
    @Bean("dashscopeStreamingChatModel")
    public StreamingChatModel dashscopeStreamingChatModel() {
        LangChain4jProperties.DashscopeConfig config = properties.getDashscope();
        if (StrUtil.isBlank(config.getApiKey())) {
            return null;
        }

        return QwenStreamingChatModel.builder()
            .apiKey(config.getApiKey())
            .modelName(config.getModel())
            .temperature(config.getTemperature().floatValue())
            .maxTokens(config.getMaxTokens())
            .build();
    }

    /**
     * 默认聊天模型
     */
    @Bean
    @Primary
    public ChatModel defaultChatModel() {
        String provider = properties.getAgent().getDefaultProvider();

        switch (provider.toLowerCase()) {
            case "openai":
                ChatModel openAiModel = openAiChatModel();
                if (openAiModel != null) {
                    return openAiModel;
                }
                break;
            case "ollama":
                return ollamaChatModel();
            case "dashscope":
                ChatModel dashscopeModel = dashscopeChatModel();
                if (dashscopeModel != null) {
                    return dashscopeModel;
                }
                break;
        }

        // 如果指定的提供商不可用，则回退到Ollama（本地模型）
        return ollamaChatModel();
    }

    /**
     * 默认流式聊天模型
     */
    @Bean
    @Primary
    public StreamingChatModel defaultStreamingChatModel() {
        String provider = properties.getAgent().getDefaultProvider();

        switch (provider.toLowerCase()) {
            case "openai":
                StreamingChatModel openAiModel = openAiStreamingChatModel();
                if (openAiModel != null) {
                    return openAiModel;
                }
                break;
            case "ollama":
                return ollamaStreamingChatModel();

            case "dashscope":
                StreamingChatModel dashscopeModel = dashscopeStreamingChatModel();
                if (dashscopeModel != null) {
                    return dashscopeModel;
                }
                break;
        }

        // 如果指定的提供商不可用，则回退到Ollama（本地模型）
        return ollamaStreamingChatModel();
    }
}
