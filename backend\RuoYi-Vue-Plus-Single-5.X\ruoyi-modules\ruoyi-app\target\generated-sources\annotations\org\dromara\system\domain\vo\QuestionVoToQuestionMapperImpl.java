package org.dromara.system.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.common.mybatis.core.domain.Question;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T11:28:40+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.11 (Amazon.com Inc.)"
)
@Component
public class QuestionVoToQuestionMapperImpl implements QuestionVoToQuestionMapper {

    @Override
    public Question convert(QuestionVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Question question = new Question();

        question.setCreateTime( arg0.getCreateTime() );
        question.setUpdateTime( arg0.getUpdateTime() );
        question.setQuestionId( arg0.getQuestionId() );
        question.setBankId( arg0.getBankId() );
        question.setQuestionCode( arg0.getQuestionCode() );
        question.setTitle( arg0.getTitle() );
        question.setDescription( arg0.getDescription() );
        question.setContent( arg0.getContent() );
        question.setAnswer( arg0.getAnswer() );
        question.setAnalysis( arg0.getAnalysis() );
        question.setDifficulty( arg0.getDifficulty() );
        question.setCategory( arg0.getCategory() );
        question.setType( arg0.getType() );
        question.setPracticeCount( arg0.getPracticeCount() );
        question.setCorrectRate( arg0.getCorrectRate() );
        question.setAcceptanceRate( arg0.getAcceptanceRate() );
        question.setCommentCount( arg0.getCommentCount() );
        question.setTags( arg0.getTags() );
        question.setSort( arg0.getSort() );
        question.setStatus( arg0.getStatus() );
        question.setRemark( arg0.getRemark() );

        return question;
    }

    @Override
    public Question convert(QuestionVo arg0, Question arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setQuestionId( arg0.getQuestionId() );
        arg1.setBankId( arg0.getBankId() );
        arg1.setQuestionCode( arg0.getQuestionCode() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setContent( arg0.getContent() );
        arg1.setAnswer( arg0.getAnswer() );
        arg1.setAnalysis( arg0.getAnalysis() );
        arg1.setDifficulty( arg0.getDifficulty() );
        arg1.setCategory( arg0.getCategory() );
        arg1.setType( arg0.getType() );
        arg1.setPracticeCount( arg0.getPracticeCount() );
        arg1.setCorrectRate( arg0.getCorrectRate() );
        arg1.setAcceptanceRate( arg0.getAcceptanceRate() );
        arg1.setCommentCount( arg0.getCommentCount() );
        arg1.setTags( arg0.getTags() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
