{"doc": "\n 成就定义Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByAchievementCode", "paramTypes": ["java.lang.String"], "doc": "\n 根据成就代码查询成就\r\n\r\n @param achievementCode 成就代码\r\n @return 成就信息\r\n"}, {"name": "selectByAchievementType", "paramTypes": ["java.lang.String"], "doc": "\n 根据成就类型查询成就列表\r\n\r\n @param achievementType 成就类型\r\n @return 成就列表\r\n"}, {"name": "selectActiveAchievements", "paramTypes": [], "doc": "\n 查询激活的成就列表\r\n\r\n @return 激活的成就列表\r\n"}, {"name": "selectOrderBySortOrder", "paramTypes": [], "doc": "\n 根据排序查询成就列表\r\n\r\n @return 按排序的成就列表\r\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "java.lang.String"], "doc": "\n 批量更新成就状态\r\n\r\n @param ids      成就ID列表\r\n @param isActive 是否激活\r\n @return 更新数量\r\n"}, {"name": "countTotalAchievements", "paramTypes": [], "doc": "\n 获取总成就数\r\n\r\n @return 总成就数\r\n"}, {"name": "countAchievementsByType", "paramTypes": ["java.lang.String"], "doc": "\n 根据类型获取成就数\r\n\r\n @param achievementType 成就类型\r\n @return 该类型的成就数\r\n"}, {"name": "sumTotalRewardPoints", "paramTypes": [], "doc": "\n 获取成就总积分\r\n\r\n @return 成就总积分\r\n"}, {"name": "countAchievementsByRarity", "paramTypes": ["java.lang.String"], "doc": "\n 根据稀有度获取成就数\r\n @param rarity 稀有度\r\n @return 该稀有度的成就数\r\n"}], "constructors": []}