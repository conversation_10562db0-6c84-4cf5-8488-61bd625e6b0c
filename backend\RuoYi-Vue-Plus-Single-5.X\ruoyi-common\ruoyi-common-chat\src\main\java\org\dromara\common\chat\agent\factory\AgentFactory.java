package org.dromara.common.chat.agent.factory;

import cn.hutool.core.util.StrUtil;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.StreamingChatModel;
import dev.langchain4j.service.AiServices;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.chat.agent.AgentContext;
import org.dromara.common.chat.agent.AgentType;
import org.dromara.common.chat.agent.service.*;
import org.dromara.common.chat.config.properties.LangChain4jProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * AI Agent工厂类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AgentFactory {

    // Agent实例缓存
    private final ConcurrentMap<String, Object> agentCache = new ConcurrentHashMap<>();
    @Autowired
    private ChatModel defaultChatModel;
    @Autowired
    private StreamingChatModel defaultStreamingChatModel;
    @Autowired(required = false)
    @Qualifier("openAiChatModel")
    private ChatModel openAiChatModel;
    @Autowired(required = false)
    @Qualifier("ollamaChatModel")
    private ChatModel ollamaChatModel;
    @Autowired(required = false)
    @Qualifier("dashscopeChatModel")
    private ChatModel dashscopeChatModel;
    @Autowired(required = false)
    @Qualifier("openAiStreamingChatModel")
    private StreamingChatModel openAiStreamingChatModel;
    @Autowired(required = false)
    @Qualifier("ollamaStreamingChatModel")
    private StreamingChatModel ollamaStreamingChatModel;
    @Autowired(required = false)
    @Qualifier("dashscopeStreamingChatModel")
    private StreamingChatModel dashscopeStreamingChatModel;
    @Autowired
    private LangChain4jProperties properties;

    /**
     * 创建Agent
     */
    @SuppressWarnings("unchecked")
    public <T> T createAgent(AgentType agentType, AgentContext context, String provider) {
        // 根据Agent类型调用相应的专用方法
        return switch (agentType) {
            case INTERVIEWER -> (T) createInterviewerAgent(context, provider);
            case RESUME_ANALYZER -> (T) createResumeAnalyzerAgent(context, provider);
            case SKILL_ASSESSOR -> (T) createSkillAssessorAgent(context, provider);
            case CAREER_ADVISOR -> (T) createCareerAdvisorAgent(context, provider);
            case MOCK_INTERVIEWER -> (T) createMockInterviewerAgent(context, provider);
            case LEARNING_GUIDE -> (T) createLearningGuideAgent(context, provider);
            default -> (T) createGeneralChatAgent(context, provider);
        };
    }

    /**
     * 创建Agent（使用默认提供商）
     */
    public <T> T createAgent(AgentType agentType, AgentContext context) {
        return createAgent(agentType, context, properties.getAgent().getDefaultProvider());
    }

    /**
     * 创建面试官Agent
     *
     * @param context  上下文
     * @param provider 模型提供商（可选）
     * @return 面试官Agent实例
     */
    public InterviewerAgent createInterviewerAgent(AgentContext context, String provider) {
        String cacheKey = buildCacheKey(AgentType.INTERVIEWER, context.getSessionId(), provider);

        return (InterviewerAgent) agentCache.computeIfAbsent(cacheKey, key -> {
            ChatModel model = selectModel(provider);
            MessageWindowChatMemory memory = MessageWindowChatMemory.withMaxMessages(
                properties.getAgent().getMaxMemorySize()
            );

            return AiServices.builder(InterviewerAgent.class)
                .chatModel(model)
                .chatMemory(memory)
                .build();
        });
    }

    /**
     * 创建简历分析Agent
     *
     * @param context  上下文
     * @param provider 模型提供商（可选）
     * @return 简历分析Agent实例
     */
    public ResumeAnalyzerAgent createResumeAnalyzerAgent(AgentContext context, String provider) {
        String cacheKey = buildCacheKey(AgentType.RESUME_ANALYZER, context.getSessionId(), provider);

        return (ResumeAnalyzerAgent) agentCache.computeIfAbsent(cacheKey, key -> {
            ChatModel model = selectModel(provider);
            MessageWindowChatMemory memory = MessageWindowChatMemory.withMaxMessages(
                properties.getAgent().getMaxMemorySize()
            );

            return AiServices.builder(ResumeAnalyzerAgent.class)
                .chatModel(model)
                .chatMemory(memory)
                .build();
        });
    }

    /**
     * 创建技能评估Agent
     *
     * @param context  上下文
     * @param provider 模型提供商（可选）
     * @return 技能评估Agent实例
     */
    public SkillAssessorAgent createSkillAssessorAgent(AgentContext context, String provider) {
        String cacheKey = buildCacheKey(AgentType.SKILL_ASSESSOR, context.getSessionId(), provider);

        return (SkillAssessorAgent) agentCache.computeIfAbsent(cacheKey, key -> {
            ChatModel model = selectModel(provider);
            MessageWindowChatMemory memory = MessageWindowChatMemory.withMaxMessages(
                properties.getAgent().getMaxMemorySize()
            );

            return AiServices.builder(SkillAssessorAgent.class)
                .chatModel(model)
                .chatMemory(memory)
                .build();
        });
    }

    /**
     * 创建职业顾问Agent
     *
     * @param context  上下文
     * @param provider 模型提供商（可选）
     * @return 职业顾问Agent实例
     */
    public CareerAdvisorAgent createCareerAdvisorAgent(AgentContext context, String provider) {
        String cacheKey = buildCacheKey(AgentType.CAREER_ADVISOR, context.getSessionId(), provider);

        return (CareerAdvisorAgent) agentCache.computeIfAbsent(cacheKey, key -> {
            ChatModel model = selectModel(provider);
            MessageWindowChatMemory memory = MessageWindowChatMemory.withMaxMessages(
                properties.getAgent().getMaxMemorySize()
            );

            return AiServices.builder(CareerAdvisorAgent.class)
                .chatModel(model)
                .chatMemory(memory)
                .build();
        });
    }

    /**
     * 创建模拟面试Agent
     *
     * @param context  上下文
     * @param provider 模型提供商（可选）
     * @return 模拟面试Agent实例
     */
    public MockInterviewerAgent createMockInterviewerAgent(AgentContext context, String provider) {
        String cacheKey = buildCacheKey(AgentType.MOCK_INTERVIEWER, context.getSessionId(), provider);

        return (MockInterviewerAgent) agentCache.computeIfAbsent(cacheKey, key -> {
            ChatModel model = selectModel(provider);
            MessageWindowChatMemory memory = MessageWindowChatMemory.withMaxMessages(
                properties.getAgent().getMaxMemorySize()
            );

            return AiServices.builder(MockInterviewerAgent.class)
                .chatModel(model)
                .chatMemory(memory)
                .build();
        });
    }

    /**
     * 创建学习导师Agent
     *
     * @param context  上下文
     * @param provider 模型提供商（可选）
     * @return 学习导师Agent实例
     */
    public LearningGuideAgent createLearningGuideAgent(AgentContext context, String provider) {
        String cacheKey = buildCacheKey(AgentType.LEARNING_GUIDE, context.getSessionId(), provider);

        return (LearningGuideAgent) agentCache.computeIfAbsent(cacheKey, key -> {
            ChatModel model = selectModel(provider);
            MessageWindowChatMemory memory = MessageWindowChatMemory.withMaxMessages(
                properties.getAgent().getMaxMemorySize()
            );

            return AiServices.builder(LearningGuideAgent.class)
                .chatModel(model)
                .chatMemory(memory)
                .build();
        });
    }

    /**
     * 创建通用聊天Agent
     *
     * @param context  上下文
     * @param provider 模型提供商（可选）
     * @return 通用聊天Agent实例
     */
    public GeneralChatAgent createGeneralChatAgent(AgentContext context, String provider) {
        String cacheKey = buildCacheKey(AgentType.GENERAL_CHAT, context.getSessionId(), provider);

        return (GeneralChatAgent) agentCache.computeIfAbsent(cacheKey, key -> {
            ChatModel model = selectModel(provider);
            MessageWindowChatMemory memory = MessageWindowChatMemory.withMaxMessages(
                properties.getAgent().getMaxMemorySize()
            );

            return AiServices.builder(GeneralChatAgent.class)
                .chatModel(model)
                .chatMemory(memory)
                .build();
        });
    }

    /**
     * 选择模型
     */
    private ChatModel selectModel(String provider) {
        if (StrUtil.isBlank(provider)) {
            return defaultChatModel;
        }

        return switch (provider.toLowerCase()) {
            case "openai" -> openAiChatModel != null ? openAiChatModel : defaultChatModel;
            case "ollama" -> ollamaChatModel != null ? ollamaChatModel : defaultChatModel;
            case "dashscope" -> dashscopeChatModel != null ? dashscopeChatModel : defaultChatModel;
            default -> defaultChatModel;
        };
    }

    /**
     * 选择流式模型
     */
    public StreamingChatModel selectStreamingModel(String provider) {
        if (StrUtil.isBlank(provider)) {
            return defaultStreamingChatModel;
        }
        switch (provider.toLowerCase()) {
            case "openai":
                log.info("使用OpenAI模型");
                return openAiStreamingChatModel != null ? openAiStreamingChatModel : defaultStreamingChatModel;
            case "ollama":
                log.info("使用Ollama模型");
                return ollamaStreamingChatModel != null ? ollamaStreamingChatModel : defaultStreamingChatModel;
            case "dashscope":
                log.info("使用DashScope模型");
                log.info("使用DashScope流式聊天模型:{}", dashscopeStreamingChatModel.getClass());
                return dashscopeStreamingChatModel != null ? dashscopeStreamingChatModel : defaultStreamingChatModel;
            default:
                log.info("使用默认模型");
                return defaultStreamingChatModel;
        }

    }

    /**
     * 获取默认流式聊天模型
     */
    public StreamingChatModel getDefaultStreamingChatModel() {
        return this.defaultStreamingChatModel;
    }

    /**
     * 根据Agent类型和模型名称获取流式聊天模型
     *
     * @param agentType Agent类型
     * @param modelName 模型名称（可选）
     * @return 流式聊天模型
     */
    public StreamingChatModel getStreamingChatModel(String agentType, String modelName) {
        // 如果指定了模型名称，优先使用指定的模型
        if (StrUtil.isNotBlank(modelName)) {
            return selectStreamingModel(modelName);
        }

        // 否则根据Agent类型选择默认的模型
        String provider = properties.getAgent().getDefaultProvider();

        // 对特定Agent类型使用特定模型
        if (agentType != null) {
            switch (agentType.toLowerCase()) {
                case "general":
                case "general_chat":
                    // 通用聊天可以使用默认模型
                    break;
                case "interviewer":
                case "mock_interviewer":
                    // 面试相关Agent优先使用能力更强的模型
                    provider = "openai";
                    break;
                case "resume_analyzer":
                    // 简历分析优先使用文本理解能力强的模型
                    provider = "openai";
                    break;
                default:
                    // 其他类型使用默认模型
                    break;
            }
        }

        return selectStreamingModel(provider);
    }

    /**
     * 构建缓存键
     */
    private String buildCacheKey(AgentType agentType, String sessionId, String provider) {
        return String.format("%s:%s:%s", agentType.getCode(), sessionId, provider);
    }

    /**
     * 清理Agent缓存
     */
    public void clearAgentCache(String sessionId) {
        agentCache.entrySet().removeIf(entry -> entry.getKey().contains(":" + sessionId + ":"));
    }

    /**
     * 清理所有Agent缓存
     */
    public void clearAllAgentCache() {
        agentCache.clear();
    }
}
