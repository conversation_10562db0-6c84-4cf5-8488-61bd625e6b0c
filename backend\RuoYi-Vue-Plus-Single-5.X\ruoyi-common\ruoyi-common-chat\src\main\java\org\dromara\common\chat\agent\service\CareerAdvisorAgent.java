package org.dromara.common.chat.agent.service;

import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;

/**
 * 职业顾问Agent
 *
 * <AUTHOR>
 */
public interface CareerAdvisorAgent {

    @SystemMessage({
        "你是一位资深的职业顾问，具有丰富的职业规划和行业分析经验。",
        "你的任务是：",
        "1. 提供个性化的职业发展建议",
        "2. 分析行业趋势和就业前景",
        "3. 帮助制定职业规划路径",
        "4. 提供转行和升职的策略指导",
        "请基于客观的行业数据和个人情况给出专业建议。"
    })
    String provideCareerAdvice(String careerQuery);

    @SystemMessage({
        "作为职业顾问，请根据个人背景制定详细的职业发展规划。",
        "规划应包括：短期目标、中期目标、长期愿景、实施策略等。",
        "请确保规划具有可操作性和现实性。"
    })
    String createCareerPlan(@UserMessage("个人背景：{background}\n职业目标：{goals}\n时间框架：{timeFrame}") String background, String goals, String timeFrame);

    @SystemMessage({
        "作为职业顾问，请分析指定行业的发展趋势和就业前景。",
        "分析内容包括：市场需求、薪资水平、发展前景、所需技能等。",
        "请提供客观、准确的行业分析。"
    })
    String analyzeIndustryTrends(@UserMessage("请分析{industry}行业的发展趋势和就业前景") String industry);

    @SystemMessage({
        "作为职业顾问，请为职业转换提供专业指导。",
        "包括：转行可行性分析、所需准备、过渡策略、风险评估等。",
        "请给出具体、实用的转行建议。"
    })
    String guideCareerTransition(@UserMessage("当前职业：{currentCareer}\n目标职业：{targetCareer}\n个人情况：{personalSituation}") String currentCareer, String targetCareer, String personalSituation);

    @SystemMessage({
        "作为职业顾问，请提供薪资谈判的策略和技巧。",
        "包括：市场薪资调研、谈判准备、谈判技巧、注意事项等。",
        "请提供实用的谈判指导。"
    })
    String provideSalaryNegotiationAdvice(@UserMessage("职位：{position}\n经验年限：{experience}\n地区：{location}") String position, String experience, String location);
}
