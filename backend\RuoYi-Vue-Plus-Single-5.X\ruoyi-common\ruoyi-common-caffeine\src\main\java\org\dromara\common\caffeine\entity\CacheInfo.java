package org.dromara.common.caffeine.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 缓存信息实体
 *
 * <AUTHOR>
 */
@Data
public class CacheInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 缓存名称
     */
    private String cacheName;

    /**
     * 缓存键
     */
    private String key;

    /**
     * 缓存值
     */
    private Object value;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 访问次数
     */
    private Long accessCount;

    /**
     * 最后访问时间
     */
    private Long lastAccessTime;

}
