<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.AiToolMapper">

    <resultMap type="org.dromara.app.domain.AiTool" id="AiToolResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="displayName" column="display_name"/>
        <result property="description" column="description"/>
        <result property="category" column="category"/>
        <result property="icon" column="icon"/>
        <result property="color" column="color"/>
        <result property="functionDefinition" column="function_definition"/>
        <result property="parameterSchema" column="parameter_schema"/>
        <result property="implementationClass" column="implementation_class"/>
        <result property="toolConfig" column="tool_config"/>
        <result property="enabled" column="enabled"/>
        <result property="isSystem" column="is_system"/>
        <result property="permissionLevel" column="permission_level"/>
        <result property="requiredPermissions" column="required_permissions"/>
        <result property="usageCount" column="usage_count"/>
        <result property="avgExecutionTime" column="avg_execution_time"/>
        <result property="successRate" column="success_rate"/>
        <result property="lastUsed" column="last_used"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="tags" column="tags"/>
        <result property="version" column="version"/>
        <result property="author" column="author"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectAiToolVo">
        select id,
               name,
               display_name,
               description,
               category,
               icon,
               color,
               function_definition,
               parameter_schema,
               implementation_class,
               tool_config,
               enabled,
               is_system,
               permission_level,
               required_permissions,
               usage_count,
               avg_execution_time,
               success_rate,
               last_used,
               sort_order,
               tags,
               version,
               author,
               create_by,
               create_time,
               update_by,
               update_time,
               del_flag
        from app_ai_tool
    </sql>

    <select id="selectAvailableTools" resultMap="AiToolResult">
        <include refid="selectAiToolVo"/>
        where del_flag = '0'
        <if test="enabled != null">
            and enabled = #{enabled}
        </if>
        <if test="category != null and category != ''">
            and category = #{category}
        </if>
        order by sort_order asc
    </select>

    <select id="selectEnabledByCategory" resultMap="AiToolResult">
        <include refid="selectAiToolVo"/>
        where category = #{category} and enabled = 1 and del_flag = '0'
        order by sort_order asc
    </select>

    <select id="selectByPermissionLevel" resultMap="AiToolResult">
        <include refid="selectAiToolVo"/>
        where enabled = 1 and permission_level &lt;= #{maxPermissionLevel} and del_flag = '0'
        order by sort_order asc
    </select>

    <select id="selectSystemTools" resultMap="AiToolResult">
        <include refid="selectAiToolVo"/>
        where is_system = 1 and enabled = 1 and del_flag = '0'
        order by sort_order asc
    </select>

    <select id="selectPopularTools" resultMap="AiToolResult">
        <include refid="selectAiToolVo"/>
        where enabled = 1 and del_flag = '0'
        order by usage_count desc
        limit #{limit}
    </select>

    <select id="selectToolsByUsage" resultMap="AiToolResult">
        <include refid="selectAiToolVo"/>
        where enabled = 1 and del_flag = '0'
        <if test="minUsageCount != null">
            and usage_count &gt;= #{minUsageCount}
        </if>
        order by usage_count desc
    </select>

    <select id="selectToolsByCategory" resultMap="AiToolResult">
        <include refid="selectAiToolVo"/>
        where enabled = 1 and del_flag = '0'
        <if test="categories != null and categories.size() > 0">
            and category in
            <foreach collection="categories" item="category" open="(" separator="," close=")">
                #{category}
            </foreach>
        </if>
        order by category, sort_order asc
    </select>

    <select id="searchTools" resultMap="AiToolResult">
        <include refid="selectAiToolVo"/>
        where enabled = 1 and del_flag = '0'
        <if test="keyword != null and keyword != ''">
            and (name like concat('%', #{keyword}, '%')
            or display_name like concat('%', #{keyword}, '%')
            or description like concat('%', #{keyword}, '%')
            or tags like concat('%', #{keyword}, '%'))
        </if>
        order by sort_order asc
    </select>

    <select id="getToolStats" resultType="java.util.Map">
        select count(*)                                       as totalTools,
               sum(case when enabled = 1 then 1 else 0 end)   as enabledTools,
               sum(case when is_system = 1 then 1 else 0 end) as systemTools,
               sum(usage_count)                               as totalUsage,
               avg(success_rate)                              as avgSuccessRate
        from app_ai_tool
        where del_flag = '0'
    </select>

    <select id="getCategoryStats" resultType="java.util.Map">
        select category,
               count(*)          as toolCount,
               sum(usage_count)  as totalUsage,
               avg(success_rate) as avgSuccessRate
        from app_ai_tool
        where enabled = 1
          and del_flag = '0'
        group by category
        order by toolCount desc
    </select>
    <select id="selectEnabledTools" resultMap="AiToolResult">
        <include refid="selectAiToolVo"/>
        where enabled = 1 and del_flag = '0'
        order by sort_order asc
    </select>

    <!-- 根据工具名称查询工具 -->
    <select id="selectByName" parameterType="String" resultMap="AiToolResult">
        <include refid="selectAiToolVo"/>
        where name = #{toolName} and del_flag = '0'
    </select>

    <update id="incrementUsageCount">
        update app_ai_tool
        set usage_count = usage_count + 1,
            last_used   = #{lastUsed}
        where id = #{toolId}
          and del_flag = '0'
    </update>

    <update id="updateToolStats">
        update app_ai_tool
        set usage_count        = #{usageCount},
            avg_execution_time = #{avgExecutionTime},
            success_rate       = #{successRate},
            last_used          = #{lastUsed}
        where id = #{toolId}
          and del_flag = '0'
    </update>

    <update id="batchUpdateToolStatus">
        update app_ai_tool
        set enabled = #{enabled}
        where id in
        <foreach collection="toolIds" item="toolId" open="(" separator="," close=")">
            #{toolId}
        </foreach>
        and del_flag = '0'
    </update>

    <delete id="batchDeleteTools">
        update app_ai_tool
        set del_flag = '1'
        where id in
        <foreach collection="toolIds" item="toolId" open="(" separator="," close=")">
            #{toolId}
        </foreach>
    </delete>

    <insert id="batchInsertDefaultTools">
        insert into app_ai_tool
        (id, name, display_name, description, category, icon, color, function_definition, parameter_schema,
        implementation_class, tool_config, enabled, is_system, permission_level, required_permissions, usage_count,
        avg_execution_time, success_rate, last_used, sort_order, tags, version, author, create_by, create_time,
        update_by, update_time, del_flag)
        values
        <foreach item="tool" collection="tools" separator=",">
            (#{tool.id}, #{tool.name}, #{tool.displayName}, #{tool.description}, #{tool.category}, #{tool.icon},
            #{tool.color}, #{tool.functionDefinition}, #{tool.parameterSchema}, #{tool.implementationClass},
            #{tool.toolConfig}, #{tool.enabled}, #{tool.isSystem}, #{tool.permissionLevel}, #{tool.requiredPermissions},
            #{tool.usageCount}, #{tool.avgExecutionTime}, #{tool.successRate}, #{tool.lastUsed}, #{tool.sortOrder},
            #{tool.tags}, #{tool.version}, #{tool.author}, #{tool.createBy}, #{tool.createTime}, #{tool.updateBy},
            #{tool.updateTime}, #{tool.delFlag})
        </foreach>
    </insert>
</mapper>
