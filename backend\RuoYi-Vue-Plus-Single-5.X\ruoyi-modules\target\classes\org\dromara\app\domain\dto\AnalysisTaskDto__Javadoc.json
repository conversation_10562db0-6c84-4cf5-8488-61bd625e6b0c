{"doc": " 分析任务数据传输对象\n\n <AUTHOR>\n @date 2025-01-19\n", "fields": [{"name": "taskId", "doc": " 任务ID\n"}, {"name": "sessionId", "doc": " 会话ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "taskType", "doc": " 任务类型：audio/video/text/comprehensive\n"}, {"name": "priority", "doc": " 任务优先级：1-10，数字越大优先级越高\n"}, {"name": "status", "doc": " 任务状态：pending/running/completed/failed/cancelled\n"}, {"name": "audioFilePath", "doc": " 音频文件路径\n"}, {"name": "videoFilePath", "doc": " 视频文件路径\n"}, {"name": "textContent", "doc": " 文本内容\n"}, {"name": "jobPosition", "doc": " 岗位信息\n"}, {"name": "createTime", "doc": " 任务创建时间\n"}, {"name": "startTime", "doc": " 任务开始时间\n"}, {"name": "completeTime", "doc": " 任务完成时间\n"}, {"name": "errorMessage", "doc": " 错误信息\n"}, {"name": "retryCount", "doc": " 重试次数\n"}, {"name": "maxRetries", "doc": " 最大重试次数\n"}, {"name": "timeoutSeconds", "doc": " 任务超时时间（秒）\n"}, {"name": "resultData", "doc": " 任务结果数据\n"}], "enumConstants": [], "methods": [], "constructors": []}