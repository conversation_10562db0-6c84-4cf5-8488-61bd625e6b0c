{"doc": "\n 讯飞数字人WebSocket连接管理服务\r\n\r\n <AUTHOR>\r\n @date 2025-07-20\r\n", "fields": [{"name": "messageHandler", "doc": "\n -- SETTER --\r\n  设置消息处理器\r\n\r\n @param messageHandler 消息处理器\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "doc": "\n -- SETTER --\r\n  设置错误处理器\r\n\r\n @param errorHandler 错误处理器\r\n"}], "enumConstants": [], "methods": [{"name": "connect", "paramTypes": ["java.lang.String", "long"], "doc": "\n 建立WebSocket连接\r\n\r\n @param requestUrl 请求URL（已包含认证信息）\r\n @param timeout    连接超时时间（秒）\r\n @return 是否连接成功\r\n"}, {"name": "sendMessage", "paramTypes": ["com.alibaba.fastjson.JSONObject"], "doc": "\n 发送消息\r\n\r\n @param request 请求消息\r\n @return 是否发送成功\r\n"}, {"name": "startAndWait", "paramTypes": ["com.alibaba.fastjson.JSONObject", "long"], "doc": "\n 发送启动请求并等待响应\r\n\r\n @param request 启动请求\r\n @param timeout 超时时间（秒）\r\n @return 是否启动成功\r\n"}, {"name": "close", "paramTypes": [], "doc": "\n 关闭连接\r\n"}, {"name": "isConnected", "paramTypes": [], "doc": "\n 检查连接状态\r\n\r\n @return 是否已连接\r\n"}, {"name": "buildWebSocketListener", "paramTypes": [], "doc": "\n 构建WebSocket监听器\r\n"}, {"name": "extractStreamUrl", "paramTypes": ["com.alibaba.fastjson.JSONObject"], "doc": "\n 从WebSocket消息中提取推流地址\r\n"}, {"name": "extractFromAvatar", "paramTypes": ["com.alibaba.fastjson.JSONObject"], "doc": "\n 从avatar字段中提取推流地址\r\n"}, {"name": "extractFromPayload", "paramTypes": ["com.alibaba.fastjson.JSONObject"], "doc": "\n 从payload根级别提取推流地址\r\n"}, {"name": "extractFromOtherFields", "paramTypes": ["com.alibaba.fastjson.JSONObject"], "doc": "\n 从其他可能的字段中提取推流地址\r\n"}, {"name": "notifyStreamUrlFound", "paramTypes": [], "doc": "\n 通知推流地址已找到\r\n"}], "constructors": []}