{"doc": " 视频详情视图对象\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 视频ID\n"}, {"name": "title", "doc": " 视频标题\n"}, {"name": "description", "doc": " 视频简介\n"}, {"name": "instructor", "doc": " 讲师名称\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "doc": " 讲师头像\n"}, {"name": "instructorId", "doc": " 讲师ID\n"}, {"name": "duration", "doc": " 视频时长\n"}, {"name": "thumbnail", "doc": " 缩略图\n"}, {"name": "category", "doc": " 分类\n"}, {"name": "difficulty", "doc": " 难度\n"}, {"name": "rating", "doc": " 评分\n"}, {"name": "studentCount", "doc": " 学习人数\n"}, {"name": "price", "doc": " 价格\n"}, {"name": "free", "doc": " 是否免费\n"}, {"name": "videoUrl", "doc": " 视频地址\n"}, {"name": "viewCount", "doc": " 播放次数\n"}, {"name": "likeCount", "doc": " 点赞数\n"}, {"name": "collectCount", "doc": " 收藏数\n"}, {"name": "shareCount", "doc": " 分享数\n"}, {"name": "tags", "doc": " 标签列表\n"}, {"name": "publishTime", "doc": " 发布时间\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}, {"name": "isLiked", "doc": " 是否已点赞\n"}, {"name": "isCollected", "doc": " 是否已收藏\n"}, {"name": "isPurchased", "doc": " 是否已购买\n"}, {"name": "isCompleted", "doc": " 是否已完成\n"}, {"name": "completionRate", "doc": " 播放进度百分比\n"}, {"name": "isFollowed", "doc": " 是否已关注讲师\n"}, {"name": "instructorFollowers", "doc": " 讲师粉丝数\n"}], "enumConstants": [], "methods": [], "constructors": []}