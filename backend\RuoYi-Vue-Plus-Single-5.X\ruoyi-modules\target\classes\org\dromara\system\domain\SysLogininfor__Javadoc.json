{"doc": " 系统访问记录表 sys_logininfor\n\n <AUTHOR>\n", "fields": [{"name": "infoId", "doc": " ID\n"}, {"name": "userName", "doc": " 用户账号\n"}, {"name": "client<PERSON>ey", "doc": " 客户端\n"}, {"name": "deviceType", "doc": " 设备类型\n"}, {"name": "status", "doc": " 登录状态 0成功 1失败\n"}, {"name": "ipaddr", "doc": " 登录IP地址\n"}, {"name": "loginLocation", "doc": " 登录地点\n"}, {"name": "browser", "doc": " 浏览器类型\n"}, {"name": "os", "doc": " 操作系统\n"}, {"name": "msg", "doc": " 提示消息\n"}, {"name": "loginTime", "doc": " 访问时间\n"}], "enumConstants": [], "methods": [], "constructors": []}