package org.dromara.app.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.app.domain.KnowledgeBase;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.io.Serial;
import java.io.Serializable;

/**
 * 知识库业务对象 app_knowledge_base
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = KnowledgeBase.class, reverseConvertGenerate = false)
public class KnowledgeBaseBo extends PageQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 知识库ID
     */
    @NotNull(message = "知识库ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 知识库名称
     */
    @NotBlank(message = "知识库名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 知识库描述
     */
    private String description;

    /**
     * 知识库类型 (general/technical/business/etc.)
     */
    private String type;

    /**
     * 知识库状态 (0=禁用 1=启用)
     */
    private Integer status;

    /**
     * 向量维度 (默认1024)
     */
    private Integer vectorDimension;

    /**
     * 索引配置 (JSON格式)
     */
    private String indexConfig;

    /**
     * 扩展配置 (JSON格式)
     */
    private String extendConfig;

    /**
     * 排序字段
     */
    private Integer sortOrder;

    /**
     * 备注
     */
    private String remark;

    // ========== 查询条件字段 ==========

    /**
     * 搜索关键词（用于名称和描述的模糊查询）
     */
    private String keyword;

    /**
     * 知识库类型列表（用于多选过滤）
     */
    private String[] types;

    /**
     * 状态列表（用于多选过滤）
     */
    private Integer[] statuses;

    /**
     * 创建时间范围 - 开始时间
     */
    private String createTimeStart;

    /**
     * 创建时间范围 - 结束时间
     */
    private String createTimeEnd;

    /**
     * 更新时间范围 - 开始时间
     */
    private String updateTimeStart;

    /**
     * 更新时间范围 - 结束时间
     */
    private String updateTimeEnd;

    /**
     * 文档数量范围 - 最小值
     */
    private Long documentCountMin;

    /**
     * 文档数量范围 - 最大值
     */
    private Long documentCountMax;

    /**
     * 向量数量范围 - 最小值
     */
    private Long vectorCountMin;

    /**
     * 向量数量范围 - 最大值
     */
    private Long vectorCountMax;
}
