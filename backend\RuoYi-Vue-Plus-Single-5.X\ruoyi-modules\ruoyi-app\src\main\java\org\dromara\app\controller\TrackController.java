package org.dromara.app.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.dto.TrackEventDto;
import org.dromara.app.service.IUserBehaviorService;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

/**
 * 埋点控制器(用于成就检测)
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/track")
public class TrackController extends BaseController {

    private final IUserBehaviorService userBehaviorService;

    /**
     * 记录用户行为埋点
     */
    @SaCheckLogin
    @Log(title = "用户行为埋点", businessType = BusinessType.INSERT)
    @PostMapping("/event")
    public R<Void> trackEvent(@Valid @RequestBody TrackEventDto trackEventDto, HttpServletRequest request) {
        try {
            // 获取当前登录用户ID
            Long userId = StpUtil.getLoginIdAsLong();
            trackEventDto.setUserId(userId);

            // 设置请求相关信息
            trackEventDto.setIpAddress(getClientIP(request));
            trackEventDto.setUserAgent(request.getHeader("User-Agent"));

            // 如果没有时间戳，设置当前时间
            if (trackEventDto.getTimestamp() == null) {
                trackEventDto.setTimestamp(System.currentTimeMillis());
            }

            // 处理用户行为数据
            userBehaviorService.processUserBehavior(trackEventDto);

            return R.ok();
        } catch (Exception e) {
            log.error("记录用户行为埋点失败", e);
            return R.fail("记录用户行为失败");
        }
    }

    /**
     * 批量记录用户行为埋点
     */
    @SaCheckLogin
    @Log(title = "批量用户行为埋点", businessType = BusinessType.INSERT)
    @PostMapping("/events")
    public R<Void> trackEvents(@Valid @RequestBody java.util.List<TrackEventDto> trackEventDtos, HttpServletRequest request) {
        try {
            // 获取当前登录用户ID
            Long userId = StpUtil.getLoginIdAsLong();
            String ipAddress = getClientIP(request);
            String userAgent = request.getHeader("User-Agent");
            long currentTime = System.currentTimeMillis();

            // 为每个事件设置用户信息
            for (TrackEventDto trackEventDto : trackEventDtos) {
                trackEventDto.setUserId(userId);
                trackEventDto.setIpAddress(ipAddress);
                trackEventDto.setUserAgent(userAgent);

                if (trackEventDto.getTimestamp() == null) {
                    trackEventDto.setTimestamp(currentTime);
                }
            }

            // 批量处理用户行为数据
            userBehaviorService.batchProcessUserBehavior(trackEventDtos);

            return R.ok();
        } catch (Exception e) {
            log.error("批量记录用户行为埋点失败", e);
            return R.fail("批量记录用户行为失败");
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIP(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }

        String xRealIP = request.getHeader("X-Real-IP");
        if (xRealIP != null && !xRealIP.isEmpty() && !"unknown".equalsIgnoreCase(xRealIP)) {
            return xRealIP;
        }

        return request.getRemoteAddr();
    }

}
