{"doc": "\n 题目视图对象 app_question\r\n\r\n <AUTHOR>\r\n @date 2025-08-01\r\n", "fields": [{"name": "questionId", "doc": "\n 题目ID\r\n"}, {"name": "title", "doc": "\n 题目标题\r\n"}, {"name": "content", "doc": "\n 题目内容/描述\r\n"}, {"name": "description", "doc": "\n 题目描述（兼容字段）\r\n"}, {"name": "difficulty", "doc": "\n 难度（简单/中等/困难）\r\n"}, {"name": "type", "doc": "\n 题目类型（单选题/多选题/判断题/编程题/简答题）\r\n"}, {"name": "tags", "doc": "\n 标签列表\r\n"}, {"name": "acceptanceRate", "doc": "\n 通过率（百分比）\r\n"}, {"name": "isCompleted", "doc": "\n 是否已完成\r\n"}, {"name": "isBookmarked", "doc": "\n 是否已收藏\r\n"}, {"name": "practiceCount", "doc": "\n 练习次数\r\n"}, {"name": "correctRate", "doc": "\n 正确率（百分比）\r\n"}, {"name": "commentCount", "doc": "\n 评论数\r\n"}, {"name": "category", "doc": "\n 分类\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "estimatedTime", "doc": "\n 预计完成时间（分钟）\r\n"}, {"name": "bankId", "doc": "\n 题库ID\r\n"}, {"name": "bankTitle", "doc": "\n 题库标题\r\n"}], "enumConstants": [], "methods": [], "constructors": []}