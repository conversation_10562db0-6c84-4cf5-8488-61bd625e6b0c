{"doc": " 埋点事件数据传输对象\n\n <AUTHOR>\n", "fields": [{"name": "eventType", "doc": " 事件类型\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "eventData", "doc": " 事件数据\n"}, {"name": "timestamp", "doc": " 时间戳\n"}, {"name": "sessionId", "doc": " 会话ID\n"}, {"name": "pageUrl", "doc": " 页面URL\n"}, {"name": "pageTitle", "doc": " 页面标题\n"}, {"name": "userAgent", "doc": " 用户代理\n"}, {"name": "ip<PERSON><PERSON><PERSON>", "doc": " IP地址\n"}, {"name": "deviceInfo", "doc": " 设备信息\n"}, {"name": "browserInfo", "doc": " 浏览器信息\n"}], "enumConstants": [], "methods": [], "constructors": []}