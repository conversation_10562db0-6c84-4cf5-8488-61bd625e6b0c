{"doc": " A {@link org.springframework.cache.CacheManager} implementation\n backed by Redisson instance.\n <p>\n 修改 RedissonSpringCacheManager 源码\n 重写 cacheName 处理方法 支持多参数\n\n <AUTHOR>\n\n", "fields": [], "enumConstants": [], "methods": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["boolean"], "doc": " Defines possibility of storing {@code null} values.\n <p>\n Default is <code>true</code>\n\n @param allowNullValues stores if <code>true</code>\n"}, {"name": "setTransactionAware", "paramTypes": ["boolean"], "doc": " Defines if cache aware of Spring-managed transactions.\n If {@code true} put/evict operations are executed only for successful transaction in after-commit phase.\n <p>\n Default is <code>false</code>\n\n @param transactionAware cache is transaction aware if <code>true</code>\n"}, {"name": "setConfig", "paramTypes": ["java.util.Map"], "doc": " Set cache config mapped by cache name\n\n @param config object\n"}, {"name": "setCacheNames", "paramTypes": ["java.util.Collection"], "doc": " Defines 'fixed' cache names.\n A new cache instance will not be created in dynamic for non-defined names.\n <p>\n `null` parameter setups dynamic mode\n\n @param names of caches\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": " Creates CacheManager supplied by Redisson instance\n"}]}