{"doc": "\n 成就通知服务实现类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendNotificationToQueue", "paramTypes": ["java.util.Map"], "doc": "\n 发送通知到消息队列\r\n"}, {"name": "processNotificationDirectly", "paramTypes": ["java.util.Map"], "doc": "\n 直接处理通知（不通过MQ）\r\n"}, {"name": "shouldSendProgressNotification", "paramTypes": ["double"], "doc": "\n 判断是否应该发送进度通知\r\n"}, {"name": "getCategoryDisplayName", "paramTypes": ["java.lang.String"], "doc": "\n 获取类别显示名称\r\n"}], "constructors": []}