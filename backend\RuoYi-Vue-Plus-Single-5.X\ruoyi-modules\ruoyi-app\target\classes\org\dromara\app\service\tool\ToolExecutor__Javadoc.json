{"doc": "\n 工具执行器接口\r\n 所有工具实现都需要实现此接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getToolName", "paramTypes": [], "doc": "\n 获取工具名称\r\n\r\n @return 工具名称\r\n"}, {"name": "getDisplayName", "paramTypes": [], "doc": "\n 获取工具显示名称\r\n\r\n @return 显示名称\r\n"}, {"name": "getDescription", "paramTypes": [], "doc": "\n 获取工具描述\r\n\r\n @return 工具描述\r\n"}, {"name": "getCategory", "paramTypes": [], "doc": "\n 获取工具分类\r\n\r\n @return 工具分类\r\n"}, {"name": "getParameterSchema", "paramTypes": [], "doc": "\n 获取参数Schema\r\n\r\n @return 参数Schema\r\n"}, {"name": "validateParameters", "paramTypes": ["java.util.Map"], "doc": "\n 验证参数\r\n\r\n @param parameters 参数\r\n @return 验证结果\r\n"}, {"name": "execute", "paramTypes": ["java.util.Map", "org.dromara.app.service.tool.ToolExecutor.ExecutionContext"], "doc": "\n 执行工具\r\n\r\n @param parameters 参数\r\n @param context    执行上下文\r\n @return 执行结果\r\n"}, {"name": "supportsAsync", "paramTypes": [], "doc": "\n 是否支持异步执行\r\n\r\n @return 是否支持异步\r\n"}, {"name": "getTimeoutSeconds", "paramTypes": [], "doc": "\n 获取执行超时时间（秒）\r\n\r\n @return 超时时间\r\n"}, {"name": "getRequiredPermissions", "paramTypes": [], "doc": "\n 获取所需权限\r\n\r\n @return 权限列表\r\n"}], "constructors": []}