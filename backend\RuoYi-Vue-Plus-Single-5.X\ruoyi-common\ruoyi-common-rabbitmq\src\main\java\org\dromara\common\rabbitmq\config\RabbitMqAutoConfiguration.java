package org.dromara.common.rabbitmq.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.rabbitmq.properties.RabbitMqProperties;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.util.StringUtils;

/**
 * RabbitMQ 自动配置类
 *
 * <AUTHOR>
 */
@Slf4j
@AutoConfiguration(after = RabbitAutoConfiguration.class)
@ConditionalOnClass({RabbitTemplate.class, Channel.class})
@EnableConfigurationProperties(RabbitMqProperties.class)
@ConditionalOnProperty(prefix = "spring.rabbitmq", name = "enabled", havingValue = "true", matchIfMissing = false)
public class RabbitMqAutoConfiguration {

    /**
     * 连接工厂
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(ConnectionFactory.class)
    @ConditionalOnProperty(prefix = "spring.rabbitmq", name = "enabled", havingValue = "true")
    public CachingConnectionFactory rabbitConnectionFactory(RabbitMqProperties properties) {
        // 验证必要的配置项
        validateRabbitMqConfig(properties);

        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(properties.getHost());
        connectionFactory.setPort(properties.getPort());
        connectionFactory.setUsername(properties.getUsername());
        connectionFactory.setPassword(properties.getPassword());
        connectionFactory.setVirtualHost(properties.getVirtualHost());

        // 设置连接超时
        connectionFactory.setConnectionTimeout(properties.getConnectionTimeout());

        // 设置缓存模式和大小
        connectionFactory.setCacheMode(CachingConnectionFactory.CacheMode.CHANNEL);
        connectionFactory.setChannelCacheSize(properties.getChannelCacheSize());

        // 开启消息确认
        connectionFactory.setPublisherConfirmType(CachingConnectionFactory.ConfirmType.CORRELATED);
        connectionFactory.setPublisherReturns(true);

        log.info("RabbitMQ 连接工厂已配置: host={}, port={}, virtualHost={}",
            properties.getHost(), properties.getPort(), properties.getVirtualHost());

        return connectionFactory;
    }

    /**
     * RabbitAdmin
     */
    @Bean
    @ConditionalOnMissingBean(RabbitAdmin.class)
    @ConditionalOnProperty(prefix = "spring.rabbitmq", name = "enabled", havingValue = "true")
    public RabbitAdmin rabbitAdmin(ConnectionFactory connectionFactory) {
        return new RabbitAdmin(connectionFactory);
    }

    /**
     * 消息转换器
     */
    @Bean
    @ConditionalOnMissingBean(MessageConverter.class)
    @ConditionalOnProperty(prefix = "spring.rabbitmq", name = "enabled", havingValue = "true")
    public MessageConverter messageConverter(ObjectMapper objectMapper) {
        return new Jackson2JsonMessageConverter(objectMapper);
    }

    /**
     * RabbitTemplate
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(RabbitTemplate.class)
    @ConditionalOnProperty(prefix = "spring.rabbitmq", name = "enabled", havingValue = "true")
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory,
                                         MessageConverter messageConverter,
                                         RabbitMqProperties properties) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(messageConverter);

        // 设置消息确认
        template.setMandatory(true);

        // 设置重试模板
        if (properties.getRetry().isEnabled()) {
            template.setRetryTemplate(retryTemplate(properties));
        }

        // 设置确认回调
        template.setConfirmCallback((correlationData, ack, cause) -> {
            if (!ack) {
                // 消息发送失败处理
                throw new AmqpException("消息发送失败: " + cause);
            }
        });

        // 设置返回回调
        template.setReturnsCallback(returned -> {
            // 消息路由失败处理
            throw new AmqpException("消息路由失败: " + returned.getMessage());
        });

        log.info("RabbitTemplate 已配置: host={}, port={}, virtualHost={}",
            properties.getHost(), properties.getPort(), properties.getVirtualHost());

        return template;
    }

    /**
     * 重试模板
     */
    private RetryTemplate retryTemplate(RabbitMqProperties properties) {
        RetryTemplate retryTemplate = new RetryTemplate();

        // 设置重试策略
        retryTemplate.setRetryPolicy(new org.springframework.retry.policy.SimpleRetryPolicy(
            properties.getRetry().getMaxAttempts()
        ));

        // 设置退避策略
        ExponentialBackOffPolicy backOffPolicy = new ExponentialBackOffPolicy();
        backOffPolicy.setInitialInterval(properties.getRetry().getInitialInterval());
        backOffPolicy.setMultiplier(properties.getRetry().getMultiplier());
        backOffPolicy.setMaxInterval(properties.getRetry().getMaxInterval());
        retryTemplate.setBackOffPolicy(backOffPolicy);


        return retryTemplate;
    }

    /**
     * 监听器容器工厂
     */
    @Bean
    @ConditionalOnMissingBean(SimpleRabbitListenerContainerFactory.class)
    @ConditionalOnProperty(prefix = "spring.rabbitmq", name = "enabled", havingValue = "true")
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(
        ConnectionFactory connectionFactory,
        MessageConverter messageConverter,
        RabbitMqProperties properties) {

        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(messageConverter);

        // 设置并发消费者
        factory.setConcurrentConsumers(properties.getListener().getConcurrency());
        factory.setMaxConcurrentConsumers(properties.getListener().getMaxConcurrency());

        // 设置预取数量
        factory.setPrefetchCount(properties.getListener().getPrefetch());

        // 设置手动确认模式
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);

        // 设置消费失败重试
        if (properties.getListener().getRetry().isEnabled()) {
            factory.setDefaultRequeueRejected(false);
            factory.setAdviceChain(
                org.springframework.amqp.rabbit.config.RetryInterceptorBuilder
                    .stateless()
                    .maxAttempts(properties.getListener().getRetry().getMaxAttempts())
                    .backOffOptions(
                        properties.getListener().getRetry().getInitialInterval(),
                        properties.getListener().getRetry().getMultiplier(),
                        properties.getListener().getRetry().getMaxInterval()
                    )
                    .recoverer(new org.springframework.amqp.rabbit.retry.RejectAndDontRequeueRecoverer())
                    .build()
            );
        }

        return factory;
    }

    /**
     * 验证 RabbitMQ 配置
     */
    private void validateRabbitMqConfig(RabbitMqProperties properties) {
        if (!properties.isEnabled()) {
            throw new IllegalStateException("RabbitMQ 未启用，请检查 spring.rabbitmq.enabled 配置");
        }

        if (!StringUtils.hasText(properties.getHost())) {
            throw new IllegalStateException("RabbitMQ host 配置不能为空");
        }

        if (properties.getPort() <= 0) {
            throw new IllegalStateException("RabbitMQ port 配置无效");
        }

        if (!StringUtils.hasText(properties.getUsername())) {
            throw new IllegalStateException("RabbitMQ username 配置不能为空");
        }

        if (!StringUtils.hasText(properties.getPassword())) {
            throw new IllegalStateException("RabbitMQ password 配置不能为空");
        }

        log.debug("RabbitMQ 配置验证通过: host={}, port={}, username={}",
            properties.getHost(), properties.getPort(), properties.getUsername());
    }
}
