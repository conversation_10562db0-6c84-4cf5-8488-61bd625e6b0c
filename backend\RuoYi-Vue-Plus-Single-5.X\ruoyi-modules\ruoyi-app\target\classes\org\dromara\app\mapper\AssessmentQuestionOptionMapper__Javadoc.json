{"doc": "\n 评估问题选项Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectOptionsByQuestionId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据问题ID查询选项列表\r\n\r\n @param questionId 问题ID\r\n @return 选项列表\r\n"}, {"name": "selectOptionByQuestionIdAndCode", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 根据问题ID和选项编码查询选项\r\n\r\n @param questionId 问题ID\r\n @param optionCode 选项编码\r\n @return 选项\r\n"}, {"name": "insertBatch", "paramTypes": ["java.util.List"], "doc": "\n 批量插入选项\r\n\r\n @param options 选项列表\r\n @return 插入数量\r\n"}, {"name": "deleteByQuestionId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据问题ID删除选项\r\n\r\n @param questionId 问题ID\r\n @return 删除数量\r\n"}], "constructors": []}