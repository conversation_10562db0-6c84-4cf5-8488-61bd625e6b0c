{"doc": " 成就管理Controller\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询成就列表\n"}, {"name": "export", "paramTypes": ["org.dromara.app.domain.bo.AchievementBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出成就列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取成就详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.app.domain.bo.AchievementBo"], "doc": " 新增成就\n"}, {"name": "edit", "paramTypes": ["org.dromara.app.domain.bo.AchievementBo"], "doc": " 修改成就\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除成就\n"}, {"name": "batchUpdateStatus", "paramTypes": ["org.dromara.app.controller.achievement.AchievementManageController.BatchUpdateStatusRequest"], "doc": " 批量更新成就状态\n"}, {"name": "copyAchievement", "paramTypes": ["java.lang.Long"], "doc": " 复制成就\n"}, {"name": "previewTriggerCondition", "paramTypes": ["org.dromara.app.controller.achievement.AchievementManageController.PreviewRequest"], "doc": " 预览成就触发条件\n"}, {"name": "testAchievementRule", "paramTypes": ["java.lang.Long", "org.dromara.app.controller.achievement.AchievementManageController.TestRuleRequest"], "doc": " 测试成就规则\n"}, {"name": "getAchievementStats", "paramTypes": [], "doc": " 获取成就统计信息\n"}, {"name": "getAchievementTypes", "paramTypes": [], "doc": " 获取成就类型列表\n"}, {"name": "getBehaviorTypes", "paramTypes": [], "doc": " 获取行为类型列表\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.Bo<PERSON>an"], "doc": " 导入成就数据\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 获取导入模板\n"}], "constructors": []}