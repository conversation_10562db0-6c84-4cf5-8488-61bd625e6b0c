{"doc": "\n AI聊天控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendMessage", "paramTypes": ["org.dromara.app.domain.dto.ChatRequestDto"], "doc": "\n 发送聊天消息（同步响应）\r\n\r\n @param request 聊天请求\r\n @return 聊天响应\r\n"}, {"name": "sendMessageStream", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 发送聊天消息（流式响应）\r\n\r\n @param message   消息内容\r\n @param sessionId 会话ID\r\n @param agentType Agent类型\r\n @return SSE流\r\n"}, {"name": "createSession", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 创建新会话\r\n\r\n @param agentType 代理类型\r\n @param title     会话标题（可选）\r\n @return 会话信息\r\n"}, {"name": "getUserSessions", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": "\n 获取用户会话列表\r\n\r\n @param pageNum  页码\r\n @param pageSize 每页大小\r\n @return 会话分页结果\r\n"}, {"name": "getSessionDetail", "paramTypes": ["java.lang.String"], "doc": "\n 获取会话详情\r\n\r\n @param sessionId 会话ID\r\n @return 会话详情\r\n"}, {"name": "getSessionMessages", "paramTypes": ["java.lang.String", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 获取会话消息列表\r\n\r\n @param sessionId 会话ID\r\n @param pageNum   页码\r\n @param pageSize  每页大小\r\n @return 消息分页结果\r\n"}, {"name": "deleteSession", "paramTypes": ["java.lang.String"], "doc": "\n 删除会话\r\n\r\n @param sessionId 会话ID\r\n @return 操作结果\r\n"}, {"name": "clearSessionMessages", "paramTypes": ["java.lang.String"], "doc": "\n 清空会话消息\r\n\r\n @param sessionId 会话ID\r\n @return 操作结果\r\n"}, {"name": "updateSessionTitle", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 更新会话标题\r\n\r\n @param sessionId 会话ID\r\n @param title     新标题\r\n @return 操作结果\r\n"}, {"name": "archiveSession", "paramTypes": ["java.lang.String", "java.lang.Bo<PERSON>an"], "doc": "\n 归档/取消归档会话\r\n\r\n @param sessionId 会话ID\r\n @param archived  是否归档\r\n @return 操作结果\r\n"}, {"name": "getUserChatStats", "paramTypes": [], "doc": "\n 获取用户聊天统计信息\r\n\r\n @return 统计信息\r\n"}, {"name": "getEnabledAgents", "paramTypes": [], "doc": "\n 获取所有启用的代理列表\r\n\r\n @return 代理列表\r\n"}, {"name": "getAgentByType", "paramTypes": ["java.lang.String"], "doc": "\n 根据类型获取代理信息\r\n\r\n @param agentType 代理类型\r\n @return 代理信息\r\n"}, {"name": "getQuickActions", "paramTypes": ["java.lang.String"], "doc": "\n 获取代理的快速操作列表\r\n\r\n @param agentType 代理类型\r\n @return 快速操作列表\r\n"}, {"name": "callAgent", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map"], "doc": "\n 调用特定类型的Agent进行处理（同步响应）\r\n\r\n @param agentType 代理类型\r\n @param message   用户消息\r\n @param params    额外参数\r\n @return 处理结果\r\n"}, {"name": "callAgentStream", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map", "java.lang.String"], "doc": "\n 调用特定类型的Agent进行处理（流式响应）\r\n\r\n @param agentType 代理类型\r\n @param message   用户消息\r\n @param params    额外参数\r\n @return SSE流\r\n"}, {"name": "uploadFile", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String"], "doc": "\n 上传聊天附件\r\n\r\n @param file 文件\r\n @param type 文件类型：image/file/voice\r\n @return 上传结果\r\n"}, {"name": "speechToText", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 语音转文字\r\n\r\n @param audioFile 音频文件\r\n @return 转换结果\r\n"}, {"name": "getAvailableProviders", "paramTypes": [], "doc": "\n 获取可用的AI提供商列表\r\n\r\n @return 提供商列表\r\n"}, {"name": "getAgentStats", "paramTypes": [], "doc": "\n 获取Agent使用统计\r\n\r\n @return 使用统计\r\n"}], "constructors": []}