{"doc": "\n AI评估服务接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "evaluateAnswer", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.String"], "doc": "\n 评估面试回答\r\n\r\n @param question    问题内容\r\n @param answer      回答内容\r\n @param audioUrl    音频URL（可选）\r\n @param videoUrl    视频URL（可选）\r\n @param duration    回答时长\r\n @param jobContext  岗位上下文信息\r\n @return 评估结果\r\n"}, {"name": "generateQuestions", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.String", "java.util.List"], "doc": "\n 生成面试问题\r\n\r\n @param jobId       岗位ID\r\n @param difficulty  难度等级\r\n @param count       问题数量\r\n @param resumeUrl   简历URL（可选）\r\n @param customQuestions 自定义问题（可选）\r\n @return 生成的问题列表\r\n"}, {"name": "generateInterviewReport", "paramTypes": ["java.lang.String"], "doc": "\n 生成面试总结报告\r\n\r\n @param sessionId 会话ID\r\n @return 面试结果\r\n"}, {"name": "analyzeResume", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 分析简历内容\r\n\r\n @param resumeUrl 简历URL\r\n @param jobId     岗位ID\r\n @return 简历分析结果\r\n"}, {"name": "checkAnswerQuality", "paramTypes": ["java.lang.String"], "doc": "\n 检查回答质量\r\n\r\n @param answer 回答内容\r\n @return 质量评分（0-100）\r\n"}, {"name": "generateImprovementSuggestions", "paramTypes": ["java.lang.String"], "doc": "\n 生成改进建议\r\n\r\n @param sessionId 会话ID\r\n @return 改进建议列表\r\n"}], "constructors": []}