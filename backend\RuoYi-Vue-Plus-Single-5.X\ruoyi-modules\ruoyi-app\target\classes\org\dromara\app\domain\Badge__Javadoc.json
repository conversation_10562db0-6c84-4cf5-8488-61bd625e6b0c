{"doc": "\n 徽章实体\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [{"name": "id", "doc": "\n 徽章ID\r\n"}, {"name": "icon", "doc": "\n 徽章图标\r\n"}, {"name": "color", "doc": "\n 徽章颜色\r\n"}, {"name": "title", "doc": "\n 徽章标题\r\n"}, {"name": "description", "doc": "\n 徽章描述\r\n"}, {"name": "category", "doc": "\n 徽章类别\r\n"}, {"name": "rarity", "doc": "\n 稀有度\r\n"}, {"name": "achievementId", "doc": "\n 关联成就ID\r\n"}, {"name": "sort", "doc": "\n 排序\r\n"}, {"name": "isEnabled", "doc": "\n 是否启用\r\n"}, {"name": "unlockCriteria", "doc": "\n 解锁条件（单独徽章可能不关联成就）\r\n"}, {"name": "effect", "doc": "\n 徽章特效\r\n"}, {"name": "specialFlag", "doc": "\n 特殊徽章标志（如限时、活动等）\r\n"}, {"name": "tags", "doc": "\n 徽章标签（用逗号分隔）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}