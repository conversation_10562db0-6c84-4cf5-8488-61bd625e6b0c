package org.dromara.common.caffeine.constant;

/**
 * Caffeine缓存常量
 *
 * <AUTHOR>
 */
public class CaffeineConstants {

    /**
     * 默认缓存名称
     */
    public static final String DEFAULT_CACHE_NAME = "default";

    /**
     * 缓存键分隔符
     */
    public static final String CACHE_KEY_SEPARATOR = ":";

    /**
     * 缓存键前缀
     */
    public static final String CACHE_KEY_PREFIX = "caffeine";

    /**
     * 用户缓存前缀
     */
    public static final String USER_CACHE_PREFIX = "user";

    /**
     * 系统缓存前缀
     */
    public static final String SYSTEM_CACHE_PREFIX = "system";

    /**
     * 配置缓存前缀
     */
    public static final String CONFIG_CACHE_PREFIX = "config";

    /**
     * 字典缓存前缀
     */
    public static final String DICT_CACHE_PREFIX = "dict";

    /**
     * 权限缓存前缀
     */
    public static final String PERMISSION_CACHE_PREFIX = "permission";

    /**
     * 默认过期时间（秒）
     */
    public static final long DEFAULT_EXPIRE_TIME = 3600L;

    /**
     * 默认最大缓存数量
     */
    public static final long DEFAULT_MAXIMUM_SIZE = 1000L;

    /**
     * 默认初始容量
     */
    public static final int DEFAULT_INITIAL_CAPACITY = 16;

}
