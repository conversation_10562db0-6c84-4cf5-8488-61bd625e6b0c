package org.dromara.app.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 题目视图对象 app_question
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@Schema(description = "题目视图对象")
public class QuestionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 题目ID
     */
    @Schema(description = "题目ID")
    private Long questionId;

    /**
     * 题目标题
     */
    private String title;

    /**
     * 题目内容/描述
     */
    private String content;

    /**
     * 题目描述（兼容字段）
     */
    private String description;

    /**
     * 难度（简单/中等/困难）
     */
    private String difficulty;

    /**
     * 题目类型（单选题/多选题/判断题/编程题/简答题）
     */
    private String type;

    /**
     * 标签列表
     */
    private List<String> tags;

    /**
     * 通过率（百分比）
     */
    private Double acceptanceRate;

    /**
     * 是否已完成
     */
    private Boolean isCompleted;

    /**
     * 是否已收藏
     */
    private Boolean isBookmarked;

    /**
     * 练习次数
     */
    private Integer practiceCount;

    /**
     * 正确率（百分比）
     */
    private Integer correctRate;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 分类
     */
    private String category;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 预计完成时间（分钟）
     */
    private Integer estimatedTime;

    /**
     * 题库ID
     */
    private String bankId;

    /**
     * 题库标题
     */
    private String bankTitle;
}
