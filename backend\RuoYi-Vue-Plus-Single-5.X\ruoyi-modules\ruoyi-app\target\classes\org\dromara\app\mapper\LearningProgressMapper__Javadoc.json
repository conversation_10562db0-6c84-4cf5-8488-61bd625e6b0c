{"doc": "\n 学习进度Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询学习进度\r\n\r\n @param userId 用户ID\r\n @return 学习进度列表\r\n"}, {"name": "selectByUserIdAndStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 根据用户ID和状态查询学习进度\r\n\r\n @param userId 用户ID\r\n @param status 状态\r\n @return 学习进度列表\r\n"}, {"name": "selectByLearningPathAndUser", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 根据学习路径ID查询进度\r\n\r\n @param learningPathId 学习路径ID\r\n @param userId 用户ID\r\n @return 学习进度\r\n"}, {"name": "selectByResourceAndUser", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 根据资源ID查询进度\r\n\r\n @param resourceId 资源ID\r\n @param userId 用户ID\r\n @return 学习进度\r\n"}, {"name": "selectUserLearningStatistics", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户学习统计\r\n\r\n @param userId 用户ID\r\n @return 统计信息\r\n"}, {"name": "selectRecentLearningProgress", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询最近的学习进度\r\n\r\n @param userId 用户ID\r\n @param limit 限制数量\r\n @return 学习进度列表\r\n"}, {"name": "selectOngoingLearning", "paramTypes": ["java.lang.Long"], "doc": "\n 查询正在进行的学习\r\n\r\n @param userId 用户ID\r\n @return 学习进度列表\r\n"}, {"name": "selectCompletedLearning", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询已完成的学习\r\n\r\n @param userId 用户ID\r\n @param limit 限制数量\r\n @return 学习进度列表\r\n"}, {"name": "selectLearningEffectivenessStats", "paramTypes": ["java.lang.Long"], "doc": "\n 查询学习效果统计\r\n\r\n @param userId 用户ID\r\n @return 效果统计\r\n"}, {"name": "selectStudyTimeStatistics", "paramTypes": ["java.lang.Long"], "doc": "\n 查询学习时间统计\r\n\r\n @param userId 用户ID\r\n @return 时间统计\r\n"}, {"name": "selectLearningTrend", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询学习趋势数据\r\n\r\n @param userId 用户ID\r\n @param days 天数\r\n @return 趋势数据\r\n"}, {"name": "selectItemsNeedingReminder", "paramTypes": [], "doc": "\n 查询需要提醒的学习项目\r\n\r\n @return 需要提醒的学习进度列表\r\n"}, {"name": "selectOverdueLearning", "paramTypes": ["java.lang.Long"], "doc": "\n 查询超期的学习项目\r\n\r\n @param userId 用户ID\r\n @return 超期学习进度列表\r\n"}, {"name": "updateLearningStatistics", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 更新学习统计\r\n\r\n @param progressId 进度ID\r\n @param statisticsJson 统计JSON\r\n @return 更新行数\r\n"}], "constructors": []}