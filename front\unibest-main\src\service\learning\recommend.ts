/**
 * 学习推荐服务模块
 * 提供个性化学习资源推荐功能
 * <AUTHOR>
 */
import { httpGet, httpPost } from '@/utils/http'

/**
 * 推荐视频类型定义
 */
export interface RecommendedVideo {
  id: string
  title: string
  description: string
  duration: number
  thumbnail: string
  instructor: string
  difficulty: string
  category: string
  tags: string[]
  rating: number
  viewCount: number
  isBookmarked?: boolean
}

/**
 * 推荐题库类型定义
 */
export interface RecommendedQuestionBank {
  id: string
  title: string
  description: string
  questionCount: number
  difficulty: string
  category: string
  tags: string[]
  completionRate: number
  averageScore: number
  isBookmarked?: boolean
}

/**
 * 推荐书籍类型定义
 */
export interface RecommendedBook {
  id: string
  title: string
  author: string
  description: string
  cover: string
  category: string
  tags: string[]
  rating: number
  pageCount: number
  readingProgress?: number
  isBookmarked?: boolean
}

/**
 * 用户能力评估类型定义
 */
export interface UserCapabilities {
  technicalSkills: number
  problemSolving: number
  communication: number
  projectExperience: number
  algorithmKnowledge: number
  systemDesign: number
}

/**
 * 推荐查询参数类型定义
 */
export interface RecommendationQuery {
  category?: string
  difficulty?: string
  tags?: string[]
  limit?: number
  offset?: number
}

/**
 * 获取推荐视频列表
 */
export const getRecommendedVideos = async (query?: RecommendationQuery): Promise<RecommendedVideo[]> => {
  try {
    const response = await httpGet('/api/learning/recommend/videos', query)
    return response.data || []
  } catch (error) {
    console.error('获取推荐视频失败:', error)
    return getMockRecommendedVideos()
  }
}

/**
 * 获取推荐题库列表
 */
export const getRecommendedQuestionBanks = async (query?: RecommendationQuery): Promise<RecommendedQuestionBank[]> => {
  try {
    const response = await httpGet('/api/learning/recommend/question-banks', query)
    return response.data || []
  } catch (error) {
    console.error('获取推荐题库失败:', error)
    return getMockRecommendedQuestionBanks()
  }
}

/**
 * 获取推荐书籍列表
 */
export const getRecommendedBooks = async (query?: RecommendationQuery): Promise<RecommendedBook[]> => {
  try {
    const response = await httpGet('/api/learning/recommend/books', query)
    return response.data || []
  } catch (error) {
    console.error('获取推荐书籍失败:', error)
    return getMockRecommendedBooks()
  }
}

/**
 * 获取用户能力评估
 */
export const getUserCapabilities = async (): Promise<UserCapabilities> => {
  try {
    const response = await httpGet('/api/learning/user/capabilities')
    return response.data || getMockUserCapabilities()
  } catch (error) {
    console.error('获取用户能力评估失败:', error)
    return getMockUserCapabilities()
  }
}

/**
 * 获取推荐统计信息
 */
export const getRecommendationStatistics = async () => {
  try {
    const response = await httpGet('/api/learning/recommend/statistics')
    return response.data || getMockRecommendationStatistics()
  } catch (error) {
    console.error('获取推荐统计信息失败:', error)
    return getMockRecommendationStatistics()
  }
}

/**
 * 记录推荐反馈
 */
export const recordRecommendationFeedback = async (feedback: {
  resourceId: string
  resourceType: string
  action: string
  rating?: number
}) => {
  try {
    const response = await httpPost('/api/learning/recommend/feedback', feedback)
    return response
  } catch (error) {
    console.error('记录推荐反馈失败:', error)
    return { success: false }
  }
}

// Mock 数据函数
const getMockRecommendedVideos = (): RecommendedVideo[] => {
  return [
    {
      id: '1',
      title: 'JavaScript 高级特性详解',
      description: '深入理解 JavaScript 的高级特性，包括闭包、原型链、异步编程等',
      duration: 3600,
      thumbnail: '/static/images/video-thumb-1.jpg',
      instructor: '张老师',
      difficulty: '中等',
      category: '前端开发',
      tags: ['JavaScript', '高级特性', '闭包'],
      rating: 4.8,
      viewCount: 12580,
      isBookmarked: false
    }
  ]
}

const getMockRecommendedQuestionBanks = (): RecommendedQuestionBank[] => {
  return [
    {
      id: '1',
      title: 'JavaScript 面试题精选',
      description: '精选 JavaScript 常见面试题，涵盖基础到高级知识点',
      questionCount: 150,
      difficulty: '中等',
      category: '前端开发',
      tags: ['JavaScript', '面试', '前端'],
      completionRate: 75,
      averageScore: 82,
      isBookmarked: false
    }
  ]
}

const getMockRecommendedBooks = (): RecommendedBook[] => {
  return [
    {
      id: '1',
      title: '你不知道的JavaScript',
      author: 'Kyle Simpson',
      description: '深入理解 JavaScript 语言核心机制',
      cover: '/static/images/book-cover-1.jpg',
      category: '前端开发',
      tags: ['JavaScript', '进阶', '核心机制'],
      rating: 4.9,
      pageCount: 320,
      readingProgress: 0,
      isBookmarked: false
    }
  ]
}

const getMockUserCapabilities = (): UserCapabilities => {
  return {
    technicalSkills: 75,
    problemSolving: 68,
    communication: 82,
    projectExperience: 70,
    algorithmKnowledge: 65,
    systemDesign: 60
  }
}

const getMockRecommendationStatistics = () => {
  return {
    totalRecommendations: 156,
    completedRecommendations: 89,
    averageRating: 4.6,
    improvementRate: 23
  }
}
