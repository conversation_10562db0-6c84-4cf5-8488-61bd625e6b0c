{"doc": "\n 书籍阅读记录Service接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryByUserIdAndBookId", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 根据用户ID和书籍ID查询阅读记录\r\n\r\n @param userId 用户ID\r\n @param bookId 书籍ID\r\n @return 阅读记录\r\n"}, {"name": "queryUserReadingHistory", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.Long"], "doc": "\n 查询用户的阅读历史（分页）\r\n\r\n @param pageNum  页码\r\n @param pageSize 每页大小\r\n @param userId   用户ID\r\n @return 阅读记录分页\r\n"}, {"name": "queryRecentReading", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询用户最近阅读的书籍\r\n\r\n @param userId 用户ID\r\n @param limit  限制数量\r\n @return 阅读记录列表\r\n"}, {"name": "queryUserReadingStats", "paramTypes": ["java.lang.Long"], "doc": "\n 统计用户阅读数据\r\n\r\n @param userId 用户ID\r\n @return 阅读统计数据\r\n"}, {"name": "saveOrUpdateReadingRecord", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.util.Map"], "doc": "\n 保存或更新阅读记录\r\n\r\n @param userId              用户ID\r\n @param bookId              书籍ID\r\n @param currentChapterId    当前章节ID\r\n @param currentChapterIndex 当前章节索引\r\n @param readingProgress     阅读进度\r\n @param readingSettings     阅读设置\r\n @return 是否成功\r\n"}, {"name": "mark<PERSON><PERSON><PERSON>erCompleted", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.String"], "doc": "\n 标记章节已完成\r\n\r\n @param userId    用户ID\r\n @param bookId    书籍ID\r\n @param chapterId 章节ID\r\n @return 是否成功\r\n"}, {"name": "markBookFinished", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 标记书籍已读完\r\n\r\n @param userId 用户ID\r\n @param bookId 书籍ID\r\n @return 是否成功\r\n"}, {"name": "addReadingTime", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Integer"], "doc": "\n 增加阅读时长\r\n\r\n @param userId      用户ID\r\n @param bookId      书籍ID\r\n @param readingTime 阅读时长（分钟）\r\n @return 是否成功\r\n"}, {"name": "deleteReadingRecord", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 删除阅读记录\r\n\r\n @param userId 用户ID\r\n @param bookId 书籍ID\r\n @return 是否成功\r\n"}], "constructors": []}