{"doc": " 意见反馈Service接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询意见反馈\n\n @param id 意见反馈主键\n @return 意见反馈\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.app.domain.bo.FeedbackBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询意见反馈列表\n\n @param bo        意见反馈\n @param pageQuery 分页查询条件\n @return 意见反馈分页列表\n"}, {"name": "selectUserFeedbackList", "paramTypes": ["java.lang.Long"], "doc": " 查询用户反馈列表\n\n @param userId 用户ID\n @return 反馈列表\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.app.domain.bo.FeedbackBo"], "doc": " 修改意见反馈\n\n @param bo 意见反馈\n @return 结果\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除意见反馈信息\n\n @param ids     需要删除的意见反馈主键集合\n @param isValid 是否校验,true-删除前校验,false-不校验\n @return 结果\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.app.domain.bo.FeedbackBo"], "doc": " 新增意见反馈\n\n @param bo 意见反馈\n @return 是否新增成功\n"}, {"name": "getUserFeedbackStats", "paramTypes": ["java.lang.Long"], "doc": " 获取用户反馈统计信息\n\n @param userId 用户ID\n @return 统计信息\n"}], "constructors": []}