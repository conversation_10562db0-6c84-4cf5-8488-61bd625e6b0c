{"doc": " Interview Result Service Implementation\n\n <AUTHOR>\n @date 2025-07-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "parseResultData", "paramTypes": ["java.lang.Object"], "doc": " 解析结果数据\n"}, {"name": "convertMapToResultData", "paramTypes": ["java.util.Map"], "doc": " 将Map转换为InterviewResultData\n"}, {"name": "createRelatedData", "paramTypes": ["java.lang.String", "org.dromara.app.service.impl.InterviewResultServiceImpl.InterviewResultData"], "doc": " 创建关联数据\n"}, {"name": "updateRelatedData", "paramTypes": ["java.lang.String", "org.dromara.app.service.impl.InterviewResultServiceImpl.InterviewResultData"], "doc": " 更新关联数据\n"}, {"name": "deleteRelatedData", "paramTypes": ["java.lang.String"], "doc": " 删除关联数据\n"}], "constructors": []}