{"doc": " 面试会话问题对象 app_session_question\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [{"name": "id", "doc": " 记录ID\n"}, {"name": "sessionId", "doc": " 会话ID\n"}, {"name": "questionId", "doc": " 问题ID（唯一标识）\n"}, {"name": "content", "doc": " 问题内容\n"}, {"name": "type", "doc": " 问题类型（technical, behavioral, project, case）\n"}, {"name": "difficulty", "doc": " 难度等级（easy, medium, hard）\n"}, {"name": "category", "doc": " 问题分类\n"}, {"name": "subcategory", "doc": " 子分类\n"}, {"name": "timeLimit", "doc": " 时间限制（秒）\n"}, {"name": "questionOrder", "doc": " 问题顺序\n"}, {"name": "tags", "doc": " 标签（JSON数组）\n"}, {"name": "status", "doc": " 问题状态（pending, current, answered, skipped）\n"}, {"name": "answerPoints", "doc": " 参考答案要点（JSON数组）\n"}, {"name": "hints", "doc": " 提示信息（JSON数组）\n"}, {"name": "scoringCriteria", "doc": " 评分标准（JSON格式）\n"}, {"name": "isCustom", "doc": " 是否为自定义问题\n"}, {"name": "source", "doc": " 问题来源（system, custom, ai_generated）\n"}, {"name": "metadata", "doc": " 元数据（JSON格式）\n"}], "enumConstants": [], "methods": [], "constructors": []}