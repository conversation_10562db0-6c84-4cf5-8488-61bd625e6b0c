package org.dromara.app.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.domain.Question;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 题目Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface AppQuestionMapper extends BaseMapperPlus<Question, Question> {

    /**
     * 根据题库ID和分类查询题目列表
     *
     * @param bankId   题库ID
     * @param category 分类
     * @return 题目列表
     */
    List<Question> selectQuestionsByCategory(@Param("bankId") Long bankId, @Param("category") String category, @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 根据题库ID查询推荐题目
     *
     * @param bankId 题库ID
     * @param limit  数量限制
     * @return 推荐题目列表
     */
    List<Question> selectRecommendedQuestions(@Param("bankId") Long bankId, @Param("limit") Integer limit);

    /**
     * 根据题库ID查询所有分类
     *
     * @param bankId 题库ID
     * @return 分类列表
     */
    List<String> selectCategoriesByBankId(@Param("bankId") Long bankId);
}
