{"doc": " 用户成就控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getUserAchievements", "paramTypes": [], "doc": " 获取当前用户成就列表\n"}, {"name": "getAchievementStats", "paramTypes": [], "doc": " 获取用户成就统计\n"}, {"name": "getRecentAchievements", "paramTypes": ["java.lang.Integer"], "doc": " 获取最近解锁的成就\n"}, {"name": "getInProgressAchievements", "paramTypes": [], "doc": " 获取进行中的成就\n"}, {"name": "getRecommendedAchievements", "paramTypes": ["java.lang.Integer"], "doc": " 获取推荐成就\n"}, {"name": "getAchievementDetail", "paramTypes": ["java.lang.String"], "doc": " 获取成就详情\n"}, {"name": "checkAndUpdateAchievements", "paramTypes": [], "doc": " 手动检查并更新成就\n"}, {"name": "initializeUserAchievements", "paramTypes": [], "doc": " 初始化用户成就\n"}, {"name": "shareAchievements", "paramTypes": ["java.lang.String"], "doc": " 分享成就墙\n"}, {"name": "getCategories", "paramTypes": [], "doc": " 获取成就分类列表\n"}, {"name": "getUserAchievementCompletion", "paramTypes": [], "doc": " 获取用户成就完成度\n"}, {"name": "recalculateUserProgress", "paramTypes": [], "doc": " 重新计算用户成就进度\n"}, {"name": "getEventStatistics", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取事件统计\n"}, {"name": "unlockAchievement", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 手动解锁成就（管理员功能）\n"}, {"name": "recordEvent", "paramTypes": ["java.lang.String", "java.util.Map", "java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": " 记录用户事件（测试用）\n"}], "constructors": []}