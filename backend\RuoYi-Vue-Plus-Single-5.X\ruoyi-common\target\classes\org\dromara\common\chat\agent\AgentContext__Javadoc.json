{"doc": " AI Agent上下文\n\n <AUTHOR>\n", "fields": [{"name": "sessionId", "doc": " 会话ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "agentType", "doc": " Agent类型\n"}, {"name": "parameters", "doc": " 上下文参数\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 最后更新时间\n"}], "enumConstants": [], "methods": [{"name": "setParameter", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 设置参数\n"}, {"name": "getParameter", "paramTypes": ["java.lang.String"], "doc": " 获取参数\n"}, {"name": "getParameter", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 获取参数（带默认值）\n"}], "constructors": []}