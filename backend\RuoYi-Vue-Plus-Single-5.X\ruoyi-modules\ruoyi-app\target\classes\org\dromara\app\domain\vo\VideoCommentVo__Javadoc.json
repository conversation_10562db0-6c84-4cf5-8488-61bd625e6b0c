{"doc": "\n 视频评论视图对象\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 评论ID\r\n"}, {"name": "videoId", "doc": "\n 视频ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "userName", "doc": "\n 用户名\r\n"}, {"name": "userAvatar", "doc": "\n 用户头像\r\n"}, {"name": "parentId", "doc": "\n 父评论ID\r\n"}, {"name": "content", "doc": "\n 评论内容\r\n"}, {"name": "likeCount", "doc": "\n 点赞数\r\n"}, {"name": "isLiked", "doc": "\n 是否已点赞\r\n"}, {"name": "replies", "doc": "\n 回复列表\r\n"}, {"name": "createTime", "doc": "\n 发布时间\r\n"}, {"name": "publishTime", "doc": "\n 发布时间格式化\r\n"}], "enumConstants": [], "methods": [], "constructors": []}