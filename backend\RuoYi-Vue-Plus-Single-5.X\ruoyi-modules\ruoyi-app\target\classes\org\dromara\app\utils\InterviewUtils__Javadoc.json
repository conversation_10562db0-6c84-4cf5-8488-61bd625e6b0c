{"doc": "\n 面试系统工具类\r\n\r\n <AUTHOR> Assistant\r\n @date 2025-01-17\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "generateSessionId", "paramTypes": [], "doc": "\n 生成会话ID\r\n\r\n @return 会话ID\r\n"}, {"name": "generateSessionToken", "paramTypes": ["java.lang.String"], "doc": "\n 生成会话令牌\r\n\r\n @param sessionId 会话ID\r\n @return 会话令牌\r\n"}, {"name": "calculateExpirationTime", "paramTypes": ["int"], "doc": "\n 计算会话过期时间\r\n\r\n @param hours 过期小时数\r\n @return 过期时间\r\n"}, {"name": "isValidKeyword", "paramTypes": ["java.lang.String", "org.dromara.app.config.InterviewConfig"], "doc": "\n 验证搜索关键词\r\n\r\n @param keyword 关键词\r\n @param config  配置\r\n @return 是否有效\r\n"}, {"name": "calculateSmartScore", "paramTypes": ["org.dromara.app.domain.vo.JobVo", "org.dromara.app.config.InterviewConfig"], "doc": "\n 计算智能排序分数\r\n\r\n @param job    岗位信息\r\n @param config 配置\r\n @return 智能分数\r\n"}, {"name": "parsePassRate", "paramTypes": ["java.lang.String"], "doc": "\n 解析通过率字符串\r\n\r\n @param passRateStr 通过率字符串（如\"68%\"）\r\n @return 通过率数值\r\n"}, {"name": "formatPassRate", "paramTypes": ["double"], "doc": "\n 格式化通过率\r\n\r\n @param passRate 通过率数值\r\n @return 格式化后的字符串\r\n"}, {"name": "isSessionExpired", "paramTypes": ["java.time.LocalDateTime"], "doc": "\n 检查会话是否过期\r\n\r\n @param expiresAt 过期时间\r\n @return 是否过期\r\n"}, {"name": "isValidTags", "paramTypes": ["java.util.List"], "doc": "\n 验证岗位标签\r\n\r\n @param tags 标签列表\r\n @return 是否有效\r\n"}, {"name": "cleanKeyword", "paramTypes": ["java.lang.String"], "doc": "\n 清理和标准化关键词\r\n\r\n @param keyword 原始关键词\r\n @return 清理后的关键词\r\n"}, {"name": "simulate<PERSON>ev<PERSON><PERSON><PERSON>ck", "paramTypes": ["org.dromara.app.config.InterviewConfig"], "doc": "\n 生成设备检测结果（模拟）\r\n\r\n @param config 配置\r\n @return 检测结果\r\n"}, {"name": "isValidModeId", "paramTypes": ["java.lang.String"], "doc": "\n 验证面试模式ID\r\n\r\n @param modeId 模式ID\r\n @return 是否有效\r\n"}, {"name": "calculateRecommendScore", "paramTypes": ["org.dromara.app.domain.vo.JobVo"], "doc": "\n 计算推荐分数\r\n\r\n @param job 岗位信息\r\n @return 推荐分数\r\n"}, {"name": "getDifficultyDescription", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取难度描述\r\n\r\n @param difficulty 难度等级\r\n @return 难度描述\r\n"}, {"name": "isValidCustomQuestions", "paramTypes": ["java.util.List"], "doc": "\n 验证自定义问题\r\n\r\n @param questions 问题列表\r\n @return 是否有效\r\n"}], "constructors": []}