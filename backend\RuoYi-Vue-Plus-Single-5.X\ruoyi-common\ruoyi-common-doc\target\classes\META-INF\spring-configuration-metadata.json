{"groups": [{"name": "springdoc", "type": "org.dromara.common.doc.config.properties.SpringDocProperties", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties"}, {"name": "springdoc.components", "type": "io.swagger.v3.oas.models.Components", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties", "sourceMethod": "getComponents()"}, {"name": "springdoc.external-docs", "type": "io.swagger.v3.oas.models.ExternalDocumentation", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties", "sourceMethod": "getExternalDocs()"}, {"name": "springdoc.info", "type": "org.dromara.common.doc.config.properties.SpringDocProperties$InfoProperties", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties", "sourceMethod": "getInfo()"}, {"name": "springdoc.info.contact", "type": "io.swagger.v3.oas.models.info.Contact", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties$InfoProperties", "sourceMethod": "getContact()"}, {"name": "springdoc.info.license", "type": "io.swagger.v3.oas.models.info.License", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties$InfoProperties", "sourceMethod": "getLicense()"}, {"name": "springdoc.paths", "type": "io.swagger.v3.oas.models.Paths", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties", "sourceMethod": "getPaths()"}], "properties": [{"name": "springdoc.components.callbacks", "type": "java.util.Map<java.lang.String,io.swagger.v3.oas.models.callbacks.Callback>", "sourceType": "io.swagger.v3.oas.models.Components"}, {"name": "springdoc.components.examples", "type": "java.util.Map<java.lang.String,io.swagger.v3.oas.models.examples.Example>", "sourceType": "io.swagger.v3.oas.models.Components"}, {"name": "springdoc.components.extensions", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "io.swagger.v3.oas.models.Components"}, {"name": "springdoc.components.headers", "type": "java.util.Map<java.lang.String,io.swagger.v3.oas.models.headers.Header>", "sourceType": "io.swagger.v3.oas.models.Components"}, {"name": "springdoc.components.links", "type": "java.util.Map<java.lang.String,io.swagger.v3.oas.models.links.Link>", "sourceType": "io.swagger.v3.oas.models.Components"}, {"name": "springdoc.components.parameters", "type": "java.util.Map<java.lang.String,io.swagger.v3.oas.models.parameters.Parameter>", "sourceType": "io.swagger.v3.oas.models.Components"}, {"name": "springdoc.components.path-items", "type": "java.util.Map<java.lang.String,io.swagger.v3.oas.models.PathItem>", "sourceType": "io.swagger.v3.oas.models.Components"}, {"name": "springdoc.components.request-bodies", "type": "java.util.Map<java.lang.String,io.swagger.v3.oas.models.parameters.RequestBody>", "sourceType": "io.swagger.v3.oas.models.Components"}, {"name": "springdoc.components.responses", "type": "java.util.Map<java.lang.String,io.swagger.v3.oas.models.responses.ApiResponse>", "sourceType": "io.swagger.v3.oas.models.Components"}, {"name": "springdoc.components.schemas", "type": "java.util.Map<java.lang.String,io.swagger.v3.oas.models.media.Schema>", "sourceType": "io.swagger.v3.oas.models.Components"}, {"name": "springdoc.components.security-schemes", "type": "java.util.Map<java.lang.String,io.swagger.v3.oas.models.security.SecurityScheme>", "sourceType": "io.swagger.v3.oas.models.Components"}, {"name": "springdoc.external-docs.description", "type": "java.lang.String", "sourceType": "io.swagger.v3.oas.models.ExternalDocumentation"}, {"name": "springdoc.external-docs.extensions", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "io.swagger.v3.oas.models.ExternalDocumentation"}, {"name": "springdoc.external-docs.url", "type": "java.lang.String", "sourceType": "io.swagger.v3.oas.models.ExternalDocumentation"}, {"name": "springdoc.info.contact.email", "type": "java.lang.String", "sourceType": "io.swagger.v3.oas.models.info.Contact"}, {"name": "springdoc.info.contact.extensions", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "io.swagger.v3.oas.models.info.Contact"}, {"name": "springdoc.info.contact.name", "type": "java.lang.String", "sourceType": "io.swagger.v3.oas.models.info.Contact"}, {"name": "springdoc.info.contact.url", "type": "java.lang.String", "sourceType": "io.swagger.v3.oas.models.info.Contact"}, {"name": "springdoc.info.description", "type": "java.lang.String", "description": "描述", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties$InfoProperties"}, {"name": "springdoc.info.license.extensions", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "io.swagger.v3.oas.models.info.License"}, {"name": "springdoc.info.license.identifier", "type": "java.lang.String", "sourceType": "io.swagger.v3.oas.models.info.License"}, {"name": "springdoc.info.license.name", "type": "java.lang.String", "sourceType": "io.swagger.v3.oas.models.info.License"}, {"name": "springdoc.info.license.url", "type": "java.lang.String", "sourceType": "io.swagger.v3.oas.models.info.License"}, {"name": "springdoc.info.title", "type": "java.lang.String", "description": "标题", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties$InfoProperties"}, {"name": "springdoc.info.version", "type": "java.lang.String", "description": "版本", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties$InfoProperties"}, {"name": "springdoc.paths.extensions", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "io.swagger.v3.oas.models.Paths"}, {"name": "springdoc.tags", "type": "java.util.List<io.swagger.v3.oas.models.tags.Tag>", "description": "标签", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties"}], "hints": []}