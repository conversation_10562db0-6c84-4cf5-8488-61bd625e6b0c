{"doc": "\n 用户书籍阅读记录对象 app_book_reading_record\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 记录ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "bookId", "doc": "\n 书籍ID\r\n"}, {"name": "currentChapterId", "doc": "\n 当前章节ID（MongoDB中的ID）\r\n"}, {"name": "currentChapterIndex", "doc": "\n 当前章节索引\r\n"}, {"name": "readingProgress", "doc": "\n 阅读进度百分比\r\n"}, {"name": "totalReadingTime", "doc": "\n 总阅读时长（分钟）\r\n"}, {"name": "lastReadingTime", "doc": "\n 最后阅读时间\r\n"}, {"name": "readingSettings", "doc": "\n 阅读设置（字体大小、主题等）\r\n"}, {"name": "completedChapters", "doc": "\n 已完成章节ID列表，逗号分隔\r\n"}, {"name": "isFinished", "doc": "\n 是否读完：0-未读完，1-已读完\r\n"}, {"name": "readingSettingsMap", "doc": "\n 阅读设置对象（转换后的字段，不存储到数据库）\r\n"}, {"name": "completedChapterList", "doc": "\n 已完成章节列表（转换后的字段，不存储到数据库）\r\n"}, {"name": "book", "doc": "\n 书籍信息（关联查询，不存储到数据库）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}