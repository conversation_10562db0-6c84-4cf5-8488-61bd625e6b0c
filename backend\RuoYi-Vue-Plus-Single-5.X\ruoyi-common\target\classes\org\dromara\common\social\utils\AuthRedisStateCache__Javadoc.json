{"doc": " 授权状态缓存\n", "fields": [], "enumConstants": [], "methods": [{"name": "cache", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 存入缓存\n\n @param key   缓存key\n @param value 缓存内容\n"}, {"name": "cache", "paramTypes": ["java.lang.String", "java.lang.String", "long"], "doc": " 存入缓存\n\n @param key     缓存key\n @param value   缓存内容\n @param timeout 指定缓存过期时间(毫秒)\n"}, {"name": "get", "paramTypes": ["java.lang.String"], "doc": " 获取缓存内容\n\n @param key 缓存key\n @return 缓存内容\n"}, {"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": " 是否存在key，如果对应key的value值已过期，也返回false\n\n @param key 缓存key\n @return true：存在key，并且value没过期；false：key不存在或者已过期\n"}], "constructors": []}