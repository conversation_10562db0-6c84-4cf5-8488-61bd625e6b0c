{"doc": "\n 活动统计响应VO\r\n\r\n <AUTHOR>\r\n @date 2025-07-16\r\n", "fields": [{"name": "today", "doc": "\n 今日活动时长(毫秒)\r\n"}, {"name": "week", "doc": "\n 本周活动时长(毫秒)\r\n"}, {"name": "month", "doc": "\n 本月活动时长(毫秒)\r\n"}, {"name": "total", "doc": "\n 总活动时长(毫秒)\r\n"}, {"name": "todayFormatted", "doc": "\n 今日活动时长(格式化)\r\n"}, {"name": "weekFormatted", "doc": "\n 本周活动时长(格式化)\r\n"}, {"name": "monthFormatted", "doc": "\n 本月活动时长(格式化)\r\n"}, {"name": "totalFormatted", "doc": "\n 总活动时长(格式化)\r\n"}, {"name": "byType", "doc": "\n 按类型统计的活动时长\r\n"}], "enumConstants": [], "methods": [{"name": "formatDuration", "paramTypes": ["java.lang.Long"], "doc": "\n 格式化时长为可读字符串\r\n\r\n @param duration 时长(毫秒)\r\n @return 格式化后的字符串\r\n"}, {"name": "setFormattedDurations", "paramTypes": [], "doc": "\n 设置格式化的时长字符串\r\n"}], "constructors": []}