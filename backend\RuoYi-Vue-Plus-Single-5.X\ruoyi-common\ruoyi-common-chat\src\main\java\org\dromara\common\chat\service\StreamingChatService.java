package org.dromara.common.chat.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.chat.StreamingChatModel;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.chat.agent.AgentContext;
import org.dromara.common.chat.agent.AgentType;
import org.dromara.common.chat.config.properties.LangChain4jProperties;
import org.dromara.common.chat.sse.SseEventHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 流式聊天服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class StreamingChatService {

    // 会话内存缓存
    private final ConcurrentMap<String, MessageWindowChatMemory> memoryCache = new ConcurrentHashMap<>();
    @Autowired
    private StreamingChatModel defaultStreamingChatModel;
    @Autowired(required = false)
    @Qualifier("openAiStreamingChatModel")
    private StreamingChatModel openAiStreamingChatModel;
    @Autowired(required = false)
    @Qualifier("ollamaStreamingChatModel")
    private StreamingChatModel ollamaStreamingChatModel;
    @Autowired(required = false)
    @Qualifier("dashscopeStreamingChatModel")
    private StreamingChatModel dashscopeStreamingChatModel;
    @Autowired
    private LangChain4jProperties properties;

    /**
     * 流式聊天
     */
    public SseEmitter streamChat(AgentContext context, String message, String provider) {
        // 创建SSE发射器
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时

        // 生成会话ID（如果没有）
        String sessionId = context.getSessionId();
        if (StrUtil.isBlank(sessionId)) {
            sessionId = IdUtil.fastSimpleUUID();
            context.setSessionId(sessionId);
        }

        // 创建SSE事件处理器
        SseEventHandler handler = new SseEventHandler(
            emitter,
            sessionId,
            context.getUserId(),
            context.getAgentType().getCode()
        );

        try {
            // 发送开始事件
            handler.sendStartEvent();

            // 获取或创建内存
            MessageWindowChatMemory memory = getOrCreateMemory(sessionId);

            // 选择流式模型
            StreamingChatModel model = selectStreamingModel(provider);

            // 构建用户消息，根据Agent类型添加系统提示
            String enhancedMessage = enhanceMessageForAgent(context.getAgentType(), message);
            UserMessage userMessage = UserMessage.from(enhancedMessage);

            // 将用户消息添加到内存
            memory.add(userMessage);

            // 发起流式聊天
            model.chat(memory.messages(), handler);

            log.info("开始流式聊天, 会话: {}, Agent: {}, 用户: {}",
                sessionId, context.getAgentType().getCode(), context.getUserId());

        } catch (Exception e) {
            log.error("流式聊天启动失败, 会话: {}, 错误: {}", sessionId, e.getMessage(), e);
            handler.onError(e);
        }

        // 设置SSE连接完成和超时回调
        String finalSessionId = sessionId;
        emitter.onCompletion(() -> {
            log.info("SSE连接完成, 会话: {}", finalSessionId);
        });

        emitter.onTimeout(() -> {
            log.warn("SSE连接超时, 会话: {}", finalSessionId);
        });

        emitter.onError((error) -> {
            log.error("SSE连接错误, 会话: {}, 错误: {}", finalSessionId, error.getMessage());
        });

        return emitter;
    }

    /**
     * 流式聊天（使用默认提供商）
     */
    public SseEmitter streamChat(AgentContext context, String message) {
        return streamChat(context, message, properties.getAgent().getDefaultProvider());
    }

    /**
     * 根据Agent类型增强消息
     */
    private String enhanceMessageForAgent(AgentType agentType, String message) {
        return switch (agentType) {
            case INTERVIEWER -> "作为专业面试官，请回答：" + message;
            case RESUME_ANALYZER -> "作为简历分析专家，请分析：" + message;
            case SKILL_ASSESSOR -> "作为技能评估师，请评估：" + message;
            case CAREER_ADVISOR -> "作为职业顾问，请建议：" + message;
            case MOCK_INTERVIEWER -> "作为模拟面试官，请模拟：" + message;
            case LEARNING_GUIDE -> "作为学习导师，请指导：" + message;
            default -> message;
        };
    }

    /**
     * 选择流式模型
     */
    private StreamingChatModel selectStreamingModel(String provider) {
        if (StrUtil.isBlank(provider)) {
            return defaultStreamingChatModel;
        }

        switch (provider.toLowerCase()) {
            case "openai":
                return openAiStreamingChatModel != null ? openAiStreamingChatModel : defaultStreamingChatModel;
            case "ollama":
                return ollamaStreamingChatModel != null ? ollamaStreamingChatModel : defaultStreamingChatModel;
            case "dashscope":
                return dashscopeStreamingChatModel != null ? dashscopeStreamingChatModel : defaultStreamingChatModel;
            default:
                return defaultStreamingChatModel;
        }
    }

    /**
     * 获取或创建内存
     */
    private MessageWindowChatMemory getOrCreateMemory(String sessionId) {
        return memoryCache.computeIfAbsent(sessionId, k ->
            MessageWindowChatMemory.withMaxMessages(properties.getAgent().getMaxMemorySize())
        );
    }

    /**
     * 清理会话内存
     */
    public void clearSessionMemory(String sessionId) {
        memoryCache.remove(sessionId);
        log.info("清理会话内存, 会话: {}", sessionId);
    }

    /**
     * 清理所有内存
     */
    public void clearAllMemory() {
        memoryCache.clear();
        log.info("清理所有会话内存");
    }

    /**
     * 获取会话内存统计
     */
    public int getMemoryCacheSize() {
        return memoryCache.size();
    }

    /**
     * 检查会话是否存在
     */
    public boolean hasSession(String sessionId) {
        return memoryCache.containsKey(sessionId);
    }
}
