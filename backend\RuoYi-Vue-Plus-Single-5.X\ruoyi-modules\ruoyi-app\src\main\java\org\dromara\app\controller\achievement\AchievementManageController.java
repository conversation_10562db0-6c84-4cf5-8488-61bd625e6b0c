package org.dromara.app.controller.achievement;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.bo.AchievementBo;
import org.dromara.app.domain.vo.AchievementVo;
import org.dromara.app.service.IAchievementService;
import org.dromara.app.service.IAchievementManageService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 成就管理Controller
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/manage/achievement")
public class AchievementManageController extends BaseController {

    private final IAchievementService achievementService;
    private final IAchievementManageService achievementManageService;

    /**
     * 查询成就列表
     */
    @SaCheckPermission("app:achievement:list")
    @GetMapping("/list")
    public R<TableDataInfo<AchievementVo>> list(PageQuery pageQuery) {
        TableDataInfo<AchievementVo> result = achievementManageService.queryPageList(pageQuery);
        return R.ok(result);
    }

    /**
     * 导出成就列表
     */
    @SaCheckPermission("app:achievement:export")
    @Log(title = "成就管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AchievementBo achievementBo, HttpServletResponse response) {
        List<AchievementVo> list = achievementManageService.queryList(achievementBo);
        List<AchievementVo> exportList = achievementManageService.exportAchievement(list);
        ExcelUtil.exportExcel(exportList, "成就数据", AchievementVo.class, response);
    }

    /**
     * 获取成就详细信息
     */
    @SaCheckPermission("app:achievement:query")
    @GetMapping("/{id}")
    public R<AchievementVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        AchievementVo result = achievementManageService.queryById(id);
        return R.ok(result);
    }

    /**
     * 新增成就
     */
    @SaCheckPermission("app:achievement:add")
    @Log(title = "成就管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AchievementBo achievementBo) {
        Boolean result = achievementManageService.insertByBo(achievementBo);
        return result ? R.ok() : R.fail("新增成就失败");
    }

    /**
     * 修改成就
     */
    @SaCheckPermission("app:achievement:edit")
    @Log(title = "成就管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AchievementBo achievementBo) {
        Boolean result = achievementManageService.updateByBo(achievementBo);
        return result ? R.ok() : R.fail("修改成就失败");
    }

    /**
     * 删除成就
     */
    @SaCheckPermission("app:achievement:remove")
    @Log(title = "成就管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        Boolean result = achievementManageService.deleteWithValidByIds(List.of(ids), true);
        return result ? R.ok() : R.fail("删除成就失败");
    }

    /**
     * 批量更新成就状态
     */
    @SaCheckPermission("app:achievement:edit")
    @Log(title = "批量更新成就状态", businessType = BusinessType.UPDATE)
    @PostMapping("/status/batch")
    public R<Void> batchUpdateStatus(@RequestBody BatchUpdateStatusRequest request) {
        Boolean result = achievementManageService.batchUpdateStatus(request.getIds(), request.getIsActive());
        return result ? R.ok() : R.fail("批量更新成就状态失败");
    }

    /**
     * 复制成就
     */
    @SaCheckPermission("app:achievement:add")
    @Log(title = "复制成就", businessType = BusinessType.INSERT)
    @PostMapping("/{id}/copy")
    public R<Void> copyAchievement(@PathVariable Long id) {
        Boolean result = achievementManageService.copyAchievement(id);
        return result ? R.ok() : R.fail("复制成就失败");
    }

    /**
     * 预览成就触发条件
     */
    @SaCheckPermission("app:achievement:query")
    @PostMapping("/preview")
    public R<Object> previewTriggerCondition(@RequestBody PreviewRequest request) {
        Object result = achievementManageService.previewTriggerCondition(request.getTriggerCondition(), request.getUserId());
        return R.ok(result);
    }

    /**
     * 测试成就规则
     */
    @SaCheckPermission("app:achievement:test")
    @Log(title = "测试成就规则", businessType = BusinessType.OTHER)
    @PostMapping("/{id}/test")
    public R<Object> testAchievementRule(@PathVariable Long id, @RequestBody TestRuleRequest request) {
        Object result = achievementManageService.testAchievementRule(id, request.getUserId(),
            request.getEventType(), request.getEventData());
        return R.ok(result);
    }

    /**
     * 获取成就统计信息
     */
    @SaCheckPermission("app:achievement:query")
    @GetMapping("/stats")
    public R<Object> getAchievementStats() {
        Object result = achievementManageService.getAchievementStats();
        return R.ok(result);
    }

    /**
     * 获取成就类型列表
     */
    @SaCheckPermission("app:achievement:query")
    @GetMapping("/types")
    public R<List<String>> getAchievementTypes() {
        List<String> types = List.of("LOGIN", "LEARNING", "SOCIAL", "TIME", "CUSTOM");
        return R.ok(types);
    }

    /**
     * 获取行为类型列表
     */
    @SaCheckPermission("app:achievement:query")
    @GetMapping("/behavior-types")
    public R<List<String>> getBehaviorTypes() {
        List<String> behaviorTypes = List.of(
            "LOGIN", "VIDEO_WATCH", "COMMENT", "LIKE", "SHARE",
            "STUDY_TIME", "COURSE_COMPLETE", "QUIZ_PASS", "FORUM_POST"
        );
        return R.ok(behaviorTypes);
    }

    /**
     * 导入成就数据
     */
    @SaCheckPermission("app:achievement:import")
    @Log(title = "成就管理", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public R<String> importData(@RequestParam("file") org.springframework.web.multipart.MultipartFile file,
                               @RequestParam(value = "updateSupport", defaultValue = "false") Boolean updateSupport) throws Exception {
        // TODO
//        String message = achievementManageService.importData(file.getInputStream(), updateSupport);
//        return R.ok(message);
        return R.ok("暂未实现");
    }

    /**
     * 获取导入模板
     */
    @SaCheckPermission("app:achievement:import")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        // TODO
    }

    /**
     * 批量更新状态请求
     */
    @lombok.Data
    public static class BatchUpdateStatusRequest {
        @NotEmpty(message = "成就ID列表不能为空")
        private List<Long> ids;

        @NotNull(message = "状态不能为空")
        private String isActive;
    }

    /**
     * 预览请求
     */
    @lombok.Data
    public static class PreviewRequest {
        @NotNull(message = "触发条件不能为空")
        private String triggerCondition;

        private Long userId;
    }

    /**
     * 测试规则请求
     */
    @lombok.Data
    public static class TestRuleRequest {
        @NotNull(message = "用户ID不能为空")
        private Long userId;

        private String eventType;
        private Object eventData;
    }

}
