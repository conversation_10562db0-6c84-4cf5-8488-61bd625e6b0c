package org.dromara.app.consumer;

import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.config.PaymentRabbitMqConfig;
import org.dromara.app.service.IPaymentService;
import org.dromara.common.rabbitmq.entity.RabbitMessage;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 支付订单超时处理消费者
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "spring.rabbitmq", name = "enabled", havingValue = "true")
public class PaymentTimeoutConsumer {

    private final IPaymentService paymentService;

    /**
     * 处理超时的支付订单
     * 监听死信队列，自动取消超时未支付的订单
     *
     * @param message 消息内容
     * @param channel 通道
     * @param tag     消息标签
     */
    @RabbitListener(queues = PaymentRabbitMqConfig.PAYMENT_DEAD_QUEUE)
    public void handleTimeoutOrder(RabbitMessage<String> message, Channel channel,
                                   @Header(AmqpHeaders.DELIVERY_TAG) long tag) {
        String orderNo = message.getContent();
        log.info("接收到超时支付订单消息，订单号：{}", orderNo);

        try {
            // 查询订单状态
            var orderStatus = paymentService.queryOrderStatus(orderNo);

            // 如果订单仍然是待支付状态，则取消订单
            if ("pending".equals(orderStatus.getStatus())) {
                boolean success = paymentService.cancelOrder(orderNo);
                if (success) {
                    log.info("支付订单超时自动取消成功，订单号：{}", orderNo);
                } else {
                    log.error("支付订单超时自动取消失败，订单号：{}", orderNo);
                }
            } else {
                log.info("支付订单状态已变更，无需取消，订单号：{}，当前状态：{}",
                    orderNo, orderStatus.getStatus());
            }

            // 手动确认消息
            channel.basicAck(tag, false);

        } catch (Exception e) {
            log.error("处理超时支付订单失败，订单号：{}，错误信息：{}", orderNo, e.getMessage(), e);

            try {
                // 拒绝消息，不重新入队（避免无限循环）
                channel.basicNack(tag, false, false);
            } catch (IOException ex) {
                log.error("拒绝消息失败", ex);
            }
        }
    }
}
