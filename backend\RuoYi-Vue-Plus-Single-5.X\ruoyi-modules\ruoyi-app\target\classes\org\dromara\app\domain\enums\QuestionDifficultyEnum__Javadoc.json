{"doc": "\n 题目难度枚举\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [{"name": "EASY", "doc": "\n 简单\r\n"}, {"name": "MEDIUM", "doc": "\n 中等\r\n"}, {"name": "HARD", "doc": "\n 困难\r\n"}], "methods": [{"name": "getDescriptionByCode", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据代码获取描述\r\n\r\n @param code 难度代码\r\n @return 难度描述\r\n"}, {"name": "getCodeByDescription", "paramTypes": ["java.lang.String"], "doc": "\n 根据描述获取代码\r\n\r\n @param description 难度描述\r\n @return 难度代码\r\n"}, {"name": "getByCode", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据代码获取枚举实例\r\n\r\n @param code 难度代码\r\n @return 枚举实例\r\n"}, {"name": "getByDescription", "paramTypes": ["java.lang.String"], "doc": "\n 根据描述获取枚举实例\r\n\r\n @param description 难度描述\r\n @return 枚举实例\r\n"}], "constructors": []}