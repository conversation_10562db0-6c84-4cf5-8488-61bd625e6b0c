{"doc": "\n 成就规则引擎\r\n 负责解析和执行成就的触发条件\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "checkAchievementCondition", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.Achievement", "org.dromara.app.domain.dto.TrackEventDto"], "doc": "\n 检查成就条件是否满足\r\n\r\n @param userId        用户ID\r\n @param achievement   成就信息\r\n @param trackEventDto 用户行为事件\r\n @return 是否满足条件\r\n"}, {"name": "calculateProgress", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.Achievement", "org.dromara.app.domain.dto.TrackEventDto"], "doc": "\n 计算成就进度\r\n\r\n @param userId        用户ID\r\n @param achievement   成就信息\r\n @param trackEventDto 用户行为事件\r\n @return 进度信息 [当前值, 目标值, 进度百分比]\r\n"}, {"name": "executeRule", "paramTypes": ["java.lang.Long", "java.lang.String", "java.util.Map", "org.dromara.app.domain.dto.TrackEventDto"], "doc": "\n 执行具体的规则检查\r\n"}, {"name": "checkLoginRule", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": "\n 检查登录类成就规则\r\n"}, {"name": "checkLearningRule", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": "\n 检查学习类成就规则\r\n"}, {"name": "checkSocialRule", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": "\n 检查社交类成就规则\r\n"}, {"name": "checkTimeRule", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": "\n 检查时间类成就规则\r\n"}, {"name": "checkCustomRule", "paramTypes": ["java.lang.Long", "java.util.Map", "org.dromara.app.domain.dto.TrackEventDto"], "doc": "\n 检查自定义成就规则\r\n"}, {"name": "getCurrentValue", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": "\n 获取当前数值\r\n"}, {"name": "getTargetValue", "paramTypes": ["java.util.Map"], "doc": "\n 获取目标数值\r\n"}], "constructors": []}