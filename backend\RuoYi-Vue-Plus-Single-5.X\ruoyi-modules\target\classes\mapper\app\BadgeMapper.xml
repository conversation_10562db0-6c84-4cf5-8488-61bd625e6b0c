<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.BadgeMapper">

    <resultMap type="org.dromara.app.domain.Badge" id="BadgeResult">
        <id property="id" column="id"/>
        <result property="icon" column="icon"/>
        <result property="color" column="color"/>
        <result property="title" column="title"/>
        <result property="description" column="description"/>
        <result property="category" column="category"/>
        <result property="rarity" column="rarity"/>
        <result property="achievementId" column="achievement_id"/>
        <result property="sort" column="sort"/>
        <result property="isEnabled" column="is_enabled"/>
        <result property="unlockCriteria" column="unlock_criteria"/>
        <result property="effect" column="effect"/>
        <result property="specialFlag" column="special_flag"/>
        <result property="tags" column="tags"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectBadgeVo">
        select
            b.id, b.icon, b.color, b.title, b.description, b.category, b.rarity,
            b.achievement_id, b.sort, b.is_enabled, b.unlock_criteria, b.effect,
            b.special_flag, b.tags, b.create_by, b.create_time, b.update_by, b.update_time, b.remark
        from app_badge b
    </sql>

    <!-- 获取总徽章数 -->
    <select id="countTotalBadges" resultType="int">
        select count(1) from app_badge where is_enabled = true
    </select>

    <!-- 根据类别获取徽章数 -->
    <select id="countBadgesByCategory" parameterType="String" resultType="int">
        select count(1) from app_badge where is_enabled = true and category = #{category}
    </select>

    <!-- 根据稀有度获取徽章数 -->
    <select id="countBadgesByRarity" parameterType="String" resultType="int">
        select count(1) from app_badge where is_enabled = true and rarity = #{rarity}
    </select>

    <!-- 根据成就ID查询徽章 -->
    <select id="selectByAchievementId" parameterType="String" resultMap="BadgeResult">
        <include refid="selectBadgeVo"/>
        where is_enabled = true and achievement_id = #{achievementId}
    </select>

    <!-- 根据多个ID批量查询徽章 -->
    <select id="selectByIds" resultMap="BadgeResult">
        <include refid="selectBadgeVo"/>
        where is_enabled = true and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 获取所有推荐的徽章 -->
    <select id="selectRecommendedBadges" resultMap="BadgeResult">
        <include refid="selectBadgeVo"/>
        where is_enabled = true
        order by sort asc, create_time desc
    </select>

    <!-- 获取指定类别的徽章 -->
    <select id="selectByCategory" parameterType="String" resultMap="BadgeResult">
        <include refid="selectBadgeVo"/>
        where is_enabled = true and category = #{category}
        order by sort asc, create_time desc
    </select>

    <!-- 获取指定稀有度的徽章 -->
    <select id="selectByRarity" parameterType="String" resultMap="BadgeResult">
        <include refid="selectBadgeVo"/>
        where is_enabled = true and rarity = #{rarity}
        order by sort asc, create_time desc
    </select>

    <!-- 获取特殊徽章（限时、活动等） -->
    <select id="selectBySpecialFlag" parameterType="String" resultMap="BadgeResult">
        <include refid="selectBadgeVo"/>
        where is_enabled = true and special_flag = #{specialFlag}
        order by sort asc, create_time desc
    </select>

    <!-- 根据标签查询徽章 -->
    <select id="selectByTag" parameterType="String" resultMap="BadgeResult">
        <include refid="selectBadgeVo"/>
        where is_enabled = true and FIND_IN_SET(#{tag}, tags) > 0
        order by sort asc, create_time desc
    </select>

    <!-- 获取所有启用的徽章 -->
    <select id="selectAllEnabled" resultMap="BadgeResult">
        <include refid="selectBadgeVo"/>
        where is_enabled = true
        order by sort asc, create_time desc
    </select>

    <!-- 根据解锁条件查询徽章 -->
    <select id="selectByUnlockCriteria" parameterType="String" resultMap="BadgeResult">
        <include refid="selectBadgeVo"/>
        where is_enabled = true and unlock_criteria like CONCAT('%', #{unlockCriteria}, '%')
        order by sort asc, create_time desc
    </select>

</mapper>
