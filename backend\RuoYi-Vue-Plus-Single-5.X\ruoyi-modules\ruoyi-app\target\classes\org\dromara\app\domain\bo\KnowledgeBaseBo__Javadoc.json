{"doc": "\n 知识库业务对象 app_knowledge_base\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 知识库ID\r\n"}, {"name": "name", "doc": "\n 知识库名称\r\n"}, {"name": "description", "doc": "\n 知识库描述\r\n"}, {"name": "type", "doc": "\n 知识库类型 (general/technical/business/etc.)\r\n"}, {"name": "status", "doc": "\n 知识库状态 (0=禁用 1=启用)\r\n"}, {"name": "vectorDimension", "doc": "\n 向量维度 (默认1024)\r\n"}, {"name": "indexConfig", "doc": "\n 索引配置 (JSON格式)\r\n"}, {"name": "extendConfig", "doc": "\n 扩展配置 (JSON格式)\r\n"}, {"name": "sortOrder", "doc": "\n 排序字段\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}, {"name": "keyword", "doc": "\n 搜索关键词（用于名称和描述的模糊查询）\r\n"}, {"name": "types", "doc": "\n 知识库类型列表（用于多选过滤）\r\n"}, {"name": "statuses", "doc": "\n 状态列表（用于多选过滤）\r\n"}, {"name": "createTimeStart", "doc": "\n 创建时间范围 - 开始时间\r\n"}, {"name": "createTimeEnd", "doc": "\n 创建时间范围 - 结束时间\r\n"}, {"name": "updateTimeStart", "doc": "\n 更新时间范围 - 开始时间\r\n"}, {"name": "updateTimeEnd", "doc": "\n 更新时间范围 - 结束时间\r\n"}, {"name": "documentCountMin", "doc": "\n 文档数量范围 - 最小值\r\n"}, {"name": "documentCountMax", "doc": "\n 文档数量范围 - 最大值\r\n"}, {"name": "vectorCountMin", "doc": "\n 向量数量范围 - 最小值\r\n"}, {"name": "vectorCountMax", "doc": "\n 向量数量范围 - 最大值\r\n"}], "enumConstants": [], "methods": [], "constructors": []}