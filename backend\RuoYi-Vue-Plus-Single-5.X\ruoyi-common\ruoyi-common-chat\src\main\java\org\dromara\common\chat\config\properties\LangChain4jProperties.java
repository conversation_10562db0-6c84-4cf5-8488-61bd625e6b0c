package org.dromara.common.chat.config.properties;

import lombok.Getter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Lang<PERSON>hain4j配置属性
 *
 * <AUTHOR>
 */
@Getter
@ConfigurationProperties(prefix = "langchain4j")
public class LangChain4jProperties {

    // 主配置类的Getters and Setters
    /**
     * 是否启用LangChain4j
     */
    private boolean enabled = true;

    /**
     * OpenAI配置
     */
    private OpenAiConfig openai = new OpenAiConfig();

    /**
     * Ollama配置
     */
    private OllamaConfig ollama = new OllamaConfig();


    /**
     * 阿里云通义千问配置
     */
    private DashscopeConfig dashscope = new DashscopeConfig();

    /**
     * Agent配置
     */
    private AgentConfig agent = new AgentConfig();

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public void setOpenai(OpenAiConfig openai) {
        this.openai = openai;
    }

    public void setOllama(OllamaConfig ollama) {
        this.ollama = ollama;
    }

    public void setDashscope(DashscopeConfig dashscope) {
        this.dashscope = dashscope;
    }

    public void setAgent(AgentConfig agent) {
        this.agent = agent;
    }

    // OpenAI配置类
    public static class OpenAiConfig {
        private String apiKey;
        private String baseUrl = "https://api.openai.com/v1";
        private String model = "gpt-3.5-turbo";
        private Double temperature = 0.7;
        private Integer maxTokens = 2048;
        private Integer timeout = 60;

        // Getters and Setters
        public String getApiKey() {
            return apiKey;
        }

        public void setApiKey(String apiKey) {
            this.apiKey = apiKey;
        }

        public String getBaseUrl() {
            return baseUrl;
        }

        public void setBaseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
        }

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }

        public Double getTemperature() {
            return temperature;
        }

        public void setTemperature(Double temperature) {
            this.temperature = temperature;
        }

        public Integer getMaxTokens() {
            return maxTokens;
        }

        public void setMaxTokens(Integer maxTokens) {
            this.maxTokens = maxTokens;
        }

        public Integer getTimeout() {
            return timeout;
        }

        public void setTimeout(Integer timeout) {
            this.timeout = timeout;
        }
    }

    // Ollama配置类
    public static class OllamaConfig {
        private String baseUrl = "http://localhost:11434";
        private String model = "deepseek-r1:1.5b";
        private Double temperature = 0.7;
        private Integer timeout = 60;

        // Getters and Setters
        public String getBaseUrl() {
            return baseUrl;
        }

        public void setBaseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
        }

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }

        public Double getTemperature() {
            return temperature;
        }

        public void setTemperature(Double temperature) {
            this.temperature = temperature;
        }

        public Integer getTimeout() {
            return timeout;
        }

        public void setTimeout(Integer timeout) {
            this.timeout = timeout;
        }
    }

    // 阿里云通义千问配置类
    public static class DashscopeConfig {
        private String apiKey = "sk-7520b9ee487c408ab02f879768871023";
        private String model = "qwen-turbo";
        private Double temperature = 0.7;
        private Integer maxTokens = 2048;

        // Getters and Setters
        public String getApiKey() {
            return apiKey;
        }

        public void setApiKey(String apiKey) {
            this.apiKey = apiKey;
        }

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }

        public Double getTemperature() {
            return temperature;
        }

        public void setTemperature(Double temperature) {
            this.temperature = temperature;
        }

        public Integer getMaxTokens() {
            return maxTokens;
        }

        public void setMaxTokens(Integer maxTokens) {
            this.maxTokens = maxTokens;
        }
    }

    // Agent配置类
    public static class AgentConfig {
        private String defaultProvider = "openai";
        private Integer maxMemorySize = 10;
        private Integer sessionTimeout = 3600;

        // Getters and Setters
        public String getDefaultProvider() {
            return defaultProvider;
        }

        public void setDefaultProvider(String defaultProvider) {
            this.defaultProvider = defaultProvider;
        }

        public Integer getMaxMemorySize() {
            return maxMemorySize;
        }

        public void setMaxMemorySize(Integer maxMemorySize) {
            this.maxMemorySize = maxMemorySize;
        }

        public Integer getSessionTimeout() {
            return sessionTimeout;
        }

        public void setSessionTimeout(Integer sessionTimeout) {
            this.sessionTimeout = sessionTimeout;
        }
    }
}
