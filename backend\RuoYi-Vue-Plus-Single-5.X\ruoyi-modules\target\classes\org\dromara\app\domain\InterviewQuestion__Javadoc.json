{"doc": " 面试问题对象 app_interview_question\n\n <AUTHOR> Assistant\n @date 2025-07-19\n", "fields": [{"name": "id", "doc": " 问题ID\n"}, {"name": "jobId", "doc": " 关联岗位ID（NULL表示通用问题）\n"}, {"name": "categoryId", "doc": " 问题分类ID\n"}, {"name": "question", "doc": " 问题内容\n"}, {"name": "questionType", "doc": " 问题类型：choice-选择题，coding-编程题，open-开放题，multimodal-多模态\n"}, {"name": "difficulty", "doc": " 难度等级（1-5）\n"}, {"name": "category", "doc": " 问题分类\n"}, {"name": "tags", "doc": " 问题标签\n"}, {"name": "hint", "doc": " 提示信息\n"}, {"name": "answer", "doc": " 参考答案\n"}, {"name": "explanation", "doc": " 答案解释\n"}, {"name": "timeLimit", "doc": " 答题时间限制（秒）\n"}, {"name": "sortOrder", "doc": " 排序号\n"}, {"name": "multimodalRequirements", "doc": " 多模态评估要求（音频/视频/文本）\n"}, {"name": "evaluationCriteria", "doc": " 评估标准\n"}, {"name": "status", "doc": " 状态（0正常 1停用）\n"}, {"name": "delFlag", "doc": " 删除标志（0代表存在 1代表删除）\n"}], "enumConstants": [], "methods": [], "constructors": []}