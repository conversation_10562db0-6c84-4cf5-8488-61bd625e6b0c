{"doc": " 任务队列状态视图对象\n\n <AUTHOR>\n @date 2025-01-19\n", "fields": [{"name": "isRunning", "doc": " 队列是否运行中\n"}, {"name": "isPaused", "doc": " 队列是否暂停\n"}, {"name": "pendingTaskCount", "doc": " 待处理任务数量\n"}, {"name": "runningTaskCount", "doc": " 正在执行任务数量\n"}, {"name": "completedTaskCount", "doc": " 已完成任务数量\n"}, {"name": "failedTaskCount", "doc": " 失败任务数量\n"}, {"name": "maxQueueCapacity", "doc": " 队列最大容量\n"}, {"name": "queueUsageRate", "doc": " 当前队列使用率（百分比）\n"}, {"name": "maxConcurrency", "doc": " 最大并发数\n"}, {"name": "currentConcurrency", "doc": " 当前并发数\n"}, {"name": "averageExecutionTime", "doc": " 平均任务执行时间（秒）\n"}, {"name": "taskSuccessRate", "doc": " 任务成功率（百分比）\n"}, {"name": "startTime", "doc": " 队列启动时间\n"}, {"name": "lastActivityTime", "doc": " 最后活动时间\n"}, {"name": "totalProcessedTasks", "doc": " 总处理任务数量\n"}, {"name": "todayProcessedTasks", "doc": " 今日处理任务数量\n"}, {"name": "systemLoad", "doc": " 系统负载状态：low/medium/high\n"}, {"name": "memoryUsage", "doc": " 内存使用情况\n"}, {"name": "cpuUsage", "doc": " CPU使用情况\n"}], "enumConstants": [], "methods": [], "constructors": []}