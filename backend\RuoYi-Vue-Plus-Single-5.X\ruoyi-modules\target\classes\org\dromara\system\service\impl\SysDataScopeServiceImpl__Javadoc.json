{"doc": " 数据权限 实现\n <p>\n 注意: 此Service内不允许调用标注`数据权限`注解的方法\n 例如: deptMapper.selectList 此 selectList 方法标注了`数据权限`注解 会出现循环解析的问题\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getRoleCustom", "paramTypes": ["java.lang.Long"], "doc": " 获取角色自定义权限\n\n @param roleId 角色Id\n @return 部门Id组\n"}, {"name": "getDeptAndChild", "paramTypes": ["java.lang.Long"], "doc": " 获取部门及以下权限\n\n @param deptId 部门Id\n @return 部门Id组\n"}], "constructors": []}