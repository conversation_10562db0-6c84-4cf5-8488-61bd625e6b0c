{"doc": " 缓存值包装类\n 支持自定义过期时间\n\n <AUTHOR>\n", "fields": [{"name": "value", "doc": " 实际缓存的值\n"}, {"name": "createTime", "doc": " 创建时间戳（毫秒）\n"}, {"name": "expireTime", "doc": " 过期时间戳（毫秒）\n"}, {"name": "ttl", "doc": " 过期时长（秒）\n"}, {"name": "neverExpire", "doc": " 是否永不过期\n"}], "enumConstants": [], "methods": [{"name": "of", "paramTypes": ["java.lang.Object"], "doc": " 创建永不过期的缓存值\n\n @param value 值\n @return 包装对象\n"}, {"name": "of", "paramTypes": ["java.lang.Object", "long"], "doc": " 创建带过期时间的缓存值\n\n @param value      值\n @param ttlSeconds 过期时间（秒）\n @return 包装对象\n"}, {"name": "isExpired", "paramTypes": [], "doc": " 检查是否已过期\n\n @return true如果已过期\n"}, {"name": "getRemainingTtl", "paramTypes": [], "doc": " 获取剩余生存时间（秒）\n\n @return 剩余秒数，-1表示永不过期，0表示已过期\n"}, {"name": "getCreateDateTime", "paramTypes": [], "doc": " 获取创建时间\n\n @return 创建时间\n"}, {"name": "getExpireDateTime", "paramTypes": [], "doc": " 获取过期时间\n\n @return 过期时间，null表示永不过期\n"}], "constructors": []}