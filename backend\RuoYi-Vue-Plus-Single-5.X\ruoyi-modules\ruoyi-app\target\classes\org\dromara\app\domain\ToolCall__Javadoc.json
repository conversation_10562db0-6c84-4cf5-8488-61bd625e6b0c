{"doc": "\n 工具调用记录对象 app_tool_call\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 调用记录ID\r\n"}, {"name": "sessionId", "doc": "\n 会话ID\r\n"}, {"name": "messageId", "doc": "\n 消息ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "toolId", "doc": "\n 工具ID\r\n"}, {"name": "toolName", "doc": "\n 工具名称\r\n"}, {"name": "parameters", "doc": "\n 调用参数（JSON格式）\r\n"}, {"name": "result", "doc": "\n 调用结果（JSON格式）\r\n"}, {"name": "status", "doc": "\n 调用状态：0-调用中，1-成功，2-失败\r\n"}, {"name": "errorMessage", "doc": "\n 错误信息\r\n"}, {"name": "executionTime", "doc": "\n 执行时间（毫秒）\r\n"}, {"name": "startTime", "doc": "\n 调用开始时间\r\n"}, {"name": "endTime", "doc": "\n 调用结束时间\r\n"}, {"name": "retryCount", "doc": "\n 重试次数\r\n"}, {"name": "source", "doc": "\n 调用来源：user/system/auto\r\n"}, {"name": "context", "doc": "\n 调用上下文（JSON格式）\r\n"}, {"name": "parametersObject", "doc": "\n 参数对象（不存储到数据库）\r\n"}, {"name": "resultObject", "doc": "\n 结果对象（不存储到数据库）\r\n"}, {"name": "contextObject", "doc": "\n 上下文对象（不存储到数据库）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}