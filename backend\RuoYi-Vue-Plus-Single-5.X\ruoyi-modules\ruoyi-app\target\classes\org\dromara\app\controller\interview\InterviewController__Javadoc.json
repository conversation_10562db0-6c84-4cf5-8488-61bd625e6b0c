{"doc": "\n 面试控制器\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getJobList", "paramTypes": ["org.dromara.app.domain.bo.JobQueryBo"], "doc": "\n 获取岗位列表\r\n"}, {"name": "getCategories", "paramTypes": ["java.lang.Bo<PERSON>an"], "doc": "\n 获取岗位分类列表\r\n"}, {"name": "getInterviewModes", "paramTypes": [], "doc": "\n 获取面试模式列表\r\n"}, {"name": "getSearchSuggestions", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取搜索建议\r\n"}, {"name": "createInterviewSession", "paramTypes": ["org.dromara.app.controller.interview.InterviewController.CreateSessionRequest"], "doc": "\n 创建面试会话\r\n"}, {"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["org.dromara.app.controller.interview.InterviewController.FavoriteJobRequest"], "doc": "\n 收藏/取消收藏岗位\r\n"}, {"name": "checkDevice", "paramTypes": [], "doc": "\n 设备检测\r\n"}, {"name": "getStatistics", "paramTypes": [], "doc": "\n 获取统计信息\r\n"}, {"name": "getInterviewHistory", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": "\n 获取面试历史记录\r\n"}, {"name": "getUserStatistics", "paramTypes": [], "doc": "\n 获取用户统计数据（用于历史页面）\r\n"}, {"name": "getJobDetail", "paramTypes": ["java.lang.Long"], "doc": "\n 获取岗位详情\r\n"}, {"name": "getSampleQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 获取示例问题\r\n"}, {"name": "getRelatedJobs", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 获取相关岗位\r\n"}, {"name": "getJobStatistics", "paramTypes": ["java.lang.Long"], "doc": "\n 获取岗位统计数据\r\n"}, {"name": "getJobInterviewModes", "paramTypes": ["java.lang.Long"], "doc": "\n 获取岗位面试模式列表\r\n"}, {"name": "checkUserReadiness", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 检查用户准备度\r\n"}, {"name": "shareJob", "paramTypes": ["org.dromara.app.controller.interview.InterviewController.ShareJobRequest"], "doc": "\n 分享岗位信息\r\n"}, {"name": "getInterviewResult", "paramTypes": ["java.lang.String"], "doc": "\n 获取面试结果\r\n"}, {"name": "getInterviewerInfo", "paramTypes": ["java.lang.String"], "doc": "\n 获取AI面试官信息\r\n"}, {"name": "submitAnswerForRoom", "paramTypes": ["org.dromara.app.controller.interview.InterviewController.SubmitAnswerForRoomRequest"], "doc": "\n 提交答案\r\n"}], "constructors": []}