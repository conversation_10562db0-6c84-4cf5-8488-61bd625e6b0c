{"doc": "\n 面试问题Service接口\r\n\r\n <AUTHOR> Assistant\r\n @date 2025-07-19\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByJobId", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 根据岗位ID查询问题列表\r\n\r\n @param jobId      岗位ID\r\n @param difficulty 难度等级\r\n @param limit      限制数量\r\n @return 问题列表\r\n"}, {"name": "selectByTechnicalDomain", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": "\n 根据技术领域查询问题\r\n\r\n @param technicalDomain 技术领域\r\n @param questionType    问题类型\r\n @param limit          限制数量\r\n @return 问题列表\r\n"}, {"name": "selectMultimodalQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询多模态问题\r\n\r\n @param jobId 岗位ID\r\n @param limit 限制数量\r\n @return 多模态问题列表\r\n"}, {"name": "selectByTags", "paramTypes": ["java.util.List", "java.lang.Integer"], "doc": "\n 根据标签查询问题\r\n\r\n @param tags  标签列表\r\n @param limit 限制数量\r\n @return 问题列表\r\n"}, {"name": "selectQuestionPage", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.String"], "doc": "\n 分页查询问题列表\r\n\r\n @param page           分页参数\r\n @param jobId          岗位ID\r\n @param questionType   问题类型\r\n @param difficulty     难度等级\r\n @param category       问题分类\r\n @return 分页结果\r\n"}, {"name": "getQuestionsByDifficulty", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 根据难度分级获取问题\r\n\r\n @param jobId      岗位ID\r\n @param easyCount  简单题数量\r\n @param mediumCount 中等题数量\r\n @param hardCount  困难题数量\r\n @return 分级问题列表\r\n"}], "constructors": []}