{"doc": " 支付SSE服务接口\n 用于管理SSE连接和推送支付状态消息\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "validatePaymentToken", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 验证支付token\n\n @param orderNo  订单号\n @param payToken 支付token\n @return 验证结果\n"}, {"name": "createConnection", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 创建SSE连接\n\n @param orderNo  订单号\n @param payToken 支付token\n @return SSE发射器\n"}, {"name": "pushPaymentSuccess", "paramTypes": ["java.lang.String"], "doc": " 推送支付成功消息\n\n @param orderNo 订单号\n"}, {"name": "pushPaymentFailed", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 推送支付失败消息\n\n @param orderNo 订单号\n @param reason  失败原因\n"}, {"name": "pushPaymentCancelled", "paramTypes": ["java.lang.String"], "doc": " 推送支付取消消息\n\n @param orderNo 订单号\n"}, {"name": "manualQueryOrderStatus", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 手动查询订单状态\n\n @param orderNo  订单号\n @param payToken 支付token\n"}, {"name": "closeConnection", "paramTypes": ["java.lang.String"], "doc": " 关闭SSE连接\n\n @param orderNo 订单号\n"}, {"name": "cleanupExpiredConnections", "paramTypes": [], "doc": " 清理过期连接\n"}], "constructors": []}