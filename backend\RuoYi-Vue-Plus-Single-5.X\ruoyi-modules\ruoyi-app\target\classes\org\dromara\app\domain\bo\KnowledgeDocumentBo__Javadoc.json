{"doc": "\n 知识库文档业务对象 app_knowledge_document\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 文档ID\r\n"}, {"name": "knowledgeBaseId", "doc": "\n 知识库ID\r\n"}, {"name": "title", "doc": "\n 文档标题\r\n"}, {"name": "content", "doc": "\n 文档内容\r\n"}, {"name": "docType", "doc": "\n 文档类型 (text/pdf/word/markdown/etc.)\r\n"}, {"name": "source", "doc": "\n 文档来源 (upload/url/api/etc.)\r\n"}, {"name": "originalFilename", "doc": "\n 原始文件名\r\n"}, {"name": "filePath", "doc": "\n 文件路径\r\n"}, {"name": "fileSize", "doc": "\n 文件大小 (字节)\r\n"}, {"name": "status", "doc": "\n 文档状态 (0=处理中 1=已完成 2=失败)\r\n"}, {"name": "processStatus", "doc": "\n 处理状态 (0=未处理 1=已向量化 2=已索引)\r\n"}, {"name": "summary", "doc": "\n 文档摘要\r\n"}, {"name": "tags", "doc": "\n 文档标签 (JSON数组)\r\n"}, {"name": "metadata", "doc": "\n 文档元数据 (JSON格式)\r\n"}, {"name": "processConfig", "doc": "\n 处理配置 (JSON格式)\r\n"}, {"name": "sortOrder", "doc": "\n 排序字段\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}, {"name": "keyword", "doc": "\n 搜索关键词（用于标题和内容的模糊查询）\r\n"}, {"name": "docTypes", "doc": "\n 文档类型列表（用于多选过滤）\r\n"}, {"name": "sources", "doc": "\n 文档来源列表（用于多选过滤）\r\n"}, {"name": "statuses", "doc": "\n 状态列表（用于多选过滤）\r\n"}, {"name": "processStatuses", "doc": "\n 处理状态列表（用于多选过滤）\r\n"}, {"name": "createTimeStart", "doc": "\n 创建时间范围 - 开始时间\r\n"}, {"name": "createTimeEnd", "doc": "\n 创建时间范围 - 结束时间\r\n"}, {"name": "fileSizeMin", "doc": "\n 文件大小范围 - 最小值\r\n"}, {"name": "fileSizeMax", "doc": "\n 文件大小范围 - 最大值\r\n"}, {"name": "vectorCountMin", "doc": "\n 向量数量范围 - 最小值\r\n"}, {"name": "vectorCountMax", "doc": "\n 向量数量范围 - 最大值\r\n"}], "enumConstants": [], "methods": [], "constructors": []}