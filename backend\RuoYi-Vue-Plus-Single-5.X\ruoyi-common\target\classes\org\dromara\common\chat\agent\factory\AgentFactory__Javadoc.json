{"doc": " AI Agent工厂类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "createAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentType", "org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 创建Agent\n"}, {"name": "createAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentType", "org.dromara.common.chat.agent.AgentContext"], "doc": " 创建Agent（使用默认提供商）\n"}, {"name": "createInterviewerAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 创建面试官Agent\n\n @param context  上下文\n @param provider 模型提供商（可选）\n @return 面试官Agent实例\n"}, {"name": "createResumeAnalyzerAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 创建简历分析Agent\n\n @param context  上下文\n @param provider 模型提供商（可选）\n @return 简历分析Agent实例\n"}, {"name": "createSkillAssessorAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 创建技能评估Agent\n\n @param context  上下文\n @param provider 模型提供商（可选）\n @return 技能评估Agent实例\n"}, {"name": "createCareerAdvisorAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 创建职业顾问Agent\n\n @param context  上下文\n @param provider 模型提供商（可选）\n @return 职业顾问Agent实例\n"}, {"name": "createMockInterviewerAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 创建模拟面试Agent\n\n @param context  上下文\n @param provider 模型提供商（可选）\n @return 模拟面试Agent实例\n"}, {"name": "createLearningGuideAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 创建学习导师Agent\n\n @param context  上下文\n @param provider 模型提供商（可选）\n @return 学习导师Agent实例\n"}, {"name": "createGeneralChatAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 创建通用聊天Agent\n\n @param context  上下文\n @param provider 模型提供商（可选）\n @return 通用聊天Agent实例\n"}, {"name": "selectModel", "paramTypes": ["java.lang.String"], "doc": " 选择模型\n"}, {"name": "selectStreamingModel", "paramTypes": ["java.lang.String"], "doc": " 选择流式模型\n"}, {"name": "getDefaultStreamingChatModel", "paramTypes": [], "doc": " 获取默认流式聊天模型\n"}, {"name": "getStreamingChatModel", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据Agent类型和模型名称获取流式聊天模型\n\n @param agentType Agent类型\n @param modelName 模型名称（可选）\n @return 流式聊天模型\n"}, {"name": "buildCacheKey", "paramTypes": ["org.dromara.common.chat.agent.AgentType", "java.lang.String", "java.lang.String"], "doc": " 构建缓存键\n"}, {"name": "clearAgentCache", "paramTypes": ["java.lang.String"], "doc": " 清理Agent缓存\n"}, {"name": "clearAllAgentCache", "paramTypes": [], "doc": " 清理所有Agent缓存\n"}], "constructors": []}