package org.dromara.app.domain.bo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.app.domain.Feedback;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 意见反馈业务对象 feedback
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Feedback.class, reverseConvertGenerate = false)
@ExcelIgnoreUnannotated
public class FeedbackBo extends BaseEntity {

    /**
     * 反馈ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 反馈类型
     */
    @NotBlank(message = "反馈类型不能为空", groups = {AddGroup.class})
    @Size(max = 50, message = "反馈类型长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String type;

    /**
     * 反馈内容
     */
    @NotBlank(message = "反馈内容不能为空", groups = {AddGroup.class})
    @Size(max = 2000, message = "反馈内容长度不能超过2000个字符", groups = {AddGroup.class, EditGroup.class})
    private String content;

    /**
     * 联系方式
     */
    @Size(max = 100, message = "联系方式长度不能超过100个字符", groups = {AddGroup.class, EditGroup.class})
    private String contactInfo;

    /**
     * 设备信息
     */
    @Size(max = 200, message = "设备信息长度不能超过200个字符", groups = {AddGroup.class, EditGroup.class})
    private String deviceInfo;

    /**
     * 应用版本
     */
    @Size(max = 50, message = "应用版本长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String appVersion;

    /**
     * 平台信息
     */
    @Size(max = 50, message = "平台信息长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String platform;

    /**
     * 状态
     */
    private String status;

    /**
     * 处理回复
     */
    @Size(max = 2000, message = "处理回复长度不能超过2000个字符", groups = {EditGroup.class})
    private String reply;

    /**
     * 处理人
     */
    @Size(max = 100, message = "处理人长度不能超过100个字符", groups = {EditGroup.class})
    private String handler;
}
