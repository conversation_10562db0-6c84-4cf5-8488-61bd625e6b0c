{"doc": "\n 面试报告Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectBySessionIdAndUserId", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 根据会话ID和用户ID查询报告\r\n\r\n @param sessionId 会话ID\r\n @param userId 用户ID\r\n @return 面试报告\r\n"}, {"name": "selectUserReportStats", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户的报告统计\r\n\r\n @param userId 用户ID\r\n @return 统计数据\r\n"}, {"name": "selectRecentReports", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询用户最近的报告\r\n\r\n @param userId 用户ID\r\n @param limit 限制数量\r\n @return 报告列表\r\n"}, {"name": "selectJobPositionStats", "paramTypes": ["java.lang.String"], "doc": "\n 查询岗位相关的报告统计\r\n\r\n @param jobPosition 岗位\r\n @return 统计数据\r\n"}, {"name": "selectScoreDistribution", "paramTypes": [], "doc": "\n 查询分数分布统计\r\n\r\n @return 分数分布\r\n"}], "constructors": []}