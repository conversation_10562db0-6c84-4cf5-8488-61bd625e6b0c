package org.dromara.app.controller.common;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.vo.DashboardSummaryVO;
import org.dromara.app.domain.vo.SmartTaskVO;
import org.dromara.app.domain.vo.StudyStatsVO;
import org.dromara.app.domain.vo.UserAbilitiesVO;
import org.dromara.app.service.IDashboardService;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 首页仪表盘控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/dashboard")
public class DashBoardController extends BaseController {

    private final IDashboardService dashboardService;

    /**
     * 获取首页仪表盘汇总数据
     */
    @SaCheckLogin
    @GetMapping("/summary")
    public R<DashboardSummaryVO> getDashboardSummary() {
        Long userId = StpUtil.getLoginIdAsLong();
        DashboardSummaryVO summary = dashboardService.getDashboardSummary(userId);
        return R.ok(summary);
    }

    /**
     * 获取用户能力评估数据
     */
    @SaCheckLogin
    @GetMapping("/abilities")
    public R<UserAbilitiesVO> getUserAbilities() {
        Long userId = StpUtil.getLoginIdAsLong();
        UserAbilitiesVO abilities = dashboardService.getUserAbilities(userId);
        return R.ok(abilities);
    }

    /**
     * 获取学习统计数据
     */
    @SaCheckLogin
    @GetMapping("/study-stats")
    public R<StudyStatsVO> getStudyStats() {
        Long userId = StpUtil.getLoginIdAsLong();
        StudyStatsVO stats = dashboardService.getStudyStats(userId);
        return R.ok(stats);
    }

    /**
     * 获取智能推荐任务
     */
    @SaCheckLogin
    @GetMapping("/smart-tasks")
    public R<List<SmartTaskVO>> getSmartTasks(@RequestParam(value = "limit", defaultValue = "5") Integer limit,
                                              @RequestParam(value = "type", required = false) String type) {
        Long userId = StpUtil.getLoginIdAsLong();
        List<SmartTaskVO> tasks = dashboardService.getSmartTasks(userId, limit, type);
        return R.ok(tasks);
    }

    /**
     * 获取最近面试记录
     */
    @SaCheckLogin
    @GetMapping("/recent-interviews")
    public R<List<Map<String, Object>>> getRecentInterviews(@RequestParam(value = "limit", defaultValue = "5") Integer limit,
                                                            @RequestParam(value = "page", defaultValue = "1") Integer page) {
        Long userId = StpUtil.getLoginIdAsLong();
        List<Map<String, Object>> interviews = dashboardService.getRecentInterviews(userId, limit, page);
        return R.ok(interviews);
    }

    /**
     * 更新用户目标岗位
     */
    @SaCheckLogin
    @PostMapping("/update-target")
    public R<Void> updateTargetPosition(@RequestBody Map<String, String> params) {
        Long userId = StpUtil.getLoginIdAsLong();
        String targetPosition = params.get("targetPosition");
        boolean result = dashboardService.updateTargetPosition(userId, targetPosition);
        return toAjax(result);
    }

    /**
     * 标记任务为已完成
     */
    @SaCheckLogin
    @PostMapping("/complete-task")
    public R<Void> completeTask(@RequestBody Map<String, Long> params) {
        Long userId = StpUtil.getLoginIdAsLong();
        Long taskId = params.get("taskId");
        boolean result = dashboardService.completeTask(userId, taskId);
        return toAjax(result);
    }

    /**
     * 获取首页所有数据（聚合接口）
     */
    @SaCheckLogin
    @GetMapping("/all")
    public R<Map<String, Object>> getDashboardData() {
        Long userId = StpUtil.getLoginIdAsLong();
        Map<String, Object> dashboardData = dashboardService.getDashboardData(userId);
        return R.ok(dashboardData);
    }
}
