{"groups": [{"name": "api-decrypt", "type": "org.dromara.common.encrypt.properties.ApiDecryptProperties", "sourceType": "org.dromara.common.encrypt.properties.ApiDecryptProperties"}, {"name": "caffeine", "type": "org.dromara.common.caffeine.properties.CaffeineProperties", "sourceType": "org.dromara.common.caffeine.properties.CaffeineProperties"}, {"name": "<PERSON><PERSON>a", "type": "org.dromara.common.web.config.properties.CaptchaProperties", "sourceType": "org.dromara.common.web.config.properties.CaptchaProperties"}, {"name": "<PERSON><PERSON><PERSON>", "type": "org.dromara.common.social.config.properties.SocialProperties", "sourceType": "org.dromara.common.social.config.properties.SocialProperties"}, {"name": "langchain4j", "type": "org.dromara.common.chat.config.properties.LangChain4jProperties", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties"}, {"name": "langchain4j.agent", "type": "org.dromara.common.chat.config.properties.LangChain4jProperties$AgentConfig", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties", "sourceMethod": "getAgent()"}, {"name": "langchain4j.dashscope", "type": "org.dromara.common.chat.config.properties.LangChain4jProperties$DashscopeConfig", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties", "sourceMethod": "getDashscope()"}, {"name": "langchain4j.ollama", "type": "org.dromara.common.chat.config.properties.LangChain4jProperties$OllamaConfig", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties", "sourceMethod": "getOllama()"}, {"name": "langchain4j.openai", "type": "org.dromara.common.chat.config.properties.LangChain4jProperties$OpenAiConfig", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties", "sourceMethod": "getOpenai()"}, {"name": "mail", "type": "org.dromara.common.mail.config.properties.MailProperties", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mybatis-encryptor", "type": "org.dromara.common.encrypt.properties.EncryptorProperties", "sourceType": "org.dromara.common.encrypt.properties.EncryptorProperties"}, {"name": "pay.alipay", "type": "org.dromara.common.pay.properties.AlipayProperties", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "redisson", "type": "org.dromara.common.redis.config.properties.RedissonProperties", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties"}, {"name": "redisson.cluster-servers-config", "type": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties", "sourceMethod": "getClusterServersConfig()"}, {"name": "redisson.single-server-config", "type": "org.dromara.common.redis.config.properties.RedissonProperties$SingleServerConfig", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties", "sourceMethod": "getSingleServerConfig()"}, {"name": "security", "type": "org.dromara.common.security.config.properties.SecurityProperties", "sourceType": "org.dromara.common.security.config.properties.SecurityProperties"}, {"name": "spring.data.mongodb", "type": "org.dromara.common.mongodb.properties.MongoProperties", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties"}, {"name": "spring.data.mongodb.pool", "type": "org.dromara.common.mongodb.properties.MongoProperties$Pool", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties", "sourceMethod": "getPool()"}, {"name": "spring.rabbitmq", "type": "org.dromara.common.rabbitmq.properties.RabbitMqProperties", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties"}, {"name": "spring.rabbitmq.listener", "type": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Listener", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties", "sourceMethod": "getListener()"}, {"name": "spring.rabbitmq.listener.retry", "type": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Listener", "sourceMethod": "getRetry()"}, {"name": "spring.rabbitmq.retry", "type": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties", "sourceMethod": "getRetry()"}, {"name": "springdoc", "type": "org.dromara.common.doc.config.properties.SpringDocProperties", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties"}, {"name": "springdoc.components", "type": "io.swagger.v3.oas.models.Components", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties", "sourceMethod": "getComponents()"}, {"name": "springdoc.external-docs", "type": "io.swagger.v3.oas.models.ExternalDocumentation", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties", "sourceMethod": "getExternalDocs()"}, {"name": "springdoc.info", "type": "org.dromara.common.doc.config.properties.SpringDocProperties$InfoProperties", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties", "sourceMethod": "getInfo()"}, {"name": "springdoc.info.contact", "type": "io.swagger.v3.oas.models.info.Contact", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties$InfoProperties", "sourceMethod": "getContact()"}, {"name": "springdoc.info.license", "type": "io.swagger.v3.oas.models.info.License", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties$InfoProperties", "sourceMethod": "getLicense()"}, {"name": "springdoc.paths", "type": "io.swagger.v3.oas.models.Paths", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties", "sourceMethod": "getPaths()"}, {"name": "sse", "type": "org.dromara.common.sse.config.SseProperties", "sourceType": "org.dromara.common.sse.config.SseProperties"}, {"name": "thread-pool", "type": "org.dromara.common.core.config.properties.ThreadPoolProperties", "sourceType": "org.dromara.common.core.config.properties.ThreadPoolProperties"}, {"name": "websocket", "type": "org.dromara.common.websocket.config.properties.WebSocketProperties", "sourceType": "org.dromara.common.websocket.config.properties.WebSocketProperties"}, {"name": "xss", "type": "org.dromara.common.web.config.properties.XssProperties", "sourceType": "org.dromara.common.web.config.properties.XssProperties"}], "properties": [{"name": "api-decrypt.enabled", "type": "java.lang.Bo<PERSON>an", "description": "加密开关", "sourceType": "org.dromara.common.encrypt.properties.ApiDecryptProperties"}, {"name": "api-decrypt.header-flag", "type": "java.lang.String", "description": "头部标识", "sourceType": "org.dromara.common.encrypt.properties.ApiDecryptProperties"}, {"name": "api-decrypt.private-key", "type": "java.lang.String", "description": "请求解密私钥", "sourceType": "org.dromara.common.encrypt.properties.ApiDecryptProperties"}, {"name": "api-decrypt.public-key", "type": "java.lang.String", "description": "响应加密公钥", "sourceType": "org.dromara.common.encrypt.properties.ApiDecryptProperties"}, {"name": "caffeine.debug-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用调试日志", "sourceType": "org.dromara.common.caffeine.properties.CaffeineProperties"}, {"name": "caffeine.default-cache-name", "type": "java.lang.String", "description": "默认缓存名称", "sourceType": "org.dromara.common.caffeine.properties.CaffeineProperties"}, {"name": "caffeine.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否开启", "sourceType": "org.dromara.common.caffeine.properties.CaffeineProperties"}, {"name": "caffeine.expire-after-access", "type": "java.lang.Long", "description": "访问后过期时间（秒）", "sourceType": "org.dromara.common.caffeine.properties.CaffeineProperties"}, {"name": "caffeine.expire-after-write", "type": "java.lang.Long", "description": "写入后过期时间（秒）", "sourceType": "org.dromara.common.caffeine.properties.CaffeineProperties"}, {"name": "caffeine.initial-capacity", "type": "java.lang.Integer", "description": "初始容量", "sourceType": "org.dromara.common.caffeine.properties.CaffeineProperties"}, {"name": "caffeine.maximum-size", "type": "java.lang.Long", "description": "缓存最大条目数", "sourceType": "org.dromara.common.caffeine.properties.CaffeineProperties"}, {"name": "caffeine.record-stats", "type": "java.lang.Bo<PERSON>an", "description": "是否启用统计", "sourceType": "org.dromara.common.caffeine.properties.CaffeineProperties"}, {"name": "caffeine.refresh-after-write", "type": "java.lang.Long", "description": "刷新时间（秒）", "sourceType": "org.dromara.common.caffeine.properties.CaffeineProperties"}, {"name": "caffeine.soft-values", "type": "java.lang.Bo<PERSON>an", "description": "是否启用软引用值", "sourceType": "org.dromara.common.caffeine.properties.CaffeineProperties"}, {"name": "caffeine.weak-keys", "type": "java.lang.Bo<PERSON>an", "description": "是否启用软引用", "sourceType": "org.dromara.common.caffeine.properties.CaffeineProperties"}, {"name": "caffeine.weak-values", "type": "java.lang.Bo<PERSON>an", "description": "是否启用弱引用值", "sourceType": "org.dromara.common.caffeine.properties.CaffeineProperties"}, {"name": "captcha.category", "type": "org.dromara.common.web.enums.CaptchaCategory", "description": "验证码类别", "sourceType": "org.dromara.common.web.config.properties.CaptchaProperties"}, {"name": "captcha.char-length", "type": "java.lang.Integer", "description": "字符验证码长度", "sourceType": "org.dromara.common.web.config.properties.CaptchaProperties"}, {"name": "captcha.enable", "type": "java.lang.Bo<PERSON>an", "sourceType": "org.dromara.common.web.config.properties.CaptchaProperties"}, {"name": "captcha.number-length", "type": "java.lang.Integer", "description": "数字验证码位数", "sourceType": "org.dromara.common.web.config.properties.CaptchaProperties"}, {"name": "captcha.type", "type": "org.dromara.common.web.enums.CaptchaType", "description": "验证码类型", "sourceType": "org.dromara.common.web.config.properties.CaptchaProperties"}, {"name": "justauth.type", "type": "java.util.Map<java.lang.String,org.dromara.common.social.config.properties.SocialLoginConfigProperties>", "description": "授权类型", "sourceType": "org.dromara.common.social.config.properties.SocialProperties"}, {"name": "langchain4j.agent.default-provider", "type": "java.lang.String", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$AgentConfig"}, {"name": "langchain4j.agent.max-memory-size", "type": "java.lang.Integer", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$AgentConfig"}, {"name": "langchain4j.agent.session-timeout", "type": "java.lang.Integer", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$AgentConfig"}, {"name": "langchain4j.dashscope.api-key", "type": "java.lang.String", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$DashscopeConfig"}, {"name": "langchain4j.dashscope.max-tokens", "type": "java.lang.Integer", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$DashscopeConfig"}, {"name": "langchain4j.dashscope.model", "type": "java.lang.String", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$DashscopeConfig"}, {"name": "langchain4j.dashscope.temperature", "type": "java.lang.Double", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$DashscopeConfig"}, {"name": "langchain4j.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用LangChain4j", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties"}, {"name": "langchain4j.ollama.base-url", "type": "java.lang.String", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OllamaConfig"}, {"name": "langchain4j.ollama.model", "type": "java.lang.String", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OllamaConfig"}, {"name": "langchain4j.ollama.temperature", "type": "java.lang.Double", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OllamaConfig"}, {"name": "langchain4j.ollama.timeout", "type": "java.lang.Integer", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OllamaConfig"}, {"name": "langchain4j.openai.api-key", "type": "java.lang.String", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OpenAiConfig"}, {"name": "langchain4j.openai.base-url", "type": "java.lang.String", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OpenAiConfig"}, {"name": "langchain4j.openai.max-tokens", "type": "java.lang.Integer", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OpenAiConfig"}, {"name": "langchain4j.openai.model", "type": "java.lang.String", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OpenAiConfig"}, {"name": "langchain4j.openai.temperature", "type": "java.lang.Double", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OpenAiConfig"}, {"name": "langchain4j.openai.timeout", "type": "java.lang.Integer", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OpenAiConfig"}, {"name": "mail.auth", "type": "java.lang.Bo<PERSON>an", "description": "是否需要用户名密码验证", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.connection-timeout", "type": "java.lang.Long", "description": "Socket连接超时值，单位毫秒，缺省值不超时", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.enabled", "type": "java.lang.Bo<PERSON>an", "description": "过滤开关", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.from", "type": "java.lang.String", "description": "发送方，遵循RFC-822标准<br> 发件人可以是以下形式： <pre> 1. <EMAIL> 2. name &lt;<EMAIL>&gt; </pre>", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.host", "type": "java.lang.String", "description": "SMTP服务器域名", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.pass", "type": "java.lang.String", "description": "密码", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.port", "type": "java.lang.Integer", "description": "SMTP服务端口", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.ssl-enable", "type": "java.lang.Bo<PERSON>an", "description": "使用 SSL安全连接", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.starttls-enable", "type": "java.lang.Bo<PERSON>an", "description": "使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。它将纯文本连接升级为加密连接（TLS或SSL）， 而不是使用一个单独的加密通信端口。", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.timeout", "type": "java.lang.Long", "description": "SMTP超时时长，单位毫秒，缺省值不超时", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mail.user", "type": "java.lang.String", "description": "用户名", "sourceType": "org.dromara.common.mail.config.properties.MailProperties"}, {"name": "mybatis-encryptor.algorithm", "type": "org.dromara.common.encrypt.enumd.AlgorithmType", "description": "默认算法", "sourceType": "org.dromara.common.encrypt.properties.EncryptorProperties"}, {"name": "mybatis-encryptor.enable", "type": "java.lang.Bo<PERSON>an", "description": "过滤开关", "sourceType": "org.dromara.common.encrypt.properties.EncryptorProperties"}, {"name": "mybatis-encryptor.encode", "type": "org.dromara.common.encrypt.enumd.EncodeType", "description": "编码方式，base64/hex", "sourceType": "org.dromara.common.encrypt.properties.EncryptorProperties"}, {"name": "mybatis-encryptor.password", "type": "java.lang.String", "description": "安全秘钥", "sourceType": "org.dromara.common.encrypt.properties.EncryptorProperties"}, {"name": "mybatis-encryptor.private-key", "type": "java.lang.String", "description": "私钥", "sourceType": "org.dromara.common.encrypt.properties.EncryptorProperties"}, {"name": "mybatis-encryptor.public-key", "type": "java.lang.String", "description": "公钥", "sourceType": "org.dromara.common.encrypt.properties.EncryptorProperties"}, {"name": "pay.alipay.alipay-public-key", "type": "java.lang.String", "description": "支付宝公钥 - 支付宝公钥", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.app-id", "type": "java.lang.String", "description": "应用ID - 支付宝应用ID", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.charset", "type": "java.lang.String", "description": "字符编码格式 - 默认UTF-8", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.enabled", "type": "java.lang.Bo<PERSON>an", "description": "支付宝功能开关", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.environment", "type": "java.lang.String", "description": "环境类型 - sandbox(沙箱) 或 production(生产)", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.format", "type": "java.lang.String", "description": "返回格式 - 默认JSON", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.notify-url", "type": "java.lang.String", "description": "服务器异步通知页面路径 - 支付成功后支付宝异步通知的地址", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.private-key", "type": "java.lang.String", "description": "商户私钥 - 应用私钥", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.return-url", "type": "java.lang.String", "description": "页面跳转同步通知页面路径 - 支付成功后页面跳转的地址", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.server-url", "type": "java.lang.String", "description": "应用服务器地址 - 用于构建回调URL的基础地址", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.sign-type", "type": "java.lang.String", "description": "签名方式 - 默认RSA2", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "redisson.cluster-servers-config.client-name", "type": "java.lang.String", "description": "客户端名称", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig"}, {"name": "redisson.cluster-servers-config.idle-connection-timeout", "type": "java.lang.Integer", "description": "连接空闲超时，单位：毫秒", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig"}, {"name": "redisson.cluster-servers-config.master-connection-minimum-idle-size", "type": "java.lang.Integer", "description": "master最小空闲连接数", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig"}, {"name": "redisson.cluster-servers-config.master-connection-pool-size", "type": "java.lang.Integer", "description": "master连接池大小", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig"}, {"name": "redisson.cluster-servers-config.read-mode", "type": "org.redisson.config.ReadMode", "description": "读取模式", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig"}, {"name": "redisson.cluster-servers-config.slave-connection-minimum-idle-size", "type": "java.lang.Integer", "description": "slave最小空闲连接数", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig"}, {"name": "redisson.cluster-servers-config.slave-connection-pool-size", "type": "java.lang.Integer", "description": "slave连接池大小", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig"}, {"name": "redisson.cluster-servers-config.subscription-connection-pool-size", "type": "java.lang.Integer", "description": "发布和订阅连接池大小", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig"}, {"name": "redisson.cluster-servers-config.subscription-mode", "type": "org.redisson.config.SubscriptionMode", "description": "订阅模式", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig"}, {"name": "redisson.cluster-servers-config.timeout", "type": "java.lang.Integer", "description": "命令等待超时，单位：毫秒", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig"}, {"name": "redisson.key-prefix", "type": "java.lang.String", "description": "redis缓存key前缀", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties"}, {"name": "redisson.netty-threads", "type": "java.lang.Integer", "description": "Netty线程池数量,默认值 = 当前处理核数量 * 2", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties"}, {"name": "redisson.single-server-config.client-name", "type": "java.lang.String", "description": "客户端名称", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$SingleServerConfig"}, {"name": "redisson.single-server-config.connection-minimum-idle-size", "type": "java.lang.Integer", "description": "最小空闲连接数", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$SingleServerConfig"}, {"name": "redisson.single-server-config.connection-pool-size", "type": "java.lang.Integer", "description": "连接池大小", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$SingleServerConfig"}, {"name": "redisson.single-server-config.idle-connection-timeout", "type": "java.lang.Integer", "description": "连接空闲超时，单位：毫秒", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$SingleServerConfig"}, {"name": "redisson.single-server-config.subscription-connection-pool-size", "type": "java.lang.Integer", "description": "发布和订阅连接池大小", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$SingleServerConfig"}, {"name": "redisson.single-server-config.timeout", "type": "java.lang.Integer", "description": "命令等待超时，单位：毫秒", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$SingleServerConfig"}, {"name": "redisson.threads", "type": "java.lang.Integer", "description": "线程池数量,默认值 = 当前处理核数量 * 2", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties"}, {"name": "security.excludes", "type": "java.lang.String[]", "description": "排除路径", "sourceType": "org.dromara.common.security.config.properties.SecurityProperties"}, {"name": "spring.data.mongodb.authentication-database", "type": "java.lang.String", "description": "认证数据库", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties"}, {"name": "spring.data.mongodb.database", "type": "java.lang.String", "description": "数据库名称", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties"}, {"name": "spring.data.mongodb.host", "type": "java.lang.String", "description": "主机地址", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties"}, {"name": "spring.data.mongodb.password", "type": "java.lang.String", "description": "密码", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties"}, {"name": "spring.data.mongodb.pool.max-connection-idle-time-ms", "type": "java.lang.Long", "description": "连接最大空闲时间(毫秒)", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties$Pool"}, {"name": "spring.data.mongodb.pool.max-connection-life-time-ms", "type": "java.lang.Long", "description": "连接最大生存时间(毫秒)", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties$Pool"}, {"name": "spring.data.mongodb.pool.max-connection-timeout-ms", "type": "java.lang.Long", "description": "连接超时时间(毫秒)", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties$Pool"}, {"name": "spring.data.mongodb.pool.max-read-timeout-ms", "type": "java.lang.Long", "description": "读取超时时间(毫秒)", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties$Pool"}, {"name": "spring.data.mongodb.pool.max-size", "type": "java.lang.Integer", "description": "最大连接数", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties$Pool"}, {"name": "spring.data.mongodb.pool.max-wait-time-ms", "type": "java.lang.Long", "description": "最大等待时间(毫秒)", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties$Pool"}, {"name": "spring.data.mongodb.pool.min-size", "type": "java.lang.Integer", "description": "最小连接数", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties$Pool"}, {"name": "spring.data.mongodb.port", "type": "java.lang.Integer", "description": "端口号", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties"}, {"name": "spring.data.mongodb.uri", "type": "java.lang.String", "description": "MongoDB连接字符串", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties"}, {"name": "spring.data.mongodb.username", "type": "java.lang.String", "description": "用户名", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties"}, {"name": "spring.rabbitmq.channel-cache-size", "type": "java.lang.Integer", "description": "通道缓存大小", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties"}, {"name": "spring.rabbitmq.connection-timeout", "type": "java.lang.Integer", "description": "连接超时时间（毫秒）", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties"}, {"name": "spring.rabbitmq.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用 RabbitMQ", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties"}, {"name": "spring.rabbitmq.host", "type": "java.lang.String", "description": "主机地址", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties"}, {"name": "spring.rabbitmq.listener.concurrency", "type": "java.lang.Integer", "description": "并发消费者数量", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Listener"}, {"name": "spring.rabbitmq.listener.max-concurrency", "type": "java.lang.Integer", "description": "最大并发消费者数量", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Listener"}, {"name": "spring.rabbitmq.listener.prefetch", "type": "java.lang.Integer", "description": "预取数量", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Listener"}, {"name": "spring.rabbitmq.listener.retry.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用重试", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.listener.retry.initial-interval", "type": "java.lang.Long", "description": "初始重试间隔（毫秒）", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.listener.retry.max-attempts", "type": "java.lang.Integer", "description": "最大重试次数", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.listener.retry.max-interval", "type": "java.lang.Long", "description": "最大重试间隔（毫秒）", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.listener.retry.multiplier", "type": "java.lang.Double", "description": "重试间隔倍数", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.password", "type": "java.lang.String", "description": "密码", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties"}, {"name": "spring.rabbitmq.port", "type": "java.lang.Integer", "description": "端口", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties"}, {"name": "spring.rabbitmq.retry.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用重试", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.retry.initial-interval", "type": "java.lang.Long", "description": "初始重试间隔（毫秒）", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.retry.max-attempts", "type": "java.lang.Integer", "description": "最大重试次数", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.retry.max-interval", "type": "java.lang.Long", "description": "最大重试间隔（毫秒）", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.retry.multiplier", "type": "java.lang.Double", "description": "重试间隔倍数", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.username", "type": "java.lang.String", "description": "用户名", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties"}, {"name": "spring.rabbitmq.virtual-host", "type": "java.lang.String", "description": "虚拟主机", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties"}, {"name": "springdoc.info.description", "type": "java.lang.String", "description": "描述", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties$InfoProperties"}, {"name": "springdoc.info.title", "type": "java.lang.String", "description": "标题", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties$InfoProperties"}, {"name": "springdoc.info.version", "type": "java.lang.String", "description": "版本", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties$InfoProperties"}, {"name": "springdoc.tags", "type": "java.util.List<io.swagger.v3.oas.models.tags.Tag>", "description": "标签", "sourceType": "org.dromara.common.doc.config.properties.SpringDocProperties"}, {"name": "sse.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "org.dromara.common.sse.config.SseProperties"}, {"name": "sse.path", "type": "java.lang.String", "description": "路径", "sourceType": "org.dromara.common.sse.config.SseProperties"}, {"name": "thread-pool.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否开启线程池", "sourceType": "org.dromara.common.core.config.properties.ThreadPoolProperties"}, {"name": "thread-pool.keep-alive-seconds", "type": "java.lang.Integer", "description": "线程池维护线程所允许的空闲时间", "sourceType": "org.dromara.common.core.config.properties.ThreadPoolProperties"}, {"name": "thread-pool.queue-capacity", "type": "java.lang.Integer", "description": "队列最大长度", "sourceType": "org.dromara.common.core.config.properties.ThreadPoolProperties"}, {"name": "websocket.allowed-origins", "type": "java.lang.String", "description": "设置访问源地址", "sourceType": "org.dromara.common.websocket.config.properties.WebSocketProperties"}, {"name": "websocket.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "org.dromara.common.websocket.config.properties.WebSocketProperties"}, {"name": "websocket.path", "type": "java.lang.String", "description": "路径", "sourceType": "org.dromara.common.websocket.config.properties.WebSocketProperties"}, {"name": "xss.enabled", "type": "java.lang.Bo<PERSON>an", "description": "Xss开关", "sourceType": "org.dromara.common.web.config.properties.XssProperties"}, {"name": "xss.exclude-urls", "type": "java.util.List<java.lang.String>", "description": "排除路径", "sourceType": "org.dromara.common.web.config.properties.XssProperties"}], "hints": []}