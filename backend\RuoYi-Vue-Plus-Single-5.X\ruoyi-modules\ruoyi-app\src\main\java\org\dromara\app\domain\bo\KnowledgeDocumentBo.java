package org.dromara.app.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.app.domain.KnowledgeDocument;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.io.Serial;
import java.io.Serializable;

/**
 * 知识库文档业务对象 app_knowledge_document
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = KnowledgeDocument.class, reverseConvertGenerate = false)
public class KnowledgeDocumentBo extends PageQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 文档ID
     */
    @NotNull(message = "文档ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 知识库ID
     */
    @NotNull(message = "知识库ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long knowledgeBaseId;

    /**
     * 文档标题
     */
    @NotBlank(message = "文档标题不能为空", groups = {AddGroup.class, EditGroup.class})
    private String title;

    /**
     * 文档内容
     */
    @NotBlank(message = "文档内容不能为空", groups = {AddGroup.class})
    private String content;

    /**
     * 文档类型 (text/pdf/word/markdown/etc.)
     */
    private String docType;

    /**
     * 文档来源 (upload/url/api/etc.)
     */
    private String source;

    /**
     * 原始文件名
     */
    private String originalFilename;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件大小 (字节)
     */
    private Long fileSize;

    /**
     * 文档状态 (0=处理中 1=已完成 2=失败)
     */
    private Integer status;

    /**
     * 处理状态 (0=未处理 1=已向量化 2=已索引)
     */
    private Integer processStatus;

    /**
     * 文档摘要
     */
    private String summary;

    /**
     * 文档标签 (JSON数组)
     */
    private String tags;

    /**
     * 文档元数据 (JSON格式)
     */
    private String metadata;

    /**
     * 处理配置 (JSON格式)
     */
    private String processConfig;

    /**
     * 排序字段
     */
    private Integer sortOrder;

    /**
     * 备注
     */
    private String remark;

    // ========== 查询条件字段 ==========

    /**
     * 搜索关键词（用于标题和内容的模糊查询）
     */
    private String keyword;

    /**
     * 文档类型列表（用于多选过滤）
     */
    private String[] docTypes;

    /**
     * 文档来源列表（用于多选过滤）
     */
    private String[] sources;

    /**
     * 状态列表（用于多选过滤）
     */
    private Integer[] statuses;

    /**
     * 处理状态列表（用于多选过滤）
     */
    private Integer[] processStatuses;

    /**
     * 创建时间范围 - 开始时间
     */
    private String createTimeStart;

    /**
     * 创建时间范围 - 结束时间
     */
    private String createTimeEnd;

    /**
     * 文件大小范围 - 最小值
     */
    private Long fileSizeMin;

    /**
     * 文件大小范围 - 最大值
     */
    private Long fileSizeMax;

    /**
     * 向量数量范围 - 最小值
     */
    private Long vectorCountMin;

    /**
     * 向量数量范围 - 最大值
     */
    private Long vectorCountMax;
}
