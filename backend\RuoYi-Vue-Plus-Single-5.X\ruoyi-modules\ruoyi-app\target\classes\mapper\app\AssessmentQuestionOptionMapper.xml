<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.AssessmentQuestionOptionMapper">

    <resultMap type="org.dromara.app.domain.AssessmentQuestionOption" id="AssessmentQuestionOptionResult">
        <result property="optionId" column="option_id"/>
        <result property="questionId" column="question_id"/>
        <result property="optionCode" column="option_code"/>
        <result property="optionText" column="option_text"/>
        <result property="optionScore" column="option_score"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectAssessmentQuestionOptionVo">
        select option_id,
               question_id,
               option_code,
               option_text,
               option_score,
               sort_order,
               create_by,
               create_time,
               update_by,
               update_time
        from assessment_question_option
    </sql>

    <select id="selectOptionsByQuestionId" resultMap="AssessmentQuestionOptionResult">
        <include refid="selectAssessmentQuestionOptionVo"/>
        where question_id = #{questionId}
        order by sort_order
    </select>

    <select id="selectOptionByQuestionIdAndCode" resultMap="AssessmentQuestionOptionResult">
        <include refid="selectAssessmentQuestionOptionVo"/>
        where question_id = #{questionId} and option_code = #{optionCode}
    </select>

    <insert id="insertBatch">
        insert into assessment_question_option(question_id, option_code, option_text, option_score,
        sort_order, create_by, create_time)
        values
        <foreach collection="options" item="option" separator=",">
            (#{option.questionId}, #{option.optionCode}, #{option.optionText}, #{option.optionScore},
            #{option.sortOrder}, #{option.createBy}, #{option.createTime})
        </foreach>
    </insert>

    <delete id="deleteByQuestionId">
        delete
        from assessment_question_option
        where question_id = #{questionId}
    </delete>

</mapper>
