package org.dromara.app.domain.dto;

import lombok.Data;

/**
 * 聊天响应DTO
 *
 * <AUTHOR>
 */
@Data
public class ChatResponseDto {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 错误信息
     */
    private String error;

    /**
     * 消息元数据
     */
    private MessageMetadata metadata;

    /**
     * 创建成功响应
     */
    public static ChatResponseDto success(String message, String messageId, String sessionId) {
        ChatResponseDto response = new ChatResponseDto();
        response.setSuccess(true);
        response.setMessage(message);
        response.setMessageId(messageId);
        response.setSessionId(sessionId);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static ChatResponseDto error(String error) {
        ChatResponseDto response = new ChatResponseDto();
        response.setSuccess(false);
        response.setError(error);
        return response;
    }

    /**
     * 消息元数据
     */
    @Data
    public static class MessageMetadata {
        private String modelName;
        private Integer inputTokens;
        private Integer outputTokens;
        private Long responseTime;
        private String provider;
        private Double temperature;
        private String finishReason;
    }
}
