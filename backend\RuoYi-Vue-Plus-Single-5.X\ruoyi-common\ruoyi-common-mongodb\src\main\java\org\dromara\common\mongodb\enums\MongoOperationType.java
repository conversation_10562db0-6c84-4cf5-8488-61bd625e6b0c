package org.dromara.common.mongodb.enums;

/**
 * MongoDB操作类型枚举
 *
 * <AUTHOR>
 */
public enum MongoOperationType {

    /**
     * 插入操作
     */
    INSERT("insert", "插入"),

    /**
     * 更新操作
     */
    UPDATE("update", "更新"),

    /**
     * 删除操作
     */
    DELETE("delete", "删除"),

    /**
     * 查询操作
     */
    FIND("find", "查询"),

    /**
     * 聚合操作
     */
    AGGREGATE("aggregate", "聚合"),

    /**
     * 计数操作
     */
    COUNT("count", "计数");

    private final String code;
    private final String description;

    MongoOperationType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static MongoOperationType fromCode(String code) {
        for (MongoOperationType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown operation type code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
