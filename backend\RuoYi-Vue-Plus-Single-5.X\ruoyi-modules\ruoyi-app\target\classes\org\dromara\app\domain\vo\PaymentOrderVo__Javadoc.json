{"doc": "\n 支付订单返回VO\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "orderId", "doc": "\n 订单ID\r\n"}, {"name": "orderNo", "doc": "\n 订单号\r\n"}, {"name": "productId", "doc": "\n 商品ID\r\n"}, {"name": "productType", "doc": "\n 商品类型\r\n"}, {"name": "productTitle", "doc": "\n 商品标题\r\n"}, {"name": "amount", "doc": "\n 支付金额\r\n"}, {"name": "paymentMethod", "doc": "\n 支付方式\r\n"}, {"name": "status", "doc": "\n 订单状态\r\n"}, {"name": "alipayTradeNo", "doc": "\n 支付宝交易号\r\n"}, {"name": "payUrl", "doc": "\n 支付页面URL\r\n"}, {"name": "payForm", "doc": "\n 支付页面表单HTML\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "payTime", "doc": "\n 支付时间\r\n"}, {"name": "expireTime", "doc": "\n 过期时间\r\n"}, {"name": "remark", "doc": "\n 备注信息\r\n"}, {"name": "payToken", "doc": "\n 支付token\r\n"}], "enumConstants": [], "methods": [], "constructors": []}