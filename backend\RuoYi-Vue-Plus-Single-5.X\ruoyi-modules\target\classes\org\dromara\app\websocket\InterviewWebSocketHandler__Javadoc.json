{"doc": " 面试WebSocket处理器\n 处理实时智能建议和表情分析功能\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleConnect", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "cn.hutool.json.JSONObject"], "doc": " 处理连接确认\n"}, {"name": "handleEmotionRequest", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "cn.hutool.json.JSONObject"], "doc": " 处理表情检测请求\n"}, {"name": "handleSpeechAnalysis", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "cn.hutool.json.JSONObject"], "doc": " 处理语音分析请求\n"}, {"name": "handleHeartbeat", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "cn.hutool.json.JSONObject"], "doc": " 处理心跳\n"}, {"name": "generateEmotionBasedSuggestion", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "cn.hutool.json.JSONObject", "java.lang.Integer"], "doc": " 基于情绪分析生成智能建议\n"}, {"name": "generateSpeechBasedSuggestion", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "cn.hutool.json.JSONObject", "java.lang.Integer"], "doc": " 基于语音分析生成智能建议\n"}, {"name": "updateContext", "paramTypes": ["java.lang.String", "java.lang.String", "cn.hutool.json.JSONObject"], "doc": " 更新会话上下文\n"}, {"name": "sendMessage", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "cn.hutool.json.JSONObject"], "doc": " 发送消息\n"}, {"name": "sendErrorMessage", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "java.lang.String"], "doc": " 发送错误消息\n"}], "constructors": []}