{"doc": "\n 面试书籍Service接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryBookPage", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String", "java.lang.String", "java.lang.Long"], "doc": "\n 分页查询书籍列表\r\n\r\n @param pageNum     页码\r\n @param pageSize    每页大小\r\n @param category    分类筛选\r\n @param searchQuery 搜索关键词\r\n @param userId      用户ID\r\n @return 分页结果\r\n"}, {"name": "queryBookDetail", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 根据ID查询书籍详情\r\n\r\n @param id     书籍ID\r\n @param userId 用户ID\r\n @return 书籍详情\r\n"}, {"name": "queryHotBooks", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询热门书籍列表\r\n\r\n @param limit 限制数量\r\n @return 热门书籍列表\r\n"}, {"name": "queryRecommended<PERSON>ooks", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询推荐书籍列表\r\n\r\n @param userId 用户ID\r\n @param limit  限制数量\r\n @return 推荐书籍列表\r\n"}, {"name": "queryCategoryStats", "paramTypes": [], "doc": "\n 查询分类统计信息\r\n\r\n @return 分类统计\r\n"}, {"name": "incrementReadCount", "paramTypes": ["java.lang.Long"], "doc": "\n 增加书籍阅读次数\r\n\r\n @param bookId 书籍ID\r\n @return 是否成功\r\n"}, {"name": "insertBook", "paramTypes": ["org.dromara.app.domain.Book"], "doc": "\n 新增书籍\r\n\r\n @param book 书籍信息\r\n @return 是否成功\r\n"}, {"name": "updateBook", "paramTypes": ["org.dromara.app.domain.Book"], "doc": "\n 修改书籍\r\n\r\n @param book 书籍信息\r\n @return 是否成功\r\n"}, {"name": "deleteBooks", "paramTypes": ["java.util.List"], "doc": "\n 删除书籍\r\n\r\n @param ids 书籍ID列表\r\n @return 是否成功\r\n"}, {"name": "updateBookStatus", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": "\n 上架/下架书籍\r\n\r\n @param id     书籍ID\r\n @param status 状态\r\n @return 是否成功\r\n"}], "constructors": []}