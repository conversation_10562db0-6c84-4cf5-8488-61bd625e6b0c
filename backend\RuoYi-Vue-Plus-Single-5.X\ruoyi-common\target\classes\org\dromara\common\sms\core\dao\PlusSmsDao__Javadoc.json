{"doc": " SmsDao缓存配置 (使用框架自带RedisUtils实现 协议统一)\n <p>主要用于短信重试和拦截的缓存\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "set", "paramTypes": ["java.lang.String", "java.lang.Object", "long"], "doc": " 存储\n\n @param key       键\n @param value     值\n @param cacheTime 缓存时间（单位：秒)\n"}, {"name": "set", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 存储\n\n @param key   键\n @param value 值\n"}, {"name": "get", "paramTypes": ["java.lang.String"], "doc": " 读取\n\n @param key 键\n @return 值\n"}, {"name": "remove", "paramTypes": ["java.lang.String"], "doc": " remove\n <p> 根据key移除缓存\n\n @param key 缓存键\n @return 被删除的value\n <AUTHOR>\n"}, {"name": "clean", "paramTypes": [], "doc": " 清空\n"}], "constructors": []}