{"doc": " 知识库实体\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 知识库ID\n"}, {"name": "name", "doc": " 知识库名称\n"}, {"name": "description", "doc": " 知识库描述\n"}, {"name": "type", "doc": " 知识库类型 (general/technical/business/etc.)\n"}, {"name": "status", "doc": " 知识库状态 (0=禁用 1=启用)\n"}, {"name": "vectorDimension", "doc": " 向量维度 (默认1024)\n"}, {"name": "documentCount", "doc": " 文档数量\n"}, {"name": "vectorCount", "doc": " 向量数量\n"}, {"name": "indexConfig", "doc": " 索引配置 (JSON格式)\n"}, {"name": "extendConfig", "doc": " 扩展配置 (JSON格式)\n"}, {"name": "lastSyncTime", "doc": " 最后更新时间\n"}, {"name": "sortOrder", "doc": " 排序字段\n"}, {"name": "remark", "doc": " 备注\n"}], "enumConstants": [], "methods": [], "constructors": []}