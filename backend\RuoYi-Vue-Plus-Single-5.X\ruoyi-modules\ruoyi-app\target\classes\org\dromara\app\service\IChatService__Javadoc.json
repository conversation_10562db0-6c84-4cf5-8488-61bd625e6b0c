{"doc": "\n 聊天服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendMessage", "paramTypes": ["org.dromara.app.domain.dto.ChatRequestDto", "java.lang.Long"], "doc": "\n 发送消息（同步响应）\r\n\r\n @param request 聊天请求\r\n @param userId  用户ID\r\n @return 聊天响应\r\n"}, {"name": "sendMessageStream", "paramTypes": ["org.dromara.app.domain.dto.ChatRequestDto", "java.lang.Long"], "doc": "\n 发送消息（流式响应）\r\n\r\n @param request 聊天请求\r\n @param userId  用户ID\r\n @return SSE流对象\r\n"}, {"name": "createSession", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String"], "doc": "\n 创建新会话\r\n\r\n @param userId    用户ID\r\n @param agentType 代理类型\r\n @param title     会话标题（可选）\r\n @return 会话信息\r\n"}, {"name": "getUserSessions", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 获取用户会话列表\r\n\r\n @param userId   用户ID\r\n @param pageNum  页码\r\n @param pageSize 每页大小\r\n @return 会话分页结果\r\n"}, {"name": "getSessionDetail", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 获取会话详情（包含消息列表）\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @return 会话详情\r\n"}, {"name": "getSessionMessages", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 获取会话消息列表\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @param pageNum   页码\r\n @param pageSize  每页大小\r\n @return 消息分页结果\r\n"}, {"name": "deleteSession", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 删除会话\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @return 是否成功\r\n"}, {"name": "clearSessionMessages", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 清空会话消息\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @return 是否成功\r\n"}, {"name": "updateSessionTitle", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.String"], "doc": "\n 更新会话标题\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @param title     新标题\r\n @return 是否成功\r\n"}, {"name": "archiveSession", "paramTypes": ["java.lang.String", "java.lang.Long", "boolean"], "doc": "\n 归档/取消归档会话\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @param archived  是否归档\r\n @return 是否成功\r\n"}, {"name": "getUserChatStats", "paramTypes": ["java.lang.Long"], "doc": "\n 获取用户聊天统计信息\r\n\r\n @param userId 用户ID\r\n @return 统计信息\r\n"}, {"name": "checkSessionPermission", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 检查会话权限\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @return 是否有权限\r\n"}, {"name": "generateSessionTitle", "paramTypes": ["java.lang.String"], "doc": "\n 生成会话标题（基于首条消息）\r\n\r\n @param firstMessage 首条消息内容\r\n @return 生成的标题\r\n"}], "constructors": []}