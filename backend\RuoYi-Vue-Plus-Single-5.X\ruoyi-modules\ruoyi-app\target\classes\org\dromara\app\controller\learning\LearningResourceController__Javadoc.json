{"doc": "\n 学习资源控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "createResource", "paramTypes": ["org.dromara.app.domain.LearningResource"], "doc": "\n 创建学习资源\r\n\r\n @param resource 学习资源\r\n @return 操作结果\r\n"}, {"name": "updateResource", "paramTypes": ["org.dromara.app.domain.LearningResource"], "doc": "\n 更新学习资源\r\n\r\n @param resource 学习资源\r\n @return 操作结果\r\n"}, {"name": "deleteResource", "paramTypes": ["java.lang.Long"], "doc": "\n 删除学习资源\r\n\r\n @param resourceId 资源ID\r\n @return 操作结果\r\n"}, {"name": "getResource", "paramTypes": ["java.lang.Long"], "doc": "\n 获取资源详情\r\n\r\n @param resourceId 资源ID\r\n @return 学习资源\r\n"}, {"name": "getResourcesPage", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 分页查询资源\r\n\r\n @param pageNum 页码\r\n @param pageSize 每页大小\r\n @param skillArea 技能领域\r\n @param type 资源类型\r\n @param difficulty 难度等级\r\n @return 分页结果\r\n"}, {"name": "getRecommendedResources", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": "\n 获取推荐资源\r\n\r\n @param skillArea 技能领域\r\n @param difficulty 难度等级\r\n @param limit 限制数量\r\n @return 资源列表\r\n"}, {"name": "getResourcesByType", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 根据类型获取资源\r\n\r\n @param type 资源类型\r\n @param limit 限制数量\r\n @return 资源列表\r\n"}, {"name": "getPopularResources", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取热门资源\r\n\r\n @param limit 限制数量\r\n @return 资源列表\r\n"}, {"name": "getFreeResources", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取免费资源\r\n\r\n @param skillArea 技能领域\r\n @param limit 限制数量\r\n @return 资源列表\r\n"}, {"name": "getResourcesByTag", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 根据标签获取资源\r\n\r\n @param tag 标签\r\n @param limit 限制数量\r\n @return 资源列表\r\n"}, {"name": "searchResources", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 搜索资源\r\n\r\n @param keyword 关键词\r\n @param limit 限制数量\r\n @return 资源列表\r\n"}, {"name": "assessResourceQuality", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.LearningResource.QualityAssessment"], "doc": "\n 评估资源质量\r\n\r\n @param resourceId 资源ID\r\n @param qualityAssessment 质量评估\r\n @return 操作结果\r\n"}, {"name": "updateResourceRating", "paramTypes": ["java.lang.Long", "java.lang.Double"], "doc": "\n 更新资源评分\r\n\r\n @param resourceId 资源ID\r\n @param rating 评分\r\n @return 操作结果\r\n"}, {"name": "startLearning", "paramTypes": ["java.lang.Long"], "doc": "\n 开始学习资源\r\n\r\n @param resourceId 资源ID\r\n @return 操作结果\r\n"}, {"name": "completeLearning", "paramTypes": ["java.lang.Long"], "doc": "\n 完成学习资源\r\n\r\n @param resourceId 资源ID\r\n @return 操作结果\r\n"}, {"name": "getSimilarResources", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 获取相似资源\r\n\r\n @param resourceId 资源ID\r\n @param limit 限制数量\r\n @return 资源列表\r\n"}, {"name": "performQualityAssessment", "paramTypes": ["java.lang.Long"], "doc": "\n 执行质量评估\r\n\r\n @param resourceId 资源ID\r\n @return 质量评估结果\r\n"}, {"name": "batchImportResources", "paramTypes": ["java.util.List"], "doc": "\n 批量导入资源\r\n\r\n @param resources 资源列表\r\n @return 导入结果\r\n"}, {"name": "getResourceStatistics", "paramTypes": [], "doc": "\n 获取资源统计信息\r\n\r\n @return 统计信息\r\n"}, {"name": "getSkillAreaDistribution", "paramTypes": [], "doc": "\n 获取技能领域分布\r\n\r\n @return 技能领域分布\r\n"}, {"name": "getResourceTypeDistribution", "paramTypes": [], "doc": "\n 获取资源类型分布\r\n\r\n @return 资源类型分布\r\n"}, {"name": "getTopProviders", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取顶级提供者\r\n\r\n @param limit 限制数量\r\n @return 提供者排行\r\n"}, {"name": "getResourceTagCloud", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取资源标签云\r\n\r\n @param limit 限制数量\r\n @return 标签统计\r\n"}, {"name": "getPersonalizedRecommendations", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 获取个性化推荐资源\r\n\r\n @param userId 用户ID\r\n @param limit 限制数量\r\n @return 个性化推荐资源列表\r\n"}, {"name": "getResourcesBySkillGaps", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": "\n 根据技能差距推荐资源\r\n\r\n @param skillAreas 技能领域列表（逗号分隔）\r\n @param difficulty 难度等级\r\n @param limit 限制数量\r\n @return 推荐资源列表\r\n"}, {"name": "getCollaborativeFilteringRecommendations", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 获取协同过滤推荐资源\r\n\r\n @param userId 用户ID\r\n @param limit 限制数量\r\n @return 推荐资源列表\r\n"}, {"name": "getTrendingResources", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": "\n 获取趋势资源\r\n\r\n @param days 天数\r\n @param limit 限制数量\r\n @return 趋势资源列表\r\n"}, {"name": "sortResourcesByRecommendation", "paramTypes": ["java.util.List", "java.util.Map"], "doc": "\n 智能推荐资源排序\r\n\r\n @param resources 原始资源列表\r\n @param userPreferences 用户偏好\r\n @return 排序后的资源列表\r\n"}], "constructors": []}