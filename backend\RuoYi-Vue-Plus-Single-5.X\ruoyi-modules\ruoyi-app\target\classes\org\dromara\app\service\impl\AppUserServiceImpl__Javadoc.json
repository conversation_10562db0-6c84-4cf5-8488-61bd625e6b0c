{"doc": "\n 应用用户Service业务层处理\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "validateSmsCode", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 验证短信验证码\r\n\r\n @param phone   手机号\r\n @param smsCode 短信验证码\r\n @return 验证结果\r\n"}, {"name": "validateEmailCode", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 验证邮箱验证码\r\n\r\n @param email     邮箱\r\n @param emailCode 邮箱验证码\r\n @return 验证结果\r\n"}], "constructors": []}