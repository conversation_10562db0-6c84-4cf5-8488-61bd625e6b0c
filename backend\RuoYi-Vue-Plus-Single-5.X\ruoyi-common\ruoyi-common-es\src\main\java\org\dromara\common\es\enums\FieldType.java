package org.dromara.common.es.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ES字段类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FieldType {

    /**
     * 文本类型
     */
    TEXT("text"),

    /**
     * 关键字类型
     */
    KEYWORD("keyword"),

    /**
     * 整数类型
     */
    INTEGER("integer"),

    /**
     * 长整数类型
     */
    LONG("long"),

    /**
     * 浮点数类型
     */
    FLOAT("float"),

    /**
     * 双精度浮点数类型
     */
    DOUBLE("double"),

    /**
     * 布尔类型
     */
    BOOLEAN("boolean"),

    /**
     * 日期类型
     */
    DATE("date"),

    /**
     * 对象类型
     */
    OBJECT("object"),

    /**
     * 嵌套类型
     */
    NESTED("nested"),

    /**
     * 地理位置类型
     */
    GEO_POINT("geo_point"),

    /**
     * IP地址类型
     */
    IP("ip");

    private final String type;

}
