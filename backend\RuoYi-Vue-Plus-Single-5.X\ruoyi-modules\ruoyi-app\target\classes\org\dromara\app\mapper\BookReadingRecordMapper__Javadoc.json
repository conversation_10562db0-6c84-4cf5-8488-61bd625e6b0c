{"doc": "\n 书籍阅读记录Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByUserIdAndBookId", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 根据用户ID和书籍ID查询阅读记录\r\n\r\n @param userId 用户ID\r\n @param bookId 书籍ID\r\n @return 阅读记录\r\n"}, {"name": "selectUserReadingHistory", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.Long"], "doc": "\n 查询用户的阅读历史（分页）\r\n\r\n @param page   分页参数\r\n @param userId 用户ID\r\n @return 阅读记录列表\r\n"}, {"name": "selectRecentReading", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询用户最近阅读的书籍\r\n\r\n @param userId 用户ID\r\n @param limit  限制数量\r\n @return 阅读记录列表\r\n"}, {"name": "selectUserReadingStats", "paramTypes": ["java.lang.Long"], "doc": "\n 统计用户阅读数据\r\n\r\n @param userId 用户ID\r\n @return 阅读统计数据\r\n"}, {"name": "insertOrUpdate", "paramTypes": ["org.dromara.app.domain.BookReadingRecord"], "doc": "\n 更新或插入阅读记录\r\n\r\n @param record 阅读记录\r\n @return 影响行数\r\n"}], "constructors": []}