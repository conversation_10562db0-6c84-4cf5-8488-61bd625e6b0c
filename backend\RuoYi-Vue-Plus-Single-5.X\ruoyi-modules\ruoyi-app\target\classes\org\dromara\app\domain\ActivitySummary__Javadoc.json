{"doc": "\n 用户活动总览对象 app_activity_summary\r\n\r\n <AUTHOR>\r\n @date 2025-07-16\r\n", "fields": [{"name": "id", "doc": "\n 总览ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "totalDuration", "doc": "\n 总活动时长(毫秒)\r\n"}, {"name": "totalSessions", "doc": "\n 总会话数\r\n"}, {"name": "courseDuration", "doc": "\n 课程学习时长(毫秒)\r\n"}, {"name": "interviewDuration", "doc": "\n 面试练习时长(毫秒)\r\n"}, {"name": "bookDuration", "doc": "\n 书籍阅读时长(毫秒)\r\n"}, {"name": "videoDuration", "doc": "\n 视频学习时长(毫秒)\r\n"}, {"name": "exerciseDuration", "doc": "\n 习题练习时长(毫秒)\r\n"}, {"name": "documentDuration", "doc": "\n 文档阅读时长(毫秒)\r\n"}, {"name": "otherDuration", "doc": "\n 其他活动时长(毫秒)\r\n"}, {"name": "lastActivityTime", "doc": "\n 最后活动时间\r\n"}], "enumConstants": [], "methods": [{"name": "initialize", "paramTypes": ["java.lang.Long"], "doc": "\n 初始化用户活动总览\r\n\r\n @param userId 用户ID\r\n @return 初始化的总览对象\r\n"}, {"name": "updateDuration", "paramTypes": ["org.dromara.app.domain.enums.ActivityType", "java.lang.Long"], "doc": "\n 更新活动时长\r\n\r\n @param activityType 活动类型\r\n @param duration     时长(毫秒)\r\n"}, {"name": "getDurationByType", "paramTypes": ["org.dromara.app.domain.enums.ActivityType"], "doc": "\n 获取指定类型的活动时长\r\n\r\n @param activityType 活动类型\r\n @return 时长(毫秒)\r\n"}], "constructors": []}