{"doc": " 题目类型枚举\n\n <AUTHOR>\n", "fields": [], "enumConstants": [{"name": "SINGLE_CHOICE", "doc": " 单选题\n"}, {"name": "MULTIPLE_CHOICE", "doc": " 多选题\n"}, {"name": "TRUE_FALSE", "doc": " 判断题\n"}, {"name": "SHORT_ANSWER", "doc": " 简答题\n"}, {"name": "PROGRAMMING", "doc": " 编程题\n"}], "methods": [{"name": "getDescriptionByCode", "paramTypes": ["java.lang.Integer"], "doc": " 根据代码获取描述\n\n @param code 类型代码\n @return 类型描述\n"}, {"name": "getCodeByDescription", "paramTypes": ["java.lang.String"], "doc": " 根据描述获取代码\n\n @param description 类型描述\n @return 类型代码\n"}, {"name": "getByCode", "paramTypes": ["java.lang.Integer"], "doc": " 根据代码获取枚举实例\n\n @param code 类型代码\n @return 枚举实例\n"}, {"name": "getByDescription", "paramTypes": ["java.lang.String"], "doc": " 根据描述获取枚举实例\n\n @param description 类型描述\n @return 枚举实例\n"}], "constructors": []}