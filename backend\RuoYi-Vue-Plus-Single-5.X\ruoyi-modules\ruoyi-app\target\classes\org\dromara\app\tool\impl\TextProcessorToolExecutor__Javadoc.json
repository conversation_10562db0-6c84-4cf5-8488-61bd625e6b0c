{"doc": "\n 文本处理工具执行器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "processText", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map"], "doc": "\n 处理文本\r\n"}, {"name": "countText", "paramTypes": ["java.lang.String"], "doc": "\n 统计文本\r\n"}, {"name": "formatText", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": "\n 格式化文本\r\n"}, {"name": "extractText", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": "\n 提取文本\r\n"}, {"name": "replaceText", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": "\n 替换文本\r\n"}, {"name": "analyzeText", "paramTypes": ["java.lang.String"], "doc": "\n 分析文本\r\n"}, {"name": "capitalizeWords", "paramTypes": ["java.lang.String"], "doc": "\n 首字母大写\r\n"}, {"name": "extractEmails", "paramTypes": ["java.lang.String"], "doc": "\n 提取邮箱\r\n"}, {"name": "extractUrls", "paramTypes": ["java.lang.String"], "doc": "\n 提取URL\r\n"}, {"name": "extractPhones", "paramTypes": ["java.lang.String"], "doc": "\n 提取电话号码\r\n"}, {"name": "extractNumbers", "paramTypes": ["java.lang.String"], "doc": "\n 提取数字\r\n"}, {"name": "extractKeywords", "paramTypes": ["java.lang.String"], "doc": "\n 提取关键词（简单实现）\r\n"}, {"name": "detectLanguage", "paramTypes": ["java.lang.String"], "doc": "\n 检测语言（简单实现）\r\n"}, {"name": "analyzeSentiment", "paramTypes": ["java.lang.String"], "doc": "\n 情感分析（简单实现）\r\n"}], "constructors": []}