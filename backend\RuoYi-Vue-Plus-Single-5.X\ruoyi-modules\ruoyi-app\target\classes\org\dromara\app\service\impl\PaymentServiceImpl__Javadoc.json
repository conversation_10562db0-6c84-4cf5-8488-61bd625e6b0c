{"doc": "\n 支付服务实现类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "createPaymentOrder", "paramTypes": ["org.dromara.app.domain.dto.PaymentOrderDto"], "doc": "\n 创建支付订单\r\n"}, {"name": "alipayPay", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 支付宝支付\r\n"}, {"name": "alipayNotify", "paramTypes": ["jakarta.servlet.http.HttpServletRequest"], "doc": "\n 支付宝异步通知处理\r\n"}, {"name": "alipayReturn", "paramTypes": ["jakarta.servlet.http.HttpServletRequest"], "doc": "\n 支付宝同步回调处理\r\n"}, {"name": "queryOrderStatus", "paramTypes": ["java.lang.String"], "doc": "\n 查询订单状态\r\n"}, {"name": "cancelOrder", "paramTypes": ["java.lang.String"], "doc": "\n 取消订单\r\n"}, {"name": "getPaymentOrderByOrderNo", "paramTypes": ["java.lang.String"], "doc": "\n 根据订单号查询支付订单\r\n"}, {"name": "validatePayToken", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 验证支付token\r\n\r\n @param orderNo  订单号\r\n @param payToken 支付token\r\n @return 验证结果\r\n"}, {"name": "markPayTokenAsUsed", "paramTypes": ["java.lang.String"], "doc": "\n 标记支付token为已使用\r\n\r\n @param orderNo 订单号\r\n"}, {"name": "handlePaymentTimeout", "paramTypes": ["java.lang.String"], "doc": "\n 处理支付超时\r\n\r\n @param orderNo 订单号\r\n"}, {"name": "removeFromTimeoutQueue", "paramTypes": ["java.lang.String"], "doc": "\n 从Redis延迟队列移除超时任务\r\n\r\n @param orderNo 订单号\r\n"}], "constructors": []}