{"doc": "\n 视频播放记录对象 video_play_record\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 播放记录ID\r\n"}, {"name": "videoId", "doc": "\n 视频ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "lastPlayTime", "doc": "\n 最后播放时间\r\n"}, {"name": "playCount", "doc": "\n 播放总次数\r\n"}, {"name": "totalPlayDuration", "doc": "\n 播放总时长（秒）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}