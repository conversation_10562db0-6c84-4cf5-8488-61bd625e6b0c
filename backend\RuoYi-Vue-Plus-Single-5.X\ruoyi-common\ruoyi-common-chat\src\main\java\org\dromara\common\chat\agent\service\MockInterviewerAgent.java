package org.dromara.common.chat.agent.service;

import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;

/**
 * 模拟面试Agent
 *
 * <AUTHOR>
 */
public interface MockInterviewerAgent {

    @SystemMessage({
        "你是一位专业的模拟面试官，致力于帮助候选人提升面试表现。",
        "你的任务是：",
        "1. 模拟真实的面试场景和氛围",
        "2. 提出挑战性和针对性的问题",
        "3. 提供实时反馈和改进建议",
        "4. 帮助候选人克服面试紧张情绪",
        "请营造专业而友好的面试氛围，给出建设性的指导。"
    })
    String conductMockInterview(String candidateResponse);

    @SystemMessage({
        "作为模拟面试官，请为指定职位设计完整的模拟面试流程。",
        "包括：开场介绍、技术问题、行为问题、场景题等。",
        "请确保面试流程专业、全面。"
    })
    String designInterviewFlow(@UserMessage("职位：{position}\n面试时长：{duration}\n重点考察：{focus}") String position, String duration, String focus);

    @SystemMessage({
        "作为模拟面试官，请进行压力面试模拟。",
        "通过提出挑战性问题和营造压力环境来测试候选人的抗压能力。",
        "请保持专业性，不要过度施压。"
    })
    String conductStressInterview(@UserMessage("场景：{scenario}\n候选人回答：{response}") String scenario, String response);

    @SystemMessage({
        "作为模拟面试官，请提供面试表现的详细反馈。",
        "评估维度：表达能力、技术深度、逻辑思维、沟通技巧、临场反应等。",
        "请给出具体的改进建议和练习方向。"
    })
    String provideFeedback(@UserMessage("面试记录：{interviewRecord}") String interviewRecord);

    @SystemMessage({
        "作为模拟面试官，请帮助候选人准备特定类型的面试问题。",
        "包括：答题思路、关键要点、常见误区、最佳实践等。",
        "请提供实用的答题策略。"
    })
    String prepareQuestionType(@UserMessage("问题类型：{questionType}\n具体问题：{question}") String questionType, String question);
}
