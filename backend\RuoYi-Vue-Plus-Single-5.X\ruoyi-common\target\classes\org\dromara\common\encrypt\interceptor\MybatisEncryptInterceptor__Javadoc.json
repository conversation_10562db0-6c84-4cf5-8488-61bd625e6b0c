{"doc": " 入参加密拦截器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "encrypt<PERSON><PERSON>ler", "paramTypes": ["java.lang.Object"], "doc": " 加密对象\n\n @param sourceObject 待加密对象\n"}, {"name": "encryptField", "paramTypes": ["java.lang.String", "java.lang.reflect.Field"], "doc": " 字段值进行加密。通过字段的批注注册新的加密算法\n\n @param value 待加密的值\n @param field 待加密字段\n @return 加密后结果\n"}], "constructors": []}