{"doc": "\n Ollama属性配置\r\n", "fields": [{"name": "baseUrl", "doc": "\n Ollama服务器地址\r\n"}, {"name": "defaultModel", "doc": "\n 默认模型名称\r\n"}, {"name": "connectTimeout", "doc": "\n 连接超时时间（秒）\r\n"}, {"name": "readTimeout", "doc": "\n 读取超时时间（秒）\r\n"}, {"name": "writeTimeout", "doc": "\n 写入超时时间（秒）\r\n"}, {"name": "defaultTemperature", "doc": "\n 默认温度参数\r\n"}, {"name": "defaultMaxTokens", "doc": "\n 默认最大Token数\r\n"}, {"name": "enableStreaming", "doc": "\n 是否启用流式响应\r\n"}, {"name": "retryCount", "doc": "\n 重试次数\r\n"}, {"name": "retryInterval", "doc": "\n 重试间隔（毫秒）\r\n"}, {"name": "enableHealthCheck", "doc": "\n 是否启用健康检查\r\n"}, {"name": "healthCheckInterval", "doc": "\n 健康检查间隔（秒）\r\n"}, {"name": "embeddingModel", "doc": "\n 嵌入模型名称\r\n"}, {"name": "vectorDimension", "doc": "\n 向量维度\r\n"}, {"name": "similarityT<PERSON><PERSON>old", "doc": "\n 相似度阈值\r\n"}, {"name": "systemPrompts", "doc": "\n 系统提示词模板\r\n"}], "enumConstants": [], "methods": [], "constructors": []}