{"doc": " 增强网络相关工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "isIPv6", "paramTypes": ["java.lang.String"], "doc": " 判断是否为IPv6地址\n\n @param ip IP地址\n @return 是否为IPv6地址\n"}, {"name": "isInnerIPv6", "paramTypes": ["java.lang.String"], "doc": " 判断IPv6地址是否为内网地址\n <br><br>\n 以下地址将归类为本地地址，如有业务场景有需要，请根据需求自行处理：\n <pre>\n 通配符地址 0:0:0:0:0:0:0:0\n 链路本地地址 fe80::/10\n 唯一本地地址 fec0::/10\n 环回地址 ::1\n </pre>\n\n @param ip IP地址\n @return 是否为内网地址\n"}, {"name": "isIPv4", "paramTypes": ["java.lang.String"], "doc": " 判断是否为IPv4地址\n\n @param ip IP地址\n @return 是否为IPv4地址\n"}], "constructors": []}