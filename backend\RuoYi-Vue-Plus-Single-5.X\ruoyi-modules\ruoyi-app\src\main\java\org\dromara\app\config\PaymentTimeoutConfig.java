package org.dromara.app.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.service.IPaymentService;
import org.dromara.common.redis.utils.QueueUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * 支付超时队列配置
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PaymentTimeoutConfig implements CommandLineRunner {

    /**
     * 支付超时队列名称
     */
    public static final String PAYMENT_TIMEOUT_QUEUE = "payment:timeout:queue";
    private final IPaymentService paymentService;

    @Override
    public void run(String... args) throws Exception {
        // 初始化支付超时队列监听器
        initPaymentTimeoutListener();
    }

    /**
     * 初始化支付超时队列监听器
     */
    private void initPaymentTimeoutListener() {
        try {
            log.info("初始化支付超时队列监听器，队列名：{}", PAYMENT_TIMEOUT_QUEUE);

            QueueUtils.subscribeBlockingQueue(PAYMENT_TIMEOUT_QUEUE, (String orderNo) -> {
                log.info("收到支付超时消息，订单号：{}", orderNo);
                return CompletableFuture.runAsync(() -> {
                    try {
                        // 处理支付超时逻辑
                        paymentService.handlePaymentTimeout(orderNo);
                    } catch (Exception e) {
                        log.error("处理支付超时失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
                    }
                });
            }, true);

            log.info("支付超时队列监听器初始化成功");
        } catch (Exception e) {
            log.error("初始化支付超时队列监听器失败：{}", e.getMessage(), e);
        }
    }
}
