package org.dromara.common.caffeine.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 缓存类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CacheTypeEnum {

    /**
     * 用户缓存
     */
    USER("user", "用户缓存"),

    /**
     * 系统缓存
     */
    SYSTEM("system", "系统缓存"),

    /**
     * 配置缓存
     */
    CONFIG("config", "配置缓存"),

    /**
     * 字典缓存
     */
    DICT("dict", "字典缓存"),

    /**
     * 权限缓存
     */
    PERMISSION("permission", "权限缓存"),

    /**
     * 临时缓存
     */
    TEMP("temp", "临时缓存");

    /**
     * 缓存类型代码
     */
    private final String code;

    /**
     * 缓存类型描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static CacheTypeEnum getByCode(String code) {
        for (CacheTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

}
