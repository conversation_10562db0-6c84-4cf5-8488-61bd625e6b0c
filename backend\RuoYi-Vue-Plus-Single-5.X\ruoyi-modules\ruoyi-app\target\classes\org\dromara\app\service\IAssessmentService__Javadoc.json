{"doc": "\n 能力评估服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getAssessmentQuestions", "paramTypes": [], "doc": "\n 获取评估问题列表\r\n\r\n @return 评估问题列表\r\n"}, {"name": "submitAssessmentResults", "paramTypes": ["java.util.List"], "doc": "\n 提交评估结果\r\n\r\n @param results 评估结果\r\n @return 能力评估结果\r\n"}, {"name": "getDetailedAbilityReport", "paramTypes": [], "doc": "\n 获取详细能力报告\r\n\r\n @return 详细能力报告\r\n"}, {"name": "getUserGrowthProfile", "paramTypes": [], "doc": "\n 获取用户成长档案\r\n\r\n @return 用户成长档案\r\n"}], "constructors": []}