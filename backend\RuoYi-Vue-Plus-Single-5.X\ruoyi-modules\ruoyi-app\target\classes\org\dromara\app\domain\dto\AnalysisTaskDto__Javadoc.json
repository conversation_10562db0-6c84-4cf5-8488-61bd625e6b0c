{"doc": "\n 分析任务数据传输对象\r\n\r\n <AUTHOR>\r\n @date 2025-01-19\r\n", "fields": [{"name": "taskId", "doc": "\n 任务ID\r\n"}, {"name": "sessionId", "doc": "\n 会话ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "taskType", "doc": "\n 任务类型：audio/video/text/comprehensive\r\n"}, {"name": "priority", "doc": "\n 任务优先级：1-10，数字越大优先级越高\r\n"}, {"name": "status", "doc": "\n 任务状态：pending/running/completed/failed/cancelled\r\n"}, {"name": "audioFilePath", "doc": "\n 音频文件路径\r\n"}, {"name": "videoFilePath", "doc": "\n 视频文件路径\r\n"}, {"name": "textContent", "doc": "\n 文本内容\r\n"}, {"name": "jobPosition", "doc": "\n 岗位信息\r\n"}, {"name": "createTime", "doc": "\n 任务创建时间\r\n"}, {"name": "startTime", "doc": "\n 任务开始时间\r\n"}, {"name": "completeTime", "doc": "\n 任务完成时间\r\n"}, {"name": "errorMessage", "doc": "\n 错误信息\r\n"}, {"name": "retryCount", "doc": "\n 重试次数\r\n"}, {"name": "maxRetries", "doc": "\n 最大重试次数\r\n"}, {"name": "timeoutSeconds", "doc": "\n 任务超时时间（秒）\r\n"}, {"name": "resultData", "doc": "\n 任务结果数据\r\n"}], "enumConstants": [], "methods": [], "constructors": []}