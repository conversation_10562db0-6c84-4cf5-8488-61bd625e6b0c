{"doc": " 工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendMessage", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 向指定的WebSocket会话发送消息\n\n @param sessionKey 要发送消息的用户id\n @param message    要发送的消息内容\n"}, {"name": "subscribeMessage", "paramTypes": ["java.util.function.Consumer"], "doc": " 订阅WebSocket消息主题，并提供一个消费者函数来处理接收到的消息\n\n @param consumer 处理WebSocket消息的消费者函数\n"}, {"name": "publishMessage", "paramTypes": ["org.dromara.common.websocket.dto.WebSocketMessageDto"], "doc": " 发布WebSocket订阅消息\n\n @param webSocketMessage 要发布的WebSocket消息对象\n"}, {"name": "publishAll", "paramTypes": ["java.lang.String"], "doc": " 向所有的WebSocket会话发布订阅的消息(群发)\n\n @param message 要发布的消息内容\n"}, {"name": "sendPongMessage", "paramTypes": ["org.springframework.web.socket.WebSocketSession"], "doc": " 向指定的WebSocket会话发送Pong消息\n\n @param session 要发送Pong消息的WebSocket会话\n"}, {"name": "sendMessage", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "java.lang.String"], "doc": " 向指定的WebSocket会话发送文本消息\n\n @param session WebSocket会话\n @param message 要发送的文本消息内容\n"}, {"name": "sendMessage", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "org.springframework.web.socket.WebSocketMessage"], "doc": " 向指定的WebSocket会话发送WebSocket消息对象\n\n @param session WebSocket会话\n @param message 要发送的WebSocket消息对象\n"}], "constructors": []}