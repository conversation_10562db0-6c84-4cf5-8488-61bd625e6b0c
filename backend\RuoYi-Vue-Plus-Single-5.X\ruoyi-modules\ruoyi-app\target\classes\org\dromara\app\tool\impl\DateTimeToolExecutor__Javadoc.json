{"doc": "\n 时间助手工具执行器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "processDateTime", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": "\n 处理日期时间操作\r\n"}, {"name": "getCurrentDateTime", "paramTypes": ["java.util.Map"], "doc": "\n 获取当前时间\r\n"}, {"name": "formatDateTime", "paramTypes": ["java.util.Map"], "doc": "\n 格式化日期时间\r\n"}, {"name": "parseDateTime", "paramTypes": ["java.util.Map"], "doc": "\n 解析日期时间\r\n"}, {"name": "calculateDateTime", "paramTypes": ["java.util.Map"], "doc": "\n 计算日期时间\r\n"}, {"name": "convertTimezone", "paramTypes": ["java.util.Map"], "doc": "\n 时区转换\r\n"}, {"name": "parseToLocalDateTime", "paramTypes": ["java.lang.String"], "doc": "\n 解析字符串为LocalDateTime\r\n"}], "constructors": []}