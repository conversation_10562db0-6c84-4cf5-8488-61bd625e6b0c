{"doc": " 成就系统状态控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getSystemHealth", "paramTypes": [], "doc": " 获取系统健康状态\n"}, {"name": "checkSystemIntegrity", "paramTypes": [], "doc": " 检查系统完整性\n"}, {"name": "checkDatabase", "paramTypes": [], "doc": " 检查数据库表\n"}, {"name": "checkRabbitMQ", "paramTypes": [], "doc": " 检查RabbitMQ配置\n"}, {"name": "logSystemStatus", "paramTypes": [], "doc": " 打印系统状态到日志\n"}, {"name": "getSystemInfo", "paramTypes": [], "doc": " 获取系统信息概览\n"}], "constructors": []}