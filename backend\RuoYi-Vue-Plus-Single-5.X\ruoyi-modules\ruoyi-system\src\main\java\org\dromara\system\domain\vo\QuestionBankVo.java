package org.dromara.system.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.common.mybatis.core.domain.QuestionBank;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 题库视图对象
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = QuestionBank.class)
public class QuestionBankVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 题库ID
     */
    @ExcelProperty(value = "题库ID")
    private Long bankId;

    /**
     * 题库编码
     */
    @ExcelProperty(value = "题库编码")
    private String bankCode;

    /**
     * 题库标题
     */
    @ExcelProperty(value = "题库标题")
    private String title;

    /**
     * 题库描述
     */
    @ExcelProperty(value = "题库描述")
    private String description;

    /**
     * 专业ID
     */
    @ExcelProperty(value = "专业ID")
    private Long majorId;

    /**
     * 专业名称（关联查询）
     */
    @ExcelProperty(value = "专业名称")
    private String majorName;

    /**
     * 题库图标
     */
    @ExcelProperty(value = "题库图标")
    private String icon;

    /**
     * 题库颜色
     */
    @ExcelProperty(value = "题库颜色")
    private String color;

    /**
     * 难度（1-简单 2-中等 3-困难）
     */
    @ExcelProperty(value = "难度", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "question_difficulty")
    private Integer difficulty;

    /**
     * 题目总数
     */
    @ExcelProperty(value = "题目总数")
    private Integer totalQuestions;

    /**
     * 练习次数
     */
    @ExcelProperty(value = "练习次数")
    private Integer practiceCount;

    /**
     * 分类标签（JSON格式）
     */
    @ExcelProperty(value = "分类标签")
    private String categories;

    /**
     * 分类标签列表（非数据库字段）
     */
    private List<String> categoryList;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Integer sort;

    /**
     * 状态（0正常 1停用）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_normal_disable")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 是否收藏（非数据库字段）
     */
    private Boolean isBookmarked;

    /**
     * 学习进度（非数据库字段）
     */
    private Integer progress;

    /**
     * 完成题目数（非数据库字段）
     */
    private Integer completedQuestions;

    /**
     * 正确率（非数据库字段）
     */
    private Double correctRate;
}
