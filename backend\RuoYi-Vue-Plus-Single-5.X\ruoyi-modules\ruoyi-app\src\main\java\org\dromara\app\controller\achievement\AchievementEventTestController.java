package org.dromara.app.controller.achievement;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.event.AchievementEventPublisher;
import org.dromara.common.core.domain.R;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.web.bind.annotation.*;

/**
 * 成就事件测试控制器
 * 用于测试各种成就事件的发布和处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/test/achievement-event")
@Tag(name = "成就事件测试", description = "成就事件发布测试接口")
public class AchievementEventTestController {

    private final AchievementEventPublisher eventPublisher;

    /**
     * 测试用户登录事件
     */
    @Operation(summary = "测试用户登录事件")
    @PostMapping("/login")
    public R<String> testLoginEvent(@Parameter(description = "登录来源") @RequestParam(defaultValue = "web") String source) {
        String userId = LoginHelper.getUserId().toString();

        // 发布简单登录事件
        eventPublisher.publishSimpleUserLoginEvent(userId, source);

        return R.ok("登录事件发布成功");
    }

    /**
     * 测试视频观看事件
     */
    @Operation(summary = "测试视频观看事件")
    @PostMapping("/video-watch")
    public R<String> testVideoWatchEvent(
            @Parameter(description = "视频ID") @RequestParam String videoId,
            @Parameter(description = "视频标题") @RequestParam(defaultValue = "测试视频") String videoTitle,
            @Parameter(description = "观看时长（秒）") @RequestParam(defaultValue = "120") Integer watchDuration) {

        String userId = LoginHelper.getUserId().toString();

        // 发布简单视频观看事件
        eventPublisher.publishSimpleVideoWatchEvent(userId, videoId, videoTitle, watchDuration);

        return R.ok("视频观看事件发布成功");
    }

    /**
     * 测试评论事件
     */
    @Operation(summary = "测试评论事件")
    @PostMapping("/comment")
    public R<String> testCommentEvent(
            @Parameter(description = "目标类型") @RequestParam(defaultValue = "video") String targetType,
            @Parameter(description = "目标ID") @RequestParam String targetId,
            @Parameter(description = "评论内容") @RequestParam(defaultValue = "这是一个测试评论") String commentContent) {

        String userId = LoginHelper.getUserId().toString();

        // 发布简单评论事件
        eventPublisher.publishSimpleCommentEvent(userId, targetType, targetId, commentContent);

        return R.ok("评论事件发布成功");
    }

    /**
     * 测试点赞事件
     */
    @Operation(summary = "测试点赞事件")
    @PostMapping("/like")
    public R<String> testLikeEvent(
            @Parameter(description = "目标类型") @RequestParam(defaultValue = "video") String targetType,
            @Parameter(description = "目标ID") @RequestParam String targetId) {

        String userId = LoginHelper.getUserId().toString();

        // 发布简单点赞事件
        eventPublisher.publishSimpleLikeEvent(userId, targetType, targetId);

        return R.ok("点赞事件发布成功");
    }

    /**
     * 测试学习时长事件
     */
    @Operation(summary = "测试学习时长事件")
    @PostMapping("/study-time")
    public R<String> testStudyTimeEvent(
            @Parameter(description = "学习时长（分钟）") @RequestParam(defaultValue = "30") Integer studyMinutes) {

        String userId = LoginHelper.getUserId().toString();

        // 发布简单学习时长事件
        eventPublisher.publishSimpleStudyTimeEvent(userId, studyMinutes);

        return R.ok("学习时长事件发布成功");
    }

    /**
     * 测试学习完成事件（复杂事件）
     */
    @Operation(summary = "测试学习完成事件")
    @PostMapping("/learning-completed")
    public R<String> testLearningCompletedEvent(
            @Parameter(description = "资源ID") @RequestParam String resourceId,
            @Parameter(description = "资源类型") @RequestParam(defaultValue = "course") String resourceType,
            @Parameter(description = "学习时长（分钟）") @RequestParam(defaultValue = "60") Integer duration,
            @Parameter(description = "得分") @RequestParam(defaultValue = "85") Integer score) {

        String userId = LoginHelper.getUserId().toString();

        // 发布学习完成事件
        eventPublisher.publishLearningCompletedEvent(userId, resourceId, resourceType, duration, score);

        return R.ok("学习完成事件发布成功");
    }

    /**
     * 测试面试完成事件（复杂事件）
     */
    @Operation(summary = "测试面试完成事件")
    @PostMapping("/interview-completed")
    public R<String> testInterviewCompletedEvent(
            @Parameter(description = "面试ID") @RequestParam Long interviewId,
            @Parameter(description = "职位ID") @RequestParam Long jobId,
            @Parameter(description = "面试得分") @RequestParam(defaultValue = "80") Integer score,
            @Parameter(description = "面试时长（分钟）") @RequestParam(defaultValue = "45") Integer duration,
            @Parameter(description = "面试模式") @RequestParam(defaultValue = "online") String mode) {

        String userId = LoginHelper.getUserId().toString();

        // 发布面试完成事件
        eventPublisher.publishInterviewCompletedEvent(userId, interviewId, jobId, score, duration, mode);

        return R.ok("面试完成事件发布成功");
    }

    /**
     * 测试能力提升事件（复杂事件）
     */
    @Operation(summary = "测试能力提升事件")
    @PostMapping("/ability-improve")
    public R<String> testAbilityImproveEvent(
            @Parameter(description = "能力类型") @RequestParam(defaultValue = "programming") String abilityType,
            @Parameter(description = "原分数") @RequestParam(defaultValue = "70") Integer oldScore,
            @Parameter(description = "新分数") @RequestParam(defaultValue = "85") Integer newScore) {

        String userId = LoginHelper.getUserId().toString();

        // 发布能力提升事件
        eventPublisher.publishAbilityImproveEvent(userId, abilityType, oldScore, newScore);

        return R.ok("能力提升事件发布成功");
    }

    /**
     * 批量测试多个事件
     */
    @Operation(summary = "批量测试多个事件")
    @PostMapping("/batch-test")
    public R<String> testBatchEvents() {
        String userId = LoginHelper.getUserId().toString();

        try {
            // 模拟一个完整的学习流程
            log.info("开始批量测试成就事件: userId={}", userId);

            // 1. 用户登录
            eventPublisher.publishSimpleUserLoginEvent(userId, "web");
            Thread.sleep(100);

            // 2. 观看视频
            eventPublisher.publishSimpleVideoWatchEvent(userId, "video-001", "Java基础教程", 300);
            Thread.sleep(100);

            // 3. 发表评论
            eventPublisher.publishSimpleCommentEvent(userId, "video", "video-001", "讲得很好，学到了很多！");
            Thread.sleep(100);

            // 4. 点赞视频
            eventPublisher.publishSimpleLikeEvent(userId, "video", "video-001");
            Thread.sleep(100);

            // 5. 记录学习时长
            eventPublisher.publishSimpleStudyTimeEvent(userId, 30);
            Thread.sleep(100);

            // 6. 完成学习
            eventPublisher.publishLearningCompletedEvent(userId, "course-001", "programming", 60, 90);

            log.info("批量测试成就事件完成: userId={}", userId);
            return R.ok("批量事件测试完成，共发布6个事件");

        } catch (Exception e) {
            log.error("批量测试事件失败: userId={}", userId, e);
            return R.fail("批量事件测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试用户注册事件
     */
    @Operation(summary = "测试用户注册事件")
    @PostMapping("/user-registration")
    public R<String> testUserRegistrationEvent(
            @Parameter(description = "注册来源") @RequestParam(defaultValue = "web") String source) {

        String userId = LoginHelper.getUserId().toString();

        // 发布用户注册事件
        eventPublisher.publishUserRegistrationEvent(userId, source);

        return R.ok("用户注册事件发布成功");
    }

    /**
     * 获取测试说明
     */
    @Operation(summary = "获取测试说明")
    @GetMapping("/help")
    public R<Object> getTestHelp() {
        return R.ok(java.util.Map.of(
            "description", "成就事件测试接口说明",
            "simpleEvents", java.util.List.of(
                "POST /login - 测试登录事件",
                "POST /video-watch - 测试视频观看事件",
                "POST /comment - 测试评论事件",
                "POST /like - 测试点赞事件",
                "POST /study-time - 测试学习时长事件"
            ),
            "complexEvents", java.util.List.of(
                "POST /learning-completed - 测试学习完成事件",
                "POST /interview-completed - 测试面试完成事件",
                "POST /ability-improve - 测试能力提升事件"
            ),
            "batchTest", java.util.List.of(
                "POST /batch-test - 批量测试多个事件",
                "POST /user-registration - 测试用户注册事件"
            ),
            "note", "所有事件都会触发相应的成就检查，请确保已配置相关成就规则"
        ));
    }

}
