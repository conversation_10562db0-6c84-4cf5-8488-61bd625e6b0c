{"doc": " LangChain4j配置类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "openAiChatModel", "paramTypes": [], "doc": " OpenAI聊天模型\n"}, {"name": "dashscopeChatModel", "paramTypes": [], "doc": " 阿里云通义千问聊天模型\n"}, {"name": "openAiStreamingChatModel", "paramTypes": [], "doc": " OpenAI流式聊天模型\n"}, {"name": "dashscopeStreamingChatModel", "paramTypes": [], "doc": " 阿里云通义千问流式聊天模型\n"}, {"name": "defaultChatModel", "paramTypes": [], "doc": " 默认聊天模型\n"}, {"name": "defaultStreamingChatModel", "paramTypes": [], "doc": " 默认流式聊天模型\n"}], "constructors": []}