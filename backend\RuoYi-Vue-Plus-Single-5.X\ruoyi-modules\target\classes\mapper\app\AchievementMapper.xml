<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.AchievementMapper">

    <resultMap type="org.dromara.app.domain.Achievement" id="AchievementResult">
        <result property="id" column="id"/>
        <result property="achievementCode" column="achievement_code"/>
        <result property="achievementName" column="achievement_name"/>
        <result property="achievementDesc" column="achievement_desc"/>
        <result property="achievementIcon" column="achievement_icon"/>
        <result property="achievementType" column="achievement_type"/>
        <result property="triggerCondition" column="trigger_condition"/>
        <result property="rewardPoints" column="reward_points"/>
        <result property="isActive" column="is_active"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <resultMap type="org.dromara.app.domain.vo.AchievementVo" id="AchievementVoResult">
        <result property="id" column="id"/>
        <result property="achievementCode" column="achievement_code"/>
        <result property="achievementName" column="achievement_name"/>
        <result property="achievementDesc" column="achievement_desc"/>
        <result property="achievementIcon" column="achievement_icon"/>
        <result property="achievementType" column="achievement_type"/>
        <result property="triggerCondition" column="trigger_condition"/>
        <result property="rewardPoints" column="reward_points"/>
        <result property="isActive" column="is_active"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="createByName" column="create_by_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectAchievementVo">
        select a.id, a.achievement_code, a.achievement_name, a.achievement_desc, 
               a.achievement_icon, a.achievement_type, a.trigger_condition, 
               a.reward_points, a.is_active, a.sort_order, 
               a.create_by, a.create_time, a.update_by, a.update_time, a.remark
        from app_achievement a
    </sql>

    <!-- 根据成就代码查询成就 -->
    <select id="selectByAchievementCode" parameterType="java.lang.String" resultMap="AchievementResult">
        <include refid="selectAchievementVo"/>
        where a.achievement_code = #{achievementCode}
    </select>

    <!-- 根据成就类型查询成就列表 -->
    <select id="selectByAchievementType" parameterType="java.lang.String" resultMap="AchievementVoResult">
        <include refid="selectAchievementVo"/>
        where a.achievement_type = #{achievementType} and a.is_active = '1'
        order by a.sort_order asc, a.create_time desc
    </select>

    <!-- 查询激活的成就列表 -->
    <select id="selectActiveAchievements" resultMap="AchievementVoResult">
        <include refid="selectAchievementVo"/>
        where a.is_active = '1'
        order by a.sort_order asc, a.create_time desc
    </select>

    <!-- 根据排序查询成就列表 -->
    <select id="selectOrderBySortOrder" resultMap="AchievementVoResult">
        <include refid="selectAchievementVo"/>
        order by a.sort_order asc, a.create_time desc
    </select>

    <!-- 批量更新成就状态 -->
    <update id="batchUpdateStatus" parameterType="map">
        update app_achievement
        set is_active = #{isActive}, update_time = now()
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 获取总成就数 -->
    <select id="countTotalAchievements" resultType="java.lang.Integer">
        select count(1) from app_achievement where is_active = '1'
    </select>

    <!-- 根据类型获取成就数 -->
    <select id="countAchievementsByType" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1) from app_achievement 
        where achievement_type = #{achievementType} and is_active = '1'
    </select>

    <!-- 获取成就总积分 -->
    <select id="sumTotalRewardPoints" resultType="java.lang.Integer">
        select COALESCE(sum(reward_points), 0) from app_achievement where is_active = '1'
    </select>

</mapper>
