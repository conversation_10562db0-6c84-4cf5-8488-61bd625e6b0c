package org.dromara.app.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 活动类型枚举
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Getter
@AllArgsConstructor
public enum ActivityType {

    /**
     * 课程学习
     */
    COURSE("course", "课程学习"),

    /**
     * 面试练习
     */
    INTERVIEW("interview", "面试练习"),

    /**
     * 书籍阅读
     */
    BOOK("book", "书籍阅读"),

    /**
     * 视频学习
     */
    VIDEO("video", "视频学习"),

    /**
     * 习题练习
     */
    EXERCISE("exercise", "习题练习"),

    /**
     * 文档阅读
     */
    DOCUMENT("document", "文档阅读"),

    /**
     * 其他活动
     */
    OTHER("other", "其他活动");

    /**
     * 活动类型代码
     */
    @EnumValue
    @JsonValue
    private final String code;

    /**
     * 活动类型描述
     */
    private final String description;

    /**
     * 根据代码获取活动类型
     *
     * @param code 活动类型代码
     * @return 活动类型枚举
     */
    public static ActivityType fromCode(String code) {
        for (ActivityType type : ActivityType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的活动类型代码: " + code);
    }

    /**
     * 检查代码是否有效
     *
     * @param code 活动类型代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        for (ActivityType type : ActivityType.values()) {
            if (type.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
