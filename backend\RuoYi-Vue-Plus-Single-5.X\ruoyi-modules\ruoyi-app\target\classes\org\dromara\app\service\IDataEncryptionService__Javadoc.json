{"doc": "\n 数据加密服务接口\r\n 用于敏感数据的加密存储和解密访问\r\n\r\n <AUTHOR>\r\n @date 2025-01-19\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "encrypt", "paramTypes": ["java.lang.String"], "doc": "\n 加密字符串\r\n\r\n @param plainText 明文\r\n @return 密文\r\n"}, {"name": "decrypt", "paramTypes": ["java.lang.String"], "doc": "\n 解密字符串\r\n\r\n @param cipherText 密文\r\n @return 明文\r\n"}, {"name": "encrypt", "paramTypes": ["byte[]"], "doc": "\n 加密字节数组\r\n\r\n @param plainBytes 明文字节数组\r\n @return 密文字节数组\r\n"}, {"name": "decrypt", "paramTypes": ["byte[]"], "doc": "\n 解密字节数组\r\n\r\n @param cipherBytes 密文字节数组\r\n @return 明文字节数组\r\n"}, {"name": "batchEncrypt", "paramTypes": ["java.util.Map"], "doc": "\n 批量加密\r\n\r\n @param plainTexts 明文列表\r\n @return 密文列表\r\n"}, {"name": "batchDecrypt", "paramTypes": ["java.util.Map"], "doc": "\n 批量解密\r\n\r\n @param cipherTexts 密文列表\r\n @return 明文列表\r\n"}, {"name": "generateHash", "paramTypes": ["java.lang.String"], "doc": "\n 生成数据摘要（哈希）\r\n\r\n @param data 原始数据\r\n @return 数据摘要\r\n"}, {"name": "verifyHash", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 验证数据摘要\r\n\r\n @param data 原始数据\r\n @param hash 数据摘要\r\n @return 是否匹配\r\n"}, {"name": "generateRandomKey", "paramTypes": ["int"], "doc": "\n 生成随机密钥\r\n\r\n @param keyLength 密钥长度\r\n @return 密钥\r\n"}, {"name": "encryptFile", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 加密文件\r\n\r\n @param inputFilePath 输入文件路径\r\n @param outputFilePath 输出文件路径\r\n @return 是否成功\r\n"}, {"name": "decryptFile", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 解密文件\r\n\r\n @param inputFilePath 输入文件路径\r\n @param outputFilePath 输出文件路径\r\n @return 是否成功\r\n"}, {"name": "getEncryptionStatus", "paramTypes": [], "doc": "\n 获取加密状态\r\n\r\n @return 加密状态信息\r\n"}, {"name": "rotateEncryptionKey", "paramTypes": [], "doc": "\n 轮换加密密钥\r\n\r\n @return 是否成功\r\n"}, {"name": "verifyEncryptionIntegrity", "paramTypes": ["java.lang.String"], "doc": "\n 验证加密完整性\r\n\r\n @param data 数据\r\n @return 是否完整\r\n"}, {"name": "setEncryptionAlgorithm", "paramTypes": ["java.lang.String"], "doc": "\n 设置加密算法\r\n\r\n @param algorithm 算法名称\r\n"}, {"name": "getSupportedAlgorithms", "paramTypes": [], "doc": "\n 获取支持的加密算法列表\r\n\r\n @return 算法列表\r\n"}], "constructors": []}