{"doc": "\n 多模态分析控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "comprehensiveAnalysis", "paramTypes": ["java.lang.String", "org.springframework.web.multipart.MultipartFile", "org.springframework.web.multipart.MultipartFile", "java.lang.String", "java.lang.String"], "doc": "\n 综合多模态分析\r\n\r\n @param sessionId 面试会话ID\r\n @param audioFile 音频文件\r\n @param videoFile 视频文件\r\n @param textContent 文本内容\r\n @param jobPosition 岗位信息\r\n @return 分析结果\r\n"}, {"name": "analyzeAudio", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String"], "doc": "\n 音频分析\r\n\r\n @param audioFile 音频文件\r\n @param sessionId 会话ID\r\n @return 音频分析结果\r\n"}, {"name": "analyzeVideo", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String"], "doc": "\n 视频分析\r\n\r\n @param videoFile 视频文件\r\n @param sessionId 会话ID\r\n @return 视频分析结果\r\n"}, {"name": "analyzeText", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 文本分析\r\n\r\n @param textContent 文本内容\r\n @param jobPosition 岗位信息\r\n @param sessionId 会话ID\r\n @return 文本分析结果\r\n"}, {"name": "analyzeAudioStream", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 实时音频分析（流式）\r\n\r\n @param sessionId 会话ID\r\n @param satoken 认证token\r\n @return SSE流\r\n"}, {"name": "analyzeVideoStream", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 实时视频分析（流式）\r\n\r\n @param sessionId 会话ID\r\n @param satoken 认证token\r\n @return SSE流\r\n"}, {"name": "getAnalysisHistory", "paramTypes": ["java.lang.String"], "doc": "\n 获取分析历史\r\n\r\n @param sessionId 会话ID\r\n @return 分析历史记录\r\n"}, {"name": "batchAnalysis", "paramTypes": ["java.lang.String", "org.springframework.web.multipart.MultipartFile[]", "java.lang.String"], "doc": "\n 批量上传文件进行分析\r\n\r\n @param sessionId 会话ID\r\n @param files 文件列表\r\n @param jobPosition 岗位信息\r\n @return 分析结果\r\n"}], "constructors": []}