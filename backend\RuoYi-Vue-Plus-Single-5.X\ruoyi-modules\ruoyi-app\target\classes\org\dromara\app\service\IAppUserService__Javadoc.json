{"doc": "\n 应用用户Service接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByPhone", "paramTypes": ["java.lang.String"], "doc": "\n 根据手机号查询用户\r\n"}, {"name": "selectByEmail", "paramTypes": ["java.lang.String"], "doc": "\n 根据邮箱查询用户\r\n"}, {"name": "checkPhoneExists", "paramTypes": ["java.lang.String"], "doc": "\n 检查手机号是否存在\r\n"}, {"name": "checkEmailExists", "paramTypes": ["java.lang.String"], "doc": "\n 检查邮箱是否存在\r\n"}, {"name": "register", "paramTypes": ["org.dromara.app.domain.dto.AppAuthDto"], "doc": "\n 用户注册\r\n"}, {"name": "loginByPhone", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 手机号登录\r\n"}, {"name": "loginByPassword", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 密码登录\r\n"}, {"name": "resetPassword", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 重置密码\r\n"}, {"name": "getUserInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID获取用户信息\r\n"}, {"name": "updateLoginInfo", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 更新最后登录信息\r\n"}], "constructors": []}