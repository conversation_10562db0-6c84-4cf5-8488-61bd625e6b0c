{"groups": [{"name": "spring.data.mongodb", "type": "org.dromara.common.mongodb.properties.MongoProperties", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties"}, {"name": "spring.data.mongodb.pool", "type": "org.dromara.common.mongodb.properties.MongoProperties$Pool", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties", "sourceMethod": "getPool()"}], "properties": [{"name": "spring.data.mongodb.authentication-database", "type": "java.lang.String", "description": "认证数据库", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties"}, {"name": "spring.data.mongodb.database", "type": "java.lang.String", "description": "数据库名称", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties", "defaultValue": "dromara"}, {"name": "spring.data.mongodb.host", "type": "java.lang.String", "description": "主机地址", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties", "defaultValue": "localhost"}, {"name": "spring.data.mongodb.password", "type": "java.lang.String", "description": "密码", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties"}, {"name": "spring.data.mongodb.pool.max-connection-idle-time-ms", "type": "java.lang.Long", "description": "连接最大空闲时间(毫秒)", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties$Pool", "defaultValue": 600000}, {"name": "spring.data.mongodb.pool.max-connection-life-time-ms", "type": "java.lang.Long", "description": "连接最大生存时间(毫秒)", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties$Pool", "defaultValue": 0}, {"name": "spring.data.mongodb.pool.max-connection-timeout-ms", "type": "java.lang.Long", "description": "连接超时时间(毫秒)", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties$Pool", "defaultValue": 30000}, {"name": "spring.data.mongodb.pool.max-read-timeout-ms", "type": "java.lang.Long", "description": "读取超时时间(毫秒)", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties$Pool", "defaultValue": 15000}, {"name": "spring.data.mongodb.pool.max-size", "type": "java.lang.Integer", "description": "最大连接数", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties$Pool", "defaultValue": 100}, {"name": "spring.data.mongodb.pool.max-wait-time-ms", "type": "java.lang.Long", "description": "最大等待时间(毫秒)", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties$Pool", "defaultValue": 30000}, {"name": "spring.data.mongodb.pool.min-size", "type": "java.lang.Integer", "description": "最小连接数", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties$Pool", "defaultValue": 10}, {"name": "spring.data.mongodb.port", "type": "java.lang.Integer", "description": "端口号", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties", "defaultValue": 27017}, {"name": "spring.data.mongodb.uri", "type": "java.lang.String", "description": "MongoDB连接字符串", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties", "defaultValue": "mongodb://localhost:27017/dromara"}, {"name": "spring.data.mongodb.username", "type": "java.lang.String", "description": "用户名", "sourceType": "org.dromara.common.mongodb.properties.MongoProperties"}], "hints": []}