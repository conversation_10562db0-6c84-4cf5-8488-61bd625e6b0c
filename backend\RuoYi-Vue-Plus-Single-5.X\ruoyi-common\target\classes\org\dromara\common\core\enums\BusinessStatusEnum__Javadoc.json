{"doc": " 业务状态枚举\n\n <AUTHOR>\n", "fields": [{"name": "status", "doc": " 状态\n"}, {"name": "desc", "doc": " 描述\n"}], "enumConstants": [{"name": "CANCEL", "doc": " 已撤销\n"}, {"name": "DRAFT", "doc": " 草稿\n"}, {"name": "WAITING", "doc": " 待审核\n"}, {"name": "FINISH", "doc": " 已完成\n"}, {"name": "INVALID", "doc": " 已作废\n"}, {"name": "BACK", "doc": " 已退回\n"}, {"name": "TERMINATION", "doc": " 已终止\n"}], "methods": [{"name": "getByStatus", "paramTypes": ["java.lang.String"], "doc": " 根据状态获取对应的 BusinessStatusEnum 枚举\n\n @param status 业务状态码\n @return 对应的 BusinessStatusEnum 枚举，如果找不到则返回 null\n"}, {"name": "findByStatus", "paramTypes": ["java.lang.String"], "doc": " 根据状态获取对应的业务状态描述信息\n\n @param status 业务状态码\n @return 返回业务状态描述，若状态码为空或未找到对应的枚举，返回空字符串\n"}, {"name": "isDraftOrCancelOrBack", "paramTypes": ["java.lang.String"], "doc": " 判断是否为指定的状态之一：草稿、已撤销或已退回\n\n @param status 要检查的状态\n @return 如果状态为草稿、已撤销或已退回之一，则返回 true；否则返回 false\n"}, {"name": "initialState", "paramTypes": ["java.lang.String"], "doc": " 判断是否为撤销，退回，作废，终止\n\n @param status status\n @return 结果\n"}, {"name": "runningStatus", "paramTypes": [], "doc": " 获取运行中的实例状态列表\n\n @return 包含运行中实例状态的不可变列表\n （包含 DRAFT、WAITING、BACK 和 CANCEL 状态）\n"}, {"name": "finishStatus", "paramTypes": [], "doc": " 获取结束实例的状态列表\n\n @return 包含结束实例状态的不可变列表\n （包含 FINISH、INVALID 和 TERMINATION 状态）\n"}, {"name": "checkStartStatus", "paramTypes": ["java.lang.String"], "doc": " 启动流程校验\n\n @param status 状态\n"}, {"name": "checkCancelStatus", "paramTypes": ["java.lang.String"], "doc": " 撤销流程校验\n\n @param status 状态\n"}, {"name": "checkBackStatus", "paramTypes": ["java.lang.String"], "doc": " 驳回流程校验\n\n @param status 状态\n"}, {"name": "checkInvalidStatus", "paramTypes": ["java.lang.String"], "doc": " 作废,终止流程校验\n\n @param status 状态\n"}], "constructors": []}