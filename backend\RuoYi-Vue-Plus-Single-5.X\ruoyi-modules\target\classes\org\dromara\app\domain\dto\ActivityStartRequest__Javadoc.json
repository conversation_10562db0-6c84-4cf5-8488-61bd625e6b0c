{"doc": " 开始活动请求DTO\n\n <AUTHOR>\n @date 2025-07-16\n", "fields": [{"name": "sessionId", "doc": " 会话ID\n"}, {"name": "type", "doc": " 活动类型\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "activityId", "doc": " 活动对象ID(如题目ID、课程ID等)\n"}, {"name": "activityName", "doc": " 活动名称\n"}, {"name": "categoryId", "doc": " 分类ID(如题库ID、课程分类ID等)\n"}, {"name": "categoryName", "doc": " 分类名称\n"}, {"name": "metadata", "doc": " 额外元数据\n"}], "enumConstants": [], "methods": [], "constructors": []}