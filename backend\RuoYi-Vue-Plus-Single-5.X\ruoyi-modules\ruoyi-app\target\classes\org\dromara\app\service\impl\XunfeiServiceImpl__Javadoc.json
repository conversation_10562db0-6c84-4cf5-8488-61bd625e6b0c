{"doc": "\n 讯飞AI服务实现类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "sparkChat", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": "\n 智能体spark\r\n"}, {"name": "sparkChatAgentSync", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map"], "doc": "\n 调用智能体服务（非流式）\r\n"}, {"name": "performRealStreamRequest", "paramTypes": ["java.lang.String", "java.util.Map", "org.dromara.app.service.IXunfeiService.StreamCallback"], "doc": "\n 使用OkHttp执行真正的流式请求\r\n"}, {"name": "extractTokenFromResponse", "paramTypes": ["cn.hutool.json.JSONObject"], "doc": "\n 从响应JSON中提取token\r\n"}, {"name": "buildSparkChatAgentRequest", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map"], "doc": "\n 构建星火大模型智能体聊天请求（角色化）\r\n"}, {"name": "invokeStreamingAgentService", "paramTypes": ["org.dromara.app.service.impl.XunfeiServiceImpl.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String", "org.dromara.app.service.impl.XunfeiServiceImpl.StreamingChatResponseHandler"], "doc": "\n 调用流式Agent服务\r\n"}, {"name": "buildSparkChatRequest", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": "\n 构建星火大模型聊天请求（OpenAI兼容格式）\r\n"}, {"name": "buildSpeechRecognitionRequest", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 构建语音识别请求\r\n"}, {"name": "buildSpeechRecognitionRequest", "paramTypes": ["byte[]"], "doc": "\n 构建语音识别请求（字节数组）\r\n"}, {"name": "buildEmotionAnalysisRequest", "paramTypes": ["java.lang.String"], "doc": "\n 构建情感分析请求\r\n"}, {"name": "buildTextToSpeechRequest", "paramTypes": ["java.lang.String", "org.dromara.app.service.IXunfeiService.VoiceConfig"], "doc": "\n 构建语音合成请求\r\n"}, {"name": "generateSparkAuthHeaders", "paramTypes": [], "doc": "\n 生成星火大模型认证头（HTTP Bearer Token）\r\n"}, {"name": "generateAuthHeaders", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 生成认证头（其他API使用的签名认证）\r\n"}, {"name": "generateSignature", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 生成签名\r\n"}, {"name": "extractSparkResponse", "paramTypes": ["cn.hutool.json.JSONObject"], "doc": "\n 提取星火大模型响应（OpenAI兼容格式）\r\n"}, {"name": "parseSpeechRecognitionResponse", "paramTypes": ["cn.hutool.json.JSONObject"], "doc": "\n 解析语音识别响应\r\n"}, {"name": "parseEmotionAnalysisResponse", "paramTypes": ["cn.hutool.json.JSONObject"], "doc": "\n 解析情感分析响应\r\n"}, {"name": "isValidAudioFormat", "paramTypes": ["java.lang.String"], "doc": "\n 验证音频格式\r\n"}, {"name": "downloadAudioFromUrl", "paramTypes": ["java.lang.String"], "doc": "\n 从URL下载音频文件\r\n"}, {"name": "analyzeVoiceFeatures", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 分析语音特征（简化实现）\r\n"}, {"name": "combineEmotionResults", "paramTypes": ["org.dromara.app.service.IXunfeiService.EmotionAnalysisResult", "java.util.Map"], "doc": "\n 综合情感分析结果\r\n"}, {"name": "calculateOverallConfidence", "paramTypes": ["java.lang.Double", "java.util.Map"], "doc": "\n 计算综合置信度\r\n"}, {"name": "processStreamResponse", "paramTypes": ["java.lang.String", "org.dromara.app.service.IXunfeiService.StreamCallback"], "doc": "\n 处理流式响应（SSE格式）\r\n"}, {"name": "parseImageEmotionResult", "paramTypes": ["java.lang.String"], "doc": "\n 解析图像情感分析结果\r\n"}, {"name": "findDominantEmotion", "paramTypes": ["cn.hutool.json.JSONObject"], "doc": "\n 找出主导情感\r\n"}, {"name": "extractEmotionScores", "paramTypes": ["cn.hutool.json.JSONObject"], "doc": "\n 提取情感分数映射\r\n"}, {"name": "buildInterviewSuggestionPrompt", "paramTypes": ["java.util.Map", "java.util.Map"], "doc": "\n 构建面试建议的prompt\r\n"}], "constructors": []}