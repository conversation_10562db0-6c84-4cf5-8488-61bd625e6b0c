{"doc": " 徽章数据层\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "countTotalBadges", "paramTypes": [], "doc": " 获取总徽章数\n\n @return 总徽章数\n"}, {"name": "countBadgesByCategory", "paramTypes": ["java.lang.String"], "doc": " 根据类别获取徽章数\n\n @param category 类别\n @return 该类别的徽章数\n"}, {"name": "countBadgesByRarity", "paramTypes": ["java.lang.String"], "doc": " 根据稀有度获取徽章数\n\n @param rarity 稀有度\n @return 该稀有度的徽章数\n"}, {"name": "selectByAchievementId", "paramTypes": ["java.lang.String"], "doc": " 根据成就ID查询徽章\n\n @param achievementId 成就ID\n @return 相关徽章列表\n"}, {"name": "selectByIds", "paramTypes": ["java.util.List"], "doc": " 根据多个ID批量查询徽章\n\n @param ids 徽章ID列表\n @return 徽章列表\n"}, {"name": "selectRecommendedBadges", "paramTypes": [], "doc": " 获取所有推荐的徽章\n\n @return 推荐徽章列表\n"}, {"name": "selectByCategory", "paramTypes": ["java.lang.String"], "doc": " 获取指定类别的徽章\n\n @param category 类别\n @return 徽章列表\n"}, {"name": "selectByRarity", "paramTypes": ["java.lang.String"], "doc": " 获取指定稀有度的徽章\n\n @param rarity 稀有度\n @return 徽章列表\n"}, {"name": "selectBySpecialFlag", "paramTypes": ["java.lang.String"], "doc": " 获取特殊徽章（限时、活动等）\n\n @param specialFlag 特殊标志\n @return 特殊徽章列表\n"}, {"name": "selectByTag", "paramTypes": ["java.lang.String"], "doc": " 根据标签查询徽章\n\n @param tag 标签\n @return 徽章列表\n"}, {"name": "selectAllEnabled", "paramTypes": [], "doc": " 获取所有启用的徽章\n\n @return 启用的徽章列表\n"}, {"name": "selectByUnlockCriteria", "paramTypes": ["java.lang.String"], "doc": " 根据解锁条件查询徽章\n\n @param unlockCriteria 解锁条件关键词\n @return 徽章列表\n"}], "constructors": []}