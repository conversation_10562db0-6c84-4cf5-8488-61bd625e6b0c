{"doc": " 成就事件实体\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [{"name": "id", "doc": " 事件ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "eventType", "doc": " 事件类型\n"}, {"name": "eventData", "doc": " 事件数据（JSON格式）\n"}, {"name": "eventTime", "doc": " 事件时间\n"}, {"name": "relatedObjectId", "doc": " 关联对象ID\n"}, {"name": "relatedObjectType", "doc": " 关联对象类型\n"}, {"name": "eventValue", "doc": " 事件值（计数类事件）\n"}, {"name": "eventSource", "doc": " 事件来源\n"}, {"name": "sessionId", "doc": " 会话ID\n"}, {"name": "clientInfo", "doc": " 客户端信息\n"}, {"name": "processStatus", "doc": " 处理状态（0=未处理，1=已处理，2=处理失败）\n"}, {"name": "processTime", "doc": " 处理时间\n"}, {"name": "processResult", "doc": " 处理结果（JSON格式）\n"}, {"name": "triggeredAchievements", "doc": " 触发的成就IDs（用逗号分隔）\n"}], "enumConstants": [], "methods": [], "constructors": []}