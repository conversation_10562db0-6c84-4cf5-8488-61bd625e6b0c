{"doc": "\n 知识库服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "createKnowledgeBase", "paramTypes": ["org.dromara.app.domain.KnowledgeBase", "java.lang.Long"], "doc": "\n 创建知识库\r\n\r\n @param knowledgeBase 知识库信息\r\n @param userId        用户ID\r\n @return 是否成功\r\n"}, {"name": "updateKnowledgeBase", "paramTypes": ["org.dromara.app.domain.KnowledgeBase", "java.lang.Long"], "doc": "\n 更新知识库\r\n\r\n @param knowledgeBase 知识库信息\r\n @param userId        用户ID\r\n @return 是否成功\r\n"}, {"name": "deleteKnowledgeBase", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 删除知识库\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @param userId          用户ID\r\n @return 是否成功\r\n"}, {"name": "getKnowledgeBase", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 获取知识库详情\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @param userId          用户ID\r\n @return 知识库详情\r\n"}, {"name": "getUserKnowledgeBases", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 获取用户知识库列表\r\n\r\n @param userId   用户ID\r\n @param pageNum  页码\r\n @param pageSize 每页大小\r\n @return 知识库分页结果\r\n"}, {"name": "getPublicKnowledgeBases", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": "\n 获取公开知识库列表\r\n\r\n @param pageNum  页码\r\n @param pageSize 每页大小\r\n @return 知识库分页结果\r\n"}, {"name": "searchKnowledgeBases", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 搜索知识库\r\n\r\n @param keyword  搜索关键词\r\n @param userId   用户ID\r\n @param pageNum  页码\r\n @param pageSize 每页大小\r\n @return 搜索结果\r\n"}, {"name": "uploadDocument", "paramTypes": ["java.lang.Long", "org.springframework.web.multipart.MultipartFile", "java.lang.Long"], "doc": "\n 上传文档到知识库\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @param file            文件\r\n @param userId          用户ID\r\n @return 文档信息\r\n"}, {"name": "addTextDocument", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.lang.Long"], "doc": "\n 添加文本文档\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @param title           文档标题\r\n @param content         文档内容\r\n @param userId          用户ID\r\n @return 文档信息\r\n"}, {"name": "deleteDocument", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 删除文档\r\n\r\n @param documentId 文档ID\r\n @param userId     用户ID\r\n @return 是否成功\r\n"}, {"name": "getKnowledgeBaseDocuments", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 获取知识库文档列表\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @param userId          用户ID\r\n @param pageNum         页码\r\n @param pageSize        每页大小\r\n @return 文档分页结果\r\n"}, {"name": "getDocumentDetail", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 获取文档详情\r\n\r\n @param documentId 文档ID\r\n @param userId     用户ID\r\n @return 文档详情\r\n"}, {"name": "reprocessDocument", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 重新处理文档（重新分块和向量化）\r\n\r\n @param documentId 文档ID\r\n @param userId     用户ID\r\n @return 是否成功\r\n"}, {"name": "searchKnowledge", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.Long"], "doc": "\n 在知识库中搜索相关内容\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @param query           查询文本\r\n @param topK            返回的top结果数\r\n @param userId          用户ID\r\n @return 搜索结果\r\n"}, {"name": "searchMultipleKnowledgeBases", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Integer", "java.lang.Long"], "doc": "\n 在多个知识库中搜索\r\n\r\n @param knowledgeBaseIds 知识库ID列表\r\n @param query            查询文本\r\n @param topK             返回的top结果数\r\n @param userId           用户ID\r\n @return 搜索结果\r\n"}, {"name": "hybridSearch", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.Double", "java.lang.Long"], "doc": "\n 混合搜索（向量搜索 + 关键词搜索）\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @param query           查询文本\r\n @param topK            返回的top结果数\r\n @param hybridAlpha     混合权重（0-1，0为纯关键词，1为纯向量）\r\n @param userId          用户ID\r\n @return 搜索结果\r\n"}, {"name": "getDocumentChunks", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 获取文档分块列表\r\n\r\n @param documentId 文档ID\r\n @param userId     用户ID\r\n @return 分块列表\r\n"}, {"name": "updateChunkContent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Long"], "doc": "\n 更新分块内容\r\n\r\n @param chunkId 分块ID\r\n @param content 新内容\r\n @param userId  用户ID\r\n @return 是否成功\r\n"}, {"name": "deleteChunk", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 删除分块\r\n\r\n @param chunkId 分块ID\r\n @param userId  用户ID\r\n @return 是否成功\r\n"}, {"name": "getKnowledgeBaseStats", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 获取知识库统计信息\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @param userId          用户ID\r\n @return 统计信息\r\n"}, {"name": "getUserKnowledgeStats", "paramTypes": ["java.lang.Long"], "doc": "\n 获取用户知识库统计\r\n\r\n @param userId 用户ID\r\n @return 统计信息\r\n"}], "constructors": []}