{"doc": "\n 面试答案对象 app_interview_answer\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [{"name": "id", "doc": "\n 答案ID\r\n"}, {"name": "sessionId", "doc": "\n 会话ID\r\n"}, {"name": "questionId", "doc": "\n 问题ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "content", "doc": "\n 答案内容（文本）\r\n"}, {"name": "audioUrl", "doc": "\n 音频URL\r\n"}, {"name": "videoUrl", "doc": "\n 视频URL\r\n"}, {"name": "duration", "doc": "\n 回答时长（秒）\r\n"}, {"name": "submittedTime", "doc": "\n 提交时间\r\n"}, {"name": "score", "doc": "\n AI评估分数\r\n"}, {"name": "feedback", "doc": "\n AI评估反馈\r\n"}, {"name": "evaluationDetails", "doc": "\n 评估详情（JSON格式）\r\n"}, {"name": "status", "doc": "\n 状态（submitted, evaluated, reviewed）\r\n"}, {"name": "skipped", "doc": "\n 是否跳过\r\n"}, {"name": "skipR<PERSON>on", "doc": "\n 跳过原因\r\n"}], "enumConstants": [], "methods": [], "constructors": []}