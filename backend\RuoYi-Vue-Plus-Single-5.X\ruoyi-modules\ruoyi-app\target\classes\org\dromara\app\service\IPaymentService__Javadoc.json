{"doc": "\n 支付服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "createPaymentOrder", "paramTypes": ["org.dromara.app.domain.dto.PaymentOrderDto"], "doc": "\n 创建支付订单\r\n\r\n @param paymentOrderDto 支付订单请求参数\r\n @return 支付订单信息\r\n"}, {"name": "alipayPay", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 支付宝支付\r\n\r\n @param orderNo  订单号\r\n @param payToken 支付token\r\n @return 支付页面表单HTML\r\n"}, {"name": "alipayNotify", "paramTypes": ["jakarta.servlet.http.HttpServletRequest"], "doc": "\n 支付宝异步通知处理\r\n\r\n @param request HTTP请求\r\n @return 处理结果\r\n"}, {"name": "alipayReturn", "paramTypes": ["jakarta.servlet.http.HttpServletRequest"], "doc": "\n 支付宝同步回调处理\r\n\r\n @param request HTTP请求\r\n @return 处理结果\r\n"}, {"name": "queryOrderStatus", "paramTypes": ["java.lang.String"], "doc": "\n 查询订单状态\r\n\r\n @param orderNo 订单号\r\n @return 订单信息\r\n"}, {"name": "cancelOrder", "paramTypes": ["java.lang.String"], "doc": "\n 取消订单\r\n\r\n @param orderNo 订单号\r\n @return 取消结果\r\n"}, {"name": "validatePayToken", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 验证支付token\r\n\r\n @param orderNo  订单号\r\n @param payToken 支付token\r\n @return 验证结果\r\n"}, {"name": "markPayTokenAsUsed", "paramTypes": ["java.lang.String"], "doc": "\n 标记支付token为已使用\r\n\r\n @param orderNo 订单号\r\n"}, {"name": "handlePaymentTimeout", "paramTypes": ["java.lang.String"], "doc": "\n 处理支付超时\r\n\r\n @param orderNo 订单号\r\n"}], "constructors": []}