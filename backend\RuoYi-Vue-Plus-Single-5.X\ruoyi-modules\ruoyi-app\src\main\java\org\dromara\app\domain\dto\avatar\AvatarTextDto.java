package org.dromara.app.domain.dto.avatar;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 数字人文本请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
@Schema(description = "数字人文本请求")
public class AvatarTextDto {

    @Schema(description = "文本内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "文本内容不能为空")
    @Size(max = 1000, message = "文本内容长度不能超过1000字符")
    private String text;

    @Schema(description = "声音ID")
    private String vcn;

    @Schema(description = "语速", example = "50")
    @Min(value = 0, message = "语速最小为0")
    @Max(value = 100, message = "语速最大为100")
    private Integer speed;

    @Schema(description = "语调", example = "50")
    @Min(value = 0, message = "语调最小为0")
    @Max(value = 100, message = "语调最大为100")
    private Integer pitch;

    @Schema(description = "音量", example = "50")
    @Min(value = 0, message = "音量最小为0")
    @Max(value = 100, message = "音量最大为100")
    private Integer volume;
}
