{"doc": "\n 题目类型枚举\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [{"name": "SINGLE_CHOICE", "doc": "\n 单选题\r\n"}, {"name": "MULTIPLE_CHOICE", "doc": "\n 多选题\r\n"}, {"name": "TRUE_FALSE", "doc": "\n 判断题\r\n"}, {"name": "SHORT_ANSWER", "doc": "\n 简答题\r\n"}, {"name": "PROGRAMMING", "doc": "\n 编程题\r\n"}], "methods": [{"name": "getDescriptionByCode", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据代码获取描述\r\n\r\n @param code 类型代码\r\n @return 类型描述\r\n"}, {"name": "getCodeByDescription", "paramTypes": ["java.lang.String"], "doc": "\n 根据描述获取代码\r\n\r\n @param description 类型描述\r\n @return 类型代码\r\n"}, {"name": "getByCode", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据代码获取枚举实例\r\n\r\n @param code 类型代码\r\n @return 枚举实例\r\n"}, {"name": "getByDescription", "paramTypes": ["java.lang.String"], "doc": "\n 根据描述获取枚举实例\r\n\r\n @param description 类型描述\r\n @return 枚举实例\r\n"}], "constructors": []}