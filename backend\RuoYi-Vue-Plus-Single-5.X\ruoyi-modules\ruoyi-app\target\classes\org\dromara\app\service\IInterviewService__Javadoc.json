{"doc": "\n 面试服务接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getJobList", "paramTypes": ["org.dromara.app.domain.bo.JobQueryBo"], "doc": "\n 获取岗位列表\r\n\r\n @param queryBo 查询参数\r\n @return 岗位列表响应\r\n"}, {"name": "getCategories", "paramTypes": ["java.lang.Bo<PERSON>an"], "doc": "\n 获取岗位分类列表\r\n\r\n @param includeJobCount 是否包含岗位数量\r\n @return 分类列表响应\r\n"}, {"name": "getInterviewModes", "paramTypes": [], "doc": "\n 获取面试模式列表\r\n\r\n @return 面试模式列表响应\r\n"}, {"name": "getSearchSuggestions", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取搜索建议\r\n\r\n @param keyword 搜索关键词\r\n @param limit   返回数量限制\r\n @return 搜索建议响应\r\n"}, {"name": "createInterviewSession", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.util.List"], "doc": "\n 创建面试会话\r\n\r\n @param jobId               岗位ID\r\n @param mode                面试模式\r\n @param resumeUrl           简历URL\r\n @param customizedQuestions 自定义问题列表\r\n @return 创建会话响应\r\n"}, {"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": "\n 收藏/取消收藏岗位\r\n\r\n @param jobId       岗位ID\r\n @param isFavorited 收藏状态\r\n"}, {"name": "checkDevice", "paramTypes": [], "doc": "\n 设备检测\r\n\r\n @return 设备检测结果\r\n"}, {"name": "getStatistics", "paramTypes": [], "doc": "\n 获取统计信息\r\n\r\n @return 统计信息响应\r\n"}, {"name": "getInterviewHistory", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": "\n 获取面试历史记录\r\n\r\n @param page     页码\r\n @param pageSize 每页大小\r\n @param category 分类筛选\r\n @param status   状态筛选\r\n @return 历史记录列表响应\r\n"}, {"name": "getUserStatistics", "paramTypes": [], "doc": "\n 获取用户统计数据\r\n\r\n @return 用户统计数据\r\n"}, {"name": "getJobDetail", "paramTypes": ["java.lang.Long"], "doc": "\n 获取岗位详情\r\n\r\n @param jobId 岗位ID\r\n @return 岗位详情响应\r\n"}, {"name": "getSampleQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 获取示例问题\r\n\r\n @param jobId 岗位ID\r\n @param count 问题数量\r\n @return 示例问题响应\r\n"}, {"name": "getRelatedJobs", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 获取相关岗位\r\n\r\n @param jobId 当前岗位ID\r\n @param limit 数量限制\r\n @return 相关岗位响应\r\n"}, {"name": "getJobStatistics", "paramTypes": ["java.lang.Long"], "doc": "\n 获取岗位统计数据\r\n\r\n @param jobId 岗位ID\r\n @return 岗位统计响应\r\n"}, {"name": "getJobInterviewModes", "paramTypes": ["java.lang.Long"], "doc": "\n 获取岗位面试模式列表\r\n\r\n @param jobId 岗位ID\r\n @return 岗位面试模式响应\r\n"}, {"name": "checkUserReadiness", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 检查用户准备度\r\n\r\n @param jobId  岗位ID\r\n @param userId 用户ID\r\n @return 用户准备度响应\r\n"}, {"name": "shareJob", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 分享岗位信息\r\n\r\n @param jobId    岗位ID\r\n @param platform 分享平台\r\n @return 分享岗位响应\r\n"}, {"name": "getSessionInfo", "paramTypes": ["java.lang.String"], "doc": "\n 获取会话信息\r\n\r\n @param sessionId 会话ID\r\n @return 会话信息\r\n"}, {"name": "getSessionQuestions", "paramTypes": ["java.lang.String"], "doc": "\n 获取面试问题列表\r\n\r\n @param sessionId 会话ID\r\n @return 问题列表响应\r\n"}, {"name": "submitAnswer", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": "\n 提交面试回答\r\n\r\n @param sessionId  会话ID\r\n @param questionId 问题ID\r\n @param answer     回答内容\r\n @param audioUrl   音频URL\r\n @param duration   回答时长(秒)\r\n @return 回答响应\r\n"}, {"name": "endSession", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 结束面试会话\r\n\r\n @param sessionId 会话ID\r\n @param reason    结束原因\r\n @return 结束会话响应\r\n"}, {"name": "submitFeedback", "paramTypes": ["java.lang.String", "java.lang.Integer", "java.lang.String", "java.util.List"], "doc": "\n 提交面试反馈\r\n\r\n @param sessionId 会话ID\r\n @param rating    评分\r\n @param comments  评论\r\n @param tags      标签\r\n @return 反馈响应\r\n"}, {"name": "getInterviewResult", "paramTypes": ["java.lang.String"], "doc": "\n 获取面试结果\r\n\r\n @param sessionId 会话ID\r\n @return 面试结果\r\n"}, {"name": "checkDevices", "paramTypes": [], "doc": "\n 检查设备状态\r\n\r\n @return 设备状态映射\r\n"}, {"name": "getSessionStatus", "paramTypes": ["java.lang.String"], "doc": "\n 获取会话状态\r\n\r\n @param sessionId 会话ID\r\n @return 会话状态\r\n"}, {"name": "getInterviewerInfo", "paramTypes": ["java.lang.String"], "doc": "\n 获取AI面试官信息\r\n\r\n @param interviewerId 面试官ID\r\n @return 面试官信息\r\n"}], "constructors": []}