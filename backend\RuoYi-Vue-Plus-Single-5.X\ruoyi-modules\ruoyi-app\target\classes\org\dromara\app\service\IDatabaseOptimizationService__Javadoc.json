{"doc": "\n 数据库优化服务接口\r\n 用于数据库查询优化和性能监控\r\n\r\n <AUTHOR>\r\n @date 2025-01-19\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "analyzeSlowQueries", "paramTypes": ["int"], "doc": "\n 分析慢查询\r\n\r\n @param limit 限制数量\r\n @return 慢查询列表\r\n"}, {"name": "optimizeIndexes", "paramTypes": [], "doc": "\n 优化数据库索引\r\n\r\n @return 优化建议\r\n"}, {"name": "getDatabasePerformance", "paramTypes": [], "doc": "\n 获取数据库性能指标\r\n\r\n @return 性能指标\r\n"}, {"name": "analyzeTableSpaceUsage", "paramTypes": [], "doc": "\n 分析表空间使用情况\r\n\r\n @return 表空间使用情况\r\n"}, {"name": "optimizeQueryPlan", "paramTypes": ["java.lang.String"], "doc": "\n 优化查询计划\r\n\r\n @param sql SQL语句\r\n @return 优化建议\r\n"}, {"name": "monitorConnectionPool", "paramTypes": [], "doc": "\n 监控数据库连接池\r\n\r\n @return 连接池状态\r\n"}, {"name": "cleanupUnusedData", "paramTypes": [], "doc": "\n 清理无用数据\r\n\r\n @return 清理的记录数\r\n"}, {"name": "analyzeDataDistribution", "paramTypes": ["java.lang.String"], "doc": "\n 分析数据分布\r\n\r\n @param tableName 表名\r\n @return 数据分布分析\r\n"}, {"name": "suggestPartitionStrategy", "paramTypes": ["java.lang.String"], "doc": "\n 建议分区策略\r\n\r\n @param tableName 表名\r\n @return 分区建议\r\n"}, {"name": "monitorLockWaits", "paramTypes": [], "doc": "\n 监控锁等待\r\n\r\n @return 锁等待信息\r\n"}, {"name": "optimizeBatchSize", "paramTypes": ["int"], "doc": "\n 优化批量操作\r\n\r\n @param batchSize 批次大小\r\n @return 优化后的批次大小\r\n"}, {"name": "analyzeQueryFrequency", "paramTypes": [], "doc": "\n 分析查询频率\r\n\r\n @return 查询频率统计\r\n"}], "constructors": []}