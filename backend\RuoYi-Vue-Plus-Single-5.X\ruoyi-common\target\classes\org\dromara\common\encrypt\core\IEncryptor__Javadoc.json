{"doc": " 加解者\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "algorithm", "paramTypes": [], "doc": " 获得当前算法\n"}, {"name": "encrypt", "paramTypes": ["java.lang.String", "org.dromara.common.encrypt.enumd.EncodeType"], "doc": " 加密\n\n @param value      待加密字符串\n @param encodeType 加密后的编码格式\n @return 加密后的字符串\n"}, {"name": "decrypt", "paramTypes": ["java.lang.String"], "doc": " 解密\n\n @param value 待加密字符串\n @return 解密后的字符串\n"}], "constructors": []}