package org.dromara.app.controller;

import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.dto.*;
import org.dromara.app.domain.enums.ActivityType;
import org.dromara.app.domain.vo.ActivityHistoryVO;
import org.dromara.app.domain.vo.ActivityStatisticsVO;
import org.dromara.app.service.IActivityTimerService;
import org.dromara.common.core.domain.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户活动时长记录控制器
 * 统计用户的学习时长(面试时长,资源学习时长)
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-07-16
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/activity")
public class TimerController {

    private final IActivityTimerService activityTimerService;

    /**
     * 开始活动记录
     *
     * @param request 开始活动请求
     * @return 操作结果
     */
    @PostMapping("/start")
    public R<Void> startActivity(@RequestBody @Validated ActivityStartRequest request) {
        try {
            // 从当前登录用户获取用户ID
            Long userId = StpUtil.getLoginIdAsLong();
            request.setUserId(userId);

            boolean success = activityTimerService.startActivity(request);
            if (success) {
                return R.ok();
            } else {
                return R.fail("开始活动记录失败");
            }
        } catch (Exception e) {
            log.error("开始活动记录异常", e);
            return R.fail("开始活动记录异常: " + e.getMessage());
        }
    }

    /**
     * 暂停活动记录
     *
     * @param request 暂停活动请求
     * @return 操作结果
     */
    @PostMapping("/pause")
    public R<Void> pauseActivity(@RequestBody @Validated ActivityPauseRequest request) {
        try {
            // 从当前登录用户获取用户ID
            Long userId = StpUtil.getLoginIdAsLong();
            request.setUserId(userId);

            boolean success = activityTimerService.pauseActivity(request);
            if (success) {
                return R.ok();
            } else {
                return R.fail("暂停活动记录失败");
            }
        } catch (Exception e) {
            log.error("暂停活动记录异常", e);
            return R.fail("暂停活动记录异常: " + e.getMessage());
        }
    }

    /**
     * 结束活动记录
     *
     * @param request 结束活动请求
     * @return 操作结果
     */
    @PostMapping("/end")
    public R<Void> endActivity(@RequestBody @Validated ActivityEndRequest request) {
        try {
            // 从当前登录用户获取用户ID
            Long userId = StpUtil.getLoginIdAsLong();
            request.setUserId(userId);

            boolean success = activityTimerService.endActivity(request);
            if (success) {
                return R.ok();
            } else {
                return R.fail("结束活动记录失败");
            }
        } catch (Exception e) {
            log.error("结束活动记录异常", e);
            return R.fail("结束活动记录异常: " + e.getMessage());
        }
    }

    /**
     * 同步活动会话
     *
     * @param request 同步请求
     * @return 操作结果
     */
    @PostMapping("/sync")
    public R<Void> syncSession(@RequestBody @Validated ActivitySyncRequest request) {
        try {
            // 从当前登录用户获取用户ID
            Long userId = StpUtil.getLoginIdAsLong();
            request.setUserId(userId);

            boolean success = activityTimerService.syncSession(request);
            if (success) {
                return R.ok();
            } else {
                return R.fail("同步会话失败");
            }
        } catch (Exception e) {
            log.error("同步会话异常", e);
            return R.fail("同步会话异常: " + e.getMessage());
        }
    }

    /**
     * 获取活动统计数据
     *
     * @param type 活动类型(可选)
     * @return 统计数据
     */
    @GetMapping("/statistics")
    public R<ActivityStatisticsVO> getStatistics(@RequestParam(required = false) ActivityType type) {
        try {
            // 从当前登录用户获取用户ID
            Long userId = StpUtil.getLoginIdAsLong();

            ActivityStatisticsRequest request = new ActivityStatisticsRequest();
            request.setUserId(userId);
            request.setType(type);

            ActivityStatisticsVO statistics = activityTimerService.getStatistics(request);
            return R.ok(statistics);
        } catch (Exception e) {
            log.error("获取活动统计异常", e);
            return R.fail("获取活动统计异常: " + e.getMessage());
        }
    }

    /**
     * 获取活动历史记录
     *
     * @param type      活动类型(可选)
     * @param startDate 开始时间(可选)
     * @param endDate   结束时间(可选)
     * @param page      页码(默认1)
     * @param pageSize  每页大小(默认10)
     * @param limit     限制数量(可选)
     * @return 历史记录
     */
    @GetMapping("/history")
    public R<ActivityHistoryVO> getHistory(
        @RequestParam(required = false) ActivityType type,
        @RequestParam(required = false) String startDate,
        @RequestParam(required = false) String endDate,
        @RequestParam(defaultValue = "1") Integer page,
        @RequestParam(defaultValue = "10") Integer pageSize,
        @RequestParam(required = false) Integer limit) {
        try {
            // 从当前登录用户获取用户ID
            Long userId = StpUtil.getLoginIdAsLong();

            ActivityHistoryRequest request = new ActivityHistoryRequest();
            request.setUserId(userId);
            request.setType(type);
            request.setPage(page);
            request.setPageSize(pageSize);
            request.setLimit(limit);

            // 解析日期参数
            if (startDate != null && !startDate.trim().isEmpty()) {
                request.setStartDate(java.time.LocalDateTime.parse(startDate));
            }
            if (endDate != null && !endDate.trim().isEmpty()) {
                request.setEndDate(java.time.LocalDateTime.parse(endDate));
            }

            ActivityHistoryVO history = activityTimerService.getHistory(request);
            return R.ok(history);
        } catch (Exception e) {
            log.error("获取活动历史异常", e);
            return R.fail("获取活动历史异常: " + e.getMessage());
        }
    }

    /**
     * 清除活动记录
     *
     * @param type 活动类型(可选，为空时清除所有类型)
     * @return 操作结果
     */
    @PostMapping("/clear")
    public R<Void> clearRecords(@RequestParam(required = false) ActivityType type) {
        try {
            // 从当前登录用户获取用户ID
            Long userId = StpUtil.getLoginIdAsLong();

            boolean success = activityTimerService.clearRecords(userId, type);
            if (success) {
                return R.ok();
            } else {
                return R.fail("清除活动记录失败");
            }
        } catch (Exception e) {
            log.error("清除活动记录异常", e);
            return R.fail("清除活动记录异常: " + e.getMessage());
        }
    }

    /**
     * 获取指定类型的活动统计
     *
     * @param type 活动类型
     * @return 统计数据
     */
    @GetMapping("/statistics/{type}")
    public R<ActivityStatisticsVO> getStatsByType(@PathVariable ActivityType type) {
        try {
            // 从当前登录用户获取用户ID
            Long userId = StpUtil.getLoginIdAsLong();

            ActivityStatisticsVO statistics = activityTimerService.getStatsByType(userId, type);
            return R.ok(statistics);
        } catch (Exception e) {
            log.error("获取指定类型活动统计异常", e);
            return R.fail("获取指定类型活动统计异常: " + e.getMessage());
        }
    }

    /**
     * 初始化用户活动总览
     *
     * @return 操作结果
     */
    @PostMapping("/init")
    public R<Void> initializeUserSummary() {
        try {
            // 从当前登录用户获取用户ID
            Long userId = StpUtil.getLoginIdAsLong();

            boolean success = activityTimerService.initializeUserSummary(userId);
            if (success) {
                return R.ok();
            } else {
                return R.fail("初始化用户活动总览失败");
            }
        } catch (Exception e) {
            log.error("初始化用户活动总览异常", e);
            return R.fail("初始化用户活动总览异常: " + e.getMessage());
        }
    }

    /**
     * 健康检查接口
     *
     * @return 服务状态
     */
    @GetMapping("/health")
    public R<String> health() {
        return R.ok("Activity Timer Service is running");
    }
}
