{"doc": "\n 面试报告控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "generateReport", "paramTypes": ["java.lang.String"], "doc": "\n 生成面试报告\r\n\r\n @param sessionId 面试会话ID\r\n @return 面试报告\r\n"}, {"name": "generatePdfReport", "paramTypes": ["java.lang.String"], "doc": "\n 生成PDF报告\r\n\r\n @param sessionId 面试会话ID\r\n @return PDF文件路径\r\n"}, {"name": "downloadPdfReport", "paramTypes": ["java.lang.String", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 下载PDF报告\r\n\r\n @param sessionId 面试会话ID\r\n @param response HTTP响应\r\n"}, {"name": "getRadarChartData", "paramTypes": ["java.lang.String"], "doc": "\n 获取雷达图数据\r\n\r\n @param sessionId 面试会话ID\r\n @return 雷达图数据\r\n"}], "constructors": []}