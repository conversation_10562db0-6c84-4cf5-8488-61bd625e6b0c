<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.UserAchievementMapper">

    <resultMap type="org.dromara.app.domain.UserAchievement" id="UserAchievementResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="achievementId" column="achievement_id"/>
        <result property="unlockTime" column="unlock_time"/>
        <result property="progress" column="progress"/>
        <result property="currentValue" column="current_value"/>
        <result property="targetValue" column="target_value"/>
        <result property="isCompleted" column="is_completed"/>
        <result property="isNotified" column="is_notified"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <resultMap type="org.dromara.app.domain.vo.UserAchievementVo" id="UserAchievementVoResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="achievementId" column="achievement_id"/>
        <result property="achievementCode" column="achievement_code"/>
        <result property="achievementName" column="achievement_name"/>
        <result property="achievementDesc" column="achievement_desc"/>
        <result property="achievementIcon" column="achievement_icon"/>
        <result property="achievementType" column="achievement_type"/>
        <result property="rewardPoints" column="reward_points"/>
        <result property="unlockTime" column="unlock_time"/>
        <result property="progress" column="progress"/>
        <result property="currentValue" column="current_value"/>
        <result property="targetValue" column="target_value"/>
        <result property="isCompleted" column="is_completed"/>
        <result property="isNotified" column="is_notified"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectUserAchievementVo">
        select ua.id, ua.user_id, ua.achievement_id, ua.unlock_time, ua.progress,
               ua.current_value, ua.target_value, ua.is_completed, ua.is_notified,
               ua.create_time, ua.update_time,
               a.achievement_code, a.achievement_name, a.achievement_desc,
               a.achievement_icon, a.achievement_type, a.reward_points
        from app_user_achievement ua
        left join app_achievement a on ua.achievement_id = a.id
    </sql>

    <!-- 根据用户ID查询用户成就列表 -->
    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="UserAchievementVoResult">
        <include refid="selectUserAchievementVo"/>
        where ua.user_id = #{userId}
        order by ua.create_time desc
    </select>

    <!-- 根据用户ID和成就ID查询用户成就 -->
    <select id="selectByUserIdAndAchievementId" parameterType="map" resultMap="UserAchievementResult">
        select id, user_id, achievement_id, unlock_time, progress, current_value, target_value,
               is_completed, is_notified, create_time, update_time
        from app_user_achievement
        where user_id = #{userId} and achievement_id = #{achievementId}
    </select>

    <!-- 查询用户已完成的成就列表 -->
    <select id="selectCompletedByUserId" parameterType="java.lang.Long" resultMap="UserAchievementVoResult">
        <include refid="selectUserAchievementVo"/>
        where ua.user_id = #{userId} and ua.is_completed = '1'
        order by ua.unlock_time desc
    </select>

    <!-- 查询用户进行中的成就列表 -->
    <select id="selectInProgressByUserId" parameterType="java.lang.Long" resultMap="UserAchievementVoResult">
        <include refid="selectUserAchievementVo"/>
        where ua.user_id = #{userId} and ua.is_completed = '0' and ua.progress > 0
        order by ua.progress desc, ua.create_time desc
    </select>

    <!-- 统计用户已完成的成就数量 -->
    <select id="countCompletedByUserId" parameterType="java.lang.Long" resultType="java.lang.Long">
        select count(1) from app_user_achievement
        where user_id = #{userId} and is_completed = '1'
    </select>

    <!-- 统计用户获得的总积分 -->
    <select id="sumRewardPointsByUserId" parameterType="java.lang.Long" resultType="java.lang.Long">
        select COALESCE(sum(a.reward_points), 0)
        from app_user_achievement ua
        inner join app_achievement a on ua.achievement_id = a.id
        where ua.user_id = #{userId} and ua.is_completed = '1'
    </select>

    <!-- 查询未通知的已完成成就 -->
    <select id="selectUnnotifiedCompletedByUserId" parameterType="java.lang.Long" resultMap="UserAchievementVoResult">
        <include refid="selectUserAchievementVo"/>
        where ua.user_id = #{userId} and ua.is_completed = '1' and ua.is_notified = '0'
        order by ua.unlock_time desc
    </select>

    <!-- 批量更新通知状态 -->
    <update id="batchUpdateNotificationStatus" parameterType="map">
        update app_user_achievement
        set is_notified = #{isNotified}, update_time = now()
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 更新用户成就进度 -->
    <update id="updateProgress" parameterType="map">
        update app_user_achievement
        set current_value = #{currentValue},
            progress = #{progress},
            update_time = now()
        where user_id = #{userId} and achievement_id = #{achievementId}
    </update>

    <!-- 完成用户成就 -->
    <update id="completeAchievement" parameterType="map">
        update app_user_achievement
        set is_completed = '1',
            progress = 100,
            unlock_time = now(),
            update_time = now()
        where user_id = #{userId} and achievement_id = #{achievementId}
    </update>

    <!-- 根据成就类型查询用户成就统计 -->
    <select id="selectStatsByUserIdAndType" parameterType="map" resultMap="UserAchievementVoResult">
        <include refid="selectUserAchievementVo"/>
        where ua.user_id = #{userId} and a.achievement_type = #{achievementType}
        order by ua.is_completed desc, ua.progress desc
    </select>

    <select id="selectRecentByUserId" parameterType="java.lang.String" resultMap="UserAchievementVoResult">
        <include refid="selectUserAchievementVo"/>
        where a.achievement_code = #{achievementCode}
    </select>
</mapper>
