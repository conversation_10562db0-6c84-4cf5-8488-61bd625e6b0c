package org.dromara.app.domain.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.dromara.common.core.xss.Xss;

/**
 * 应用用户个人信息DTO
 *
 * <AUTHOR>
 */
@Data
public class AppUserProfileDto {

    /**
     * 用户昵称
     */
    @Xss(message = "用户昵称不能包含脚本字符")
    @NotBlank(message = "用户昵称不能为空")
    @Size(min = 0, max = 30, message = "用户昵称长度不能超过{max}个字符")
    private String name;

    /**
     * 用户性别（男/女）
     */
    private String gender;

    /**
     * 学校名称
     */
    @Size(min = 0, max = 100, message = "学校名称长度不能超过{max}个字符")
    private String school;

    /**
     * 专业
     */
    @Size(min = 0, max = 50, message = "专业长度不能超过{max}个字符")
    private String major;

    /**
     * 年级
     */
    @Size(min = 0, max = 20, message = "年级长度不能超过{max}个字符")
    private String grade;

    /**
     * 个人简介
     */
    @Size(min = 0, max = 200, message = "个人简介长度不能超过{max}个字符")
    private String introduction;

    /**
     * 用户头像
     */
    private String avatar;
}
