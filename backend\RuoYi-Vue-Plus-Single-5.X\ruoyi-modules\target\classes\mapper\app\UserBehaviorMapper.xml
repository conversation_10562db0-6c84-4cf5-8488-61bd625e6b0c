<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.UserBehaviorMapper">

    <resultMap type="org.dromara.app.domain.UserBehavior" id="UserBehaviorResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="behaviorType" column="behavior_type"/>
        <result property="behaviorData" column="behavior_data"/>
        <result property="ipAddress" column="ip_address"/>
        <result property="userAgent" column="user_agent"/>
        <result property="sessionId" column="session_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectUserBehaviorVo">
        select id, user_id, behavior_type, behavior_data, ip_address, user_agent, session_id, 
               create_by, create_time, update_by, update_time, remark
        from app_user_behavior
    </sql>

    <!-- 根据用户ID和行为类型查询行为记录 -->
    <select id="selectByUserIdAndBehaviorType" parameterType="map" resultMap="UserBehaviorResult">
        <include refid="selectUserBehaviorVo"/>
        where user_id = #{userId} and behavior_type = #{behaviorType}
        order by create_time desc
    </select>

    <!-- 根据用户ID和时间范围查询行为记录 -->
    <select id="selectByUserIdAndTimeRange" parameterType="map" resultMap="UserBehaviorResult">
        <include refid="selectUserBehaviorVo"/>
        where user_id = #{userId}
        <if test="startTime != null">
            and create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime}
        </if>
        order by create_time desc
    </select>

    <!-- 统计用户某种行为的次数 -->
    <select id="countByUserIdAndBehaviorType" parameterType="map" resultType="java.lang.Long">
        select count(1) from app_user_behavior
        where user_id = #{userId} and behavior_type = #{behaviorType}
    </select>

    <!-- 统计用户某种行为在指定时间范围内的次数 -->
    <select id="countByUserIdAndBehaviorTypeAndTimeRange" parameterType="map" resultType="java.lang.Long">
        select count(1) from app_user_behavior
        where user_id = #{userId} and behavior_type = #{behaviorType}
        <if test="startTime != null">
            and create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime}
        </if>
    </select>

    <!-- 查询用户连续登录天数 -->
    <select id="selectConsecutiveLoginDays" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT 
            CASE 
                WHEN COUNT(*) = 0 THEN 0
                ELSE (
                    SELECT COUNT(DISTINCT DATE(create_time))
                    FROM app_user_behavior b2
                    WHERE b2.user_id = #{userId} 
                    AND b2.behavior_type = 'LOGIN'
                    AND DATE(b2.create_time) >= (
                        SELECT DATE_SUB(CURDATE(), INTERVAL (
                            SELECT COUNT(DISTINCT DATE(create_time))
                            FROM app_user_behavior b3
                            WHERE b3.user_id = #{userId}
                            AND b3.behavior_type = 'LOGIN'
                            AND DATE(b3.create_time) >= (
                                SELECT MIN(consecutive_date)
                                FROM (
                                    SELECT DATE(create_time) as consecutive_date,
                                           ROW_NUMBER() OVER (ORDER BY DATE(create_time) DESC) as rn,
                                           DATE_SUB(DATE(create_time), INTERVAL ROW_NUMBER() OVER (ORDER BY DATE(create_time) DESC) DAY) as group_date
                                    FROM app_user_behavior
                                    WHERE user_id = #{userId} AND behavior_type = 'LOGIN'
                                    AND DATE(create_time) &lt;= CURDATE()
                                    GROUP BY DATE(create_time)
                                    ORDER BY DATE(create_time) DESC
                                ) t
                                WHERE group_date = (
                                    SELECT DATE_SUB(DATE(create_time), INTERVAL ROW_NUMBER() OVER (ORDER BY DATE(create_time) DESC) DAY)
                                    FROM app_user_behavior
                                    WHERE user_id = #{userId} AND behavior_type = 'LOGIN'
                                    AND DATE(create_time) = CURDATE()
                                    GROUP BY DATE(create_time)
                                    LIMIT 1
                                )
                            )
                        ) - 1 DAY)
                    )
                )
            END as consecutive_days
        FROM app_user_behavior
        WHERE user_id = #{userId} AND behavior_type = 'LOGIN'
    </select>

    <!-- 查询用户累计学习时长（分钟） -->
    <select id="selectTotalStudyMinutes" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT COALESCE(SUM(
            CASE 
                WHEN JSON_VALID(behavior_data) AND JSON_EXTRACT(behavior_data, '$.studyMinutes') IS NOT NULL 
                THEN CAST(JSON_EXTRACT(behavior_data, '$.studyMinutes') AS UNSIGNED)
                WHEN JSON_VALID(behavior_data) AND JSON_EXTRACT(behavior_data, '$.watchDuration') IS NOT NULL 
                THEN CAST(JSON_EXTRACT(behavior_data, '$.watchDuration') AS UNSIGNED) / 60
                ELSE 0
            END
        ), 0) as total_minutes
        FROM app_user_behavior
        WHERE user_id = #{userId} 
        AND behavior_type IN ('STUDY_TIME', 'VIDEO_WATCH')
    </select>

    <!-- 批量插入用户行为记录 -->
    <insert id="batchInsert" parameterType="list">
        insert into app_user_behavior(user_id, behavior_type, behavior_data, ip_address, user_agent, session_id, create_time)
        values
        <foreach collection="behaviors" item="behavior" separator=",">
            (#{behavior.userId}, #{behavior.behaviorType}, #{behavior.behaviorData}, 
             #{behavior.ipAddress}, #{behavior.userAgent}, #{behavior.sessionId}, #{behavior.createTime})
        </foreach>
    </insert>

</mapper>
