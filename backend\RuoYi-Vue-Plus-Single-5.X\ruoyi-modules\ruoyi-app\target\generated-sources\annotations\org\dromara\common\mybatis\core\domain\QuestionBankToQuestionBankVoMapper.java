package org.dromara.common.mybatis.core.domain;

import io.github.linpeilie.AutoMapperConfig__162;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.QuestionBankBoToQuestionBankMapper;
import org.dromara.system.domain.vo.QuestionBankVo;
import org.dromara.system.domain.vo.QuestionBankVoToQuestionBankMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__162.class,
    uses = {QuestionBankVoToQuestionBankMapper.class,QuestionBankBoToQuestionBankMapper.class},
    imports = {}
)
public interface QuestionBankToQuestionBankVoMapper extends BaseMapper<QuestionBank, QuestionBankVo> {
}
