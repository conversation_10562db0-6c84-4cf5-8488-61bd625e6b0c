{"doc": "\n 面试模块全局异常处理器\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleInterviewException", "paramTypes": ["org.dromara.app.exception.InterviewException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 面试业务异常\r\n"}, {"name": "handleJobNotFoundException", "paramTypes": ["org.dromara.app.exception.InterviewException.JobNotFoundException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 岗位不存在异常\r\n"}, {"name": "handleInterviewModeNotFoundException", "paramTypes": ["org.dromara.app.exception.InterviewException.InterviewModeNotFoundException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 面试模式不存在异常\r\n"}, {"name": "handleSessionExpiredException", "paramTypes": ["org.dromara.app.exception.InterviewException.SessionExpiredException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 会话已过期异常\r\n"}, {"name": "handleDeviceCheckFailedException", "paramTypes": ["org.dromara.app.exception.InterviewException.DeviceCheckFailedException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 设备检测失败异常\r\n"}, {"name": "handleUserNotLoginException", "paramTypes": ["org.dromara.app.exception.InterviewException.UserNotLoginException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 用户未登录异常\r\n"}], "constructors": []}