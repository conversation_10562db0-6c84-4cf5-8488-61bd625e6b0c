package org.dromara.app.controller.learning;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import org.dromara.app.domain.vo.RecommendationResponseVo;
import org.dromara.app.service.ILearningRecommendService;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 学习推荐控制器
 * 基于用户能力评估和学习历史，提供个性化的学习资源推荐
 *
 * @Author: SevenJL
 * @CreateTime: 2025-07-27
 * @Version: 1.0
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/learning/recommend")
public class LearningRecommendController extends BaseController {

    private final ILearningRecommendService learningRecommendService;

    /**
     * 获取推荐视频列表
     * 基于用户能力短板和学习偏好推荐视频课程
     *
     * @param pageNum     页码
     * @param pageSize    每页大小
     * @param searchQuery 搜索关键词（可选）
     * @return 推荐视频列表
     */
    @SaCheckLogin
    @GetMapping("/videos")
    public R<RecommendationResponseVo> getRecommendedVideos(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String searchQuery) {

        Long userId = StpUtil.getLoginIdAsLong();
        RecommendationResponseVo result = learningRecommendService.getRecommendedVideos(
                userId, pageNum, pageSize, searchQuery);
        return R.ok(result);
    }

    /**
     * 获取推荐题库列表
     * 基于用户能力短板和练习历史推荐题库
     *
     * @param pageNum     页码
     * @param pageSize    每页大小
     * @param searchQuery 搜索关键词（可选）
     * @return 推荐题库列表
     */
    @SaCheckLogin
    @GetMapping("/question-banks")
    public R<RecommendationResponseVo> getRecommendedQuestionBanks(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String searchQuery) {

        Long userId = StpUtil.getLoginIdAsLong();
        RecommendationResponseVo result = learningRecommendService.getRecommendedQuestionBanks(
                userId, pageNum, pageSize, searchQuery);
        return R.ok(result);
    }

    /**
     * 获取推荐书籍列表
     * 基于用户能力短板和阅读偏好推荐书籍
     *
     * @param pageNum     页码
     * @param pageSize    每页大小
     * @param searchQuery 搜索关键词（可选）
     * @return 推荐书籍列表
     */
    @SaCheckLogin
    @GetMapping("/books")
    public R<RecommendationResponseVo> getRecommendedBooks(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String searchQuery) {

        Long userId = StpUtil.getLoginIdAsLong();
        RecommendationResponseVo result = learningRecommendService.getRecommendedBooks(
                userId, pageNum, pageSize, searchQuery);
        return R.ok(result);
    }

    /**
     * 获取用户能力评估数据
     * 用于前端显示用户当前能力状况和薄弱环节
     *
     * @return 用户能力评估数据
     */
    @SaCheckLogin
    @GetMapping("/user-capabilities")
    public R<Map<String, Object>> getUserCapabilities() {
        Long userId = StpUtil.getLoginIdAsLong();
        Map<String, Object> capabilities = learningRecommendService.getUserCapabilities(userId);
        return R.ok(capabilities);
    }

    /**
     * 获取推荐统计信息
     * 包括推荐总数、各类型推荐数量等统计信息
     *
     * @return 推荐统计信息
     */
    @SaCheckLogin
    @GetMapping("/statistics")
    public R<Map<String, Object>> getRecommendationStatistics() {
        Long userId = StpUtil.getLoginIdAsLong();
        Map<String, Object> statistics = learningRecommendService.getRecommendationStatistics(userId);
        return R.ok(statistics);
    }

    /**
     * 刷新推荐算法
     * 基于用户最新的学习行为和能力评估重新计算推荐
     *
     * @return 刷新结果
     */
    @SaCheckLogin
    @PostMapping("/refresh")
    @Log(title = "刷新推荐算法", businessType = BusinessType.UPDATE)
    public R<String> refreshRecommendations() {
        Long userId = StpUtil.getLoginIdAsLong();
        learningRecommendService.refreshUserRecommendations(userId);
        return R.ok("推荐算法已刷新");
    }

    /**
     * 记录用户对推荐内容的反馈
     * 用于优化推荐算法
     *
     * @param resourceType 资源类型（video/question-bank/book）
     * @param resourceId   资源ID
     * @param action       用户行为（view/like/dislike/bookmark/start-learning）
     * @return 反馈记录结果
     */
    @SaCheckLogin
    @PostMapping("/feedback")
    @Log(title = "记录推荐反馈", businessType = BusinessType.INSERT)
    public R<String> recordRecommendationFeedback(
            @RequestParam String resourceType,
            @RequestParam Long resourceId,
            @RequestParam String action) {

        Long userId = StpUtil.getLoginIdAsLong();
        learningRecommendService.recordRecommendationFeedback(userId, resourceType, resourceId, action);
        return R.ok("反馈已记录");
    }
}
