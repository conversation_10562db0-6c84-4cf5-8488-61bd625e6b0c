{"doc": "\n 面试结果服务接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-17\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getResultSummary", "paramTypes": ["java.lang.String"], "doc": "\n 获取面试结果摘要\r\n\r\n @param resultId 结果ID或会话ID\r\n @return 面试结果摘要\r\n"}, {"name": "getResultDetail", "paramTypes": ["java.lang.String"], "doc": "\n 获取面试结果详情\r\n\r\n @param resultId 结果ID或会话ID\r\n @return 面试结果详情\r\n"}, {"name": "getPerformanceMetrics", "paramTypes": ["java.lang.String"], "doc": "\n 获取性能指标\r\n\r\n @param resultId 结果ID\r\n @return 性能指标数据\r\n"}, {"name": "saveToHistory", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 保存到历史记录\r\n\r\n @param resultId 结果ID\r\n @param title    自定义标题（可选）\r\n @return 保存结果\r\n"}, {"name": "shareResult", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 分享结果\r\n\r\n @param resultId 结果ID\r\n @param platform 分享平台\r\n @param content  分享内容（可选）\r\n @return 分享结果\r\n"}, {"name": "getImprovementPlan", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 获取提升计划\r\n\r\n @param resultId 结果ID\r\n @param userId   用户ID（可选）\r\n @return 提升计划\r\n"}, {"name": "getLearningResources", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取学习资源推荐\r\n\r\n @param resultId 结果ID\r\n @param limit    数量限制\r\n @return 学习资源列表\r\n"}, {"name": "createInterviewResult", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 创建面试结果\r\n\r\n @param sessionId 会话ID\r\n @param resultData 结果数据\r\n @return 结果ID\r\n"}, {"name": "updateInterviewResult", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 更新面试结果\r\n\r\n @param resultId 结果ID\r\n @param resultData 结果数据\r\n @return 是否成功\r\n"}, {"name": "deleteInterviewResult", "paramTypes": ["java.lang.String"], "doc": "\n 删除面试结果\r\n\r\n @param resultId 结果ID\r\n @return 是否成功\r\n"}, {"name": "getUserResults", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 获取用户面试结果列表\r\n\r\n @param userId 用户ID\r\n @param limit  限制数量\r\n @return 面试结果列表\r\n"}, {"name": "getUserStatistics", "paramTypes": ["java.lang.Long"], "doc": "\n 获取用户面试统计\r\n\r\n @param userId 用户ID\r\n @return 统计数据\r\n"}, {"name": "generateReport", "paramTypes": ["java.lang.String"], "doc": "\n 生成面试报告\r\n\r\n @param resultId 结果ID\r\n @return 报告内容\r\n"}], "constructors": []}