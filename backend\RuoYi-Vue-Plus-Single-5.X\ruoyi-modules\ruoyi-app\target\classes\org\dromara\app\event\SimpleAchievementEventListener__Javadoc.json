{"doc": "\n 简化版成就事件监听器\r\n 专注于核心的成就检查功能\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleUserLoginEvent", "paramTypes": ["org.dromara.app.event.SimpleAchievementEventListener.UserLoginEvent"], "doc": "\n 监听用户登录事件\r\n"}, {"name": "handleVideoWatchEvent", "paramTypes": ["org.dromara.app.event.SimpleAchievementEventListener.VideoWatchEvent"], "doc": "\n 监听视频观看事件\r\n"}, {"name": "handleCommentEvent", "paramTypes": ["org.dromara.app.event.SimpleAchievementEventListener.CommentEvent"], "doc": "\n 监听评论事件\r\n"}, {"name": "handleLikeEvent", "paramTypes": ["org.dromara.app.event.SimpleAchievementEventListener.LikeEvent"], "doc": "\n 监听点赞事件\r\n"}, {"name": "handleStudyTimeEvent", "paramTypes": ["org.dromara.app.event.SimpleAchievementEventListener.StudyTimeEvent"], "doc": "\n 监听学习时长事件\r\n"}], "constructors": []}