{"doc": " 支付服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "createPaymentOrder", "paramTypes": ["org.dromara.app.domain.dto.PaymentOrderDto"], "doc": " 创建支付订单\n\n @param paymentOrderDto 支付订单请求参数\n @return 支付订单信息\n"}, {"name": "alipayPay", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 支付宝支付\n\n @param orderNo  订单号\n @param payToken 支付token\n @return 支付页面表单HTML\n"}, {"name": "alipayNotify", "paramTypes": ["jakarta.servlet.http.HttpServletRequest"], "doc": " 支付宝异步通知处理\n\n @param request HTTP请求\n @return 处理结果\n"}, {"name": "alipayReturn", "paramTypes": ["jakarta.servlet.http.HttpServletRequest"], "doc": " 支付宝同步回调处理\n\n @param request HTTP请求\n @return 处理结果\n"}, {"name": "queryOrderStatus", "paramTypes": ["java.lang.String"], "doc": " 查询订单状态\n\n @param orderNo 订单号\n @return 订单信息\n"}, {"name": "cancelOrder", "paramTypes": ["java.lang.String"], "doc": " 取消订单\n\n @param orderNo 订单号\n @return 取消结果\n"}, {"name": "validatePayToken", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 验证支付token\n\n @param orderNo  订单号\n @param payToken 支付token\n @return 验证结果\n"}, {"name": "markPayTokenAsUsed", "paramTypes": ["java.lang.String"], "doc": " 标记支付token为已使用\n\n @param orderNo 订单号\n"}, {"name": "handlePaymentTimeout", "paramTypes": ["java.lang.String"], "doc": " 处理支付超时\n\n @param orderNo 订单号\n"}], "constructors": []}