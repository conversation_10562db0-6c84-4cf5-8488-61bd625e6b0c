package org.dromara.app.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 视频购买状态视图对象
 *
 * <AUTHOR>
 */
@Data
public class VideoPurchaseStatusVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 视频ID
     */
    private Long videoId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 是否已购买
     */
    private Boolean isPurchased;

    /**
     * 是否免费
     */
    private Boolean free;

    /**
     * 购买时间
     */
    private LocalDateTime purchaseTime;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 购买金额
     */
    private BigDecimal amount;
}
