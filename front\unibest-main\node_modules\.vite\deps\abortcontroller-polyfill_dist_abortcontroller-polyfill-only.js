// node_modules/abortcontroller-polyfill/dist/abortcontroller-polyfill-only.js
(function(factory) {
  typeof define === "function" && define.amd ? define(factory) : factory();
})(function() {
  "use strict";
  function _arrayLikeToArray(r, a) {
    (null == a || a > r.length) && (a = r.length);
    for (var e = 0, n = Array(a); e < a; e++)
      n[e] = r[e];
    return n;
  }
  function _assertThisInitialized(e) {
    if (void 0 === e)
      throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
    return e;
  }
  function _callSuper(t, o, e) {
    return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));
  }
  function _classCallCheck(a, n) {
    if (!(a instanceof n))
      throw new TypeError("Cannot call a class as a function");
  }
  function _defineProperties(e, r) {
    for (var t = 0; t < r.length; t++) {
      var o = r[t];
      o.enumerable = o.enumerable || false, o.configurable = true, "value" in o && (o.writable = true), Object.defineProperty(e, _toPropertyKey(o.key), o);
    }
  }
  function _createClass(e, r, t) {
    return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", {
      writable: false
    }), e;
  }
  function _createForOfIteratorHelper(r, e) {
    var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"];
    if (!t) {
      if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) {
        t && (r = t);
        var n = 0, F = function() {
        };
        return {
          s: F,
          n: function() {
            return n >= r.length ? {
              done: true
            } : {
              done: false,
              value: r[n++]
            };
          },
          e: function(r2) {
            throw r2;
          },
          f: F
        };
      }
      throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
    }
    var o, a = true, u = false;
    return {
      s: function() {
        t = t.call(r);
      },
      n: function() {
        var r2 = t.next();
        return a = r2.done, r2;
      },
      e: function(r2) {
        u = true, o = r2;
      },
      f: function() {
        try {
          a || null == t.return || t.return();
        } finally {
          if (u)
            throw o;
        }
      }
    };
  }
  function _get() {
    return _get = "undefined" != typeof Reflect && Reflect.get ? Reflect.get.bind() : function(e, t, r) {
      var p = _superPropBase(e, t);
      if (p) {
        var n = Object.getOwnPropertyDescriptor(p, t);
        return n.get ? n.get.call(arguments.length < 3 ? e : r) : n.value;
      }
    }, _get.apply(null, arguments);
  }
  function _getPrototypeOf(t) {
    return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(t2) {
      return t2.__proto__ || Object.getPrototypeOf(t2);
    }, _getPrototypeOf(t);
  }
  function _inherits(t, e) {
    if ("function" != typeof e && null !== e)
      throw new TypeError("Super expression must either be null or a function");
    t.prototype = Object.create(e && e.prototype, {
      constructor: {
        value: t,
        writable: true,
        configurable: true
      }
    }), Object.defineProperty(t, "prototype", {
      writable: false
    }), e && _setPrototypeOf(t, e);
  }
  function _isNativeReflectConstruct() {
    try {
      var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
      }));
    } catch (t2) {
    }
    return (_isNativeReflectConstruct = function() {
      return !!t;
    })();
  }
  function _possibleConstructorReturn(t, e) {
    if (e && ("object" == typeof e || "function" == typeof e))
      return e;
    if (void 0 !== e)
      throw new TypeError("Derived constructors may only return object or undefined");
    return _assertThisInitialized(t);
  }
  function _setPrototypeOf(t, e) {
    return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(t2, e2) {
      return t2.__proto__ = e2, t2;
    }, _setPrototypeOf(t, e);
  }
  function _superPropBase(t, o) {
    for (; !{}.hasOwnProperty.call(t, o) && null !== (t = _getPrototypeOf(t)); )
      ;
    return t;
  }
  function _superPropGet(t, o, e, r) {
    var p = _get(_getPrototypeOf(1 & r ? t.prototype : t), o, e);
    return 2 & r && "function" == typeof p ? function(t2) {
      return p.apply(e, t2);
    } : p;
  }
  function _toPrimitive(t, r) {
    if ("object" != typeof t || !t)
      return t;
    var e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
      var i = e.call(t, r || "default");
      if ("object" != typeof i)
        return i;
      throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return ("string" === r ? String : Number)(t);
  }
  function _toPropertyKey(t) {
    var i = _toPrimitive(t, "string");
    return "symbol" == typeof i ? i : i + "";
  }
  function _unsupportedIterableToArray(r, a) {
    if (r) {
      if ("string" == typeof r)
        return _arrayLikeToArray(r, a);
      var t = {}.toString.call(r).slice(8, -1);
      return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;
    }
  }
  (function(self2) {
    return {
      NativeAbortSignal: self2.AbortSignal,
      NativeAbortController: self2.AbortController
    };
  })(typeof self !== "undefined" ? self : global);
  function createAbortEvent(reason) {
    var event;
    try {
      event = new Event("abort");
    } catch (e) {
      if (typeof document !== "undefined") {
        if (!document.createEvent) {
          event = document.createEventObject();
          event.type = "abort";
        } else {
          event = document.createEvent("Event");
          event.initEvent("abort", false, false);
        }
      } else {
        event = {
          type: "abort",
          bubbles: false,
          cancelable: false
        };
      }
    }
    event.reason = reason;
    return event;
  }
  function normalizeAbortReason(reason) {
    if (reason === void 0) {
      if (typeof document === "undefined") {
        reason = new Error("This operation was aborted");
        reason.name = "AbortError";
      } else {
        try {
          reason = new DOMException("signal is aborted without reason");
          Object.defineProperty(reason, "name", {
            value: "AbortError"
          });
        } catch (err) {
          reason = new Error("This operation was aborted");
          reason.name = "AbortError";
        }
      }
    }
    return reason;
  }
  var Emitter = function() {
    function Emitter2() {
      _classCallCheck(this, Emitter2);
      Object.defineProperty(this, "listeners", {
        value: {},
        writable: true,
        configurable: true
      });
    }
    return _createClass(Emitter2, [{
      key: "addEventListener",
      value: function addEventListener(type, callback, options) {
        if (!(type in this.listeners)) {
          this.listeners[type] = [];
        }
        this.listeners[type].push({
          callback,
          options
        });
      }
    }, {
      key: "removeEventListener",
      value: function removeEventListener(type, callback) {
        if (!(type in this.listeners)) {
          return;
        }
        var stack = this.listeners[type];
        for (var i = 0, l = stack.length; i < l; i++) {
          if (stack[i].callback === callback) {
            stack.splice(i, 1);
            return;
          }
        }
      }
    }, {
      key: "dispatchEvent",
      value: function dispatchEvent(event) {
        var _this = this;
        if (!(event.type in this.listeners)) {
          return;
        }
        var stack = this.listeners[event.type];
        var stackToCall = stack.slice();
        var _loop = function _loop2() {
          var listener = stackToCall[i];
          try {
            listener.callback.call(_this, event);
          } catch (e) {
            Promise.resolve().then(function() {
              throw e;
            });
          }
          if (listener.options && listener.options.once) {
            _this.removeEventListener(event.type, listener.callback);
          }
        };
        for (var i = 0, l = stackToCall.length; i < l; i++) {
          _loop();
        }
        return !event.defaultPrevented;
      }
    }]);
  }();
  var AbortSignal = function(_Emitter) {
    function AbortSignal2() {
      var _this2;
      _classCallCheck(this, AbortSignal2);
      _this2 = _callSuper(this, AbortSignal2);
      if (!_this2.listeners) {
        Emitter.call(_this2);
      }
      Object.defineProperty(_this2, "aborted", {
        value: false,
        writable: true,
        configurable: true
      });
      Object.defineProperty(_this2, "onabort", {
        value: null,
        writable: true,
        configurable: true
      });
      Object.defineProperty(_this2, "reason", {
        value: void 0,
        writable: true,
        configurable: true
      });
      return _this2;
    }
    _inherits(AbortSignal2, _Emitter);
    return _createClass(AbortSignal2, [{
      key: "toString",
      value: function toString() {
        return "[object AbortSignal]";
      }
    }, {
      key: "dispatchEvent",
      value: function dispatchEvent(event) {
        if (event.type === "abort") {
          this.aborted = true;
          if (typeof this.onabort === "function") {
            this.onabort.call(this, event);
          }
        }
        _superPropGet(AbortSignal2, "dispatchEvent", this, 3)([event]);
      }
      /**
       * @see {@link https://developer.mozilla.org/zh-CN/docs/Web/API/AbortSignal/throwIfAborted}
       */
    }, {
      key: "throwIfAborted",
      value: function throwIfAborted() {
        var aborted = this.aborted, _this$reason = this.reason, reason = _this$reason === void 0 ? "Aborted" : _this$reason;
        if (!aborted)
          return;
        throw reason;
      }
      /**
       * @see {@link https://developer.mozilla.org/zh-CN/docs/Web/API/AbortSignal/timeout_static}
       * @param {number} time The "active" time in milliseconds before the returned {@link AbortSignal} will abort.
       *                      The value must be within range of 0 and {@link Number.MAX_SAFE_INTEGER}.
       * @returns {AbortSignal} The signal will abort with its {@link AbortSignal.reason} property set to a `TimeoutError` {@link DOMException} on timeout,
       *                        or an `AbortError` {@link DOMException} if the operation was user-triggered.
       */
    }], [{
      key: "timeout",
      value: function timeout(time) {
        var controller = new AbortController();
        setTimeout(function() {
          return controller.abort(new DOMException("This signal is timeout in ".concat(time, "ms"), "TimeoutError"));
        }, time);
        return controller.signal;
      }
      /**
       * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/any_static}
       * @param {Iterable<AbortSignal>} iterable An {@link Iterable} (such as an {@link Array}) of abort signals.
       * @returns {AbortSignal} - **Already aborted**, if any of the abort signals given is already aborted.
       *                          The returned {@link AbortSignal}'s reason will be already set to the `reason` of the first abort signal that was already aborted.
       *                        - **Asynchronously aborted**, when any abort signal in `iterable` aborts.
       *                          The `reason` will be set to the reason of the first abort signal that is aborted.
       */
    }, {
      key: "any",
      value: function any(iterable) {
        var controller = new AbortController();
        function abort() {
          controller.abort(this.reason);
          clean();
        }
        function clean() {
          var _iterator = _createForOfIteratorHelper(iterable), _step;
          try {
            for (_iterator.s(); !(_step = _iterator.n()).done; ) {
              var signal2 = _step.value;
              signal2.removeEventListener("abort", abort);
            }
          } catch (err) {
            _iterator.e(err);
          } finally {
            _iterator.f();
          }
        }
        var _iterator2 = _createForOfIteratorHelper(iterable), _step2;
        try {
          for (_iterator2.s(); !(_step2 = _iterator2.n()).done; ) {
            var signal = _step2.value;
            if (signal.aborted) {
              controller.abort(signal.reason);
              break;
            } else
              signal.addEventListener("abort", abort);
          }
        } catch (err) {
          _iterator2.e(err);
        } finally {
          _iterator2.f();
        }
        return controller.signal;
      }
    }]);
  }(Emitter);
  var AbortController = function() {
    function AbortController2() {
      _classCallCheck(this, AbortController2);
      Object.defineProperty(this, "signal", {
        value: new AbortSignal(),
        writable: true,
        configurable: true
      });
    }
    return _createClass(AbortController2, [{
      key: "abort",
      value: function abort(reason) {
        var signalReason = normalizeAbortReason(reason);
        var event = createAbortEvent(signalReason);
        this.signal.reason = signalReason;
        this.signal.dispatchEvent(event);
      }
    }, {
      key: "toString",
      value: function toString() {
        return "[object AbortController]";
      }
    }]);
  }();
  if (typeof Symbol !== "undefined" && Symbol.toStringTag) {
    AbortController.prototype[Symbol.toStringTag] = "AbortController";
    AbortSignal.prototype[Symbol.toStringTag] = "AbortSignal";
  }
  function polyfillNeeded(self2) {
    if (self2.__FORCE_INSTALL_ABORTCONTROLLER_POLYFILL) {
      console.log("__FORCE_INSTALL_ABORTCONTROLLER_POLYFILL=true is set, will force install polyfill");
      return true;
    }
    return typeof self2.Request === "function" && !self2.Request.prototype.hasOwnProperty("signal") || !self2.AbortController;
  }
  (function(self2) {
    if (!polyfillNeeded(self2)) {
      return;
    }
    self2.AbortController = AbortController;
    self2.AbortSignal = AbortSignal;
  })(typeof self !== "undefined" ? self : global);
});
//# sourceMappingURL=abortcontroller-polyfill_dist_abortcontroller-polyfill-only.js.map
