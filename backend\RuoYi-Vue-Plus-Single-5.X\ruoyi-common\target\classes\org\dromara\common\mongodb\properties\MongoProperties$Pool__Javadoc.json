{"doc": " 连接池配置\n", "fields": [{"name": "maxSize", "doc": " 最大连接数\n"}, {"name": "minSize", "doc": " 最小连接数\n"}, {"name": "maxConnectionTimeoutMs", "doc": " 连接超时时间(毫秒)\n"}, {"name": "maxReadTimeoutMs", "doc": " 读取超时时间(毫秒)\n"}, {"name": "maxWaitTimeMs", "doc": " 最大等待时间(毫秒)\n"}, {"name": "maxConnectionIdleTimeMs", "doc": " 连接最大空闲时间(毫秒)\n"}, {"name": "maxConnectionLifeTimeMs", "doc": " 连接最大生存时间(毫秒)\n"}], "enumConstants": [], "methods": [], "constructors": []}