{"doc": " 知识库视图对象 app_knowledge_base\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 知识库ID\n"}, {"name": "name", "doc": " 知识库名称\n"}, {"name": "description", "doc": " 知识库描述\n"}, {"name": "type", "doc": " 知识库类型 (general/technical/business/etc.)\n"}, {"name": "status", "doc": " 知识库状态 (0=禁用 1=启用)\n"}, {"name": "vectorDimension", "doc": " 向量维度 (默认1024)\n"}, {"name": "documentCount", "doc": " 文档数量\n"}, {"name": "vectorCount", "doc": " 向量数量\n"}, {"name": "indexConfig", "doc": " 索引配置 (JSON格式)\n"}, {"name": "extendConfig", "doc": " 扩展配置 (JSON格式)\n"}, {"name": "lastSyncTime", "doc": " 最后更新时间\n"}, {"name": "sortOrder", "doc": " 排序字段\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "createDept", "doc": " 创建部门\n"}, {"name": "createBy", "doc": " 创建者\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateBy", "doc": " 更新者\n"}, {"name": "updateTime", "doc": " 更新时间\n"}, {"name": "typeName", "doc": " 知识库类型名称（用于显示）\n"}, {"name": "statusName", "doc": " 状态名称（用于显示）\n"}, {"name": "createByName", "doc": " 创建者名称\n"}, {"name": "updateByName", "doc": " 更新者名称\n"}, {"name": "deptName", "doc": " 部门名称\n"}, {"name": "avgSimilarity", "doc": " 平均向量相似度（统计信息）\n"}, {"name": "recentProcessedCount", "doc": " 最近处理文档数量\n"}, {"name": "indexHealth", "doc": " 索引健康状态\n"}], "enumConstants": [], "methods": [], "constructors": []}