package org.dromara.common.pay.core;

import com.alipay.api.AlipayClient;
import com.alipay.api.response.AlipayTradeQueryResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.pay.constant.PayConstants;
import org.dromara.common.pay.enums.PaymentMethod;
import org.dromara.common.pay.enums.PaymentStatus;
import org.dromara.common.pay.exception.PaymentException;
import org.dromara.common.pay.properties.AlipayProperties;
import org.dromara.common.pay.utils.AlipayUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 支付核心服务类
 * 提供统一的支付服务接口，支持多种支付方式
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnBean(AlipayClient.class)
public class PaymentService {

    private final AlipayClient alipayClient;
    private final AlipayProperties alipayProperties;

    /**
     * 创建支付订单
     *
     * @param paymentMethod 支付方式
     * @param orderNo       订单号
     * @param subject       商品标题
     * @param totalAmount   支付金额（元）
     * @param body          商品描述
     * @return 支付结果（支付宝返回HTML表单，微信返回支付参数等）
     */
    public String createPayment(PaymentMethod paymentMethod,
                                String orderNo,
                                String subject,
                                BigDecimal totalAmount,
                                String body) {

        log.info("创建支付订单，支付方式：{}，订单号：{}，金额：{}",
            paymentMethod.getName(), orderNo, totalAmount);

        // 验证参数
        validatePaymentParams(paymentMethod, orderNo, subject, totalAmount);

        switch (paymentMethod) {
            case ALIPAY:
                return createAlipayPayment(orderNo, subject, totalAmount, body);
            case WECHAT:
                throw new PaymentException("WECHAT_NOT_IMPLEMENTED", "微信支付功能暂未实现");
            default:
                throw new PaymentException("UNSUPPORTED_PAYMENT_METHOD", "不支持的支付方式：" + paymentMethod.getName());
        }
    }

    /**
     * 查询支付订单状态
     *
     * @param paymentMethod 支付方式
     * @param orderNo       订单号
     * @return 支付状态
     */
    public PaymentStatus queryPaymentStatus(PaymentMethod paymentMethod, String orderNo) {

        log.info("查询支付状态，支付方式：{}，订单号：{}", paymentMethod.getName(), orderNo);

        if (!StringUtils.hasText(orderNo)) {
            throw new PaymentException("INVALID_ORDER_NO", "订单号不能为空");
        }

        switch (paymentMethod) {
            case ALIPAY:
                return queryAlipayStatus(orderNo);
            case WECHAT:
                throw new PaymentException("WECHAT_NOT_IMPLEMENTED", "微信支付查询功能暂未实现");
            default:
                throw new PaymentException("UNSUPPORTED_PAYMENT_METHOD", "不支持的支付方式：" + paymentMethod.getName());
        }
    }

    /**
     * 验证支付异步通知
     *
     * @param paymentMethod 支付方式
     * @param params        通知参数
     * @return 通知结果
     */
    public NotifyResult verifyPaymentNotify(PaymentMethod paymentMethod, Map<String, String> params) {

        log.info("验证支付异步通知，支付方式：{}", paymentMethod.getName());

        switch (paymentMethod) {
            case ALIPAY:
                return verifyAlipayNotify(params);
            case WECHAT:
                throw new PaymentException("WECHAT_NOT_IMPLEMENTED", "微信支付通知验证功能暂未实现");
            default:
                throw new PaymentException("UNSUPPORTED_PAYMENT_METHOD", "不支持的支付方式：" + paymentMethod.getName());
        }
    }

    /**
     * 验证支付同步回调
     *
     * @param paymentMethod 支付方式
     * @param params        回调参数
     * @return 回调结果
     */
    public CallbackResult verifyPaymentCallback(PaymentMethod paymentMethod, Map<String, String> params) {

        log.info("验证支付同步回调，支付方式：{}", paymentMethod.getName());

        switch (paymentMethod) {
            case ALIPAY:
                return verifyAlipayCallback(params);
            case WECHAT:
                throw new PaymentException("WECHAT_NOT_IMPLEMENTED", "微信支付回调验证功能暂未实现");
            default:
                throw new PaymentException("UNSUPPORTED_PAYMENT_METHOD", "不支持的支付方式：" + paymentMethod.getName());
        }
    }

    /**
     * 创建支付宝支付
     */
    private String createAlipayPayment(String orderNo, String subject, BigDecimal totalAmount, String body) {
        try {
            return AlipayUtils.createPayPage(alipayClient, alipayProperties, orderNo, subject, totalAmount, body);
        } catch (Exception e) {
            log.error("创建支付宝支付失败，订单号：{}", orderNo, e);
            throw new PaymentException("ALIPAY_CREATE_ERROR", "创建支付宝支付失败：" + e.getMessage(), e);
        }
    }

    /**
     * 查询支付宝支付状态
     */
    private PaymentStatus queryAlipayStatus(String orderNo) {
        try {
            AlipayTradeQueryResponse response = AlipayUtils.queryTradeStatus(alipayClient, orderNo);

            if (response.isSuccess()) {
                String tradeStatus = response.getTradeStatus();
                String statusCode = AlipayUtils.convertTradeStatus(tradeStatus);
                return PaymentStatus.fromCode(statusCode);
            } else {
                log.warn("支付宝订单状态查询失败，订单号：{}，错误码：{}，错误信息：{}",
                    orderNo, response.getCode(), response.getMsg());
                return PaymentStatus.UNPAID;
            }
        } catch (Exception e) {
            log.error("查询支付宝订单状态异常，订单号：{}", orderNo, e);
            throw new PaymentException("ALIPAY_QUERY_ERROR", "查询支付宝订单状态失败：" + e.getMessage(), e);
        }
    }

    /**
     * 验证支付宝异步通知
     */
    private NotifyResult verifyAlipayNotify(Map<String, String> params) {
        try {
            // 验证签名
            boolean signVerified = AlipayUtils.verifyNotify(params,
                alipayProperties.getAlipayPublicKey(),
                alipayProperties.getCharset(),
                alipayProperties.getSignType());

            if (!signVerified) {
                log.warn("支付宝异步通知签名验证失败");
                return NotifyResult.failure("签名验证失败");
            }

            String tradeStatus = params.get("trade_status");
            String outTradeNo = params.get("out_trade_no");
            String tradeNo = params.get("trade_no");
            String totalAmount = params.get("total_amount");

            log.info("支付宝异步通知验证成功，订单号：{}，交易号：{}，交易状态：{}，金额：{}",
                outTradeNo, tradeNo, tradeStatus, totalAmount);

            String statusCode = AlipayUtils.convertTradeStatus(tradeStatus);
            PaymentStatus paymentStatus = PaymentStatus.fromCode(statusCode);

            return NotifyResult.success()
                .setOrderNo(outTradeNo)
                .setTradeNo(tradeNo)
                .setPaymentStatus(paymentStatus)
                .setTotalAmount(new BigDecimal(totalAmount))
                .setTradeTime(LocalDateTime.now());

        } catch (Exception e) {
            log.error("支付宝异步通知验证异常", e);
            return NotifyResult.failure("通知验证异常：" + e.getMessage());
        }
    }

    /**
     * 验证支付宝同步回调
     */
    private CallbackResult verifyAlipayCallback(Map<String, String> params) {
        try {
            // 验证签名
            boolean signVerified = AlipayUtils.verifyReturn(params,
                alipayProperties.getAlipayPublicKey(),
                alipayProperties.getCharset(),
                alipayProperties.getSignType());

            if (!signVerified) {
                log.warn("支付宝同步回调签名验证失败");
                return CallbackResult.failure("签名验证失败");
            }

            String outTradeNo = params.get("out_trade_no");
            String tradeNo = params.get("trade_no");
            String totalAmount = params.get("total_amount");

            log.info("支付宝同步回调验证成功，订单号：{}，交易号：{}，金额：{}",
                outTradeNo, tradeNo, totalAmount);

            return CallbackResult.success()
                .setOrderNo(outTradeNo)
                .setTradeNo(tradeNo)
                .setTotalAmount(new BigDecimal(totalAmount));

        } catch (Exception e) {
            log.error("支付宝同步回调验证异常", e);
            return CallbackResult.failure("回调验证异常：" + e.getMessage());
        }
    }

    /**
     * 验证支付参数
     */
    private void validatePaymentParams(PaymentMethod paymentMethod, String orderNo, String subject, BigDecimal totalAmount) {
        if (paymentMethod == null) {
            throw new PaymentException("INVALID_PAYMENT_METHOD", "支付方式不能为空");
        }

        if (!StringUtils.hasText(orderNo)) {
            throw new PaymentException("INVALID_ORDER_NO", "订单号不能为空");
        }

        if (!StringUtils.hasText(subject)) {
            throw new PaymentException("INVALID_SUBJECT", "商品标题不能为空");
        }

        if (totalAmount == null || totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new PaymentException("INVALID_AMOUNT", "支付金额必须大于0");
        }

        // 验证金额范围
        AlipayUtils.validateAmount(totalAmount);
    }

    /**
     * 异步通知结果
     */
    public static class NotifyResult {
        private boolean success;
        private String message;
        private String orderNo;
        private String tradeNo;
        private PaymentStatus paymentStatus;
        private BigDecimal totalAmount;
        private LocalDateTime tradeTime;

        public static NotifyResult success() {
            NotifyResult result = new NotifyResult();
            result.success = true;
            result.message = PayConstants.Alipay.NOTIFY_VERIFY_SUCCESS;
            return result;
        }

        public static NotifyResult failure(String message) {
            NotifyResult result = new NotifyResult();
            result.success = false;
            result.message = message;
            return result;
        }

        // Getters and Setters
        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }

        public String getOrderNo() {
            return orderNo;
        }

        public NotifyResult setOrderNo(String orderNo) {
            this.orderNo = orderNo;
            return this;
        }

        public String getTradeNo() {
            return tradeNo;
        }

        public NotifyResult setTradeNo(String tradeNo) {
            this.tradeNo = tradeNo;
            return this;
        }

        public PaymentStatus getPaymentStatus() {
            return paymentStatus;
        }

        public NotifyResult setPaymentStatus(PaymentStatus paymentStatus) {
            this.paymentStatus = paymentStatus;
            return this;
        }

        public BigDecimal getTotalAmount() {
            return totalAmount;
        }

        public NotifyResult setTotalAmount(BigDecimal totalAmount) {
            this.totalAmount = totalAmount;
            return this;
        }

        public LocalDateTime getTradeTime() {
            return tradeTime;
        }

        public NotifyResult setTradeTime(LocalDateTime tradeTime) {
            this.tradeTime = tradeTime;
            return this;
        }
    }

    /**
     * 同步回调结果
     */
    public static class CallbackResult {
        private boolean success;
        private String message;
        private String orderNo;
        private String tradeNo;
        private BigDecimal totalAmount;

        public static CallbackResult success() {
            CallbackResult result = new CallbackResult();
            result.success = true;
            result.message = "回调验证成功";
            return result;
        }

        public static CallbackResult failure(String message) {
            CallbackResult result = new CallbackResult();
            result.success = false;
            result.message = message;
            return result;
        }

        // Getters and Setters
        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }

        public String getOrderNo() {
            return orderNo;
        }

        public CallbackResult setOrderNo(String orderNo) {
            this.orderNo = orderNo;
            return this;
        }

        public String getTradeNo() {
            return tradeNo;
        }

        public CallbackResult setTradeNo(String tradeNo) {
            this.tradeNo = tradeNo;
            return this;
        }

        public BigDecimal getTotalAmount() {
            return totalAmount;
        }

        public CallbackResult setTotalAmount(BigDecimal totalAmount) {
            this.totalAmount = totalAmount;
            return this;
        }
    }
}
