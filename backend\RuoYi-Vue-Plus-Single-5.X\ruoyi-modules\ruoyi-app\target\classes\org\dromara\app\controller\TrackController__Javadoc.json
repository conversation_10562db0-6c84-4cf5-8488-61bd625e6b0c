{"doc": "\n 埋点控制器(用于成就检测)\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "trackEvent", "paramTypes": ["org.dromara.app.domain.dto.TrackEventDto", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 记录用户行为埋点\r\n"}, {"name": "trackEvents", "paramTypes": ["java.util.List", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 批量记录用户行为埋点\r\n"}, {"name": "getClientIP", "paramTypes": ["jakarta.servlet.http.HttpServletRequest"], "doc": "\n 获取客户端IP地址\r\n"}], "constructors": []}