{"doc": "\n 聊天内存管理器\r\n 负责管理聊天会话的内存，防止内存泄露\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "MEMORY_EXPIRE_HOURS", "doc": "\n 内存过期时间（小时）\r\n"}, {"name": "MAX_MEMORY_COUNT", "doc": "\n 最大内存数量\r\n"}, {"name": "CLEANUP_INTERVAL_MINUTES", "doc": "\n 清理任务执行间隔（分钟）\r\n"}, {"name": "memoryCache", "doc": "\n 内存缓存，存储会话ID到内存的映射\r\n"}, {"name": "scheduler", "doc": "\n 定时清理任务执行器\r\n"}], "enumConstants": [], "methods": [{"name": "getOrCreateMemory", "paramTypes": ["java.lang.String"], "doc": "\n 获取或创建会话内存\r\n\r\n @param sessionId 会话ID\r\n @return 聊天内存\r\n"}, {"name": "getOrCreateMemory", "paramTypes": ["java.lang.String", "int"], "doc": "\n 获取或创建会话内存\r\n\r\n @param sessionId   会话ID\r\n @param maxMessages 最大消息数\r\n @return 聊天内存\r\n"}, {"name": "remove<PERSON><PERSON>ory", "paramTypes": ["java.lang.String"], "doc": "\n 移除会话内存\r\n\r\n @param sessionId 会话ID\r\n"}, {"name": "cleanExpiredMemory", "paramTypes": [], "doc": "\n 清理过期内存\r\n"}, {"name": "cleanOldestMemory", "paramTypes": [], "doc": "\n 清理最旧的内存（当内存数量超限时）\r\n"}, {"name": "getStats", "paramTypes": [], "doc": "\n 获取内存统计信息\r\n\r\n @return 统计信息\r\n"}], "constructors": []}