{"doc": "\n 文本嵌入服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "generateEmbedding", "paramTypes": ["java.lang.String"], "doc": "\n 生成文本向量\r\n\r\n @param text 文本内容\r\n @return 向量数组\r\n"}, {"name": "generateEmbeddings", "paramTypes": ["java.util.List"], "doc": "\n 批量生成文本向量\r\n\r\n @param texts 文本列表\r\n @return 向量列表\r\n"}, {"name": "calculateSimilarity", "paramTypes": ["float[]", "float[]"], "doc": "\n 计算向量相似度\r\n\r\n @param vector1 向量1\r\n @param vector2 向量2\r\n @return 相似度分数 (0-1)\r\n"}, {"name": "getVectorDimension", "paramTypes": [], "doc": "\n 获取向量维度\r\n\r\n @return 向量维度\r\n"}, {"name": "getModelName", "paramTypes": [], "doc": "\n 获取嵌入模型名称\r\n\r\n @return 模型名称\r\n"}, {"name": "isAvailable", "paramTypes": [], "doc": "\n 检查嵌入服务是否可用\r\n\r\n @return 是否可用\r\n"}], "constructors": []}