package org.dromara.common.pay.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;

/**
 * 支付组件自动配置类
 *
 * <AUTHOR>
 */
@Slf4j
@AutoConfiguration
@ComponentScan("org.dromara.common.pay")
@ConditionalOnProperty(prefix = "pay", name = "enabled", havingValue = "true", matchIfMissing = false)
public class PayAutoConfiguration {

    public PayAutoConfiguration() {
        log.info("支付组件自动配置已启用");
    }
}
