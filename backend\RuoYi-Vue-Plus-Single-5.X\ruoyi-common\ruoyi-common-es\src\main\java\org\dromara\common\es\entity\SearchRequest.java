package org.dromara.common.es.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * ES搜索请求
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SearchRequest {

    /**
     * 索引名称
     */
    private String indexName;

    /**
     * 查询关键字
     */
    private String keyword;

    /**
     * 查询字段
     */
    private List<String> fields;

    /**
     * 过滤条件
     */
    private Map<String, Object> filters;

    /**
     * 排序字段
     */
    private List<SortField> sorts;

    /**
     * 高亮字段
     */
    private List<String> highlightFields;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;

    /**
     * 是否返回源数据
     */
    private Boolean fetchSource = true;

    /**
     * 包含字段
     */
    private List<String> includeFields;

    /**
     * 排除字段
     */
    private List<String> excludeFields;

    /**
     * 聚合配置
     */
    private List<AggregationConfig> aggregations;

    /**
     * 排序字段
     */
    @Data
    @Accessors(chain = true)
    public static class SortField {
        private String field;
        private String order = "asc"; // asc, desc
    }

    /**
     * 聚合配置
     */
    @Data
    @Accessors(chain = true)
    public static class AggregationConfig {
        private String name;
        private String type; // terms, avg, sum, max, min, count
        private String field;
        private Integer size = 10;
    }

}
