{"doc": " 题目难度枚举\n\n <AUTHOR>\n", "fields": [], "enumConstants": [{"name": "EASY", "doc": " 简单\n"}, {"name": "MEDIUM", "doc": " 中等\n"}, {"name": "HARD", "doc": " 困难\n"}], "methods": [{"name": "getDescriptionByCode", "paramTypes": ["java.lang.Integer"], "doc": " 根据代码获取描述\n\n @param code 难度代码\n @return 难度描述\n"}, {"name": "getCodeByDescription", "paramTypes": ["java.lang.String"], "doc": " 根据描述获取代码\n\n @param description 难度描述\n @return 难度代码\n"}, {"name": "getByCode", "paramTypes": ["java.lang.Integer"], "doc": " 根据代码获取枚举实例\n\n @param code 难度代码\n @return 枚举实例\n"}, {"name": "getByDescription", "paramTypes": ["java.lang.String"], "doc": " 根据描述获取枚举实例\n\n @param description 难度描述\n @return 枚举实例\n"}], "constructors": []}