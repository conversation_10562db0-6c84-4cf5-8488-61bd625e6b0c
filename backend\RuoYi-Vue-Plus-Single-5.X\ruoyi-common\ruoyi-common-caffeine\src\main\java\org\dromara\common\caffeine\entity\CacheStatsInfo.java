package org.dromara.common.caffeine.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 缓存统计信息实体
 *
 * <AUTHOR>
 */
@Data
public class CacheStatsInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 命中次数
     */
    private Long hitCount;

    /**
     * 未命中次数
     */
    private Long missCount;

    /**
     * 命中率
     */
    private Double hitRate;

    /**
     * 加载次数
     */
    private Long loadCount;

    /**
     * 驱逐次数
     */
    private Long evictionCount;

    /**
     * 平均加载时间（纳秒）
     */
    private Double averageLoadPenalty;

    /**
     * 当前缓存大小
     */
    private Long estimatedSize;

}
