{"doc": " WebSocket握手请求的拦截器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "beforeHandshake", "paramTypes": ["org.springframework.http.server.ServerHttpRequest", "org.springframework.http.server.ServerHttpResponse", "org.springframework.web.socket.WebSocketHandler", "java.util.Map"], "doc": " WebSocket握手之前执行的前置处理方法\n\n @param request    WebSocket握手请求\n @param response   WebSocket握手响应\n @param wsHandler  WebSocket处理程序\n @param attributes 与WebSocket会话关联的属性\n @return 如果允许握手继续进行，则返回true；否则返回false\n"}, {"name": "afterHandshake", "paramTypes": ["org.springframework.http.server.ServerHttpRequest", "org.springframework.http.server.ServerHttpResponse", "org.springframework.web.socket.WebSocketHandler", "java.lang.Exception"], "doc": " WebSocket握手成功后执行的后置处理方法\n\n @param request   WebSocket握手请求\n @param response  WebSocket握手响应\n @param wsHandler WebSocket处理程序\n @param exception 握手过程中可能出现的异常\n"}], "constructors": []}