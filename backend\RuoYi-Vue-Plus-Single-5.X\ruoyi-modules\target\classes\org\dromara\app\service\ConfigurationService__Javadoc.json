{"doc": " 配置管理服务\n 提供动态配置管理功能\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getConfig", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 获取配置值\n"}, {"name": "setConfig", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 设置运行时配置\n"}, {"name": "isFeatureEnabled", "paramTypes": ["java.lang.String"], "doc": " 检查特性是否启用\n"}, {"name": "setFeatureEnabled", "paramTypes": ["java.lang.String", "boolean"], "doc": " 启用/禁用特性\n"}, {"name": "getAllConfig", "paramTypes": [], "doc": " 获取所有配置\n"}, {"name": "getAllFeatureFlags", "paramTypes": [], "doc": " 获取所有特性开关\n"}, {"name": "resetConfig", "paramTypes": [], "doc": " 重置配置\n"}, {"name": "resetFeatureFlags", "paramTypes": [], "doc": " 重置特性开关\n"}], "constructors": []}