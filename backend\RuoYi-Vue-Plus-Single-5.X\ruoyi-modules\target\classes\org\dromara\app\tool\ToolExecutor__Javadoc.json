{"doc": " 工具执行器接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "execute", "paramTypes": ["java.util.Map", "org.dromara.app.service.IToolService.ToolCallContext"], "doc": " 执行工具\n\n @param parameters 参数\n @param context    上下文\n @return 执行结果\n"}, {"name": "getToolId", "paramTypes": [], "doc": " 获取工具ID\n\n @return 工具ID\n"}, {"name": "getToolName", "paramTypes": [], "doc": " 获取工具名称\n\n @return 工具名称\n"}, {"name": "getDescription", "paramTypes": [], "doc": " 获取工具描述\n\n @return 工具描述\n"}, {"name": "isAvailable", "paramTypes": [], "doc": " 检查工具是否可用\n\n @return 是否可用\n"}, {"name": "getConfig", "paramTypes": [], "doc": " 获取工具配置\n\n @return 工具配置\n"}], "constructors": []}