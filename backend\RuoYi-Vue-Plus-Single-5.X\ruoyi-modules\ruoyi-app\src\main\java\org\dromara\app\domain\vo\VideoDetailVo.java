package org.dromara.app.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.app.domain.Video;
import org.dromara.app.utils.VideoMappingUtils;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 视频详情视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = Video.class, uses = VideoMappingUtils.class)
public class VideoDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 视频ID
     */
    private Long id;

    /**
     * 视频标题
     */
    private String title;

    /**
     * 视频简介
     */
    private String description;

    /**
     * 讲师名称
     */
    private String instructor;

    /**
     * 讲师头像
     */
    private String instructorAvatar;

    /**
     * 讲师ID
     */
    private Long instructorId;

    /**
     * 视频时长
     */
    private String duration;

    /**
     * 缩略图
     */
    private String thumbnail;

    /**
     * 分类
     */
    private String category;

    /**
     * 难度
     */
    private String difficulty;

    /**
     * 评分
     */
    private BigDecimal rating;

    /**
     * 学习人数
     */
    private Integer studentCount;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 是否免费
     */
    private Boolean free;

    /**
     * 视频地址
     */
    private String videoUrl;

    /**
     * 播放次数
     */
    private Integer viewCount;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 收藏数
     */
    private Integer collectCount;

    /**
     * 分享数
     */
    private Integer shareCount;

    /**
     * 标签列表
     */
    private List<String> tags;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否已点赞
     */
    private Boolean isLiked;

    /**
     * 是否已收藏
     */
    private Boolean isCollected;

    /**
     * 是否已购买
     */
    private Boolean isPurchased;

    /**
     * 是否已完成
     */
    private Boolean isCompleted;

    /**
     * 播放进度百分比
     */
    private BigDecimal completionRate;

    /**
     * 是否已关注讲师
     */
    private Boolean isFollowed;

    /**
     * 讲师粉丝数
     */
    private Integer instructorFollowers;
}
