package org.dromara.common.mongodb.exception;

import com.mongodb.MongoException;
import com.mongodb.MongoTimeoutException;
import com.mongodb.MongoWriteException;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * MongoDB异常处理器
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class MongoExceptionHandler {

    /**
     * MongoDB通用异常
     */
    @ExceptionHandler(MongoException.class)
    public R<Void> handleMongoException(MongoException e) {
        log.error("MongoDB操作异常", e);
        return R.fail("数据库操作异常：" + e.getMessage());
    }

    /**
     * MongoDB超时异常
     */
    @ExceptionHandler(MongoTimeoutException.class)
    public R<Void> handleMongoTimeoutException(MongoTimeoutException e) {
        log.error("MongoDB连接超时", e);
        return R.fail("数据库连接超时，请稍后重试");
    }

    /**
     * MongoDB写入异常
     */
    @ExceptionHandler(MongoWriteException.class)
    public R<Void> handleMongoWriteException(MongoWriteException e) {
        log.error("MongoDB写入异常", e);
        String message = "数据写入失败";
        if (e.getError().getCode() == 11000) {
            message = "数据已存在，不能重复添加";
        }
        return R.fail(message);
    }

    /**
     * 数据访问异常
     */
    @ExceptionHandler(DataAccessException.class)
    public R<Void> handleDataAccessException(DataAccessException e) {
        log.error("数据访问异常", e);
        return R.fail("数据访问异常：" + e.getMessage());
    }

    /**
     * 重复键异常
     */
    @ExceptionHandler(DuplicateKeyException.class)
    public R<Void> handleDuplicateKeyException(DuplicateKeyException e) {
        log.error("重复键异常", e);
        return R.fail("数据已存在，不能重复添加");
    }
}
