package org.dromara.app.controller.pay;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.net.NetUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.dto.PaymentOrderDto;
import org.dromara.app.domain.vo.PaymentOrderVo;
import org.dromara.app.service.IPaymentService;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * 支付控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/pay")
@Tag(name = "支付管理", description = "支付相关接口")
public class PayController extends BaseController {

    private final IPaymentService paymentService;

    /**
     * 创建支付订单
     */
    @PostMapping("/create-order")
    @Operation(summary = "创建支付订单", description = "创建支付订单并返回订单信息")
    @Log(title = "创建支付订单", businessType = BusinessType.INSERT)
    public R<PaymentOrderVo> createPaymentOrder(@Valid @RequestBody PaymentOrderDto paymentOrderDto, HttpServletRequest request) {
        try {
            // 获取客户端IP
            String clientIp = NetUtil.getLocalhostStr();
            try {
                clientIp = getClientIP(request);
            } catch (Exception e) {
                log.warn("获取客户端IP失败，使用默认IP：{}", clientIp);
            }

            // 获取用户代理
            String userAgent = request.getHeader("User-Agent");

            // 设置客户端信息
            paymentOrderDto.setClientIp(clientIp);
            paymentOrderDto.setUserAgent(userAgent);

            // 创建支付订单
            PaymentOrderVo result = paymentService.createPaymentOrder(paymentOrderDto);

            return R.ok(result);

        } catch (Exception e) {
            log.error("创建支付订单失败：{}", e.getMessage(), e);
            return R.fail("创建支付订单失败：" + e.getMessage());
        }
    }

    /**
     * 支付宝支付
     */
    @GetMapping("/alipay")
    @SaIgnore
    @Operation(summary = "支付宝支付", description = "发起支付宝支付")
    @Log(title = "支付宝支付", businessType = BusinessType.OTHER)
    public void alipayPay(
        @Parameter(description = "订单号", required = true)
        @NotBlank(message = "订单号不能为空")
        @RequestParam String orderNo,
        @Parameter(description = "支付token", required = true)
        @NotBlank(message = "支付token不能为空")
        @RequestParam String payToken,
        HttpServletResponse response
    ) throws IOException {
        try {
            // 调用支付宝支付
            String payForm = paymentService.alipayPay(orderNo, payToken);

            // 返回支付页面HTML
            response.setContentType(MediaType.TEXT_HTML_VALUE + ";charset=UTF-8");
            response.getWriter().write(payForm);
            response.getWriter().flush();
        } catch (Exception e) {
            log.error("支付宝支付失败，订单号：{}，错误信息：{}", orderNo, e.getMessage(), e);
            // 返回错误页面
            String errorHtml = buildErrorHtml("支付失败", e.getMessage());
            response.setContentType(MediaType.TEXT_HTML_VALUE + ";charset=UTF-8");
            response.getWriter().write(errorHtml);
            response.getWriter().flush();
        }
    }

    /**
     * 支付宝异步通知
     */
    @PostMapping("/notify")
    @SaIgnore
    @Operation(summary = "支付宝异步通知", description = "处理支付宝异步通知回调")
    public String alipayNotify(HttpServletRequest request) {
        try {
            log.info("收到支付宝异步通知");
            return paymentService.alipayNotify(request);
        } catch (Exception e) {
            log.error("支付宝异步通知处理失败：{}", e.getMessage(), e);
            return "failure";
        }
    }

    /**
     * 支付宝同步回调
     */
    @GetMapping("/return")
    @SaIgnore
    @Operation(summary = "支付宝同步回调", description = "处理支付宝同步回调")
    public void alipayReturn(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            log.info("收到支付宝同步回调");
            String result = paymentService.alipayReturn(request);

            String html;
            if ("success".equals(result)) {
                html = buildSuccessHtml();
            } else {
                html = buildErrorHtml("支付验证失败", "支付信息验证失败，请联系客服");
            }

            response.setContentType(MediaType.TEXT_HTML_VALUE + ";charset=UTF-8");
            response.getWriter().write(html);
            response.getWriter().flush();

        } catch (Exception e) {
            log.error("支付宝同步回调处理失败：{}", e.getMessage(), e);

            String errorHtml = buildErrorHtml("系统异常", "处理支付回调时发生异常，请联系客服");
            response.setContentType(MediaType.TEXT_HTML_VALUE + ";charset=UTF-8");
            response.getWriter().write(errorHtml);
            response.getWriter().flush();
        }
    }

    /**
     * 查询订单状态
     */
    @GetMapping("/query-status")
    @Operation(summary = "查询订单状态", description = "根据订单号查询支付状态")
    public R<PaymentOrderVo> queryOrderStatus(
        @Parameter(description = "订单号", required = true)
        @NotBlank(message = "订单号不能为空")
        @RequestParam String orderNo
    ) {
        try {
            PaymentOrderVo result = paymentService.queryOrderStatus(orderNo);
            return R.ok(result);
        } catch (Exception e) {
            log.error("查询订单状态失败，订单号：{}，错误信息：{}", orderNo, e.getMessage(), e);
            return R.fail("查询订单状态失败：" + e.getMessage());
        }
    }

    /**
     * 取消订单
     */
    @PostMapping("/cancel")
    @Operation(summary = "取消订单", description = "取消待支付订单")
    @Log(title = "取消支付订单", businessType = BusinessType.UPDATE)
    public R<Void> cancelOrder(
        @Parameter(description = "订单号", required = true)
        @NotBlank(message = "订单号不能为空")
        @RequestParam String orderNo
    ) {
        try {
            boolean success = paymentService.cancelOrder(orderNo);
            if (success) {
                return R.ok("取消订单成功");
            } else {
                return R.fail("取消订单失败");
            }
        } catch (Exception e) {
            log.error("取消订单失败，订单号：{}，错误信息：{}", orderNo, e.getMessage(), e);
            return R.fail("取消订单失败：" + e.getMessage());
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIP(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 构建成功页面HTML
     */
    private String buildSuccessHtml() {
        return "<!DOCTYPE html>" +
            "<html><head><meta charset='UTF-8'><title>" + "支付成功" + "</title>" +
            "<style>body{font-family:Arial,sans-serif;text-align:center;padding:50px;}" +
            ".success{color:#52c41a;font-size:18px;margin:20px 0;}" +
            ".btn{display:inline-block;padding:10px 20px;background:#1890ff;color:white;text-decoration:none;border-radius:5px;margin:10px;}" +
            "</style></head><body>" +
            "<h2 style='color:#52c41a;'>✓ " + "支付成功" + "</h2>" +
            "<p class='success'>" + "您的支付已完成，感谢您的购买！" + "</p>" +
            "<a href='javascript:window.close()' class='btn'>关闭页面</a>" +
            "</body></html>";
    }

    /**
     * 构建错误页面HTML
     */
    private String buildErrorHtml(String title, String message) {
        return "<!DOCTYPE html>" +
            "<html><head><meta charset='UTF-8'><title>" + title + "</title>" +
            "<style>body{font-family:Arial,sans-serif;text-align:center;padding:50px;}" +
            ".error{color:#ff4d4f;font-size:18px;margin:20px 0;}" +
            ".btn{display:inline-block;padding:10px 20px;background:#1890ff;color:white;text-decoration:none;border-radius:5px;margin:10px;}" +
            "</style></head><body>" +
            "<h2 style='color:#ff4d4f;'>✗ " + title + "</h2>" +
            "<p class='error'>" + message + "</p>" +
            "<a href='javascript:window.close()' class='btn'>关闭页面</a>" +
            "</body></html>";
    }
}
