{"doc": "\n 学习资源服务实现类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "checkDatabaseCompatibility", "paramTypes": ["org.dromara.app.domain.LearningResource"], "doc": "\n 检查资源数据与数据库模式的兼容性\r\n 主要用于防止数据库错误，检查字段长度和约束\r\n\r\n @param resource 学习资源\r\n"}, {"name": "checkField<PERSON>ength", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.String", "int"], "doc": "\n 检查字段长度\r\n\r\n @param errors 错误列表\r\n @param fieldName 字段名称\r\n @param value 字段值\r\n @param maxLength 最大长度\r\n"}, {"name": "checkJsonFieldsConsistency", "paramTypes": ["org.dromara.app.domain.LearningResource", "java.util.List"], "doc": "\n 检查JSON字段的一致性\r\n\r\n @param resource 学习资源\r\n @param errors 错误列表\r\n"}, {"name": "validateResource", "paramTypes": ["org.dromara.app.domain.LearningResource", "boolean"], "doc": "\n 验证学习资源数据\r\n\r\n @param resource 学习资源\r\n @param isCreate 是否为创建操作\r\n"}, {"name": "validate<PERSON><PERSON><PERSON><PERSON>s", "paramTypes": ["org.dromara.app.domain.LearningResource", "java.util.List"], "doc": "\n 验证JSON格式字段\r\n\r\n @param resource 学习资源\r\n @param errors 错误列表\r\n"}, {"name": "validateQualityAssessment", "paramTypes": ["org.dromara.app.domain.LearningResource.QualityAssessment"], "doc": "\n 验证质量评估信息\r\n @param qualityAssessment 质量评估信息\r\n"}, {"name": "calculateOverallQuality", "paramTypes": ["org.dromara.app.domain.LearningResource.QualityAssessment"], "doc": "\n 计算综合质量评分\r\n"}, {"name": "calculateContentQuality", "paramTypes": ["org.dromara.app.domain.LearningResource"], "doc": "\n 计算内容质量评分\r\n"}, {"name": "calculateTeachingEffectiveness", "paramTypes": ["org.dromara.app.domain.LearningResource"], "doc": "\n 计算教学效果评分\r\n"}, {"name": "calculateTimeliness", "paramTypes": ["org.dromara.app.domain.LearningResource"], "doc": "\n 计算更新及时性评分\r\n"}, {"name": "calculateUserExperience", "paramTypes": ["org.dromara.app.domain.LearningResource"], "doc": "\n 计算用户体验评分\r\n"}, {"name": "calculateRecommendationScore", "paramTypes": ["org.dromara.app.domain.LearningResource", "java.util.Map"], "doc": "\n 计算推荐评分\r\n"}], "constructors": []}