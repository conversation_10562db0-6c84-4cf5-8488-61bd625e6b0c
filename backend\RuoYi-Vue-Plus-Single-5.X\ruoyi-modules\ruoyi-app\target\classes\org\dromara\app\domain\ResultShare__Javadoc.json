{"doc": "\n 结果分享记录对象 app_result_share\r\n\r\n <AUTHOR>\r\n @date 2025-07-17\r\n", "fields": [{"name": "id", "doc": "\n 分享记录ID\r\n"}, {"name": "resultId", "doc": "\n 结果ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "platform", "doc": "\n 分享平台（wechat,qq,weibo,link）\r\n"}, {"name": "shareUrl", "doc": "\n 分享链接\r\n"}, {"name": "content", "doc": "\n 分享内容\r\n"}, {"name": "viewCount", "doc": "\n 查看次数\r\n"}, {"name": "expiresAt", "doc": "\n 过期时间\r\n"}, {"name": "status", "doc": "\n 状态（active,expired,disabled）\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}], "enumConstants": [], "methods": [], "constructors": []}