{"doc": "\n AI代理对象 app_agent\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 代理ID\r\n"}, {"name": "name", "doc": "\n 代理名称\r\n"}, {"name": "description", "doc": "\n 代理描述\r\n"}, {"name": "icon", "doc": "\n 代理图标\r\n"}, {"name": "color", "doc": "\n 代理颜色\r\n"}, {"name": "agentType", "doc": "\n 代理类型：general/interviewer/resume_analyzer/skill_assessor/career_advisor/mock_interviewer/learning_guide\r\n"}, {"name": "systemPrompt", "doc": "\n 系统提示词\r\n"}, {"name": "modelConfig", "doc": "\n 模型配置（JSON格式）\r\n"}, {"name": "capabilities", "doc": "\n 代理能力列表（JSON格式）\r\n"}, {"name": "quickActions", "doc": "\n 快速操作列表（JSON格式）\r\n"}, {"name": "enabled", "doc": "\n 是否启用：0-禁用，1-启用\r\n"}, {"name": "sortOrder", "doc": "\n 排序序号\r\n"}, {"name": "usageCount", "doc": "\n 使用次数\r\n"}, {"name": "averageRating", "doc": "\n 平均评分\r\n"}, {"name": "extendConfig", "doc": "\n 代理配置扩展（JSON格式）\r\n"}, {"name": "capabilityList", "doc": "\n 能力列表（不存储到数据库，用于返回给前端）\r\n"}, {"name": "quickActionList", "doc": "\n 快速操作列表（不存储到数据库，用于返回给前端）\r\n"}, {"name": "modelConfigObject", "doc": "\n 模型配置对象（不存储到数据库）\r\n"}, {"name": "delFlag", "doc": "\n delFlag\r\n"}], "enumConstants": [], "methods": [], "constructors": []}