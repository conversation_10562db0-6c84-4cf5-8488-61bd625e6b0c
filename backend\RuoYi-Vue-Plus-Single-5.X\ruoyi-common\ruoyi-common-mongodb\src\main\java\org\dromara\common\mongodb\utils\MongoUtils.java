package org.dromara.common.mongodb.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.SpringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.Collection;
import java.util.List;

/**
 * MongoDB工具类
 *
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MongoUtils {

    private static final MongoTemplate mongoTemplate = SpringUtils.getBean(MongoTemplate.class);

    /**
     * 保存文档
     */
    public static <T> T save(T entity) {
        return mongoTemplate.save(entity);
    }

    /**
     * 批量保存文档
     */
    public static <T> Collection<T> saveAll(Collection<T> entities) {
        return mongoTemplate.insertAll(entities);
    }

    /**
     * 根据ID查询文档
     */
    public static <T> T findById(Object id, Class<T> entityClass) {
        return mongoTemplate.findById(id, entityClass);
    }

    /**
     * 查询所有文档
     */
    public static <T> List<T> findAll(Class<T> entityClass) {
        return mongoTemplate.findAll(entityClass);
    }

    /**
     * 根据条件查询文档
     */
    public static <T> List<T> find(Query query, Class<T> entityClass) {
        return mongoTemplate.find(query, entityClass);
    }

    /**
     * 根据条件查询单个文档
     */
    public static <T> T findOne(Query query, Class<T> entityClass) {
        return mongoTemplate.findOne(query, entityClass);
    }

    /**
     * 根据字段查询文档
     */
    public static <T> List<T> findByField(String field, Object value, Class<T> entityClass) {
        Query query = new Query(Criteria.where(field).is(value));
        return mongoTemplate.find(query, entityClass);
    }

    /**
     * 根据字段查询单个文档
     */
    public static <T> T findOneByField(String field, Object value, Class<T> entityClass) {
        Query query = new Query(Criteria.where(field).is(value));
        return mongoTemplate.findOne(query, entityClass);
    }

    /**
     * 分页查询
     */
    public static <T> List<T> findByPage(Query query, int page, int size, Class<T> entityClass) {
        query.skip((long) (page - 1) * size).limit(size);
        return mongoTemplate.find(query, entityClass);
    }

    /**
     * 分页查询带排序
     */
    public static <T> List<T> findByPage(Query query, int page, int size, Sort sort, Class<T> entityClass) {
        query.with(sort).skip((long) (page - 1) * size).limit(size);
        return mongoTemplate.find(query, entityClass);
    }

    /**
     * 计数
     */
    public static long count(Query query, Class<?> entityClass) {
        return mongoTemplate.count(query, entityClass);
    }

    /**
     * 计数所有
     */
    public static long countAll(Class<?> entityClass) {
        return mongoTemplate.count(new Query(), entityClass);
    }

    /**
     * 更新文档
     */
    public static void update(Query query, Update update, Class<?> entityClass) {
        mongoTemplate.updateFirst(query, update, entityClass);
    }

    /**
     * 批量更新文档
     */
    public static void updateMulti(Query query, Update update, Class<?> entityClass) {
        mongoTemplate.updateMulti(query, update, entityClass);
    }

    /**
     * 根据ID更新文档
     */
    public static void updateById(Object id, Update update, Class<?> entityClass) {
        Query query = new Query(Criteria.where("id").is(id));
        mongoTemplate.updateFirst(query, update, entityClass);
    }

    /**
     * 删除文档
     */
    public static void remove(Query query, Class<?> entityClass) {
        mongoTemplate.remove(query, entityClass);
    }

    /**
     * 根据ID删除文档
     */
    public static void removeById(Object id, Class<?> entityClass) {
        Query query = new Query(Criteria.where("id").is(id));
        mongoTemplate.remove(query, entityClass);
    }

    /**
     * 删除所有文档
     */
    public static void removeAll(Class<?> entityClass) {
        mongoTemplate.remove(new Query(), entityClass);
    }

    /**
     * 检查文档是否存在
     */
    public static boolean exists(Query query, Class<?> entityClass) {
        return mongoTemplate.exists(query, entityClass);
    }

    /**
     * 根据ID检查文档是否存在
     */
    public static boolean existsById(Object id, Class<?> entityClass) {
        Query query = new Query(Criteria.where("id").is(id));
        return mongoTemplate.exists(query, entityClass);
    }

    /**
     * 创建查询条件
     */
    public static Query createQuery() {
        return new Query();
    }

    /**
     * 创建更新条件
     */
    public static Update createUpdate() {
        return new Update();
    }

    /**
     * 创建排序条件
     */
    public static Sort createSort(Sort.Direction direction, String... properties) {
        return Sort.by(direction, properties);
    }

    /**
     * 创建升序排序
     */
    public static Sort createSortAsc(String... properties) {
        return Sort.by(Sort.Direction.ASC, properties);
    }

    /**
     * 创建降序排序
     */
    public static Sort createSortDesc(String... properties) {
        return Sort.by(Sort.Direction.DESC, properties);
    }

    /**
     * 构建模糊查询条件
     */
    public static Criteria buildLikeCriteria(String field, String value) {
        if (StrUtil.isBlank(value)) {
            return null;
        }
        return Criteria.where(field).regex(".*" + value + ".*", "i");
    }

    /**
     * 构建范围查询条件
     */
    public static Criteria buildRangeCriteria(String field, Object min, Object max) {
        Criteria criteria = Criteria.where(field);
        if (min != null) {
            criteria.gte(min);
        }
        if (max != null) {
            criteria.lte(max);
        }
        return criteria;
    }

    /**
     * 构建IN查询条件
     */
    public static Criteria buildInCriteria(String field, Collection<?> values) {
        if (CollUtil.isEmpty(values)) {
            return null;
        }
        return Criteria.where(field).in(values);
    }
}
