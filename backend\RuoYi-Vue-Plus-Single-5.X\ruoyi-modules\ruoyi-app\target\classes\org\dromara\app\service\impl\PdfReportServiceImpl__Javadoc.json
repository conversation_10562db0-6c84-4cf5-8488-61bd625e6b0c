{"doc": "\n PDF报告生成服务实现类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "generateReportContent", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 生成报告内容\r\n"}, {"name": "generateCoverPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 生成封面页\r\n"}, {"name": "generateOverviewPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 生成总览页\r\n"}, {"name": "generateCapabilityAssessmentPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 生成能力评估页\r\n"}, {"name": "generateImprovementSuggestionsPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 生成改进建议页\r\n"}, {"name": "generateLearningPathPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 生成学习路径页\r\n"}, {"name": "addInfoRow", "paramTypes": ["com.itextpdf.layout.element.Table", "java.lang.String", "java.lang.String", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 添加信息行到表格\r\n"}, {"name": "addTableHeader", "paramTypes": ["com.itextpdf.layout.element.Table", "java.lang.String", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 添加表格头部\r\n"}, {"name": "addTableCell", "paramTypes": ["com.itextpdf.layout.element.Table", "java.lang.String", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 添加表格单元格\r\n"}, {"name": "addInfoCell", "paramTypes": ["com.itextpdf.layout.element.Table", "java.lang.String", "java.lang.String", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.colors.Color"], "doc": "\n 添加信息单元格\r\n"}, {"name": "getPriorityColor", "paramTypes": ["java.lang.String"], "doc": "\n 获取优先级颜色\r\n"}, {"name": "loadChineseFont", "paramTypes": [], "doc": "\n 加载中文字体\r\n"}, {"name": "loadBoldChineseFont", "paramTypes": [], "doc": "\n 加载粗体中文字体\r\n"}, {"name": "addCoverPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 添加封面页\r\n"}, {"name": "addOverviewPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 添加总览页\r\n"}, {"name": "addDimensionAnalysisPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 添加维度分析页\r\n"}, {"name": "addImprovementSuggestionsPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 添加改进建议页\r\n"}, {"name": "addLearningPathPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 添加学习路径页\r\n"}, {"name": "addAppendixPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 添加附录页\r\n"}, {"name": "add<PERSON>ageFooter", "paramTypes": ["com.itextpdf.layout.Document", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 添加页脚\r\n"}, {"name": "addTableRow", "paramTypes": ["com.itextpdf.layout.element.Table", "java.lang.String", "java.lang.String", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 添加表格行\r\n"}, {"name": "createHeaderCell", "paramTypes": ["java.lang.String", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 创建表头单元格\r\n"}, {"name": "createTableCell", "paramTypes": ["java.lang.String", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 创建表格单元格\r\n"}, {"name": "createInfoCell", "paramTypes": ["java.lang.String", "java.lang.String", "com.itextpdf.kernel.font.PdfFont"], "doc": "\n 创建信息单元格\r\n"}, {"name": "getScoreColor", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据分数获取颜色\r\n"}, {"name": "getLevelText", "paramTypes": ["java.lang.String"], "doc": "\n 获取等级文本\r\n"}, {"name": "getScoreLevel", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据分数获取等级\r\n"}], "constructors": []}