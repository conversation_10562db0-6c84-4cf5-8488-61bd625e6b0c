{"doc": "\n 文档分块对象 app_document_chunk\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 分块ID\r\n"}, {"name": "documentId", "doc": "\n 文档ID\r\n"}, {"name": "knowledgeBaseId", "doc": "\n 知识库ID\r\n"}, {"name": "chunkIndex", "doc": "\n 分块序号\r\n"}, {"name": "content", "doc": "\n 分块内容\r\n"}, {"name": "title", "doc": "\n 分块标题（可选）\r\n"}, {"name": "contentLength", "doc": "\n 内容长度\r\n"}, {"name": "vector", "doc": "\n 向量数据（JSON格式存储浮点数组）\r\n"}, {"name": "vectorDimension", "doc": "\n 向量维度\r\n"}, {"name": "embeddingModel", "doc": "\n 嵌入模型名称\r\n"}, {"name": "chunkType", "doc": "\n 分块类型：text/code/table/image_caption\r\n"}, {"name": "startPosition", "doc": "\n 开始位置（在原文档中的字符位置）\r\n"}, {"name": "endPosition", "doc": "\n 结束位置\r\n"}, {"name": "metadata", "doc": "\n 分块元数据（JSON格式）\r\n"}, {"name": "weight", "doc": "\n 分块权重\r\n"}, {"name": "enabled", "doc": "\n 是否启用：0-禁用，1-启用\r\n"}, {"name": "contentHash", "doc": "\n 哈希值（用于去重）\r\n"}, {"name": "vectorizedAt", "doc": "\n 向量化时间\r\n"}, {"name": "metadataObject", "doc": "\n 分块元数据对象（不存储到数据库）\r\n"}, {"name": "vectorArray", "doc": "\n 向量数组（不存储到数据库，用于计算）\r\n"}, {"name": "similarityScore", "doc": "\n 相似度分数（搜索时使用）\r\n"}, {"name": "rerankScore", "doc": "\n 重排序分数（搜索时使用）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}