{"doc": " 流程任务监听\n\n <AUTHOR>\n", "fields": [{"name": "flowCode", "doc": " 流程定义编码\n"}, {"name": "nodeType", "doc": " 节点类型（0开始节点 1中间节点 2结束节点 3互斥网关 4并行网关）\n"}, {"name": "nodeCode", "doc": " 流程节点编码\n"}, {"name": "nodeName", "doc": " 流程节点名称\n"}, {"name": "taskId", "doc": " 任务id\n"}, {"name": "businessId", "doc": " 业务id\n"}, {"name": "status", "doc": " 流程状态\n"}], "enumConstants": [], "methods": [], "constructors": []}