{"doc": "\n 活动时长记录服务接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-16\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "startActivity", "paramTypes": ["org.dromara.app.domain.dto.ActivityStartRequest"], "doc": "\n 开始活动记录\r\n\r\n @param request 开始活动请求\r\n @return 是否成功\r\n"}, {"name": "pauseActivity", "paramTypes": ["org.dromara.app.domain.dto.ActivityPauseRequest"], "doc": "\n 暂停活动记录\r\n\r\n @param request 暂停活动请求\r\n @return 是否成功\r\n"}, {"name": "endActivity", "paramTypes": ["org.dromara.app.domain.dto.ActivityEndRequest"], "doc": "\n 结束活动记录\r\n\r\n @param request 结束活动请求\r\n @return 是否成功\r\n"}, {"name": "syncSession", "paramTypes": ["org.dromara.app.domain.dto.ActivitySyncRequest"], "doc": "\n 同步活动会话\r\n\r\n @param request 同步请求\r\n @return 是否成功\r\n"}, {"name": "getStatistics", "paramTypes": ["org.dromara.app.domain.dto.ActivityStatisticsRequest"], "doc": "\n 获取活动统计数据\r\n\r\n @param request 统计请求\r\n @return 统计数据\r\n"}, {"name": "getHistory", "paramTypes": ["org.dromara.app.domain.dto.ActivityHistoryRequest"], "doc": "\n 获取活动历史记录\r\n\r\n @param request 历史记录请求\r\n @return 历史记录\r\n"}, {"name": "clearRecords", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType"], "doc": "\n 清除活动记录\r\n\r\n @param userId       用户ID\r\n @param activityType 活动类型(可选)\r\n @return 是否成功\r\n"}, {"name": "getStatsByType", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType"], "doc": "\n 获取用户指定类型的活动统计\r\n\r\n @param userId       用户ID\r\n @param activityType 活动类型\r\n @return 统计数据\r\n"}, {"name": "initializeUserSummary", "paramTypes": ["java.lang.Long"], "doc": "\n 初始化用户活动总览\r\n\r\n @param userId 用户ID\r\n @return 是否成功\r\n"}], "constructors": []}