{"doc": "\n 聊天响应DTO\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "success", "doc": "\n 是否成功\r\n"}, {"name": "message", "doc": "\n 响应消息\r\n"}, {"name": "messageId", "doc": "\n 消息ID\r\n"}, {"name": "sessionId", "doc": "\n 会话ID\r\n"}, {"name": "error", "doc": "\n 错误信息\r\n"}, {"name": "metadata", "doc": "\n 消息元数据\r\n"}], "enumConstants": [], "methods": [{"name": "success", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 创建成功响应\r\n"}, {"name": "error", "paramTypes": ["java.lang.String"], "doc": "\n 创建失败响应\r\n"}], "constructors": []}