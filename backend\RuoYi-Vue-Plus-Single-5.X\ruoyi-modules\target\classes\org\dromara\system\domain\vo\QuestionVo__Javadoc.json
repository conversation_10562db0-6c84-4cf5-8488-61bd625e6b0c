{"doc": " 题目视图对象\n\n <AUTHOR>\n", "fields": [{"name": "questionId", "doc": " 题目ID\n"}, {"name": "bankId", "doc": " 题库ID\n"}, {"name": "bankTitle", "doc": " 题库标题（关联查询）\n"}, {"name": "questionCode", "doc": " 题目编码\n"}, {"name": "title", "doc": " 题目标题\n"}, {"name": "description", "doc": " 题目描述\n"}, {"name": "content", "doc": " 题目内容\n"}, {"name": "answer", "doc": " 参考答案\n"}, {"name": "analysis", "doc": " 答案解析\n"}, {"name": "difficulty", "doc": " 难度（1-简单 2-中等 3-困难）\n"}, {"name": "category", "doc": " 分类\n"}, {"name": "type", "doc": " 题目类型（1-单选题 2-多选题 3-判断题 4-简答题 5-编程题）\n"}, {"name": "practiceCount", "doc": " 练习次数\n"}, {"name": "correctRate", "doc": " 正确率\n"}, {"name": "acceptanceRate", "doc": " 通过率（百分比）\n"}, {"name": "commentCount", "doc": " 评论数\n"}, {"name": "tags", "doc": " 标签（JSON格式）\n"}, {"name": "sort", "doc": " 排序\n"}, {"name": "status", "doc": " 状态（0正常 1停用）\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}, {"name": "isBookmarked", "doc": " 是否收藏（非数据库字段）\n"}, {"name": "userAnswerStatus", "doc": " 用户答题状态（非数据库字段）\n"}], "enumConstants": [], "methods": [], "constructors": []}