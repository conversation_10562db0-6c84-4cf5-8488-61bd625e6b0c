# SmartInterview智能面试系统 - 记忆中枢 v1.0

**最后更新:** 2025-01-08 22:40:00

## 项目概述

### 项目名称
SmartInterview智能面试系统 (software-xunfei)

### 项目定位
基于AI Agent技术的智能面试平台，为用户提供全方位的面试准备和技能评估服务。

### 核心价值
通过多种AI Agent协同工作，实现智能化的面试流程，包括问题生成、实时评估、个性化反馈和学习指导。

## 技术架构

### 整体架构
- **架构模式**: 前后端分离 + 微服务架构
- **部署模式**: 多端统一 (Web + 移动端 + 小程序)

### 后端技术栈
- **基础框架**: RuoYi-Vue-Plus-Single-5.X
- **核心技术**: Spring Boot 3.4.6 + Java 17
- **数据访问**: MyBatis-Plus 3.5.12
- **安全框架**: Sa-Token 1.42.0
- **数据库**: MySQL 8.0 + Redis (Redisson 3.45.1)
- **消息队列**: 规划使用RocketMQ
- **搜索引擎**: 规划使用Elasticsearch

### 前端技术栈
- **Web端**: Vue 3 + Element Plus (./front/plus-ui/)
- **移动端**: uni-app 3.0.0 + Vue 3.4.21 (./front/unibest-main/)
- **UI框架**: Tailwind CSS 4.1.7, wot-design-uni
- **状态管理**: Pinia 2.0.36
- **构建工具**: Vite 5.2.8

### AI技术栈 (规划中)
- **AI服务**: Python Flask/FastAPI
- **NLP模型**: Transformers, BERT/GPT
- **机器学习**: scikit-learn, TensorFlow/PyTorch

## 核心功能模块

### AI Agent体系 (7个核心Agent)
1. **面试官AI Agent** - 智能问题生成、动态追问机制
2. **简历分析AI Agent** - 简历解析、技能匹配评估
3. **技能评估AI Agent** - 技术能力测试、编程技能评估
4. **职业顾问AI Agent** - 职业路径规划、行业趋势分析
5. **模拟面试AI Agent** - 真实面试模拟、多场景面试
6. **反馈分析AI Agent** - 面试表现分析、多维度评分
7. **学习指导AI Agent** - 学习计划制定、资源推荐

### 数据库设计 (规划)
- **ai_agent_sessions** - AI Agent会话管理
- **ai_conversations** - AI对话记录
- **skill_assessments** - 技能评估结果
- **interview_records** - 面试记录

## 项目结构

### 目录结构
```
softwart-xunfei-code2/
├── backend/                    # 后端代码
│   ├── RuoYi-Vue-Plus-Single-5.X/  # 主要后端框架
│   └── logs/                   # 运行日志
├── front/                      # 前端代码
│   ├── plus-ui/               # Web端 (Vue3 + Element Plus)
│   └── unibest-main/          # 移动端 (uni-app)
├── doc/                       # 项目文档
│   ├── ai-agent-implementation-plan.md  # AI实现方案
│   ├── 前端页面文档/           # 前端设计文档
│   └── 原型/                  # HTML原型页面
├── target/                    # 编译输出
└── 记忆与任务/                # AI助手工作区
```

### 关键配置文件
- **后端**: `./backend/RuoYi-Vue-Plus-Single-5.X/pom.xml`
- **前端**: `./front/unibest-main/package.json`
- **构建**: `./front/unibest-main/vite.config.ts`

## 开发规范

### 代码规范
- **Java**: 遵循Spring Boot最佳实践
- **前端**: ESLint + Prettier + TypeScript
- **提交**: Conventional Commits规范

### 依赖管理
- **后端**: Maven (pom.xml)
- **前端**: pnpm (package.json)

### 环境要求
- **Java**: 17+
- **Node.js**: 18+
- **pnpm**: 7.30+

## 实施计划

### Phase 1 - 基础功能 (1-2周)
- AI Agent基础框架搭建
- 简单对话功能实现
- 基础面试问题生成

### Phase 2 - 核心功能 (3-4周)
- 面试官AI Agent完整实现
- 简历分析功能
- 基础技能评估

### Phase 3 - 高级功能 (5-6周)
- 模拟面试功能
- 实时反馈系统
- 学习推荐引擎

### Phase 4 - 扩展功能 (7-8周)
- 多模态交互 (语音、视频)
- 高级分析报告
- 个性化学习路径

## 关键决策记录

### 技术选型决策
1. **选择RuoYi-Vue-Plus**: 成熟的企业级框架，快速开发
2. **采用uni-app**: 一套代码多端运行，降低开发成本
3. **使用Spring Boot 3.x**: 最新技术栈，性能优化

### 架构决策
1. **微服务架构**: 支持AI服务独立部署和扩展
2. **前后端分离**: 提高开发效率和系统灵活性
3. **多端统一**: 覆盖更多用户场景

### 模块依赖优化 (2025-01-08)
**问题**: ruoyi-system模块与ruoyi-app模块存在循环依赖，导致QuestionBank相关类编译错误
**解决方案**: 将QuestionBank实体类重构到ruoyi-common-mybatis公共模块
**影响**:
- 避免了模块间循环依赖
- 提高了代码的可维护性
- 为后续类似问题提供了解决模式
**位置**: `ruoyi-common-mybatis/src/main/java/org/dromara/common/mybatis/core/domain/QuestionBank.java`

## 风险与挑战

### 技术风险
1. **AI集成复杂度**: 多个AI模型的协调和管理
2. **性能要求**: AI响应时间和并发处理能力
3. **数据安全**: 用户隐私和敏感信息保护

### 业务风险
1. **用户体验**: AI交互的自然度和准确性
2. **内容质量**: 面试题目和评估标准的专业性
3. **市场竞争**: 同类产品的竞争压力

## 监控指标

### 性能指标
- AI响应时间 < 3秒
- 文件上传处理 < 10秒
- 同时支持1000+用户在线

### 质量指标
- AI模型准确率
- 用户满意度
- 系统可用性 99.9%
