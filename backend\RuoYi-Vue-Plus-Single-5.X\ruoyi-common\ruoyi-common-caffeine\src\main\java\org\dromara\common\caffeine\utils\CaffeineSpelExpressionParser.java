package org.dromara.common.caffeine.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.expression.MethodBasedEvaluationContext;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.*;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * Caffeine缓存专用的SpEL表达式解析工具类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CaffeineSpelExpressionParser {

    private static final ExpressionParser parser = new SpelExpressionParser();
    private final ParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();

    /**
     * 解析SpEL表达式并返回字符串结果
     *
     * @param expression SpEL表达式
     * @param method     方法
     * @param args       参数
     * @return 解析结果
     */
    public String parseExpression(String expression, Object target, Method method, Object[] args) {
        return parseExpression(expression, target, method, args, null);
    }

    /**
     * 解析SpEL表达式并返回字符串结果
     *
     * @param expression SpEL表达式
     * @param method     方法
     * @param args       参数
     * @param result     方法返回值
     * @return 解析结果
     */
    public String parseExpression(String expression, Object target, Method method, Object[] args, Object result) {
        try {
            EvaluationContext context = createEvaluationContext(target, method, args, result);
            Expression exp = parser.parseExpression(expression);
            Object value = exp.getValue(context);
            return value != null ? value.toString() : "null";
        } catch (Exception e) {
            log.error("SpEL表达式解析失败: {}", expression, e);
            throw new RuntimeException("SpEL表达式解析失败: " + expression, e);
        }
    }

    /**
     * 解析条件表达式并返回布尔结果
     *
     * @param condition 条件表达式
     * @param method    方法
     * @param args      参数
     * @param result    方法返回值（可为null）
     * @return 条件结果
     */
    public boolean parseCondition(String condition, Object target, Method method, Object[] args, Object result) {
        try {
            EvaluationContext context = createEvaluationContext(target, method, args, result);
            Expression exp = parser.parseExpression(condition);
            Boolean value = exp.getValue(context, Boolean.class);
            return value != null && value;
        } catch (Exception e) {
            log.error("条件表达式解析失败: {}", condition, e);
            return false;
        }
    }

    /**
     * 创建SpEL评估上下文
     *
     * @param method 方法
     * @param args   参数
     * @param result 返回值
     * @return 评估上下文
     */
    private EvaluationContext createEvaluationContext(Object target, Method method, Object[] args, Object result) {
        MethodBasedEvaluationContext context = new MethodBasedEvaluationContext(
            target, method, args, parameterNameDiscoverer);

        // 添加方法参数
        if (args != null) {
            for (int i = 0; i < args.length; i++) {
                context.setVariable("p" + i, args[i]);
                context.setVariable("a" + i, args[i]);
            }
        }

        // 添加返回值
        if (result != null) {
            context.setVariable("result", result);
        }

        // 添加常用变量
        context.setVariable("methodName", method.getName());
        context.setVariable("className", method.getDeclaringClass().getSimpleName());

        return context;
    }

    /**
     * 获取原始的SpEL表达式解析器
     *
     * @return SpEL表达式解析器
     */
    public ExpressionParser getParser() {
        return parser;
    }

    /**
     * 解析表达式字符串
     *
     * @param expressionString 表达式字符串
     * @return 表达式对象
     * @throws ParseException 解析异常
     */
    public Expression parseExpression(String expressionString) throws ParseException {
        try {
            return parser.parseExpression(expressionString);
        } catch (ParseException e) {
            log.error("SpEL表达式解析失败: {}", expressionString, e);
            throw e;
        }
    }

    /**
     * 解析表达式字符串（带上下文）
     *
     * @param expressionString 表达式字符串
     * @param context          解析上下文
     * @return 表达式对象
     * @throws ParseException 解析异常
     */
    public Expression parseExpression(String expressionString, ParserContext context) throws ParseException {
        try {
            return parser.parseExpression(expressionString, context);
        } catch (ParseException e) {
            log.error("SpEL表达式解析失败: {}", expressionString, e);
            throw e;
        }
    }
}
