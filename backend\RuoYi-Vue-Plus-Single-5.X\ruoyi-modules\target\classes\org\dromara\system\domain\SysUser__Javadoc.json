{"doc": " 用户对象 sys_user\n\n <AUTHOR>\n", "fields": [{"name": "userId", "doc": " 用户ID\n"}, {"name": "userGrade", "doc": " 用户等级\n"}, {"name": "userBalance", "doc": " 用户余额\n"}, {"name": "deptId", "doc": " 部门ID\n"}, {"name": "userName", "doc": " 用户账号\n"}, {"name": "nick<PERSON><PERSON>", "doc": " 用户昵称\n"}, {"name": "userType", "doc": " 用户类型（sys_user系统用户）\n"}, {"name": "email", "doc": " 用户邮箱\n"}, {"name": "phonenumber", "doc": " 手机号码\n"}, {"name": "sex", "doc": " 用户性别\n"}, {"name": "avatar", "doc": " 用户头像\n"}, {"name": "password", "doc": " 密码\n"}, {"name": "status", "doc": " 帐号状态（0正常 1停用）\n"}, {"name": "delFlag", "doc": " 删除标志（0代表存在 1代表删除）\n"}, {"name": "loginIp", "doc": " 最后登录IP\n"}, {"name": "loginDate", "doc": " 最后登录时间\n"}, {"name": "remark", "doc": " 备注\n"}], "enumConstants": [], "methods": [], "constructors": []}