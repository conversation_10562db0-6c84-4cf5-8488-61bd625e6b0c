{"doc": "\n 面试分析服务接口\r\n \r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "analyzeEmotion", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 分析表情情绪\r\n \r\n @param imageData base64编码的图像数据\r\n @param questionId 问题ID\r\n @return 情绪分析结果\r\n"}, {"name": "analyzeSpeech", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 分析语音内容\r\n \r\n @param audioData base64编码的音频数据\r\n @param questionId 问题ID\r\n @return 语音分析结果\r\n"}, {"name": "generateEmotionSuggestion", "paramTypes": ["cn.hutool.json.JSONObject", "java.lang.Integer"], "doc": "\n 基于情绪分析生成智能建议\r\n \r\n @param emotionResult 情绪分析结果\r\n @param questionId 问题ID\r\n @return 智能建议\r\n"}, {"name": "generateSpeechSuggestion", "paramTypes": ["cn.hutool.json.JSONObject", "java.lang.Integer"], "doc": "\n 基于语音分析生成智能建议\r\n \r\n @param speechResult 语音分析结果\r\n @param questionId 问题ID\r\n @return 智能建议\r\n"}, {"name": "generateComprehensiveSuggestion", "paramTypes": ["cn.hutool.json.JSONObject"], "doc": "\n 生成综合面试建议\r\n \r\n @param context 面试上下文\r\n @return 综合建议\r\n"}], "constructors": []}