{"doc": " MongoDB配置类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "mongoDatabaseFactory", "paramTypes": ["com.mongodb.client.MongoClient"], "doc": " MongoDB数据库工厂\n"}, {"name": "mongoTemplate", "paramTypes": ["org.springframework.data.mongodb.MongoDatabaseFactory"], "doc": " MongoDB模板\n"}, {"name": "transactionManager", "paramTypes": ["org.springframework.data.mongodb.MongoDatabaseFactory"], "doc": " MongoDB事务管理器\n"}], "constructors": []}