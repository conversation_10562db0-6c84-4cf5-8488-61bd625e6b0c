{"doc": " 用户活动统计对象 app_activity_statistics\n\n <AUTHOR>\n @date 2025-07-16\n", "fields": [{"name": "id", "doc": " 统计ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "activityType", "doc": " 活动类型\n"}, {"name": "statDate", "doc": " 统计日期\n"}, {"name": "totalDuration", "doc": " 总时长(毫秒)\n"}, {"name": "sessionCount", "doc": " 会话次数\n"}, {"name": "avgDuration", "doc": " 平均时长(毫秒)\n"}, {"name": "maxDuration", "doc": " 最长时长(毫秒)\n"}], "enumConstants": [], "methods": [{"name": "initialize", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType", "java.time.LocalDate", "java.lang.Long"], "doc": " 初始化统计数据\n\n @param userId          用户ID\n @param activityType    活动类型\n @param statDate        统计日期\n @param sessionDuration 首次会话时长\n @return 初始化的统计对象\n"}, {"name": "updateStatistics", "paramTypes": ["java.lang.Long"], "doc": " 更新统计数据\n\n @param sessionDuration 新增的会话时长\n"}], "constructors": []}