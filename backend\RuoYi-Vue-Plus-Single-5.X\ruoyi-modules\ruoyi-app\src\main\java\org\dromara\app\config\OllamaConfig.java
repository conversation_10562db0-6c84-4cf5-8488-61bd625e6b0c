package org.dromara.app.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

/**
 * Ollama配置类
 *
 * <AUTHOR>
 */
@Configuration
public class OllamaConfig {

    /**
     * 配置Ollama RestTemplate
     */
    @Bean(name = "ollamaRestTemplate")
    public RestTemplate ollamaRestTemplate() {
        OkHttp3ClientHttpRequestFactory factory = new OkHttp3ClientHttpRequestFactory();
        factory.setConnectTimeout(Duration.ofSeconds(30));
        factory.setReadTimeout(Duration.ofMinutes(5)); // 长时间等待模型响应
        factory.setWriteTimeout(Duration.ofSeconds(30));

        return new RestTemplate(factory);
    }

    /**
     * 配置WebClient用于嵌入服务
     */
    @Bean
    public org.springframework.web.reactive.function.client.WebClient webClient() {
        return org.springframework.web.reactive.function.client.WebClient.builder()
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB
            .build();
    }

    /**
     * Ollama属性配置
     */
    @Data
    @Configuration
    @ConfigurationProperties(prefix = "ollama")
    public static class OllamaProperties {

        /**
         * Ollama服务器地址
         */
        private String baseUrl = "http://localhost:11434";

        /**
         * 默认模型名称
         */
        private String defaultModel = "deepseek-r1:1.5b";

        /**
         * 连接超时时间（秒）
         */
        private Integer connectTimeout = 30;

        /**
         * 读取超时时间（秒）
         */
        private Integer readTimeout = 300;

        /**
         * 写入超时时间（秒）
         */
        private Integer writeTimeout = 30;

        /**
         * 默认温度参数
         */
        private Double defaultTemperature = 0.7;

        /**
         * 默认最大Token数
         */
        private Integer defaultMaxTokens = 4096;

        /**
         * 是否启用流式响应
         */
        private Boolean enableStreaming = true;

        /**
         * 重试次数
         */
        private Integer retryCount = 3;

        /**
         * 重试间隔（毫秒）
         */
        private Long retryInterval = 1000L;

        /**
         * 是否启用健康检查
         */
        private Boolean enableHealthCheck = true;

        /**
         * 健康检查间隔（秒）
         */
        private Integer healthCheckInterval = 60;

        /**
         * 嵌入模型名称
         */
        private String embeddingModel = "mxbai-embed-large";

        /**
         * 向量维度
         */
        private Integer vectorDimension = 1024;

        /**
         * 相似度阈值
         */
        private Double similarityThreshold = 0.7;

        /**
         * 系统提示词模板
         */
        private SystemPrompts systemPrompts = new SystemPrompts();

        @Data
        public static class SystemPrompts {
            private String general = "你是一个专业的rjb-sias，可以回答各种问题并提供有用的建议。请用中文回答。";
            private String interviewer = "你是一个专业的面试官，负责进行技术面试。请根据候选人的背景和目标职位提出相关问题。";
            private String resumeAnalyzer = "你是一个专业的简历分析师，可以分析简历内容并提供改进建议。";
            private String skillAssessor = "你是一个技能评估专家，可以评估候选人的技术能力和给出建议。";
            private String careerAdvisor = "你是一个职业规划顾问，可以为求职者提供职业发展建议。";
            private String mockInterviewer = "你是一个模拟面试官，请进行逼真的面试模拟，并在最后给出评价和建议。";
            private String learningGuide = "你是一个学习指导师，可以制定学习计划并提供学习建议。";
        }
    }
}
