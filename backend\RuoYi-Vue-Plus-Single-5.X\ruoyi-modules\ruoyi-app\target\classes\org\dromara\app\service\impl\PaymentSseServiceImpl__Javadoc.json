{"doc": "\n 支付SSE服务实现类\r\n 管理SSE连接和推送支付状态消息\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "SSE_TIMEOUT", "doc": "\n SSE连接超时时间（毫秒）- 5分钟\r\n"}, {"name": "sseConnections", "doc": "\n SSE连接存储Map\r\n Key: 订单号, Value: SSE连接信息\r\n"}], "enumConstants": [], "methods": [{"name": "validatePaymentToken", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 验证支付token\r\n"}, {"name": "createConnection", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 创建SSE连接\r\n"}, {"name": "pushPaymentSuccess", "paramTypes": ["java.lang.String"], "doc": "\n 推送支付成功消息\r\n"}, {"name": "pushPaymentFailed", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 推送支付失败消息\r\n"}, {"name": "pushPaymentCancelled", "paramTypes": ["java.lang.String"], "doc": "\n 推送支付取消消息\r\n"}, {"name": "manualQueryOrderStatus", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 手动查询订单状态\r\n"}, {"name": "closeConnection", "paramTypes": ["java.lang.String"], "doc": "\n 关闭SSE连接\r\n"}, {"name": "cleanupExpiredConnections", "paramTypes": [], "doc": "\n 清理过期连接\r\n"}, {"name": "sendMessage", "paramTypes": ["org.springframework.web.servlet.mvc.method.annotation.SseEmitter", "java.lang.String", "java.lang.String", "java.lang.Object"], "doc": "\n 发送SSE消息\r\n"}, {"name": "sendHeartbeat", "paramTypes": ["org.springframework.web.servlet.mvc.method.annotation.SseEmitter"], "doc": "\n 发送心跳消息\r\n"}, {"name": "setTimeout", "paramTypes": ["java.lang.Runnable", "long"], "doc": "\n 延迟执行任务\r\n"}, {"name": "handlePaymentSuccessEvent", "paramTypes": ["org.dromara.app.event.PaymentEvent.PaymentSuccessEvent"], "doc": "\n 监听支付成功事件\r\n"}, {"name": "handlePaymentFailedEvent", "paramTypes": ["org.dromara.app.event.PaymentEvent.PaymentFailedEvent"], "doc": "\n 监听支付失败事件\r\n"}, {"name": "handlePaymentCancelledEvent", "paramTypes": ["org.dromara.app.event.PaymentEvent.PaymentCancelledEvent"], "doc": "\n 监听支付取消事件\r\n"}], "constructors": []}