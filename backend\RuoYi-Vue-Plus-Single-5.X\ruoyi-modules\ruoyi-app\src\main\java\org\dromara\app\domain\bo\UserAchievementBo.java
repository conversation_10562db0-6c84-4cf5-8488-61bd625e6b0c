package org.dromara.app.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户成就业务对象 app_user_achievement
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户成就业务对象")
public class UserAchievementBo extends BaseEntity {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 成就ID
     */
    @Schema(description = "成就ID")
    @NotNull(message = "成就ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long achievementId;

    /**
     * 解锁时间
     */
    @Schema(description = "解锁时间")
    private Date unlockTime;

    /**
     * 进度百分比(0-100)
     */
    @Schema(description = "进度百分比")
    private BigDecimal progress;

    /**
     * 当前数值
     */
    @Schema(description = "当前数值")
    private Long currentValue;

    /**
     * 目标数值
     */
    @Schema(description = "目标数值")
    private Long targetValue;

    /**
     * 是否完成(0否 1是)
     */
    @Schema(description = "是否完成")
    private String isCompleted;

    /**
     * 是否已通知(0否 1是)
     */
    @Schema(description = "是否已通知")
    private String isNotified;

}
