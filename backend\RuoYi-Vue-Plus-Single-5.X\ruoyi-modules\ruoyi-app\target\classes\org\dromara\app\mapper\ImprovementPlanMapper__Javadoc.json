{"doc": "\n 提升计划Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-17\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByResultId", "paramTypes": ["java.lang.String"], "doc": "\n 根据结果ID查询提升计划\r\n\r\n @param resultId 结果ID\r\n @return 提升计划\r\n"}, {"name": "selectByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询提升计划列表\r\n\r\n @param userId 用户ID\r\n @return 提升计划列表\r\n"}, {"name": "selectByUserIdAndStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 根据用户ID和状态查询提升计划列表\r\n\r\n @param userId 用户ID\r\n @param status 状态\r\n @return 提升计划列表\r\n"}, {"name": "deleteByResultId", "paramTypes": ["java.lang.String"], "doc": "\n 根据结果ID删除提升计划\r\n\r\n @param resultId 结果ID\r\n @return 删除数量\r\n"}, {"name": "updateProgress", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 更新计划进度\r\n\r\n @param id       计划ID\r\n @param progress 进度\r\n @return 更新数量\r\n"}, {"name": "selectActiveByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户活跃的提升计划\r\n\r\n @param userId 用户ID\r\n @return 提升计划列表\r\n"}, {"name": "selectExpiringPlans", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询即将到期的提升计划\r\n\r\n @param userId 用户ID\r\n @param days   天数\r\n @return 提升计划列表\r\n"}], "constructors": []}