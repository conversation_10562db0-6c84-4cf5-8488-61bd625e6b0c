{"doc": " 学习资源服务实现类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "checkDatabaseCompatibility", "paramTypes": ["org.dromara.app.domain.LearningResource"], "doc": " 检查资源数据与数据库模式的兼容性\n 主要用于防止数据库错误，检查字段长度和约束\n\n @param resource 学习资源\n"}, {"name": "checkField<PERSON>ength", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.String", "int"], "doc": " 检查字段长度\n\n @param errors 错误列表\n @param fieldName 字段名称\n @param value 字段值\n @param maxLength 最大长度\n"}, {"name": "checkJsonFieldsConsistency", "paramTypes": ["org.dromara.app.domain.LearningResource", "java.util.List"], "doc": " 检查JSON字段的一致性\n\n @param resource 学习资源\n @param errors 错误列表\n"}, {"name": "validateResource", "paramTypes": ["org.dromara.app.domain.LearningResource", "boolean"], "doc": " 验证学习资源数据\n\n @param resource 学习资源\n @param isCreate 是否为创建操作\n"}, {"name": "validate<PERSON><PERSON><PERSON><PERSON>s", "paramTypes": ["org.dromara.app.domain.LearningResource", "java.util.List"], "doc": " 验证JSON格式字段\n\n @param resource 学习资源\n @param errors 错误列表\n"}, {"name": "validateQualityAssessment", "paramTypes": ["org.dromara.app.domain.LearningResource.QualityAssessment"], "doc": " 验证质量评估信息\n @param qualityAssessment 质量评估信息\n"}, {"name": "calculateOverallQuality", "paramTypes": ["org.dromara.app.domain.LearningResource.QualityAssessment"], "doc": " 计算综合质量评分\n"}, {"name": "calculateContentQuality", "paramTypes": ["org.dromara.app.domain.LearningResource"], "doc": " 计算内容质量评分\n"}, {"name": "calculateTeachingEffectiveness", "paramTypes": ["org.dromara.app.domain.LearningResource"], "doc": " 计算教学效果评分\n"}, {"name": "calculateTimeliness", "paramTypes": ["org.dromara.app.domain.LearningResource"], "doc": " 计算更新及时性评分\n"}, {"name": "calculateUserExperience", "paramTypes": ["org.dromara.app.domain.LearningResource"], "doc": " 计算用户体验评分\n"}, {"name": "calculateRecommendationScore", "paramTypes": ["org.dromara.app.domain.LearningResource", "java.util.Map"], "doc": " 计算推荐评分\n"}], "constructors": []}