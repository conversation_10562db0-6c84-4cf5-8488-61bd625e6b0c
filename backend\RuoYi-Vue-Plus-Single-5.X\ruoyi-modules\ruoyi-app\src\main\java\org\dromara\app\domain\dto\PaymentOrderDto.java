package org.dromara.app.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 支付订单请求DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "支付订单请求参数")
public class PaymentOrderDto {

    /**
     * 商品ID
     */
    @NotNull(message = "商品ID不能为空")
    @Schema(description = "商品ID", example = "1")
    private Long productId;

    /**
     * 商品类型
     */
    @NotBlank(message = "商品类型不能为空")
    @Size(max = 50, message = "商品类型长度不能超过50个字符")
    @Schema(description = "商品类型", example = "book", allowableValues = {"book", "video", "question-bank", "course", "plan"})
    private String productType;

    /**
     * 商品标题
     */
    @NotBlank(message = "商品标题不能为空")
    @Size(max = 200, message = "商品标题长度不能超过200个字符")
    @Schema(description = "商品标题", example = "技术面试完全指南")
    private String productTitle;

    /**
     * 支付金额
     */
    @NotNull(message = "支付金额不能为空")
    @DecimalMin(value = "0.01", message = "支付金额必须大于0")
    @Schema(description = "支付金额", example = "59.00")
    private BigDecimal amount;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "1")
    private Long userId;

    /**
     * 支付方式
     */
    @NotBlank(message = "支付方式不能为空")
    @Schema(description = "支付方式", example = "alipay", allowableValues = {"alipay", "wechat"})
    private String paymentMethod;

    /**
     * 客户端IP
     */
    @Schema(description = "客户端IP", example = "***********")
    private String clientIp;

    /**
     * 用户代理
     */
    @Schema(description = "用户代理")
    private String userAgent;

    /**
     * 备注信息
     */
    @Size(max = 500, message = "备注信息长度不能超过500个字符")
    @Schema(description = "备注信息")
    private String remark;
}
