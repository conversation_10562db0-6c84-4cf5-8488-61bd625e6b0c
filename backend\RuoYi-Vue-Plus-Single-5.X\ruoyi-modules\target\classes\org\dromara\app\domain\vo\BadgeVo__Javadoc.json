{"doc": " 徽章数据视图对象\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [{"name": "id", "doc": " 徽章ID\n"}, {"name": "icon", "doc": " 徽章图标\n"}, {"name": "color", "doc": " 徽章颜色\n"}, {"name": "title", "doc": " 徽章标题\n"}, {"name": "desc", "doc": " 徽章描述\n"}, {"name": "unlocked", "doc": " 是否解锁\n"}, {"name": "unlockedAt", "doc": " 解锁时间\n"}, {"name": "isPinned", "doc": " 是否置顶\n"}, {"name": "pinnedAt", "doc": " 置顶时间\n"}, {"name": "category", "doc": " 徽章类别\n"}, {"name": "rarity", "doc": " 稀有度\n"}, {"name": "achievementId", "doc": " 关联成就ID\n"}, {"name": "sort", "doc": " 排序\n"}], "enumConstants": [], "methods": [], "constructors": []}