package org.dromara.common.es.constant;

/**
 * ES常量
 *
 * <AUTHOR>
 */
public interface EsConstant {

    /**
     * 默认索引分片数
     */
    Integer DEFAULT_SHARDS = 1;

    /**
     * 默认索引副本数
     */
    Integer DEFAULT_REPLICAS = 1;

    /**
     * 默认批量操作大小
     */
    Integer DEFAULT_BATCH_SIZE = 1000;

    /**
     * 默认查询超时时间（毫秒）
     */
    Long DEFAULT_TIMEOUT = 30000L;

    /**
     * 默认页大小
     */
    Integer DEFAULT_PAGE_SIZE = 10;

    /**
     * 最大页大小
     */
    Integer MAX_PAGE_SIZE = 10000;

    /**
     * 默认高亮前缀
     */
    String DEFAULT_HIGHLIGHT_PRE_TAG = "<em>";

    /**
     * 默认高亮后缀
     */
    String DEFAULT_HIGHLIGHT_POST_TAG = "</em>";

    /**
     * 聚合桶最大数量
     */
    Integer MAX_BUCKET_SIZE = 10000;

}
