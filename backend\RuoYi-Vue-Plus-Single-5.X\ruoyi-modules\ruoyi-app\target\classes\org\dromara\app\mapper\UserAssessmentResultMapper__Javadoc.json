{"doc": "\n 用户评估结果Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectResultsByRecordId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据记录ID查询评估结果列表\r\n\r\n @param recordId 记录ID\r\n @return 评估结果列表\r\n"}, {"name": "selectResultsByRecordIdAndCategory", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 根据记录ID和类别查询评估结果列表\r\n\r\n @param recordId 记录ID\r\n @param category 类别\r\n @return 评估结果列表\r\n"}, {"name": "selectCategoryAverageScoresByRecordId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据记录ID查询各类别的平均分\r\n\r\n @param recordId 记录ID\r\n @return 各类别平均分\r\n"}, {"name": "insertBatch", "paramTypes": ["java.util.List"], "doc": "\n 批量插入评估结果\r\n\r\n @param results 评估结果列表\r\n @return 插入数量\r\n"}, {"name": "deleteByRecordId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据记录ID删除评估结果\r\n\r\n @param recordId 记录ID\r\n @return 删除数量\r\n"}, {"name": "selectResultsByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询所有评估结果\r\n\r\n @param userId 用户ID\r\n @return 评估结果列表\r\n"}], "constructors": []}