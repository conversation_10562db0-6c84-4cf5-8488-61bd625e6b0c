{"doc": " 基于LangChain4j的聊天服务实现类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "callAgentService", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map", "java.lang.Long"], "doc": " 调用特定类型的Agent服务（同步）\n\n @param agentType Agent类型\n @param message   用户消息\n @param params    额外参数\n @param userId    用户ID\n @return 处理结果\n"}, {"name": "callAgentServiceStream", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map", "java.lang.Long"], "doc": " 调用特定类型的Agent服务（流式）\n\n @param agentType Agent类型\n @param message   用户消息\n @param params    额外参数\n @param userId    用户ID\n @return SSE流\n"}, {"name": "invokeAgentService", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 根据Agent类型调用相应的服务\n"}, {"name": "invokeXunfeiStreamingService", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "dev.langchain4j.model.chat.response.StreamingChatResponseHandler"], "doc": " 调用讯飞流式Agent服务\n"}, {"name": "buildXunfeiContext", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 构建讯飞大模型需要的上下文信息\n"}, {"name": "buildSystemPromptForAgent", "paramTypes": ["java.lang.String"], "doc": " 根据Agent类型构建系统提示词\n"}, {"name": "buildChatHistoryForXunfei", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 构建讯飞大模型需要的历史对话记录\n"}, {"name": "invokeStreamingAgentService", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String", "dev.langchain4j.model.chat.response.StreamingChatResponseHandler"], "doc": " 调用流式Agent服务\n"}, {"name": "createAgentContext", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.String"], "doc": " 创建Agent上下文\n"}, {"name": "getOrCreateMemory", "paramTypes": ["java.lang.String"], "doc": " 获取或创建内存\n"}, {"name": "loadHistoryToMemory", "paramTypes": ["java.lang.String", "dev.langchain4j.memory.chat.MessageWindowChatMemory"], "doc": " 从数据库加载历史消息到内存\n"}, {"name": "enhanceMessageWithRAG", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 使用RAG增强消息\n"}, {"name": "buildMetadata", "paramTypes": ["org.dromara.app.domain.dto.ChatRequestDto", "org.dromara.app.domain.Agent"], "doc": " 构建消息元数据\n"}, {"name": "convertHistoryToConversationTurns", "paramTypes": ["java.lang.String", "java.lang.Long", "int"], "doc": " 将聊天历史转换为ConversationTurn对象列表\n\n @param sessionId  会话ID\n @param userId     用户ID\n @param maxHistory 最大历史消息数量\n @return 对话历史转换结果\n"}, {"name": "buildChainOfThoughtPrompt", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 构建思维链提示词\n\n @param agentType Agent类型\n @param message   用户消息\n @return 增强后的提示词\n"}, {"name": "buildOptimizedSystemPrompt", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Long"], "doc": " 构建优化的系统提示词\n\n @param originalPrompt 原始系统提示词\n @param agentType      Agent类型\n @param userId         用户ID\n @return 优化后的系统提示词\n"}, {"name": "buildConversationalPrompt", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Long", "int"], "doc": " 构建对话提示词\n\n @param currentQuery     当前查询\n @param sessionId        会话ID\n @param userId           用户ID\n @param maxHistoryLength 最大历史长度\n @return 构建的对话提示词\n"}, {"name": "adaptPrompt<PERSON>ength", "paramTypes": ["java.lang.String", "int"], "doc": " 适应提示词长度，避免超出模型限制\n\n @param prompt    提示词\n @param maxTokens 最大token数量\n @return 调整后的提示词\n"}], "constructors": []}