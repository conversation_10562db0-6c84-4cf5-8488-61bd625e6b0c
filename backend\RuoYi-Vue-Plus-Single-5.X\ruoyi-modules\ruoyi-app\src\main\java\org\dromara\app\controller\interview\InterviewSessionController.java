package org.dromara.app.controller.interview;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.vo.InterviewResponseVo;
import org.dromara.app.service.IInterviewService;
import org.dromara.app.service.IMultimodalAnalysisService;
import org.dromara.common.core.domain.R;
import org.dromara.common.ratelimiter.annotation.RateLimiter;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 面试会话控制器
 * 专门处理面试房间相关的API请求，集成多模态分析功能
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/interview/session")
@Tag(name = "面试会话管理", description = "面试房间会话相关接口")
public class InterviewSessionController extends BaseController {

    private final IInterviewService interviewService;
    private final IMultimodalAnalysisService multimodalAnalysisService;

    /**
     * 获取会话信息
     */
    @Operation(summary = "获取会话信息", description = "获取指定面试会话的详细信息")
    @GetMapping("/info")
    @RateLimiter(key = "app:interview:session:info", count = 10)
    public R<InterviewResponseVo.SessionInfo> getSessionInfo(
        @Parameter(description = "会话ID", required = true) @RequestParam String sessionId) {
        log.info("获取会话信息，sessionId: {}", sessionId);

        InterviewResponseVo.SessionInfo result = interviewService.getSessionInfo(sessionId);
        return R.ok("获取成功", result);
    }

    /**
     * 获取面试问题
     */
    @Operation(summary = "获取面试问题", description = "获取指定会话的面试问题")
    @GetMapping("/question")
    @RateLimiter(key = "app:interview:session:question", count = 10)
    public R<InterviewResponseVo.InterviewQuestion> getQuestion(
        @Parameter(description = "会话ID", required = true) @RequestParam String sessionId,
        @Parameter(description = "问题索引") @RequestParam(required = false) Integer index) {
        log.info("获取面试问题，sessionId: {}, index: {}", sessionId, index);

        // 获取问题列表
        InterviewResponseVo.QuestionListResponse questionList = interviewService.getSessionQuestions(sessionId);

        if (questionList.getQuestions() == null || questionList.getQuestions().isEmpty()) {
            return R.fail("没有找到面试问题");
        }

        // 如果没有指定索引，返回第一个问题
        int questionIndex = index != null ? index : 0;
        if (questionIndex >= questionList.getQuestions().size()) {
            return R.fail("问题索引超出范围");
        }

        InterviewResponseVo.InterviewQuestion question = questionList.getQuestions().get(questionIndex);
        return R.ok("获取成功", question);
    }

    /**
     * 提交面试回答（集成多模态分析）
     */
    @Operation(summary = "提交面试回答", description = "提交用户对特定问题的回答，并进行多模态分析")
    @PostMapping("/submit-answer")
    @RateLimiter(key = "app:interview:session:submit-answer", count = 5)
    public R<SubmitAnswerResponse> submitAnswer(
        @RequestParam @NotBlank(message = "会话ID不能为空") String sessionId,
        @RequestParam @NotBlank(message = "问题ID不能为空") String questionId,
        @RequestParam @NotBlank(message = "回答内容不能为空") String answer,
        @RequestParam(required = false) MultipartFile audioFile,
        @RequestParam(required = false) MultipartFile videoFile,
        @RequestParam(required = false) Integer duration,
        @RequestParam(required = false) String jobPosition) {
        
        log.info("提交面试回答，sessionId: {}, questionId: {}", sessionId, questionId);

        try {
            // 1. 提交基础回答
            InterviewResponseVo.AnswerResponse basicResponse = interviewService.submitAnswer(
                sessionId, questionId, answer, null, duration);

            // 2. 进行多模态分析
            IMultimodalAnalysisService.MultimodalAnalysisResult analysisResult = null;
            if (audioFile != null || videoFile != null || answer != null) {
                analysisResult = multimodalAnalysisService.comprehensiveAnalysis(
                    sessionId, audioFile, videoFile, answer, jobPosition);
            }

            // 3. 构建响应
            SubmitAnswerResponse response = new SubmitAnswerResponse();
            response.setSuccess(true);
            response.setMessage("回答提交成功");
            response.setBasicResponse(basicResponse);
            response.setAnalysisResult(analysisResult);

            return R.ok("回答已提交并完成分析", response);

        } catch (Exception e) {
            log.error("提交回答失败", e);
            return R.fail("提交回答失败: " + e.getMessage());
        }
    }

    /**
     * 结束面试会话
     */
    @Operation(summary = "结束面试会话", description = "结束指定的面试会话")
    @PostMapping("/end")
    @RateLimiter(key = "app:interview:session:end", count = 5)
    public R<InterviewResponseVo.EndSessionResponse> endSession(
        @RequestParam @NotBlank(message = "会话ID不能为空") String sessionId,
        @RequestParam(required = false) String reason) {
        log.info("结束面试会话，sessionId: {}, reason: {}", sessionId, reason);

        InterviewResponseVo.EndSessionResponse result = interviewService.endSession(sessionId, reason);
        return R.ok("面试已结束", result);
    }

    /**
     * 提交面试反馈
     */
    @Operation(summary = "提交面试反馈", description = "提交用户对面试的反馈")
    @PostMapping("/feedback")
    @RateLimiter(key = "app:interview:session:feedback", count = 5)
    public R<InterviewResponseVo.FeedbackResponse> submitFeedback(
        @RequestParam @NotBlank(message = "会话ID不能为空") String sessionId,
        @RequestParam Integer rating,
        @RequestParam(required = false) String comments) {
        log.info("提交面试反馈，sessionId: {}, rating: {}", sessionId, rating);

        InterviewResponseVo.FeedbackResponse result = interviewService.submitFeedback(
            sessionId, rating, comments, null);
        return R.ok("反馈已提交", result);
    }

    /**
     * 检查设备状态
     */
    @Operation(summary = "检查设备状态", description = "检查用户设备的麦克风和摄像头状态")
    @GetMapping("/check-devices")
    public R<Map<String, Boolean>> checkDevices() {
        log.info("检查设备状态");

        Map<String, Boolean> result = interviewService.checkDevices();
        return R.ok("设备状态检查完成", result);
    }

    /**
     * 获取会话状态
     */
    @Operation(summary = "获取会话状态", description = "获取指定会话的当前状态")
    @GetMapping("/status")
    public R<InterviewResponseVo.SessionStatus> getSessionStatus(
        @Parameter(description = "会话ID", required = true) @RequestParam String sessionId) {
        log.info("获取会话状态，sessionId: {}", sessionId);

        InterviewResponseVo.SessionStatus result = interviewService.getSessionStatus(sessionId);
        return R.ok("获取成功", result);
    }

    /**
     * 提交回答响应对象
     */
    public static class SubmitAnswerResponse {
        private boolean success;
        private String message;
        private InterviewResponseVo.AnswerResponse basicResponse;
        private IMultimodalAnalysisService.MultimodalAnalysisResult analysisResult;

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public InterviewResponseVo.AnswerResponse getBasicResponse() { return basicResponse; }
        public void setBasicResponse(InterviewResponseVo.AnswerResponse basicResponse) { this.basicResponse = basicResponse; }
        public IMultimodalAnalysisService.MultimodalAnalysisResult getAnalysisResult() { return analysisResult; }
        public void setAnalysisResult(IMultimodalAnalysisService.MultimodalAnalysisResult analysisResult) { this.analysisResult = analysisResult; }
    }
}
