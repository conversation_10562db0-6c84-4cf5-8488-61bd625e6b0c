package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__162;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.FeedbackBoToFeedbackMapper;
import org.dromara.app.domain.vo.FeedbackVo;
import org.dromara.app.domain.vo.FeedbackVoToFeedbackMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__162.class,
    uses = {FeedbackVoToFeedbackMapper.class,FeedbackBoToFeedbackMapper.class},
    imports = {}
)
public interface FeedbackToFeedbackVoMapper extends BaseMapper<Feedback, FeedbackVo> {
}
