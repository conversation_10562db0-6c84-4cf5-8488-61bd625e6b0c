{"doc": " JavaMail 配置属性\n\n <AUTHOR>\n", "fields": [{"name": "enabled", "doc": " 过滤开关\n"}, {"name": "host", "doc": " SMTP服务器域名\n"}, {"name": "port", "doc": " SMTP服务端口\n"}, {"name": "auth", "doc": " 是否需要用户名密码验证\n"}, {"name": "user", "doc": " 用户名\n"}, {"name": "pass", "doc": " 密码\n"}, {"name": "from", "doc": " 发送方，遵循RFC-822标准<br>\n 发件人可以是以下形式：\n\n <pre>\n 1. <EMAIL>\n 2.  name &lt;<EMAIL>&gt;\n </pre>\n"}, {"name": "starttlsEnable", "doc": " 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。它将纯文本连接升级为加密连接（TLS或SSL）， 而不是使用一个单独的加密通信端口。\n"}, {"name": "sslEnable", "doc": " 使用 SSL安全连接\n"}, {"name": "timeout", "doc": " SMTP超时时长，单位毫秒，缺省值不超时\n"}, {"name": "connectionTimeout", "doc": " Socket连接超时值，单位毫秒，缺省值不超时\n"}], "enumConstants": [], "methods": [], "constructors": []}