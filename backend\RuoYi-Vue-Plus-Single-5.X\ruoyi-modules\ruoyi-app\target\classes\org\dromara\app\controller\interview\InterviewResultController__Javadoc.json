{"doc": "\n 面试结果Controller\r\n\r\n <AUTHOR>\r\n @date 2025-07-17\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getResultSummary", "paramTypes": ["java.lang.String"], "doc": "\n 获取面试结果摘要\r\n"}, {"name": "getResultDetail", "paramTypes": ["java.lang.String"], "doc": "\n 获取面试结果详情\r\n"}, {"name": "getPerformanceMetrics", "paramTypes": ["java.lang.String"], "doc": "\n 获取性能指标\r\n"}, {"name": "saveToHistory", "paramTypes": ["org.dromara.app.domain.bo.InterviewResultBo.SaveToHistoryRequest"], "doc": "\n 保存到历史记录\r\n"}, {"name": "shareResult", "paramTypes": ["org.dromara.app.domain.bo.InterviewResultBo.ShareResultRequest"], "doc": "\n 分享结果\r\n"}, {"name": "getImprovementPlan", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 获取提升计划\r\n"}, {"name": "getLearningResources", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取学习资源推荐\r\n"}, {"name": "getUserResults", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 获取用户面试结果列表\r\n"}, {"name": "getUserStatistics", "paramTypes": ["java.lang.Long"], "doc": "\n 获取用户面试统计\r\n"}, {"name": "generateReport", "paramTypes": ["java.lang.String"], "doc": "\n 生成面试报告\r\n"}, {"name": "deleteResult", "paramTypes": ["java.lang.String"], "doc": "\n 删除面试结果\r\n"}], "constructors": []}