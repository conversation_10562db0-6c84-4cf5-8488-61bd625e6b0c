{"doc": " RabbitMQ 模板封装\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "send", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 发送消息到默认交换机\n\n @param routingKey 路由键\n @param message    消息内容\n"}, {"name": "send", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object"], "doc": " 发送消息到指定交换机\n\n @param exchange   交换机名称\n @param routingKey 路由键\n @param message    消息内容\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object", "long"], "doc": " 发送延迟消息\n\n @param exchange   交换机名称\n @param routingKey 路由键\n @param message    消息内容\n @param delayTime  延迟时间（毫秒）\n"}, {"name": "sendAndReceive", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object"], "doc": " 发送消息并接收响应（RPC模式）\n\n @param exchange   交换机名称\n @param routingKey 路由键\n @param message    消息内容\n @return 响应结果\n"}, {"name": "sendToDead", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object"], "doc": " 发送消息到死信队列\n\n @param deadExchange   死信交换机\n @param deadRoutingKey 死信路由键\n @param message        消息内容\n"}, {"name": "receive", "paramTypes": ["java.lang.String"], "doc": " 接收并转换消息\n\n @param queueName 队列名称\n @return 消息内容\n"}, {"name": "receive", "paramTypes": ["java.lang.String", "long"], "doc": " 接收并转换消息（带超时）\n\n @param queueName 队列名称\n @param timeout   超时时间（毫秒）\n @return 消息内容\n"}, {"name": "createTextMessage", "paramTypes": ["java.lang.String"], "doc": " 创建文本消息\n\n @param content 消息内容\n @return Message对象\n"}], "constructors": []}