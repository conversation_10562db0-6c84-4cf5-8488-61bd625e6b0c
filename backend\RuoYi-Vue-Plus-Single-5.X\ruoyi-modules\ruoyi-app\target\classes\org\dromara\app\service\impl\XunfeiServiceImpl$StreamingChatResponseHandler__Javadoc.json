{"doc": "\n 流式聊天响应处理器\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "onResponse", "paramTypes": ["java.lang.String"], "doc": "\n 收到响应片段\r\n\r\n @param token 文本片段\r\n"}, {"name": "onComplete", "paramTypes": ["java.lang.String"], "doc": "\n 完成回调\r\n\r\n @param fullResponse 完整响应\r\n"}, {"name": "onError", "paramTypes": ["java.lang.Throwable"], "doc": "\n 错误回调\r\n\r\n @param throwable 异常信息\r\n"}], "constructors": []}