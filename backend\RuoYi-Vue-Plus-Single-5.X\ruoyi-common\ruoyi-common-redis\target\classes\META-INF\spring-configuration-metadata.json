{"groups": [{"name": "redisson", "type": "org.dromara.common.redis.config.properties.RedissonProperties", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties"}, {"name": "redisson.cluster-servers-config", "type": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties", "sourceMethod": "getClusterServersConfig()"}, {"name": "redisson.single-server-config", "type": "org.dromara.common.redis.config.properties.RedissonProperties$SingleServerConfig", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties", "sourceMethod": "getSingleServerConfig()"}], "properties": [{"name": "redisson.cluster-servers-config.client-name", "type": "java.lang.String", "description": "客户端名称", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig"}, {"name": "redisson.cluster-servers-config.idle-connection-timeout", "type": "java.lang.Integer", "description": "连接空闲超时，单位：毫秒", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig", "defaultValue": 0}, {"name": "redisson.cluster-servers-config.master-connection-minimum-idle-size", "type": "java.lang.Integer", "description": "master最小空闲连接数", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig", "defaultValue": 0}, {"name": "redisson.cluster-servers-config.master-connection-pool-size", "type": "java.lang.Integer", "description": "master连接池大小", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig", "defaultValue": 0}, {"name": "redisson.cluster-servers-config.read-mode", "type": "org.redisson.config.ReadMode", "description": "读取模式", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig"}, {"name": "redisson.cluster-servers-config.slave-connection-minimum-idle-size", "type": "java.lang.Integer", "description": "slave最小空闲连接数", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig", "defaultValue": 0}, {"name": "redisson.cluster-servers-config.slave-connection-pool-size", "type": "java.lang.Integer", "description": "slave连接池大小", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig", "defaultValue": 0}, {"name": "redisson.cluster-servers-config.subscription-connection-pool-size", "type": "java.lang.Integer", "description": "发布和订阅连接池大小", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig", "defaultValue": 0}, {"name": "redisson.cluster-servers-config.subscription-mode", "type": "org.redisson.config.SubscriptionMode", "description": "订阅模式", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig"}, {"name": "redisson.cluster-servers-config.timeout", "type": "java.lang.Integer", "description": "命令等待超时，单位：毫秒", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$ClusterServersConfig", "defaultValue": 0}, {"name": "redisson.key-prefix", "type": "java.lang.String", "description": "redis缓存key前缀", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties"}, {"name": "redisson.netty-threads", "type": "java.lang.Integer", "description": "Netty线程池数量,默认值 = 当前处理核数量 * 2", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties", "defaultValue": 0}, {"name": "redisson.single-server-config.client-name", "type": "java.lang.String", "description": "客户端名称", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$SingleServerConfig"}, {"name": "redisson.single-server-config.connection-minimum-idle-size", "type": "java.lang.Integer", "description": "最小空闲连接数", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$SingleServerConfig", "defaultValue": 0}, {"name": "redisson.single-server-config.connection-pool-size", "type": "java.lang.Integer", "description": "连接池大小", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$SingleServerConfig", "defaultValue": 0}, {"name": "redisson.single-server-config.idle-connection-timeout", "type": "java.lang.Integer", "description": "连接空闲超时，单位：毫秒", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$SingleServerConfig", "defaultValue": 0}, {"name": "redisson.single-server-config.subscription-connection-pool-size", "type": "java.lang.Integer", "description": "发布和订阅连接池大小", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$SingleServerConfig", "defaultValue": 0}, {"name": "redisson.single-server-config.timeout", "type": "java.lang.Integer", "description": "命令等待超时，单位：毫秒", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties$SingleServerConfig", "defaultValue": 0}, {"name": "redisson.threads", "type": "java.lang.Integer", "description": "线程池数量,默认值 = 当前处理核数量 * 2", "sourceType": "org.dromara.common.redis.config.properties.RedissonProperties", "defaultValue": 0}], "hints": []}