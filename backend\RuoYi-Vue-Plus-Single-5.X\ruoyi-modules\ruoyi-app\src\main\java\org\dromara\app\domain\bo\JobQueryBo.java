package org.dromara.app.domain.bo;

import lombok.Data;

import java.util.List;

/**
 * 岗位查询业务对象
 *
 * <AUTHOR> Assistant
 * @date 2025-07-19
 */
@Data
public class JobQueryBo {

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 岗位名称
     */
    private String name;

    /**
     * 公司名称
     */
    private String company;

    /**
     * 技术领域
     */
    private String technicalDomain;

    /**
     * 搜索关键词（用于标题、公司名和标签的模糊查询）
     */
    private String keyword;

    /**
     * 排序字段
     * 可选值：smart(智能排序), hot(热门优先), difficulty(难度),
     * duration(时长), pass_rate(通过率), created_time(创建时间)
     */
    private String sortBy = "smart";

    /**
     * 排序方向
     * 可选值：asc(升序), desc(降序)
     */
    private String sortOrder = "desc";

    /**
     * 筛选条件
     */
    private List<String> filters;
}
