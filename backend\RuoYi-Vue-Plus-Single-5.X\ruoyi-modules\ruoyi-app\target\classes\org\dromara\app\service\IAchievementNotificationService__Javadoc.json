{"doc": "\n 成就通知服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendAchievementUnlockNotification", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.AchievementVo"], "doc": "\n 发送成就解锁通知\r\n\r\n @param userId      用户ID\r\n @param achievement 成就信息\r\n"}, {"name": "batchSendAchievementUnlockNotification", "paramTypes": ["java.lang.Long", "java.util.List"], "doc": "\n 批量发送成就解锁通知\r\n\r\n @param userId       用户ID\r\n @param achievements 成就列表\r\n"}, {"name": "sendAchievementProgressNotification", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.UserAchievementVo"], "doc": "\n 发送成就进度更新通知\r\n\r\n @param userId           用户ID\r\n @param userAchievement  用户成就信息\r\n"}, {"name": "sendAchievementReminderNotification", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.vo.AchievementVo", "java.lang.String"], "doc": "\n 发送成就提醒通知\r\n\r\n @param userId      用户ID\r\n @param achievement 成就信息\r\n @param message     提醒消息\r\n"}, {"name": "sendLeaderboardNotification", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.String"], "doc": "\n 发送成就排行榜通知\r\n\r\n @param userId   用户ID\r\n @param ranking  排名\r\n @param category 类别\r\n"}, {"name": "markNotificationAsRead", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 标记通知为已读\r\n\r\n @param userId         用户ID\r\n @param notificationId 通知ID\r\n @return 操作结果\r\n"}, {"name": "getUnreadNotificationCount", "paramTypes": ["java.lang.Long"], "doc": "\n 获取用户未读通知数量\r\n\r\n @param userId 用户ID\r\n @return 未读通知数量\r\n"}, {"name": "getUserNotifications", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 获取用户通知列表\r\n\r\n @param userId 用户ID\r\n @param limit  限制数量\r\n @return 通知列表\r\n"}, {"name": "cleanExpiredNotifications", "paramTypes": ["java.lang.Integer"], "doc": "\n 清理过期通知\r\n\r\n @param days 保留天数\r\n @return 清理数量\r\n"}], "constructors": []}