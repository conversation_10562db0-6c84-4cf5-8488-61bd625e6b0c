package org.dromara.common.caffeine.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 缓存值包装类
 * 支持自定义过期时间
 *
 * <AUTHOR>
 */
@Data
public class CacheValueWrapper implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实际缓存的值
     */
    private Object value;

    /**
     * 创建时间戳（毫秒）
     */
    private long createTime;

    /**
     * 过期时间戳（毫秒）
     */
    private long expireTime;

    /**
     * 过期时长（秒）
     */
    private long ttl;

    /**
     * 是否永不过期
     */
    private boolean neverExpire;

    public CacheValueWrapper() {
        this.createTime = System.currentTimeMillis();
    }

    public CacheValueWrapper(Object value) {
        this();
        this.value = value;
        this.neverExpire = true;
    }

    public CacheValueWrapper(Object value, long ttlSeconds) {
        this();
        this.value = value;
        this.ttl = ttlSeconds;
        this.expireTime = this.createTime + (ttlSeconds * 1000);
        this.neverExpire = false;
    }

    /**
     * 创建永不过期的缓存值
     *
     * @param value 值
     * @return 包装对象
     */
    public static CacheValueWrapper of(Object value) {
        return new CacheValueWrapper(value);
    }

    /**
     * 创建带过期时间的缓存值
     *
     * @param value      值
     * @param ttlSeconds 过期时间（秒）
     * @return 包装对象
     */
    public static CacheValueWrapper of(Object value, long ttlSeconds) {
        return new CacheValueWrapper(value, ttlSeconds);
    }

    /**
     * 检查是否已过期
     *
     * @return true如果已过期
     */
    public boolean isExpired() {
        if (neverExpire) {
            return false;
        }
        return System.currentTimeMillis() > expireTime;
    }

    /**
     * 获取剩余生存时间（秒）
     *
     * @return 剩余秒数，-1表示永不过期，0表示已过期
     */
    public long getRemainingTtl() {
        if (neverExpire) {
            return -1;
        }

        long remaining = (expireTime - System.currentTimeMillis()) / 1000;
        return Math.max(0, remaining);
    }

    /**
     * 获取创建时间
     *
     * @return 创建时间
     */
    public LocalDateTime getCreateDateTime() {
        return LocalDateTime.ofInstant(
            java.time.Instant.ofEpochMilli(createTime),
            java.time.ZoneId.systemDefault()
        );
    }

    /**
     * 获取过期时间
     *
     * @return 过期时间，null表示永不过期
     */
    public LocalDateTime getExpireDateTime() {
        if (neverExpire) {
            return null;
        }
        return LocalDateTime.ofInstant(
            java.time.Instant.ofEpochMilli(expireTime),
            java.time.ZoneId.systemDefault()
        );
    }

}
