{"doc": " 用户简历管理控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询用户简历分页列表\n"}, {"name": "getMyResumeList", "paramTypes": [], "doc": " 查询当前用户的所有简历列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.String"], "doc": " 获取用户简历详细信息\n\n @param resumeId 简历主键\n"}, {"name": "uploadResume", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String", "java.lang.String"], "doc": " 上传用户简历文件\n\n @param file     简历文件\n @param fileName 原始文件名（可选）\n @param fileType 文件类型（可选）\n"}, {"name": "add", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo"], "doc": " 新增用户简历\n"}, {"name": "edit", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo"], "doc": " 修改用户简历\n"}, {"name": "renameResume", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 重命名简历\n\n @param resumeId 简历ID\n @param request  重命名请求参数\n"}, {"name": "setDefaultResume", "paramTypes": ["java.lang.Long"], "doc": " 设置默认简历\n\n @param resumeId 简历ID\n"}, {"name": "cancelDefaultResume", "paramTypes": ["java.lang.Long"], "doc": " 取消默认简历\n\n @param resumeId 简历ID\n"}, {"name": "getDefaultResume", "paramTypes": [], "doc": " 获取当前用户的默认简历\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除用户简历\n\n @param resumeIds 简历主键串\n"}, {"name": "downloadResume", "paramTypes": ["java.lang.Long", "jakarta.servlet.http.HttpServletResponse"], "doc": " 下载用户简历文件\n\n @param resumeId 简历ID\n"}, {"name": "previewResumeContent", "paramTypes": ["java.lang.Long"], "doc": " 预览简历文件内容\n\n @param resumeId 简历ID\n @return 预览内容响应\n"}, {"name": "getStructuredResumeContent", "paramTypes": ["java.lang.Long"], "doc": " 获取简历结构化预览内容\n\n <p>将简历文件内容解析为结构化数据，包括：</p>\n <ul>\n   <li>基本信息：简历名称、创建时间等</li>\n   <li>个人信息：姓名、联系方式、求职意向等</li>\n   <li>教育经历：学校、专业、学历等</li>\n   <li>工作经验：公司、职位、工作内容等</li>\n   <li>技能特长：技能名称、熟练度等</li>\n   <li>其他信息：项目经历、获奖情况、证书等</li>\n </ul>\n\n @param resumeId 简历ID，必须为正整数\n @return 结构化简历内容响应，包含完整的简历数据结构\n @throws ServiceException 当简历不存在、已禁用或解析失败时抛出\n"}, {"name": "getResumePreviewImage", "paramTypes": ["java.lang.Long"], "doc": " 获取简历预览图片\n\n <p>生成简历的预览图片，用于快速预览简历内容。</p>\n <p>返回的数据包含：</p>\n <ul>\n   <li>imageUrl: 完整尺寸预览图URL</li>\n   <li>thumbnailUrl: 缩略图URL（可选）</li>\n   <li>resumeId: 简历ID</li>\n   <li>resumeName: 简历名称</li>\n </ul>\n\n <p>注意：目前返回占位图片，后续可扩展为真实的图片生成功能。</p>\n\n @param resumeId 简历ID，必须为正整数\n @return 预览图片数据响应，包含图片URL和相关信息\n @throws ServiceException 当简历不存在、已禁用或生成失败时抛出\n"}], "constructors": []}