{"doc": "\n 用户简历服务实现\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryPageList", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo", "<PERSON><PERSON><PERSON><PERSON>"], "doc": "\n 查询用户简历分页列表\r\n\r\n @param bo        查询条件\r\n @param pageQuery 分页参数\r\n @return 分页结果\r\n"}, {"name": "selectUserResumeList", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户的所有简历列表\r\n\r\n @param userId 用户ID\r\n @return 简历列表\r\n"}, {"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": "\n 根据简历ID查询简历详情\r\n\r\n @param resumeId 简历ID\r\n @return 简历详情\r\n"}, {"name": "uploadResume", "paramTypes": ["java.lang.Long", "org.springframework.web.multipart.MultipartFile", "java.lang.String", "java.lang.String"], "doc": "\n 上传用户简历文件\r\n\r\n @param userId   用户ID\r\n @param file     简历文件\r\n @param fileName 原始文件名（可选）\r\n @param fileType 文件类型（可选）\r\n @return 上传结果\r\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo"], "doc": "\n 新增用户简历\r\n\r\n @param bo 简历信息\r\n @return 新增结果\r\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo"], "doc": "\n 修改用户简历\r\n\r\n @param bo 简历信息\r\n @return 修改结果\r\n"}, {"name": "renameResume", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 重命名简历\r\n\r\n @param resumeId   简历ID\r\n @param resumeName 新名称\r\n @return 重命名结果\r\n"}, {"name": "setDefaultResume", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 设置默认简历\r\n\r\n @param userId   用户ID\r\n @param resumeId 简历ID\r\n @return 设置结果\r\n"}, {"name": "cancelDefaultResume", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 取消默认简历\r\n\r\n @param userId   用户ID\r\n @param resumeId 简历ID\r\n @return 取消结果\r\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": "\n 校验并批量删除用户简历信息\r\n\r\n @param ids     主键集合\r\n @param isValid 是否校验,true-删除前校验,false-不校验\r\n @return 删除结果\r\n"}, {"name": "downloadResume", "paramTypes": ["java.lang.Long", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 下载用户简历文件\r\n\r\n @param resumeId 简历ID\r\n @param response 响应对象\r\n @throws IOException IO异常\r\n"}, {"name": "getDefaultResume", "paramTypes": ["java.lang.Long"], "doc": "\n 获取用户的默认简历\r\n\r\n @param userId 用户ID\r\n @return 默认简历\r\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.app.domain.UserResume"], "doc": "\n 保存前的数据校验\r\n\r\n @param entity 实体类数据\r\n"}, {"name": "buildQueryWrapper", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo"], "doc": "\n 构建查询条件\r\n\r\n @param bo 业务对象\r\n @return 查询条件\r\n"}, {"name": "formatFileSize", "paramTypes": ["org.dromara.app.domain.vo.UserResumeVo"], "doc": "\n 格式化文件大小\r\n\r\n @param vo 视图对象\r\n"}, {"name": "formatFileSize", "paramTypes": ["java.lang.Long"], "doc": "\n 格式化文件大小\r\n\r\n @param size 文件大小(字节)\r\n @return 格式化后的大小字符串\r\n"}, {"name": "previewResumeContent", "paramTypes": ["java.lang.Long"], "doc": "\n 预览简历文件内容\r\n\r\n @param resumeId 简历ID\r\n @return 预览内容数据\r\n @throws Exception 预览异常\r\n"}, {"name": "getStructuredResumeContent", "paramTypes": ["java.lang.Long"], "doc": "\n 获取简历结构化预览内容\r\n\r\n @param resumeId 简历ID\r\n @return 结构化简历内容数据\r\n @throws Exception 解析异常\r\n"}, {"name": "generateResumePreviewImage", "paramTypes": ["java.lang.Long"], "doc": "\n 生成简历预览图片\r\n\r\n @param resumeId 简历ID\r\n @return 预览图片数据\r\n @throws Exception 生成异常\r\n"}, {"name": "extractPdfContent", "paramTypes": ["SysOssVo"], "doc": "\n 提取PDF文件内容\r\n"}, {"name": "extractDocContent", "paramTypes": ["SysOssVo"], "doc": "\n 提取DOC文件内容\r\n"}, {"name": "extractDocxContent", "paramTypes": ["SysOssVo"], "doc": "\n 提取DOCX文件内容\r\n"}, {"name": "extractFileContent", "paramTypes": ["java.lang.String", "SysOssVo"], "doc": "\n 根据文件类型提取文件内容\r\n"}, {"name": "parseResumeContent", "paramTypes": ["java.lang.String", "org.dromara.app.domain.vo.UserResumeVo"], "doc": "\n 解析简历内容为结构化数据\r\n"}, {"name": "createDefaultPersonalInfo", "paramTypes": [], "doc": "\n 创建默认个人信息\r\n"}, {"name": "parsePersonalInfo", "paramTypes": ["java.lang.String"], "doc": "\n 解析个人信息\r\n"}, {"name": "parseEducationList", "paramTypes": ["java.lang.String"], "doc": "\n 解析教育经历\r\n"}, {"name": "parseWorkExperienceList", "paramTypes": ["java.lang.String"], "doc": "\n 解析工作经验\r\n"}, {"name": "parseSkillList", "paramTypes": ["java.lang.String"], "doc": "\n 解析技能列表\r\n"}, {"name": "parseProjectList", "paramTypes": ["java.lang.String"], "doc": "\n 解析项目经历\r\n"}, {"name": "parseAwardList", "paramTypes": ["java.lang.String"], "doc": "\n 解析获奖情况\r\n"}, {"name": "parseCertificateList", "paramTypes": ["java.lang.String"], "doc": "\n 解析证书资质\r\n"}, {"name": "parseLanguageList", "paramTypes": ["java.lang.String"], "doc": "\n 解析语言能力\r\n"}, {"name": "parseHobbies", "paramTypes": ["java.lang.String"], "doc": "\n 解析兴趣爱好\r\n"}, {"name": "parseSelfEvaluation", "paramTypes": ["java.lang.String"], "doc": "\n 解析自我评价\r\n"}, {"name": "extractField", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map", "java.lang.String"], "doc": "\n 提取字段值的辅助方法\r\n"}, {"name": "getFileInputStream", "paramTypes": ["SysOssVo"], "doc": "\n 获取OSS文件的输入流\r\n"}], "constructors": []}