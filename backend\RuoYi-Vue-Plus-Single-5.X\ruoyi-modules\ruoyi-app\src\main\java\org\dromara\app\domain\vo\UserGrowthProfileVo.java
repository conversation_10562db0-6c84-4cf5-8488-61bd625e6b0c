package org.dromara.app.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户成长档案视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(description = "用户成长档案视图对象")
public class UserGrowthProfileVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 当前阶段
     */
    @Schema(description = "当前阶段")
    private String currentStage;

    /**
     * 加入日期
     */
    @Schema(description = "加入日期")
    private Date joinDate;

    /**
     * 最后活跃日期
     */
    @Schema(description = "最后活跃日期")
    private Date lastActiveDate;

    /**
     * 总面试次数
     */
    @Schema(description = "总面试次数")
    private Integer totalInterviews;

    /**
     * 初始能力评估
     */
    @Schema(description = "初始能力评估")
    private InitialAbilityAssessmentVo initialAssessment;

    /**
     * 当前能力评估
     */
    @Schema(description = "当前能力评估")
    private InitialAbilityAssessmentVo currentAssessment;

    /**
     * 进步率
     */
    @Schema(description = "进步率")
    private Integer improvementRate;

    /**
     * 目标职位
     */
    @Schema(description = "目标职位")
    private String targetPosition;

    /**
     * 学习目标列表
     */
    @Schema(description = "学习目标列表")
    private List<String> learningGoals;

    /**
     * 成就列表
     */
    @Schema(description = "成就列表")
    private List<String> achievements;

    /**
     * 连续学习天数
     */
    @Schema(description = "连续学习天数")
    private Integer continuousLearningDays;

    /**
     * 已完成课程数
     */
    @Schema(description = "已完成课程数")
    private Integer completedCourses;
}
