{"doc": "\n 聊天会话Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectUserSessions", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.Long", "java.lang.Integer"], "doc": "\n 分页查询用户会话列表\r\n\r\n @param page   分页对象\r\n @param userId 用户ID\r\n @param status 会话状态（可选）\r\n @return 会话分页结果\r\n"}, {"name": "selectByIdAndUserId", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 根据会话ID和用户ID查询会话详情\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @return 会话详情\r\n"}, {"name": "updateSessionStats", "paramTypes": ["java.lang.String", "java.lang.Integer", "java.lang.String", "java.lang.Long"], "doc": "\n 更新会话消息统计\r\n\r\n @param sessionId      会话ID\r\n @param messageCount   消息数量\r\n @param lastMessage    最后消息内容\r\n @param lastActiveTime 最后活跃时间\r\n @return 影响行数\r\n"}, {"name": "updateSessionTitle", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.String"], "doc": "\n 更新会话标题\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @param title     新标题\r\n @return 影响行数\r\n"}, {"name": "updateSessionStatus", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.Integer"], "doc": "\n 归档/取消归档会话\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @param status    状态（0-归档，1-活跃）\r\n @return 影响行数\r\n"}, {"name": "getUserChatStats", "paramTypes": ["java.lang.Long"], "doc": "\n 获取用户聊天统计信息\r\n\r\n @param userId 用户ID\r\n @return 统计信息\r\n"}, {"name": "deleteByIdAndUserId", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 删除用户会话（软删除）\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @return 影响行数\r\n"}, {"name": "getPopularAgentTypes", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取热门Agent类型统计\r\n\r\n @param limit 返回数量限制\r\n @return Agent使用统计\r\n"}, {"name": "selectActiveSessions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询用户活跃会话列表\r\n\r\n @param userId 用户ID\r\n @param limit  数量限制\r\n @return 活跃会话列表\r\n"}, {"name": "selectUserSessionsByAgent", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer"], "doc": "\n 根据Agent类型查询用户会话\r\n\r\n @param userId    用户ID\r\n @param agentType Agent类型\r\n @param limit     数量限制\r\n @return 会话列表\r\n"}, {"name": "countUserSessions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 统计用户会话数量\r\n\r\n @param userId 用户ID\r\n @param status 会话状态（可选）\r\n @return 会话数量\r\n"}, {"name": "selectRecentSessions", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 查询最近活跃的会话\r\n\r\n @param userId    用户ID\r\n @param timestamp 时间戳阈值\r\n @return 最近活跃会话列表\r\n"}, {"name": "updateLastActiveTime", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 更新会话最后活跃时间\r\n\r\n @param sessionId      会话ID\r\n @param lastActiveTime 最后活跃时间\r\n @return 影响行数\r\n"}, {"name": "batchUpdateSessionStatus", "paramTypes": ["java.util.List", "java.lang.Long", "java.lang.Integer"], "doc": "\n 批量更新会话状态\r\n\r\n @param sessionIds 会话ID列表\r\n @param userId     用户ID\r\n @param status     新状态\r\n @return 影响行数\r\n"}, {"name": "selectSessionStats", "paramTypes": ["java.lang.Long"], "doc": "\n 获取用户各Agent类型的会话统计\r\n\r\n @param userId 用户ID\r\n @return 统计信息\r\n"}, {"name": "archiveInactiveSessions", "paramTypes": ["java.lang.Long"], "doc": "\n 清理长时间未活跃的会话（自动归档）\r\n\r\n @param threshold 时间阈值\r\n @return 影响行数\r\n"}, {"name": "resetSessionStats", "paramTypes": ["java.lang.String"], "doc": "\n 重置会话统计信息\r\n\r\n @param sessionId 会话ID\r\n @return 影响行数\r\n"}, {"name": "updateTitle", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.String"], "doc": "\n 更新会话标题\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @param title     新标题\r\n @return 影响行数\r\n"}, {"name": "updateArchiveStatus", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": "\n 更新归档状态\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @param archived  归档状态\r\n @return 影响行数\r\n"}, {"name": "countUserSessions", "paramTypes": ["java.lang.Long"], "doc": "\n 统计用户会话总数\r\n\r\n @param userId 用户ID\r\n @return 会话总数\r\n"}, {"name": "countActiveSessions", "paramTypes": ["java.lang.Long"], "doc": "\n 统计活跃会话数\r\n\r\n @param userId 用户ID\r\n @return 活跃会话数\r\n"}, {"name": "countSessionsByAgent", "paramTypes": ["java.lang.Long"], "doc": "\n 按Agent类型统计会话\r\n\r\n @param userId 用户ID\r\n @return 统计结果\r\n"}], "constructors": []}