{"doc": "\n 学习推荐控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "generateLearningRecommendations", "paramTypes": ["java.lang.String"], "doc": "\n 基于面试会话生成学习推荐\r\n\r\n @param sessionId 面试会话ID\r\n @return 学习路径推荐列表\r\n"}, {"name": "generateRecommendationsByWeaknesses", "paramTypes": ["org.dromara.app.controller.learning.LearningRecommendationController.WeaknessRecommendationRequest"], "doc": "\n 基于弱项生成学习推荐\r\n\r\n @param request 请求参数\r\n @return 学习路径推荐列表\r\n"}, {"name": "generateRecommendationsByJob", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 基于岗位生成学习推荐\r\n\r\n @param jobPosition 岗位信息\r\n @param userLevel 用户水平\r\n @return 学习路径推荐列表\r\n"}, {"name": "getRecommendedResources", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 获取学习资源推荐\r\n\r\n @param skillArea 技能领域\r\n @param difficulty 难度等级\r\n @param resourceType 资源类型\r\n @return 学习资源列表\r\n"}, {"name": "calculateLearningPriorities", "paramTypes": ["org.dromara.app.controller.learning.LearningRecommendationController.PriorityCalculationRequest"], "doc": "\n 计算学习优先级\r\n\r\n @param request 请求参数\r\n @return 优先级映射\r\n"}, {"name": "personalizelearningPaths", "paramTypes": ["org.dromara.app.controller.learning.LearningRecommendationController.PersonalizationRequest"], "doc": "\n 个性化学习路径调整\r\n\r\n @param request 请求参数\r\n @return 调整后的学习路径\r\n"}, {"name": "getLearningPathDetail", "paramTypes": ["java.lang.String"], "doc": "\n 获取学习路径详情\r\n\r\n @param pathId 路径ID\r\n @return 学习路径详情\r\n"}, {"name": "getPopularLearningPaths", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取热门学习路径\r\n\r\n @param category 分类\r\n @param limit 限制数量\r\n @return 热门学习路径列表\r\n"}, {"name": "convertToInterviewReport", "paramTypes": ["org.dromara.app.service.IReportGenerationService.InterviewReportData"], "doc": "\n 转换报告数据为InterviewReport对象\r\n"}, {"name": "convertToDomainScore", "paramTypes": ["org.dromara.app.service.IReportGenerationService.DimensionScore"], "doc": "\n 转换维度评分\r\n"}, {"name": "getPopularPaths", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取热门学习路径（模拟数据）\r\n"}], "constructors": []}