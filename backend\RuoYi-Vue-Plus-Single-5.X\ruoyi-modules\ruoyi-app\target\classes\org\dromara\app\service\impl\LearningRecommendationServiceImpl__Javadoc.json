{"doc": "\n 学习推荐服务实现类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "initializeSkillAreaMapping", "paramTypes": [], "doc": "\n 初始化技能领域映射\r\n"}, {"name": "initializeJobSkillMapping", "paramTypes": [], "doc": "\n 初始化岗位技能映射\r\n"}, {"name": "initializeResourceLibrary", "paramTypes": [], "doc": "\n 初始化资源库\r\n"}, {"name": "createResource", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.Double", "java.lang.String", "java.lang.Bo<PERSON>an"], "doc": "\n 创建学习资源\r\n"}, {"name": "createTechnicalSkillPath", "paramTypes": ["java.lang.String", "int"], "doc": "\n 创建技术技能学习路径\r\n"}, {"name": "createCommunicationSkillPath", "paramTypes": ["int"], "doc": "\n 创建沟通技能学习路径\r\n"}, {"name": "createLogicalThinkingPath", "paramTypes": ["int"], "doc": "\n 创建逻辑思维学习路径\r\n"}, {"name": "createBodyLanguagePath", "paramTypes": ["int"], "doc": "\n 创建肢体语言学习路径\r\n"}, {"name": "createInnovationPath", "paramTypes": ["int"], "doc": "\n 创建创新能力学习路径\r\n"}, {"name": "createInterviewSkillsPath", "paramTypes": ["int"], "doc": "\n 创建面试技巧学习路径\r\n"}], "constructors": []}