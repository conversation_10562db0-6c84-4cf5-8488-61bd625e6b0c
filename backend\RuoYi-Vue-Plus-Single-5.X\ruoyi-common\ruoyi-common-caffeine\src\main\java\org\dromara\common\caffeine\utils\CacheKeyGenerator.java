package org.dromara.common.caffeine.utils;

import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 缓存键生成器
 *
 * <AUTHOR>
 */
@Component
public class CacheKeyGenerator {

    /**
     * 生成缓存键
     *
     * @param method 方法
     * @param args   参数
     * @return 缓存键
     */
    public String generate(Method method, Object... args) {
        StringBuilder keyBuilder = new StringBuilder();

        // 类名
        keyBuilder.append(method.getDeclaringClass().getSimpleName());
        keyBuilder.append(".");

        // 方法名
        keyBuilder.append(method.getName());

        // 参数
        if (args != null && args.length > 0) {
            String argsStr = Arrays.stream(args)
                .map(this::getArgString)
                .collect(Collectors.joining(","));
            keyBuilder.append("(").append(argsStr).append(")");
        } else {
            keyBuilder.append("()");
        }

        return keyBuilder.toString();
    }

    /**
     * 获取参数字符串表示
     */
    private String getArgString(Object arg) {
        if (arg == null) {
            return "null";
        }

        if (arg instanceof String) {
            return "'" + arg + "'";
        }

        if (arg instanceof Number || arg instanceof Boolean) {
            return arg.toString();
        }

        // 对于复杂对象，使用hashCode
        return arg.getClass().getSimpleName() + "@" + arg.hashCode();
    }

    /**
     * 简单键生成（仅使用参数）
     *
     * @param args 参数
     * @return 缓存键
     */
    public String generateSimpleKey(Object... args) {
        if (args == null || args.length == 0) {
            return "default";
        }

        if (args.length == 1) {
            return getArgString(args[0]);
        }

        return Arrays.stream(args)
            .map(this::getArgString)
            .collect(Collectors.joining(":"));
    }

}
