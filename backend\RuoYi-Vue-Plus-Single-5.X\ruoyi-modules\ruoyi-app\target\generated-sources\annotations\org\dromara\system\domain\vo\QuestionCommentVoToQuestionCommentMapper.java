package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__162;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.mybatis.core.domain.QuestionComment;
import org.dromara.common.mybatis.core.domain.QuestionCommentToQuestionCommentVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__162.class,
    uses = {QuestionCommentToQuestionCommentVoMapper.class,QuestionCommentToQuestionCommentVoMapper.class},
    imports = {}
)
public interface QuestionCommentVoToQuestionCommentMapper extends BaseMapper<QuestionCommentVo, QuestionComment> {
}
