{"doc": " 岗位信息对象 app_job\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [{"name": "id", "doc": " 岗位ID\n"}, {"name": "categoryId", "doc": " 分类ID\n"}, {"name": "name", "doc": " 岗位名称\n"}, {"name": "company", "doc": " 公司名称\n"}, {"name": "logo", "doc": " 公司Logo\n"}, {"name": "difficulty", "doc": " 难度等级（1-5）\n"}, {"name": "duration", "doc": " 面试时长（分钟）\n"}, {"name": "questionCount", "doc": " 题目数量\n"}, {"name": "tags", "doc": " 标签（JSON数组）\n"}, {"name": "description", "doc": " 岗位描述\n"}, {"name": "interviewers", "doc": " 面试人数\n"}, {"name": "passRate", "doc": " 通过率（百分比）\n"}, {"name": "viewCount", "doc": " 浏览次数\n"}, {"name": "favoriteCount", "doc": " 收藏次数\n"}, {"name": "sortOrder", "doc": " 排序号\n"}, {"name": "status", "doc": " 状态（0正常 1停用）\n"}, {"name": "delFlag", "doc": " 删除标志（0代表存在 1代表删除）\n"}, {"name": "technicalDomain", "doc": " 技术领域分类\n"}, {"name": "level", "doc": " 岗位级别（初级/中级/高级/专家）\n"}, {"name": "experienceYears", "doc": " 工作经验要求（年）\n"}, {"name": "educationRequirement", "doc": " 学历要求\n"}, {"name": "salaryRange", "doc": " 薪资范围\n"}, {"name": "location", "doc": " 工作地点\n"}, {"name": "coreSkills", "doc": " 核心技能要求（JSON数组）\n"}, {"name": "responsibilities", "doc": " 岗位职责\n"}, {"name": "requirements", "doc": " 任职要求\n"}], "enumConstants": [], "methods": [], "constructors": []}