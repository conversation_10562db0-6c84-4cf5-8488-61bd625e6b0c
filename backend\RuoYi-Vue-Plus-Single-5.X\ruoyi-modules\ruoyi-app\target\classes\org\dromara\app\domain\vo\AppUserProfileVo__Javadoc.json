{"doc": "\n 应用用户个人信息VO\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "phone", "doc": "\n 手机号\r\n"}, {"name": "email", "doc": "\n 邮箱\r\n"}, {"name": "name", "doc": "\n 用户姓名\r\n"}, {"name": "gender", "doc": "\n 用户性别（男/女）\r\n"}, {"name": "studentId", "doc": "\n 学号\r\n"}, {"name": "school", "doc": "\n 学校名称\r\n"}, {"name": "major", "doc": "\n 专业\r\n"}, {"name": "grade", "doc": "\n 年级\r\n"}, {"name": "introduction", "doc": "\n 个人简介\r\n"}, {"name": "avatar", "doc": "\n 用户头像\r\n"}, {"name": "registeredAt", "doc": "\n 注册时间\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}], "enumConstants": [], "methods": [], "constructors": []}