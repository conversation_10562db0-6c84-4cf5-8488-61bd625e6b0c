{"doc": "\n 学习推荐控制器\r\n 基于用户能力评估和学习历史，提供个性化的学习资源推荐\r\n\r\n @Author: SevenJL\r\n @CreateTime: 2025-07-27\r\n @Version: 1.0\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getRecommendedVideos", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": "\n 获取推荐视频列表\r\n 基于用户能力短板和学习偏好推荐视频课程\r\n\r\n @param pageNum     页码\r\n @param pageSize    每页大小\r\n @param searchQuery 搜索关键词（可选）\r\n @return 推荐视频列表\r\n"}, {"name": "getRecommendedQuestionBanks", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": "\n 获取推荐题库列表\r\n 基于用户能力短板和练习历史推荐题库\r\n\r\n @param pageNum     页码\r\n @param pageSize    每页大小\r\n @param searchQuery 搜索关键词（可选）\r\n @return 推荐题库列表\r\n"}, {"name": "getRecommendedBooks", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": "\n 获取推荐书籍列表\r\n 基于用户能力短板和阅读偏好推荐书籍\r\n\r\n @param pageNum     页码\r\n @param pageSize    每页大小\r\n @param searchQuery 搜索关键词（可选）\r\n @return 推荐书籍列表\r\n"}, {"name": "getUserCapabilities", "paramTypes": [], "doc": "\n 获取用户能力评估数据\r\n 用于前端显示用户当前能力状况和薄弱环节\r\n\r\n @return 用户能力评估数据\r\n"}, {"name": "getRecommendationStatistics", "paramTypes": [], "doc": "\n 获取推荐统计信息\r\n 包括推荐总数、各类型推荐数量等统计信息\r\n\r\n @return 推荐统计信息\r\n"}, {"name": "refreshRecommendations", "paramTypes": [], "doc": "\n 刷新推荐算法\r\n 基于用户最新的学习行为和能力评估重新计算推荐\r\n\r\n @return 刷新结果\r\n"}, {"name": "recordRecommendationFeedback", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.String"], "doc": "\n 记录用户对推荐内容的反馈\r\n 用于优化推荐算法\r\n\r\n @param resourceType 资源类型（video/question-bank/book）\r\n @param resourceId   资源ID\r\n @param action       用户行为（view/like/dislike/bookmark/start-learning）\r\n @return 反馈记录结果\r\n"}], "constructors": []}