{"doc": "\n 分析回调接口\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "onProgress", "paramTypes": ["int", "java.lang.String"], "doc": "\n 分析进度更新\r\n\r\n @param progress 进度百分比\r\n @param stage 当前阶段\r\n"}, {"name": "onComplete", "paramTypes": ["java.lang.Object"], "doc": "\n 分析完成\r\n\r\n @param result 分析结果\r\n"}, {"name": "onError", "paramTypes": ["java.lang.Throwable"], "doc": "\n 分析失败\r\n\r\n @param error 错误信息\r\n"}], "constructors": []}