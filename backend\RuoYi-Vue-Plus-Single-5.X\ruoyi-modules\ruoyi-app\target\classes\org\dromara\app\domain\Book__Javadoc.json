{"doc": "\n 面试书籍对象 app_book\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 书籍ID\r\n"}, {"name": "title", "doc": "\n 书籍标题\r\n"}, {"name": "author", "doc": "\n 作者\r\n"}, {"name": "cover", "doc": "\n 封面图片URL\r\n"}, {"name": "category", "doc": "\n 分类：technical/behavioral/algorithm/resume/salary\r\n"}, {"name": "rating", "doc": "\n 评分\r\n"}, {"name": "readCount", "doc": "\n 阅读次数\r\n"}, {"name": "chapters", "doc": "\n 章节数\r\n"}, {"name": "pages", "doc": "\n 页数\r\n"}, {"name": "isCompleted", "doc": "\n 是否完结：0-连载中，1-已完结\r\n"}, {"name": "tags", "doc": "\n 标签，逗号分隔\r\n"}, {"name": "description", "doc": "\n 书籍描述\r\n"}, {"name": "difficulty", "doc": "\n 难度：入门/进阶/高级\r\n"}, {"name": "price", "doc": "\n 价格\r\n"}, {"name": "isFree", "doc": "\n 是否免费：0-付费，1-免费\r\n"}, {"name": "status", "doc": "\n 状态：0-下架，1-上架\r\n"}, {"name": "sortOrder", "doc": "\n 排序序号\r\n"}, {"name": "tagList", "doc": "\n 标签列表（转换后的字段，不存储到数据库）\r\n"}, {"name": "readingProgress", "doc": "\n 当前用户阅读进度（不存储到数据库）\r\n"}, {"name": "isPurchased", "doc": "\n 是否已购买（不存储到数据库）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}