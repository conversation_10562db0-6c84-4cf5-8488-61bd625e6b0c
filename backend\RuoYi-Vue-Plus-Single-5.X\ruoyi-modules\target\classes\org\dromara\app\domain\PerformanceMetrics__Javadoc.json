{"doc": " 性能指标对象 app_performance_metrics\n\n <AUTHOR>\n @date 2025-07-17\n", "fields": [{"name": "id", "doc": " 性能指标ID\n"}, {"name": "resultId", "doc": " 结果ID\n"}, {"name": "technical", "doc": " 技术能力\n"}, {"name": "communication", "doc": " 沟通能力\n"}, {"name": "problemSolving", "doc": " 问题解决\n"}, {"name": "teamwork", "doc": " 团队合作\n"}, {"name": "leadership", "doc": " 领导力\n"}, {"name": "creativity", "doc": " 创造力\n"}, {"name": "detailedMetrics", "doc": " 详细指标（JSON对象）\n"}, {"name": "industryAverage", "doc": " 行业平均值（JSON对象）\n"}, {"name": "skillGaps", "doc": " 技能差距（JSON数组）\n"}, {"name": "skillStrengths", "doc": " 技能优势（JSON数组）\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}], "enumConstants": [], "methods": [], "constructors": []}