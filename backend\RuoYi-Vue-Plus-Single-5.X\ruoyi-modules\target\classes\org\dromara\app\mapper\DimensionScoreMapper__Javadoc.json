{"doc": " 维度评分Mapper接口\n\n <AUTHOR>\n @date 2025-07-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByResultId", "paramTypes": ["java.lang.String"], "doc": " 根据结果ID查询维度评分列表\n\n @param resultId 结果ID\n @return 维度评分列表\n"}, {"name": "selectByResultIdAndDimension", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据结果ID和维度名称查询维度评分\n\n @param resultId  结果ID\n @param dimension 维度名称\n @return 维度评分\n"}, {"name": "batchInsert", "paramTypes": ["java.util.List"], "doc": " 批量插入维度评分\n\n @param dimensionScores 维度评分列表\n @return 插入数量\n"}, {"name": "deleteByResultId", "paramTypes": ["java.lang.String"], "doc": " 根据结果ID删除维度评分\n\n @param resultId 结果ID\n @return 删除数量\n"}, {"name": "selectAvgScoreByDimension", "paramTypes": ["java.lang.String"], "doc": " 查询维度平均分数\n\n @param dimension 维度名称\n @return 平均分数\n"}, {"name": "selectUserDimensionHistory", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer"], "doc": " 查询用户在某维度的历史分数\n\n @param userId    用户ID\n @param dimension 维度名称\n @param limit     限制数量\n @return 分数列表\n"}], "constructors": []}