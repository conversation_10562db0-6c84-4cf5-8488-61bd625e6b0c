{"doc": "\n 用户徽章实体\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [{"name": "id", "doc": "\n 用户徽章ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "badgeId", "doc": "\n 徽章ID\r\n"}, {"name": "unlocked", "doc": "\n 是否解锁\r\n"}, {"name": "unlockedAt", "doc": "\n 解锁时间\r\n"}, {"name": "isPinned", "doc": "\n 是否置顶\r\n"}, {"name": "pinnedAt", "doc": "\n 置顶时间\r\n"}, {"name": "pinnedSort", "doc": "\n 置顶排序\r\n"}, {"name": "notificationStatus", "doc": "\n 通知状态（0=未通知，1=已通知）\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "doc": "\n 是否查看过\r\n"}, {"name": "viewedAt", "doc": "\n 查看时间\r\n"}, {"name": "unlockChannel", "doc": "\n 解锁渠道\r\n"}, {"name": "userAchievementId", "doc": "\n 关联用户成就ID\r\n"}, {"name": "extraData", "doc": "\n 额外数据（JSON格式）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}