{"doc": " 公告 服务层实现\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNoticeById", "paramTypes": ["java.lang.Long"], "doc": " 查询公告信息\n\n @param noticeId 公告ID\n @return 公告信息\n"}, {"name": "selectNoticeList", "paramTypes": ["org.dromara.system.domain.bo.SysNoticeBo"], "doc": " 查询公告列表\n\n @param notice 公告信息\n @return 公告集合\n"}, {"name": "insertNotice", "paramTypes": ["org.dromara.system.domain.bo.SysNoticeBo"], "doc": " 新增公告\n\n @param bo 公告信息\n @return 结果\n"}, {"name": "updateNotice", "paramTypes": ["org.dromara.system.domain.bo.SysNoticeBo"], "doc": " 修改公告\n\n @param bo 公告信息\n @return 结果\n"}, {"name": "deleteNoticeById", "paramTypes": ["java.lang.Long"], "doc": " 删除公告对象\n\n @param noticeId 公告ID\n @return 结果\n"}, {"name": "deleteNoticeByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除公告信息\n\n @param noticeIds 需要删除的公告ID\n @return 结果\n"}], "constructors": []}