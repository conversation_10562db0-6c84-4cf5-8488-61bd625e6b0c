{"doc": "\n 埋点事件数据传输对象\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "eventType", "doc": "\n 事件类型\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "eventData", "doc": "\n 事件数据\r\n"}, {"name": "timestamp", "doc": "\n 时间戳\r\n"}, {"name": "sessionId", "doc": "\n 会话ID\r\n"}, {"name": "pageUrl", "doc": "\n 页面URL\r\n"}, {"name": "pageTitle", "doc": "\n 页面标题\r\n"}, {"name": "userAgent", "doc": "\n 用户代理\r\n"}, {"name": "ip<PERSON><PERSON><PERSON>", "doc": "\n IP地址\r\n"}, {"name": "deviceInfo", "doc": "\n 设备信息\r\n"}, {"name": "browserInfo", "doc": "\n 浏览器信息\r\n"}], "enumConstants": [], "methods": [], "constructors": []}