package org.dromara.app.controller.interview;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.bo.InterviewResultBo;
import org.dromara.app.domain.vo.InterviewResultResponseVo;
import org.dromara.app.service.IInterviewResultService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.ratelimiter.annotation.RateLimiter;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 面试结果Controller
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/interview/result")
@Tag(name = "面试结果管理", description = "面试结果相关接口")
public class InterviewResultController extends BaseController {

    private final IInterviewResultService interviewResultService;

    /**
     * 获取面试结果摘要
     */
    @Operation(summary = "获取面试结果摘要", description = "根据结果ID或会话ID获取面试结果摘要信息")
    @GetMapping("/summary")
    public R<InterviewResultResponseVo.InterviewResultSummary> getResultSummary(
        @Parameter(description = "结果ID或会话ID", required = true)
        @RequestParam @NotBlank(message = "结果ID不能为空") String resultId) {
        log.info("获取面试结果摘要，resultId: {}", resultId);

        try {
            InterviewResultResponseVo.InterviewResultSummary summary = interviewResultService.getResultSummary(resultId);
            return R.ok("获取成功", summary);
        } catch (ServiceException e) {
            log.error("获取面试结果摘要失败: {}", e.getMessage());
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("获取面试结果摘要异常", e);
            return R.fail("系统异常，请稍后重试");
        }
    }

    /**
     * 获取面试结果详情
     */
    @Operation(summary = "获取面试结果详情", description = "根据结果ID或会话ID获取面试结果详细信息")
    @GetMapping("/detail")
    public R<InterviewResultResponseVo.InterviewResultDetail> getResultDetail(
        @Parameter(description = "结果ID或会话ID", required = true)
        @RequestParam @NotBlank(message = "结果ID不能为空") String resultId) {
        log.info("获取面试结果详情，resultId: {}", resultId);

        try {
            InterviewResultResponseVo.InterviewResultDetail detail = interviewResultService.getResultDetail(resultId);
            return R.ok("获取成功", detail);
        } catch (ServiceException e) {
            log.error("获取面试结果详情失败: {}", e.getMessage());
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("获取面试结果详情异常", e);
            return R.fail("系统异常，请稍后重试");
        }
    }

    /**
     * 获取性能指标
     */
    @Operation(summary = "获取性能指标", description = "根据结果ID获取性能指标数据")
    @GetMapping("/metrics")
    public R<InterviewResultResponseVo.PerformanceMetrics> getPerformanceMetrics(
        @Parameter(description = "结果ID", required = true)
        @RequestParam @NotBlank(message = "结果ID不能为空") String resultId) {
        log.info("获取性能指标，resultId: {}", resultId);

        try {
            InterviewResultResponseVo.PerformanceMetrics metrics = interviewResultService.getPerformanceMetrics(resultId);
            return R.ok("获取成功", metrics);
        } catch (ServiceException e) {
            log.error("获取性能指标失败: {}", e.getMessage());
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("获取性能指标异常", e);
            return R.fail("系统异常，请稍后重试");
        }
    }

    /**
     * 保存到历史记录
     */
    @Operation(summary = "保存到历史记录", description = "将面试结果保存到用户历史记录")
    @Log(title = "保存面试结果到历史记录", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public R<InterviewResultResponseVo.SaveHistoryResponse> saveToHistory(
        @RequestBody @Valid InterviewResultBo.SaveToHistoryRequest request) {
        log.info("保存到历史记录，请求参数: {}", request);

        try {
            InterviewResultResponseVo.SaveHistoryResponse response = interviewResultService.saveToHistory(
                request.getResultId(), request.getTitle());
            return R.ok("保存成功", response);
        } catch (ServiceException e) {
            log.error("保存到历史记录失败: {}", e.getMessage());
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("保存到历史记录异常", e);
            return R.fail("系统异常，请稍后重试");
        }
    }

    /**
     * 分享结果
     */
    @Operation(summary = "分享结果", description = "分享面试结果到指定平台")
    @Log(title = "分享面试结果", businessType = BusinessType.INSERT)
    @PostMapping("/share")
    public R<InterviewResultResponseVo.ShareResultResponse> shareResult(
        @RequestBody @Valid InterviewResultBo.ShareResultRequest request) {
        log.info("分享结果，请求参数: {}", request);

        try {
            InterviewResultResponseVo.ShareResultResponse response = interviewResultService.shareResult(
                request.getResultId(), request.getPlatform(), request.getContent());
            return R.ok("分享成功", response);
        } catch (ServiceException e) {
            log.error("分享结果失败: {}", e.getMessage());
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("分享结果异常", e);
            return R.fail("系统异常，请稍后重试");
        }
    }

    /**
     * 获取提升计划
     */
    @Operation(summary = "获取提升计划", description = "根据面试结果生成个性化提升计划")
    @GetMapping("/improvement-plan")
    public R<InterviewResultResponseVo.ImprovementPlan> getImprovementPlan(
        @Parameter(description = "结果ID", required = true)
        @RequestParam @NotBlank(message = "结果ID不能为空") String resultId,
        @Parameter(description = "用户ID（可选）")
        @RequestParam(required = false) Long userId) {
        log.info("获取提升计划，resultId: {}, userId: {}", resultId, userId);

        try {
            InterviewResultResponseVo.ImprovementPlan plan = interviewResultService.getImprovementPlan(resultId, userId);
            return R.ok("获取成功", plan);
        } catch (ServiceException e) {
            log.error("获取提升计划失败: {}", e.getMessage());
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("获取提升计划异常", e);
            return R.fail("系统异常，请稍后重试");
        }
    }

    /**
     * 获取学习资源推荐
     */
    @Operation(summary = "获取学习资源推荐", description = "根据面试结果推荐相关学习资源")
    @GetMapping("/learning-resources")
    public R<List<InterviewResultResponseVo.LearningResource>> getLearningResources(
        @Parameter(description = "结果ID", required = true)
        @RequestParam @NotBlank(message = "结果ID不能为空") String resultId,
        @Parameter(description = "数量限制")
        @RequestParam(required = false, defaultValue = "10")
        @Min(value = 1, message = "数量限制不能小于1")
        @Max(value = 50, message = "数量限制不能大于50") Integer limit) {
        log.info("获取学习资源推荐，resultId: {}, limit: {}", resultId, limit);

        try {
            List<InterviewResultResponseVo.LearningResource> resources =
                interviewResultService.getLearningResources(resultId, limit);
            return R.ok("获取成功", resources);
        } catch (ServiceException e) {
            log.error("获取学习资源推荐失败: {}", e.getMessage());
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("获取学习资源推荐异常", e);
            return R.fail("系统异常，请稍后重试");
        }
    }

    /**
     * 获取用户面试结果列表
     */
    @Operation(summary = "获取用户面试结果列表", description = "获取当前用户的面试结果列表")
    @GetMapping("/user/list")
    public R<List<InterviewResultResponseVo.InterviewResultSummary>> getUserResults(
        @Parameter(description = "用户ID（可选，默认当前用户）")
        @RequestParam(required = false) Long userId,
        @Parameter(description = "数量限制")
        @RequestParam(required = false, defaultValue = "20")
        @Min(value = 1, message = "数量限制不能小于1")
        @Max(value = 100, message = "数量限制不能大于100") Integer limit) {
        log.info("获取用户面试结果列表，userId: {}, limit: {}", userId, limit);

        try {
            List<InterviewResultResponseVo.InterviewResultSummary> results =
                interviewResultService.getUserResults(userId, limit);
            return R.ok("获取成功", results);
        } catch (ServiceException e) {
            log.error("获取用户面试结果列表失败: {}", e.getMessage());
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("获取用户面试结果列表异常", e);
            return R.fail("系统异常，请稍后重试");
        }
    }

    /**
     * 获取用户面试统计
     */
    @Operation(summary = "获取用户面试统计", description = "获取用户的面试统计数据")
    @GetMapping("/user/statistics")
    public R<Object> getUserStatistics(
        @Parameter(description = "用户ID（可选，默认当前用户）")
        @RequestParam(required = false) Long userId) {
        log.info("获取用户面试统计，userId: {}", userId);

        try {
            Object statistics = interviewResultService.getUserStatistics(userId);
            return R.ok("获取成功", statistics);
        } catch (ServiceException e) {
            log.error("获取用户面试统计失败: {}", e.getMessage());
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("获取用户面试统计异常", e);
            return R.fail("系统异常，请稍后重试");
        }
    }

    /**
     * 生成面试报告
     */
    @Operation(summary = "生成面试报告", description = "生成详细的面试报告")
    @GetMapping("/report")
    public R<Object> generateReport(
        @Parameter(description = "结果ID", required = true)
        @RequestParam @NotBlank(message = "结果ID不能为空") String resultId) {
        log.info("生成面试报告，resultId: {}", resultId);

        try {
            Object report = interviewResultService.generateReport(resultId);
            return R.ok("生成成功", report);
        } catch (ServiceException e) {
            log.error("生成面试报告失败: {}", e.getMessage());
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("生成面试报告异常", e);
            return R.fail("系统异常，请稍后重试");
        }
    }

    /**
     * 删除面试结果
     */
    @Operation(summary = "删除面试结果", description = "删除指定的面试结果")
    @Log(title = "删除面试结果", businessType = BusinessType.DELETE)
    @DeleteMapping("/{resultId}")
    @RateLimiter(key = "app:interview:result:delete", count = 5)
    public R<Void> deleteResult(
        @Parameter(description = "结果ID", required = true)
        @PathVariable @NotBlank(message = "结果ID不能为空") String resultId) {
        log.info("删除面试结果，resultId: {}", resultId);

        try {
            Boolean success = interviewResultService.deleteInterviewResult(resultId);
            return toAjax(success);
        } catch (ServiceException e) {
            log.error("删除面试结果失败: {}", e.getMessage());
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("删除面试结果异常", e);
            return R.fail("系统异常，请稍后重试");
        }
    }

}
