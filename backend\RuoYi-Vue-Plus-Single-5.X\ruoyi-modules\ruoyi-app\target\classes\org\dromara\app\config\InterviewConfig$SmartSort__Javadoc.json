{"doc": "\n 智能排序权重配置\r\n", "fields": [{"name": "hotWeight", "doc": "\n 热度权重\r\n"}, {"name": "passRateWeight", "doc": "\n 通过率权重\r\n"}, {"name": "difficultyWeight", "doc": "\n 难度权重\r\n"}, {"name": "durationWeight", "doc": "\n 时长权重\r\n"}, {"name": "questionCountWeight", "doc": "\n 题目数量权重\r\n"}, {"name": "optimalPassRate", "doc": "\n 最佳通过率\r\n"}, {"name": "optimalDiff<PERSON>ulty", "doc": "\n 最佳难度\r\n"}, {"name": "optimalDuration", "doc": "\n 最佳时长\r\n"}, {"name": "optimalQuestionCount", "doc": "\n 最佳题目数量\r\n"}], "enumConstants": [], "methods": [], "constructors": []}