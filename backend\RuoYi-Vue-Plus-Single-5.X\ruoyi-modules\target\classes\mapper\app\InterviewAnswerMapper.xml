<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.InterviewAnswerMapper">

    <resultMap type="org.dromara.app.domain.InterviewAnswer" id="InterviewAnswerResult">
        <id property="id" column="id"/>
        <result property="sessionId" column="session_id"/>
        <result property="questionId" column="question_id"/>
        <result property="userId" column="user_id"/>
        <result property="content" column="content"/>
        <result property="audioUrl" column="audio_url"/>
        <result property="videoUrl" column="video_url"/>
        <result property="duration" column="duration"/>
        <result property="submittedTime" column="submitted_time"/>
        <result property="score" column="score"/>
        <result property="feedback" column="feedback"/>
        <result property="evaluationDetails" column="evaluation_details"/>
        <result property="status" column="status"/>
        <result property="skipped" column="skipped"/>
        <result property="skipReason" column="skip_reason"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <sql id="selectInterviewAnswerVo">
        SELECT id, session_id, question_id, user_id, content, audio_url, video_url, duration,
               submitted_time, score, feedback, evaluation_details, status, skipped, skip_reason,
               create_time, update_time, create_by, update_by
        FROM app_interview_answer
    </sql>

    <select id="selectBySessionId" parameterType="String" resultMap="InterviewAnswerResult">
        <include refid="selectInterviewAnswerVo"/>
        WHERE session_id = #{sessionId}
        ORDER BY submitted_time ASC
    </select>

    <select id="selectBySessionIdAndQuestionId" resultMap="InterviewAnswerResult">
        <include refid="selectInterviewAnswerVo"/>
        WHERE session_id = #{sessionId} AND question_id = #{questionId}
        LIMIT 1
    </select>

    <select id="selectRecentByUserId" resultMap="InterviewAnswerResult">
        <include refid="selectInterviewAnswerVo"/>
        WHERE user_id = #{userId}
        ORDER BY submitted_time DESC
        LIMIT #{limit}
    </select>

    <select id="countAnsweredBySessionId" parameterType="String" resultType="Integer">
        SELECT COUNT(*)
        FROM app_interview_answer
        WHERE session_id = #{sessionId} AND skipped = 0
    </select>

    <select id="countSkippedBySessionId" parameterType="String" resultType="Integer">
        SELECT COUNT(*)
        FROM app_interview_answer
        WHERE session_id = #{sessionId} AND skipped = 1
    </select>

    <select id="selectAvgScoreBySessionId" parameterType="String" resultType="Double">
        SELECT AVG(score)
        FROM app_interview_answer
        WHERE session_id = #{sessionId} AND score IS NOT NULL
    </select>

    <select id="selectBySessionIdAndStatus" resultMap="InterviewAnswerResult">
        <include refid="selectInterviewAnswerVo"/>
        WHERE session_id = #{sessionId} AND status = #{status}
        ORDER BY submitted_time ASC
    </select>

</mapper>
