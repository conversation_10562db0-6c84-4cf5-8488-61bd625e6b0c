{"doc": "\n 岗位分类视图对象 app_job_category\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [{"name": "id", "doc": "\n 分类ID\r\n"}, {"name": "name", "doc": "\n 分类名称\r\n"}, {"name": "icon", "doc": "\n 分类图标\r\n"}, {"name": "color", "doc": "\n 分类颜色\r\n"}, {"name": "description", "doc": "\n 分类描述\r\n"}, {"name": "jobCount", "doc": "\n 岗位数量\r\n"}, {"name": "sortOrder", "doc": "\n 排序号\r\n"}, {"name": "status", "doc": "\n 状态（0正常 1停用）\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}], "enumConstants": [], "methods": [], "constructors": []}