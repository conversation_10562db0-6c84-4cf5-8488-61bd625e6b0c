package org.dromara.common.caffeine.annotation;

import java.lang.annotation.*;

/**
 * Caffeine缓存注解
 * 用于缓存方法返回值，类似于Spring Cache的@Cacheable
 *
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CaffeineCache {

    /**
     * 缓存名称（可选）
     */
    String cacheName() default "";

    /**
     * 缓存键的SpEL表达式
     * 默认使用方法参数生成键
     */
    String key() default "";

    /**
     * 缓存键前缀
     */
    String keyPrefix() default "";

    /**
     * 缓存条件的SpEL表达式
     * 当条件为true时才进行缓存
     */
    String condition() default "";

    /**
     * 排除缓存条件的SpEL表达式
     * 当条件为true时不缓存结果（即使method执行成功）
     */
    String unless() default "";

    /**
     * 是否同步执行
     * 当多个线程同时访问相同key时，是否同步执行方法
     */
    boolean sync() default false;

    /**
     * 自定义过期时间（秒）
     * 支持SpEL表达式，如果为-1则使用全局配置
     */
    String expire() default "-1";

    /**
     * 过期时间单位
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

}
