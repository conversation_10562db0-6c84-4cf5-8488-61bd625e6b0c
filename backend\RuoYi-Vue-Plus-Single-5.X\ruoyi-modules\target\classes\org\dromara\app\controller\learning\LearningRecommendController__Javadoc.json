{"doc": " 学习推荐控制器\n 基于用户能力评估和学习历史，提供个性化的学习资源推荐\n\n @Author: SevenJL\n @CreateTime: 2025-07-27\n @Version: 1.0\n", "fields": [], "enumConstants": [], "methods": [{"name": "getRecommendedVideos", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": " 获取推荐视频列表\n 基于用户能力短板和学习偏好推荐视频课程\n\n @param pageNum     页码\n @param pageSize    每页大小\n @param searchQuery 搜索关键词（可选）\n @return 推荐视频列表\n"}, {"name": "getRecommendedQuestionBanks", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": " 获取推荐题库列表\n 基于用户能力短板和练习历史推荐题库\n\n @param pageNum     页码\n @param pageSize    每页大小\n @param searchQuery 搜索关键词（可选）\n @return 推荐题库列表\n"}, {"name": "getRecommendedBooks", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": " 获取推荐书籍列表\n 基于用户能力短板和阅读偏好推荐书籍\n\n @param pageNum     页码\n @param pageSize    每页大小\n @param searchQuery 搜索关键词（可选）\n @return 推荐书籍列表\n"}, {"name": "getUserCapabilities", "paramTypes": [], "doc": " 获取用户能力评估数据\n 用于前端显示用户当前能力状况和薄弱环节\n\n @return 用户能力评估数据\n"}, {"name": "getRecommendationStatistics", "paramTypes": [], "doc": " 获取推荐统计信息\n 包括推荐总数、各类型推荐数量等统计信息\n\n @return 推荐统计信息\n"}, {"name": "refreshRecommendations", "paramTypes": [], "doc": " 刷新推荐算法\n 基于用户最新的学习行为和能力评估重新计算推荐\n\n @return 刷新结果\n"}, {"name": "recordRecommendationFeedback", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.String"], "doc": " 记录用户对推荐内容的反馈\n 用于优化推荐算法\n\n @param resourceType 资源类型（video/question-bank/book）\n @param resourceId   资源ID\n @param action       用户行为（view/like/dislike/bookmark/start-learning）\n @return 反馈记录结果\n"}], "constructors": []}