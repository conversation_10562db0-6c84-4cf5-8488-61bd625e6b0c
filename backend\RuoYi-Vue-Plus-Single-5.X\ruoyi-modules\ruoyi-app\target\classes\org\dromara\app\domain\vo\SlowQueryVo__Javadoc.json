{"doc": "\n 慢查询视图对象\r\n\r\n <AUTHOR>\r\n @date 2025-01-19\r\n", "fields": [{"name": "queryId", "doc": "\n 查询ID\r\n"}, {"name": "sqlText", "doc": "\n SQL语句\r\n"}, {"name": "executionTime", "doc": "\n 执行时间（秒）\r\n"}, {"name": "lockTime", "doc": "\n 锁定时间（秒）\r\n"}, {"name": "rowsExamined", "doc": "\n 扫描行数\r\n"}, {"name": "rowsSent", "doc": "\n 返回行数\r\n"}, {"name": "executionCount", "doc": "\n 执行次数\r\n"}, {"name": "averageExecutionTime", "doc": "\n 平均执行时间（秒）\r\n"}, {"name": "maxExecutionTime", "doc": "\n 最大执行时间（秒）\r\n"}, {"name": "minExecutionTime", "doc": "\n 最小执行时间（秒）\r\n"}, {"name": "databaseName", "doc": "\n 数据库名\r\n"}, {"name": "userName", "doc": "\n 用户名\r\n"}, {"name": "clientIp", "doc": "\n 客户端IP\r\n"}, {"name": "queryType", "doc": "\n 查询类型：SELECT/INSERT/UPDATE/DELETE\r\n"}, {"name": "tablesUsed", "doc": "\n 涉及的表\r\n"}, {"name": "indexUsed", "doc": "\n 是否使用索引\r\n"}, {"name": "tempTableUsed", "doc": "\n 是否使用临时表\r\n"}, {"name": "fileSortUsed", "doc": "\n 是否使用文件排序\r\n"}, {"name": "executionPlan", "doc": "\n 查询计划\r\n"}, {"name": "optimizationSuggestion", "doc": "\n 优化建议\r\n"}, {"name": "firstExecutionTime", "doc": "\n 首次执行时间\r\n"}, {"name": "lastExecutionTime", "doc": "\n 最后执行时间\r\n"}, {"name": "frequency", "doc": "\n 查询频率：high/medium/low\r\n"}, {"name": "impactLevel", "doc": "\n 影响级别：critical/high/medium/low\r\n"}, {"name": "isOptimized", "doc": "\n 是否已优化\r\n"}], "enumConstants": [], "methods": [], "constructors": []}