package org.dromara.system.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.domain.bo.QuestionBankBo;
import org.dromara.system.domain.vo.QuestionBankVo;

import java.util.Collection;
import java.util.List;

/**
 * 题库Service接口
 *
 * <AUTHOR>
 */
public interface IQuestionBankService {

    /**
     * 查询题库
     *
     * @param bankId 题库主键
     * @return 题库
     */
    QuestionBankVo queryById(Long bankId);

    /**
     * 查询题库列表
     *
     * @param bo 题库查询条件
     * @return 题库集合
     */
    List<QuestionBankVo> queryList(QuestionBankBo bo);

    /**
     * 分页查询题库列表
     *
     * @param bo        题库查询条件
     * @param pageQuery 分页参数
     * @return 题库分页集合
     */
    TableDataInfo<QuestionBankVo> queryPageList(QuestionBankBo bo, PageQuery pageQuery);

    /**
     * 新增题库
     *
     * @param bo 题库信息
     * @return 新增结果
     */
    Boolean insertByBo(QuestionBankBo bo);

    /**
     * 修改题库
     *
     * @param bo 题库信息
     * @return 修改结果
     */
    Boolean updateByBo(QuestionBankBo bo);

    /**
     * 校验并批量删除题库信息
     *
     * @param ids 需要删除的题库主键集合
     * @return 删除结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids);

    /**
     * 根据题库编码查询题库
     *
     * @param bankCode 题库编码
     * @return 题库信息
     */
    QuestionBankVo queryByBankCode(String bankCode);

    /**
     * 根据专业ID查询题库列表
     *
     * @param majorId 专业ID
     * @return 题库集合
     */
    List<QuestionBankVo> queryByMajorId(Long majorId);

    /**
     * 查询用户收藏的题库列表
     *
     * @param userId 用户ID
     * @return 题库集合
     */
    List<QuestionBankVo> queryBookmarkedBanks(Long userId);

    /**
     * 查询热门题库列表
     *
     * @param limit 限制数量
     * @return 题库集合
     */
    List<QuestionBankVo> queryHotBanks(Integer limit);

    /**
     * 更新题库练习次数
     *
     * @param bankId 题库ID
     * @return 更新结果
     */
    Boolean updatePracticeCount(Long bankId);

    /**
     * 更新题库题目总数
     *
     * @param bankId 题库ID
     * @return 更新结果
     */
    Boolean updateTotalQuestions(Long bankId);

    /**
     * 检查题库编码是否唯一
     *
     * @param bo 题库信息
     * @return 是否唯一
     */
    Boolean checkBankCodeUnique(QuestionBankBo bo);

    /**
     * 批量导入题库
     *
     * @param list 题库列表
     * @return 导入结果
     */
    String importQuestionBank(List<QuestionBankBo> list);

    /**
     * 导出题库数据
     *
     * @param bo 题库查询条件
     * @return 题库集合
     */
    List<QuestionBankVo> exportQuestionBank(QuestionBankBo bo);

    /**
     * 启用/停用题库
     *
     * @param bankId 题库ID
     * @param status 状态
     * @return 操作结果
     */
    Boolean changeStatus(Long bankId, String status);

    /**
     * 复制题库
     *
     * @param bankId 源题库ID
     * @param title  新题库标题
     * @return 操作结果
     */
    Boolean copyQuestionBank(Long bankId, String title);

    /**
     * 获取题库统计信息
     *
     * @param bankId 题库ID
     * @return 统计信息
     */
    QuestionBankVo getStatistics(Long bankId);
}
