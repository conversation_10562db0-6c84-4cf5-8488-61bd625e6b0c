{"doc": "\n 学习进度服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "startLearning", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Long"], "doc": "\n 开始学习\r\n\r\n @param userId 用户ID\r\n @param learningPathId 学习路径ID\r\n @param resourceId 资源ID\r\n @return 学习进度\r\n"}, {"name": "updateProgress", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 更新学习进度\r\n\r\n @param progressId 进度ID\r\n @param completionPercentage 完成百分比\r\n @param studyMinutes 学习时长\r\n @return 是否成功\r\n"}, {"name": "completeLearning", "paramTypes": ["java.lang.Long", "java.lang.Double", "java.lang.Double", "java.lang.String"], "doc": "\n 完成学习\r\n\r\n @param progressId 进度ID\r\n @param effectivenessRating 效果评分\r\n @param satisfactionRating 满意度评分\r\n @param notes 学习笔记\r\n @return 是否成功\r\n"}, {"name": "pauseLearning", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 暂停学习\r\n\r\n @param progressId 进度ID\r\n @param reason 暂停原因\r\n @return 是否成功\r\n"}, {"name": "resumeLearning", "paramTypes": ["java.lang.Long"], "doc": "\n 恢复学习\r\n\r\n @param progressId 进度ID\r\n @return 是否成功\r\n"}, {"name": "getUserLearningProgress", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 获取用户学习进度列表\r\n\r\n @param userId 用户ID\r\n @param status 状态筛选\r\n @return 学习进度列表\r\n"}, {"name": "getLearningProgressPage", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": "\n 分页查询学习进度\r\n\r\n @param userId 用户ID\r\n @param pageNum 页码\r\n @param pageSize 每页大小\r\n @param status 状态筛选\r\n @return 分页结果\r\n"}, {"name": "getLearningProgressDetail", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 获取学习进度详情\r\n\r\n @param progressId 进度ID\r\n @param userId 用户ID\r\n @return 学习进度\r\n"}, {"name": "getProgressByLearningPath", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 根据学习路径获取进度\r\n\r\n @param learningPathId 学习路径ID\r\n @param userId 用户ID\r\n @return 学习进度\r\n"}, {"name": "getProgressByResource", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 根据资源获取进度\r\n\r\n @param resourceId 资源ID\r\n @param userId 用户ID\r\n @return 学习进度\r\n"}, {"name": "getUserLearningStatistics", "paramTypes": ["java.lang.Long"], "doc": "\n 获取用户学习统计\r\n\r\n @param userId 用户ID\r\n @return 统计信息\r\n"}, {"name": "getLearningEffectivenessAssessment", "paramTypes": ["java.lang.Long"], "doc": "\n 获取学习效果评估\r\n\r\n @param userId 用户ID\r\n @return 效果评估\r\n"}, {"name": "getStudyTimeStatistics", "paramTypes": ["java.lang.Long"], "doc": "\n 获取学习时间统计\r\n\r\n @param userId 用户ID\r\n @return 时间统计\r\n"}, {"name": "getLearningTrend", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 获取学习趋势\r\n\r\n @param userId 用户ID\r\n @param days 天数\r\n @return 趋势数据\r\n"}, {"name": "getRecentLearningRecords", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 获取最近学习记录\r\n\r\n @param userId 用户ID\r\n @param limit 限制数量\r\n @return 学习记录列表\r\n"}, {"name": "getOngoingLearning", "paramTypes": ["java.lang.Long"], "doc": "\n 获取正在进行的学习\r\n\r\n @param userId 用户ID\r\n @return 学习进度列表\r\n"}, {"name": "getCompletedLearning", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 获取已完成的学习\r\n\r\n @param userId 用户ID\r\n @param limit 限制数量\r\n @return 学习进度列表\r\n"}, {"name": "getOverdueLearning", "paramTypes": ["java.lang.Long"], "doc": "\n 获取超期学习项目\r\n\r\n @param userId 用户ID\r\n @return 超期学习列表\r\n"}, {"name": "createLearningPlan", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.LearningProgress.LearningPlan"], "doc": "\n 创建学习计划\r\n\r\n @param progressId 进度ID\r\n @param learningPlan 学习计划\r\n @return 是否成功\r\n"}, {"name": "updateLearningPlan", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.LearningProgress.LearningPlan"], "doc": "\n 更新学习计划\r\n\r\n @param progressId 进度ID\r\n @param learningPlan 学习计划\r\n @return 是否成功\r\n"}, {"name": "addLearningFeedback", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.LearningProgress.LearningFeedback"], "doc": "\n 添加学习反馈\r\n\r\n @param progressId 进度ID\r\n @param feedback 学习反馈\r\n @return 是否成功\r\n"}, {"name": "updateMilestoneStatus", "paramTypes": ["java.lang.Long", "java.lang.String", "boolean"], "doc": "\n 更新里程碑完成状态\r\n\r\n @param progressId 进度ID\r\n @param milestone 里程碑名称\r\n @param completed 是否完成\r\n @return 是否成功\r\n"}, {"name": "calculateLearningEfficiency", "paramTypes": ["java.lang.Long"], "doc": "\n 计算学习效率\r\n\r\n @param progressId 进度ID\r\n @return 学习效率评分\r\n"}, {"name": "generateLearningReport", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String"], "doc": "\n 生成学习报告\r\n\r\n @param userId 用户ID\r\n @param startDate 开始日期\r\n @param endDate 结束日期\r\n @return 学习报告\r\n"}, {"name": "recommendLearningAdjustments", "paramTypes": ["java.lang.Long"], "doc": "\n 推荐学习内容调整\r\n\r\n @param userId 用户ID\r\n @return 调整建议\r\n"}, {"name": "getLearningReminders", "paramTypes": ["java.lang.Long"], "doc": "\n 获取学习提醒列表\r\n\r\n @param userId 用户ID\r\n @return 提醒列表\r\n"}, {"name": "setLearningReminder", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.LearningProgress.ReminderSettings"], "doc": "\n 设置学习提醒\r\n\r\n @param progressId 进度ID\r\n @param reminderSettings 提醒设置\r\n @return 是否成功\r\n"}, {"name": "batchUpdateLearningStatistics", "paramTypes": ["java.lang.Long"], "doc": "\n 批量更新学习统计\r\n\r\n @param userId 用户ID\r\n @return 更新数量\r\n"}, {"name": "deleteLearningProgress", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 删除学习进度\r\n\r\n @param progressId 进度ID\r\n @param userId 用户ID\r\n @return 是否成功\r\n"}, {"name": "exportLearningData", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 导出学习数据\r\n\r\n @param userId 用户ID\r\n @param format 导出格式\r\n @return 导出文件路径\r\n"}, {"name": "analyzeLearningData", "paramTypes": ["java.lang.Long"], "doc": "\n 学习数据分析\r\n\r\n @param userId 用户ID\r\n @return 分析结果\r\n"}, {"name": "getLearningAchievements", "paramTypes": ["java.lang.Long"], "doc": "\n 获取学习成就\r\n\r\n @param userId 用户ID\r\n @return 成就列表\r\n"}, {"name": "getPersonalizedRecommendations", "paramTypes": ["java.lang.Long"], "doc": "\n 获取个性化推荐\r\n\r\n @param userId 用户ID\r\n @return 个性化推荐列表\r\n"}, {"name": "batchUpdateProgress", "paramTypes": ["java.util.List", "java.util.Map"], "doc": "\n 批量更新学习进度\r\n\r\n @param progressIds 进度ID列表\r\n @param updateData 更新数据\r\n @return 是否成功\r\n"}, {"name": "importLearningData", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": "\n 导入学习数据\r\n\r\n @param userId 用户ID\r\n @param importData 导入数据\r\n @return 是否成功\r\n"}, {"name": "getProgressComparison", "paramTypes": ["java.lang.Long", "java.util.List"], "doc": "\n 获取学习进度对比\r\n\r\n @param userId 用户ID\r\n @param compareUserIds 对比用户ID列表\r\n @return 对比结果\r\n"}, {"name": "getLearningInsights", "paramTypes": ["java.lang.Long"], "doc": "\n 获取学习洞察\r\n\r\n @param userId 用户ID\r\n @return 学习洞察数据\r\n"}], "constructors": []}