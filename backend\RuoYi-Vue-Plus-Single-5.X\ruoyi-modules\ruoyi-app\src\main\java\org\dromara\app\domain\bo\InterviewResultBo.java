package org.dromara.app.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.*;
import lombok.Data;
import org.dromara.app.domain.InterviewResult;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 面试结果业务对象 app_interview_result
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@AutoMapper(target = InterviewResult.class, reverseConvertGenerate = false)
public class InterviewResultBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 结果ID
     */
    @NotBlank(message = "结果ID不能为空", groups = {EditGroup.class})
    private String id;

    /**
     * 会话ID
     */
    @NotBlank(message = "会话ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private String sessionId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 岗位ID
     */
    @NotNull(message = "岗位ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long jobId;

    /**
     * 岗位名称
     */
    @NotBlank(message = "岗位名称不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 200, message = "岗位名称长度不能超过200个字符")
    private String jobName;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 200, message = "公司名称长度不能超过200个字符")
    private String company;

    /**
     * 面试模式
     */
    @NotBlank(message = "面试模式不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 50, message = "面试模式长度不能超过50个字符")
    private String mode;

    /**
     * 面试时长
     */
    @Size(max = 50, message = "面试时长长度不能超过50个字符")
    private String duration;

    /**
     * 总分
     */
    @Min(value = 0, message = "总分不能小于0")
    @Max(value = 100, message = "总分不能大于100")
    private Integer totalScore;

    /**
     * 等级（excellent,good,average,poor）
     */
    @Pattern(regexp = "^(excellent|good|average|poor)$", message = "等级只能是excellent、good、average、poor中的一个")
    private String rank;

    /**
     * 等级文本
     */
    @Size(max = 50, message = "等级文本长度不能超过50个字符")
    private String rankText;

    /**
     * 百分位数
     */
    @Min(value = 0, message = "百分位数不能小于0")
    @Max(value = 100, message = "百分位数不能大于100")
    private Integer percentile;

    /**
     * 已回答问题数
     */
    @Min(value = 0, message = "已回答问题数不能小于0")
    private Integer answeredQuestions;

    /**
     * 总问题数
     */
    @Min(value = 1, message = "总问题数不能小于1")
    private Integer totalQuestions;

    /**
     * 状态（completed,partial,cancelled）
     */
    @Pattern(regexp = "^(completed|partial|cancelled)$", message = "状态只能是completed、partial、cancelled中的一个")
    private String status;

    /**
     * 主要优势（JSON数组）
     */
    private List<String> topStrengths;

    /**
     * 主要劣势（JSON数组）
     */
    private List<String> topWeaknesses;

    /**
     * 总体反馈
     */
    private String overallFeedback;

    /**
     * 保存到历史记录请求
     */
    @Data
    public static class SaveToHistoryRequest implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 结果ID
         */
        @NotBlank(message = "结果ID不能为空")
        private String resultId;

        /**
         * 自定义标题
         */
        @Size(max = 200, message = "标题长度不能超过200个字符")
        private String title;
    }

    /**
     * 分享结果请求
     */
    @Data
    public static class ShareResultRequest implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 结果ID
         */
        @NotBlank(message = "结果ID不能为空")
        private String resultId;

        /**
         * 分享平台
         */
        @NotBlank(message = "分享平台不能为空")
        @Pattern(regexp = "^(wechat|qq|weibo|link)$", message = "分享平台只能是wechat、qq、weibo、link中的一个")
        private String platform;

        /**
         * 分享内容
         */
        @Size(max = 500, message = "分享内容长度不能超过500个字符")
        private String content;
    }

    /**
     * 查询用户结果请求
     */
    @Data
    public static class UserResultsRequest implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 用户ID
         */
        private Long userId;

        /**
         * 数量限制
         */
        @Min(value = 1, message = "数量限制不能小于1")
        @Max(value = 100, message = "数量限制不能大于100")
        private Integer limit = 20;

        /**
         * 状态过滤
         */
        @Pattern(regexp = "^(completed|partial|cancelled)$", message = "状态只能是completed、partial、cancelled中的一个")
        private String status;
    }

    /**
     * 学习资源请求
     */
    @Data
    public static class LearningResourcesRequest implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 结果ID
         */
        @NotBlank(message = "结果ID不能为空")
        private String resultId;

        /**
         * 数量限制
         */
        @Min(value = 1, message = "数量限制不能小于1")
        @Max(value = 50, message = "数量限制不能大于50")
        private Integer limit = 10;

        /**
         * 资源类型过滤
         */
        @Pattern(regexp = "^(course|article|video|book|exercise)$", message = "资源类型只能是course、article、video、book、exercise中的一个")
        private String type;

        /**
         * 难度过滤
         */
        @Pattern(regexp = "^(beginner|intermediate|advanced)$", message = "难度只能是beginner、intermediate、advanced中的一个")
        private String difficulty;
    }

}
