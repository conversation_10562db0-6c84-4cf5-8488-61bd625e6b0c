{"doc": " 学习资源Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectBySkillAreaAndDifficulty", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": " 根据技能领域和难度查询资源\n\n @param skillArea 技能领域\n @param difficulty 难度等级\n @param limit 限制数量\n @return 资源列表\n"}, {"name": "selectByType", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 根据资源类型查询资源\n\n @param type 资源类型\n @param limit 限制数量\n @return 资源列表\n"}, {"name": "selectPopularResources", "paramTypes": ["java.lang.Integer"], "doc": " 查询热门资源\n\n @param limit 限制数量\n @return 资源列表\n"}, {"name": "selectFreeResources", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 查询免费资源\n\n @param skillArea 技能领域\n @param limit 限制数量\n @return 资源列表\n"}, {"name": "selectByTag", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 根据标签查询资源\n\n @param tag 标签\n @param limit 限制数量\n @return 资源列表\n"}, {"name": "selectResourceStatistics", "paramTypes": [], "doc": " 查询资源统计信息\n\n @return 统计信息\n"}, {"name": "selectSkillAreaDistribution", "paramTypes": [], "doc": " 查询技能领域分布\n\n @return 技能领域分布\n"}, {"name": "selectResourceTypeDistribution", "paramTypes": [], "doc": " 查询资源类型分布\n\n @return 资源类型分布\n"}, {"name": "selectTopProviders", "paramTypes": ["java.lang.Integer"], "doc": " 查询提供者排行\n\n @param limit 限制数量\n @return 提供者排行\n"}, {"name": "searchResources", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 全文搜索资源\n\n @param keyword 关键词\n @param limit 限制数量\n @return 资源列表\n"}, {"name": "updateUsageStatistics", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 更新资源使用统计\n\n @param resourceId 资源ID\n @param statisticsJson 统计信息JSON\n @return 更新行数\n"}, {"name": "selectRecommendedResources", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询推荐学习资源\n\n @param userId 用户ID（可为null，表示通用推荐）\n @param limit 限制数量\n @return 推荐资源列表\n"}, {"name": "selectResourcesBySkillGaps", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Integer"], "doc": " 根据技能差距推荐资源\n\n @param skillAreas 技能领域列表\n @param difficulty 难度等级\n @param limit 限制数量\n @return 推荐资源列表\n"}, {"name": "selectCollaborativeFilteringResources", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询用户相似度推荐资源\n\n @param userId 用户ID\n @param limit 限制数量\n @return 推荐资源列表\n"}, {"name": "selectTrendingResources", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": " 查询趋势资源（最近热门）\n\n @param days 天数\n @param limit 限制数量\n @return 趋势资源列表\n"}], "constructors": []}