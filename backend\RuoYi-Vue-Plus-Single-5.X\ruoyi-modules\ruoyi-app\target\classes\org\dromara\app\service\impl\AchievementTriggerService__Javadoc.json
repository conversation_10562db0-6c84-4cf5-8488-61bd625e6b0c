{"doc": "\n 成就触发服务，用于监听系统事件并触发成就\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleUserLogin", "paramTypes": ["org.dromara.app.service.impl.AchievementTriggerService.UserLoginEvent"], "doc": "\n 用户登录事件处理\r\n\r\n @param event 登录事件\r\n"}, {"name": "handleInterviewCompleted", "paramTypes": ["org.dromara.app.service.impl.AchievementTriggerService.InterviewCompletedEvent"], "doc": "\n 用户完成面试事件处理\r\n\r\n @param event 面试完成事件\r\n"}, {"name": "handleUserLearn", "paramTypes": ["org.dromara.app.service.impl.AchievementTriggerService.UserLearnEvent"], "doc": "\n 用户学习事件处理\r\n\r\n @param event 学习事件\r\n"}, {"name": "handleConsecutiveLogin", "paramTypes": ["org.dromara.app.service.impl.AchievementTriggerService.ConsecutiveLoginEvent"], "doc": "\n 用户连续登录事件处理\r\n\r\n @param event 连续登录事件\r\n"}, {"name": "handleAbilityImprove", "paramTypes": ["org.dromara.app.service.impl.AchievementTriggerService.AbilityImproveEvent"], "doc": "\n 能力提升事件处理\r\n\r\n @param event 能力提升事件\r\n"}, {"name": "handleUserShare", "paramTypes": ["org.dromara.app.service.impl.AchievementTriggerService.UserShareEvent"], "doc": "\n 用户分享事件处理\r\n\r\n @param event 分享事件\r\n"}, {"name": "handleResumeSubmit", "paramTypes": ["org.dromara.app.service.impl.AchievementTriggerService.ResumeSubmitEvent"], "doc": "\n 简历提交事件处理\r\n\r\n @param event 简历提交事件\r\n"}], "constructors": []}