{"doc": "\n 用户成就视图对象 app_user_achievement\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 主键ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "achievementId", "doc": "\n 成就ID\r\n"}, {"name": "achievementCode", "doc": "\n 成就代码\r\n"}, {"name": "achievementName", "doc": "\n 成就名称\r\n"}, {"name": "achievementDesc", "doc": "\n 成就描述\r\n"}, {"name": "achievementIcon", "doc": "\n 成就图标URL\r\n"}, {"name": "achievementType", "doc": "\n 成就类型\r\n"}, {"name": "rewardPoints", "doc": "\n 奖励积分\r\n"}, {"name": "unlockTime", "doc": "\n 解锁时间\r\n"}, {"name": "progress", "doc": "\n 进度百分比(0-100)\r\n"}, {"name": "currentValue", "doc": "\n 当前数值\r\n"}, {"name": "targetValue", "doc": "\n 目标数值\r\n"}, {"name": "isCompleted", "doc": "\n 是否完成(0否 1是)\r\n"}, {"name": "isNotified", "doc": "\n 是否已通知(0否 1是)\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}, {"name": "createByName", "doc": "\n 创建者名称\r\n"}, {"name": "updateByName", "doc": "\n 更新者名称\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}, {"name": "completionStatusDesc", "doc": "\n 成就完成状态描述\r\n"}, {"name": "progressDesc", "doc": "\n 进度描述\r\n"}, {"name": "achievementTypeDesc", "doc": "\n 成就类型描述\r\n"}, {"name": "remainingValue", "doc": "\n 距离完成还需要的数值\r\n"}, {"name": "estimatedCompletionTime", "doc": "\n 预计完成时间（基于当前进度）\r\n"}, {"name": "difficultyLevel", "doc": "\n 成就难度等级\r\n"}, {"name": "isRecommended", "doc": "\n 是否为推荐成就\r\n"}, {"name": "sortOrder", "doc": "\n 成就排序\r\n"}, {"name": "rarity", "doc": "\n 获取成就稀有度（基于完成用户数量，需要外部设置）\r\n"}], "enumConstants": [], "methods": [{"name": "getCompletionStatusDesc", "paramTypes": [], "doc": "\n 获取完成状态描述\r\n"}, {"name": "getProgressDesc", "paramTypes": [], "doc": "\n 获取进度描述\r\n"}, {"name": "getAchievementTypeDesc", "paramTypes": [], "doc": "\n 获取成就类型描述\r\n"}, {"name": "getRemainingValue", "paramTypes": [], "doc": "\n 获取距离完成还需要的数值\r\n"}, {"name": "isNearCompletion", "paramTypes": [], "doc": "\n 判断是否接近完成（进度超过80%）\r\n"}, {"name": "isCompleted", "paramTypes": [], "doc": "\n 判断是否已完成\r\n"}, {"name": "isNotified", "paramTypes": [], "doc": "\n 判断是否已通知\r\n"}, {"name": "getCompletionDays", "paramTypes": [], "doc": "\n 获取成就完成天数（如果已完成）\r\n"}], "constructors": []}