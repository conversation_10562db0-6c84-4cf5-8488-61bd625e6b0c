{"doc": " 支付订单返回VO\n\n <AUTHOR>\n", "fields": [{"name": "orderId", "doc": " 订单ID\n"}, {"name": "orderNo", "doc": " 订单号\n"}, {"name": "productId", "doc": " 商品ID\n"}, {"name": "productType", "doc": " 商品类型\n"}, {"name": "productTitle", "doc": " 商品标题\n"}, {"name": "amount", "doc": " 支付金额\n"}, {"name": "paymentMethod", "doc": " 支付方式\n"}, {"name": "status", "doc": " 订单状态\n"}, {"name": "alipayTradeNo", "doc": " 支付宝交易号\n"}, {"name": "payUrl", "doc": " 支付页面URL\n"}, {"name": "payForm", "doc": " 支付页面表单HTML\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "payTime", "doc": " 支付时间\n"}, {"name": "expireTime", "doc": " 过期时间\n"}, {"name": "remark", "doc": " 备注信息\n"}, {"name": "payToken", "doc": " 支付token\n"}], "enumConstants": [], "methods": [], "constructors": []}