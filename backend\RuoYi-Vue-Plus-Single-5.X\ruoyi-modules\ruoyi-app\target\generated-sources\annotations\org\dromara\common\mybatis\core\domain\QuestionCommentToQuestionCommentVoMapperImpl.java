package org.dromara.common.mybatis.core.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.QuestionCommentVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T11:28:39+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.11 (Amazon.com Inc.)"
)
@Component
public class QuestionCommentToQuestionCommentVoMapperImpl implements QuestionCommentToQuestionCommentVoMapper {

    @Override
    public QuestionCommentVo convert(QuestionComment arg0) {
        if ( arg0 == null ) {
            return null;
        }

        QuestionCommentVo questionCommentVo = new QuestionCommentVo();

        questionCommentVo.setCommentId( arg0.getCommentId() );
        questionCommentVo.setQuestionId( arg0.getQuestionId() );
        questionCommentVo.setUserId( arg0.getUserId() );
        questionCommentVo.setParentId( arg0.getParentId() );
        questionCommentVo.setContent( arg0.getContent() );
        questionCommentVo.setLikeCount( arg0.getLikeCount() );
        questionCommentVo.setReplyCount( arg0.getReplyCount() );
        questionCommentVo.setStatus( arg0.getStatus() );
        questionCommentVo.setSort( arg0.getSort() );
        questionCommentVo.setIpAddress( arg0.getIpAddress() );
        questionCommentVo.setRemark( arg0.getRemark() );
        questionCommentVo.setCreateTime( arg0.getCreateTime() );
        questionCommentVo.setUpdateTime( arg0.getUpdateTime() );

        return questionCommentVo;
    }

    @Override
    public QuestionCommentVo convert(QuestionComment arg0, QuestionCommentVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCommentId( arg0.getCommentId() );
        arg1.setQuestionId( arg0.getQuestionId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setContent( arg0.getContent() );
        arg1.setLikeCount( arg0.getLikeCount() );
        arg1.setReplyCount( arg0.getReplyCount() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setSort( arg0.getSort() );
        arg1.setIpAddress( arg0.getIpAddress() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateTime( arg0.getUpdateTime() );

        return arg1;
    }
}
