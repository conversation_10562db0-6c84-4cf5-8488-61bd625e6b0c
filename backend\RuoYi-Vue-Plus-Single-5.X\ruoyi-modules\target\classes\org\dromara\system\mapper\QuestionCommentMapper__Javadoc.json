{"doc": " 题目评论Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectVoById", "paramTypes": ["java.lang.Long"], "doc": " 查询题目评论\n\n @param commentId 评论主键\n @return 题目评论\n"}, {"name": "selectVoList", "paramTypes": ["org.dromara.system.domain.bo.QuestionCommentBo"], "doc": " 查询题目评论列表\n\n @param questionComment 题目评论\n @return 题目评论集合\n"}, {"name": "selectVoPage", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "org.dromara.system.domain.bo.QuestionCommentBo"], "doc": " 分页查询题目评论列表\n\n @param page            分页参数\n @param questionComment 题目评论查询条件\n @return 题目评论集合\n"}, {"name": "selectPageQuestionCommentList", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery", "org.dromara.system.domain.bo.QuestionCommentBo"], "doc": " 根据条件分页查询题目评论列表\n\n @param pageQuery       分页参数\n @param questionComment 题目评论查询条件\n @return 题目评论集合\n"}, {"name": "selectVoByIds", "paramTypes": ["java.util.Collection"], "doc": " 查询题目评论列表\n\n @param commentIds 评论主键集合\n @return 题目评论集合\n"}, {"name": "selectVoByQuestionId", "paramTypes": ["java.lang.Long"], "doc": " 根据题目ID查询评论列表\n\n @param questionId 题目ID\n @return 评论集合\n"}, {"name": "selectPageCommentByQuestionId", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery", "java.lang.Long", "org.dromara.system.domain.bo.QuestionCommentBo"], "doc": " 根据题目ID分页查询评论列表\n\n @param pageQuery       分页参数\n @param questionId      题目ID\n @param questionComment 评论查询条件\n @return 评论集合\n"}, {"name": "selectVoByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询评论列表\n\n @param userId 用户ID\n @return 评论集合\n"}, {"name": "selectVoByParentId", "paramTypes": ["java.lang.Long"], "doc": " 根据父评论ID查询子评论列表\n\n @param parentId 父评论ID\n @return 子评论集合\n"}, {"name": "selectCommentTree", "paramTypes": ["java.lang.Long"], "doc": " 查询评论树形结构\n\n @param questionId 题目ID\n @return 评论树形集合\n"}, {"name": "selectHotComments", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询热门评论列表\n\n @param questionId 题目ID\n @param limit      限制数量\n @return 评论集合\n"}, {"name": "selectLatestComments", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询最新评论列表\n\n @param questionId 题目ID\n @param limit      限制数量\n @return 评论集合\n"}, {"name": "updateLikeCount", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 更新评论点赞数\n\n @param commentId 评论ID\n @param increment 增量（可为负数）\n @return 影响行数\n"}, {"name": "updateReplyCount", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 更新评论回复数\n\n @param commentId 评论ID\n @param increment 增量（可为负数）\n @return 影响行数\n"}, {"name": "deleteQuestionCommentByIds", "paramTypes": ["java.util.Collection"], "doc": " 批量删除题目评论\n\n @param commentIds 需要删除的评论主键集合\n @return 影响行数\n"}, {"name": "deleteCommentByQuestionId", "paramTypes": ["java.lang.Long"], "doc": " 根据题目ID删除评论\n\n @param questionId 题目ID\n @return 影响行数\n"}, {"name": "deleteCommentByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID删除评论\n\n @param userId 用户ID\n @return 影响行数\n"}, {"name": "countCommentsByQuestionId", "paramTypes": ["java.lang.Long"], "doc": " 统计题目下的评论数量\n\n @param questionId 题目ID\n @return 评论数量\n"}, {"name": "countCommentsByUserId", "paramTypes": ["java.lang.Long"], "doc": " 统计用户的评论数量\n\n @param userId 用户ID\n @return 评论数量\n"}], "constructors": []}