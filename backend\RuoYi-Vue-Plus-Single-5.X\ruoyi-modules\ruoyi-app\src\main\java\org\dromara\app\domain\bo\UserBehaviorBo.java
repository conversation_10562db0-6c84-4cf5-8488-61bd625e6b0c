package org.dromara.app.domain.bo;

import lombok.Data;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;

/**
 * 用户行为记录业务对象 app_user_behavior
 *
 * <AUTHOR>
 */
@Data
public class UserBehaviorBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = {AddGroup.class})
    private Long userId;

    /**
     * 行为类型
     */
    @NotBlank(message = "行为类型不能为空", groups = {AddGroup.class})
    private String behaviorType;

    /**
     * 行为数据(JSON格式)
     */
    private String behaviorData;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 备注
     */
    private String remark;

}
