{"doc": " RabbitMQ 自动配置类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "rabbitConnectionFactory", "paramTypes": ["org.dromara.common.rabbitmq.properties.RabbitMqProperties"], "doc": " 连接工厂\n"}, {"name": "rabbitAdmin", "paramTypes": ["org.springframework.amqp.rabbit.connection.ConnectionFactory"], "doc": " RabbitAdmin\n"}, {"name": "messageConverter", "paramTypes": ["com.fasterxml.jackson.databind.ObjectMapper"], "doc": " 消息转换器\n"}, {"name": "rabbitTemplate", "paramTypes": ["org.springframework.amqp.rabbit.connection.ConnectionFactory", "org.springframework.amqp.support.converter.MessageConverter", "org.dromara.common.rabbitmq.properties.RabbitMqProperties"], "doc": " RabbitTemplate\n"}, {"name": "retryTemplate", "paramTypes": ["org.dromara.common.rabbitmq.properties.RabbitMqProperties"], "doc": " 重试模板\n"}, {"name": "rabbitListenerContainerFactory", "paramTypes": ["org.springframework.amqp.rabbit.connection.ConnectionFactory", "org.springframework.amqp.support.converter.MessageConverter", "org.dromara.common.rabbitmq.properties.RabbitMqProperties"], "doc": " 监听器容器工厂\n"}, {"name": "validateRabbitMqConfig", "paramTypes": ["org.dromara.common.rabbitmq.properties.RabbitMqProperties"], "doc": " 验证 RabbitMQ 配置\n"}], "constructors": []}