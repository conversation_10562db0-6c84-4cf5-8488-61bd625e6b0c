{"doc": "\n 面试问题Mapper接口\r\n\r\n <AUTHOR> Assistant\r\n @date 2025-07-19\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByJobId", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 根据岗位ID查询问题列表\r\n\r\n @param jobId      岗位ID\r\n @param difficulty 难度等级\r\n @param limit      限制数量\r\n @return 问题列表\r\n"}, {"name": "selectByTechnicalDomain", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": "\n 根据技术领域查询问题\r\n\r\n @param technicalDomain 技术领域\r\n @param questionType    问题类型\r\n @param limit          限制数量\r\n @return 问题列表\r\n"}, {"name": "selectMultimodalQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询多模态问题\r\n\r\n @param jobId 岗位ID\r\n @param limit 限制数量\r\n @return 多模态问题列表\r\n"}, {"name": "selectByTags", "paramTypes": ["java.util.List", "java.lang.Integer"], "doc": "\n 根据标签查询问题\r\n\r\n @param tags  标签列表\r\n @param limit 限制数量\r\n @return 问题列表\r\n"}, {"name": "selectQuestionPage", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.String"], "doc": "\n 分页查询问题列表\r\n\r\n @param page           分页参数\r\n @param jobId          岗位ID\r\n @param questionType   问题类型\r\n @param difficulty     难度等级\r\n @param category       问题分类\r\n @return 分页结果\r\n"}, {"name": "selectByIds", "paramTypes": ["java.util.List"], "doc": "\n 根据问题ID批量查询\r\n\r\n @param ids 问题ID列表\r\n @return 问题列表\r\n"}, {"name": "count<PERSON>y<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": "\n 根据分类查询问题数量\r\n\r\n @param category 问题分类\r\n @return 问题数量\r\n"}, {"name": "countByJobId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据岗位ID查询问题数量\r\n\r\n @param jobId 岗位ID\r\n @return 问题数量\r\n"}, {"name": "selectRandomQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.String", "java.lang.Integer"], "doc": "\n 查询随机问题\r\n\r\n @param jobId        岗位ID\r\n @param difficulty   难度等级\r\n @param questionType 问题类型\r\n @param limit        限制数量\r\n @return 随机问题列表\r\n"}, {"name": "selectQuestionStatistics", "paramTypes": [], "doc": "\n 查询问题统计信息\r\n\r\n @return 统计信息Map\r\n"}, {"name": "batchInsert", "paramTypes": ["java.util.List"], "doc": "\n 批量插入问题\r\n\r\n @param questions 问题列表\r\n @return 插入数量\r\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Long"], "doc": "\n 批量更新问题状态\r\n\r\n @param ids      问题ID列表\r\n @param status   新状态\r\n @param updateBy 更新人\r\n @return 更新数量\r\n"}], "constructors": []}