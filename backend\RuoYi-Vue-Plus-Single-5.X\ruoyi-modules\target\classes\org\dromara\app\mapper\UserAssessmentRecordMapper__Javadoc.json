{"doc": " 用户评估记录Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectRecordsByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询评估记录列表\n\n @param userId 用户ID\n @return 评估记录列表\n"}, {"name": "selectRecordsByUserIdAndType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 根据用户ID和评估类型查询评估记录\n\n @param userId         用户ID\n @param assessmentType 评估类型\n @return 评估记录列表\n"}, {"name": "selectLatestRecordByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询最新的评估记录\n\n @param userId 用户ID\n @return 最新评估记录\n"}, {"name": "selectLatestRecordByUserIdAndType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 根据用户ID和评估类型查询最新的评估记录\n\n @param userId         用户ID\n @param assessmentType 评估类型\n @return 最新评估记录\n"}, {"name": "countCompletedRecordsByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询已完成的评估记录数量\n\n @param userId 用户ID\n @return 已完成评估记录数量\n"}, {"name": "selectRecordsByUserIdAndStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 根据用户ID和状态查询评估记录\n\n @param userId 用户ID\n @param status 状态\n @return 评估记录列表\n"}], "constructors": []}