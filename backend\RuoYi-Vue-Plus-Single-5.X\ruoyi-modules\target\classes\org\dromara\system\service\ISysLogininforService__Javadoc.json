{"doc": " 系统访问日志情况信息 服务层\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "insertLogininfor", "paramTypes": ["org.dromara.system.domain.bo.SysLogininforBo"], "doc": " 新增系统登录日志\n\n @param bo 访问日志对象\n"}, {"name": "selectLogininforList", "paramTypes": ["org.dromara.system.domain.bo.SysLogininforBo"], "doc": " 查询系统登录日志集合\n\n @param logininfor 访问日志对象\n @return 登录记录集合\n"}, {"name": "deleteLogininforByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除系统登录日志\n\n @param infoIds 需要删除的登录日志ID\n @return 结果\n"}, {"name": "cleanLogininfor", "paramTypes": [], "doc": " 清空系统登录日志\n"}], "constructors": []}