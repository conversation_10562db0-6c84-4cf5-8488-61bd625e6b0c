package org.dromara.app.controller.auth;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.stp.parameter.SaLoginParameter;
import cn.hutool.core.util.RandomUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.dto.AppAuthDto;
import org.dromara.app.domain.vo.AppUserInfoVo;
import org.dromara.app.service.IAppUserService;
import org.dromara.common.core.constant.Constants;
import org.dromara.common.core.constant.GlobalConstants;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.model.AppLoginUser;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mail.config.properties.MailProperties;
import org.dromara.common.mail.utils.MailUtils;
import org.dromara.common.ratelimiter.annotation.RateLimiter;
import org.dromara.common.ratelimiter.enums.LimitType;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.api.entity.SmsResponse;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 认证控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/auth")
public class AppAuthController {

    private final IAppUserService appUserService;
    private final MailProperties mailProperties;

    /**
     * 发送手机验证码
     */
    @SaIgnore
    @RateLimiter(key = "#phone", time = 60, count = 1, limitType = LimitType.IP)
    @PostMapping("/send-sms-code")
    public R<Void> sendSmsCode(@RequestBody @Valid Map<String, String> request) {
        String phone = request.get("phone");
        if (StringUtils.isBlank(phone)) {
            return R.fail("手机号不能为空");
        }

        String key = GlobalConstants.CAPTCHA_CODE_KEY + phone;
        String code = RandomUtil.randomNumbers(6);
        RedisUtils.setCacheObject(key, code, Duration.ofMinutes(Constants.CAPTCHA_EXPIRATION));

        // 验证码模板id (需要在阿里云短信控制台配置)
        String templateId = "SMS_154950909";
        LinkedHashMap<String, String> map = new LinkedHashMap<>(1);
        map.put("code", code);

        try {
            SmsBlend smsBlend = SmsFactory.getSmsBlend("config1");
            SmsResponse smsResponse = smsBlend.sendMessage(phone, templateId, map);
            if (!smsResponse.isSuccess()) {
                log.error("验证码短信发送异常 => {}", smsResponse);
                return R.fail("验证码发送失败，请稍后再试");
            }
            log.info("手机号：{} 验证码发送成功", phone);
            return R.ok();
        } catch (Exception e) {
            log.error("验证码短信发送异常", e);
            return R.fail("验证码发送失败，请稍后再试");
        }
    }

    /**
     * 发送邮箱验证码
     */
    @SaIgnore
    @RateLimiter(key = "#email", time = 60, count = 1, limitType = LimitType.IP)
    @PostMapping("/send-email-code")
    public R<Void> sendEmailCode(@RequestBody @Valid Map<String, String> request) {
        String email = request.get("email");
        if (StringUtils.isBlank(email)) {
            return R.fail("邮箱不能为空");
        }

        if (!mailProperties.getEnabled()) {
            return R.fail("当前系统没有开启邮箱功能！");
        }

        String key = GlobalConstants.CAPTCHA_CODE_KEY + email;
        String code = RandomUtil.randomNumbers(6);
        RedisUtils.setCacheObject(key, code, Duration.ofMinutes(Constants.CAPTCHA_EXPIRATION));

        try {
            String subject = "邮箱验证码";
            String text = String.format("您的验证码是：%s，有效期为%d分钟，请勿泄露给他人。", code, Constants.CAPTCHA_EXPIRATION);
            MailUtils.sendText(email, subject, text);
            log.info("邮箱：{} 验证码发送成功", email);
            return R.ok();
        } catch (Exception e) {
            log.error("邮箱验证码发送异常", e);
            return R.fail("验证码发送失败，请稍后再试");
        }
    }

    /**
     * 手机号验证码登录
     */
    @SaIgnore
    @PostMapping("/login/phone")
    public R<AppUserInfoVo> loginByPhone(@RequestBody @Valid AppAuthDto authDto) {
        if (StringUtils.isBlank(authDto.getPhone()) || StringUtils.isBlank(authDto.getCode())) {
            return R.fail("手机号和验证码不能为空");
        }

        try {
            AppUserInfoVo userInfo = appUserService.loginByPhone(authDto.getPhone(), authDto.getCode());

            // 登录成功，生成token
            Long userId = Long.valueOf(userInfo.getId());
            StpUtil.login(userId);
            userInfo.setTokenValue(StpUtil.getTokenValue());
            userInfo.setTokenName(StpUtil.getTokenName());
            // 更新登录信息
            String loginIp = ServletUtils.getClientIP();
            appUserService.updateLoginInfo(userId, loginIp);
            AppLoginUser appLoginUser = new AppLoginUser();
            BeanUtils.copyProperties(appLoginUser, userInfo);
            // 登录 Session存储登录信息
            LoginHelper.appLogin(appLoginUser, new SaLoginParameter().setDeviceType("app"));
            return R.ok(userInfo);
        } catch (Exception e) {
            log.error("手机号登录失败：{}", e.getMessage());
            return R.fail(e.getMessage());
        }
    }

    /**
     * 密码登录
     */
    @SaIgnore
    @PostMapping("/login/password")
    public R<AppUserInfoVo> loginByPassword(@RequestBody @Valid AppAuthDto authDto) {
        if (StringUtils.isAnyBlank(authDto.getPhone(), authDto.getPassword())) {
            return R.fail("手机号和密码不能为空");
        }

        try {
            AppUserInfoVo userInfo = appUserService.loginByPassword(authDto.getPhone(), authDto.getPassword());

            // 登录成功，生成token
            long userId = Long.parseLong(userInfo.getId());
            StpUtil.login(userId);
            log.info("手机号：{} 登录成功", authDto.getPhone());
            userInfo.setTokenValue(StpUtil.getTokenValue());
            userInfo.setTokenName(StpUtil.getTokenName());

            // 更新登录信息
            String loginIp = ServletUtils.getClientIP();
            appUserService.updateLoginInfo(userId, loginIp);

            return R.ok(userInfo);
        } catch (Exception e) {
            log.error("密码登录失败：{}", e.getMessage());
            return R.fail(e.getMessage());
        }
    }

    /**
     * 用户注册
     */
    @SaIgnore
    @PostMapping("/register")
    public R<AppUserInfoVo> register(@RequestBody @Valid AppAuthDto authDto) {
        if (StringUtils.isAnyBlank(authDto.getEmail(), authDto.getCode(), authDto.getPassword(),
            authDto.getRealName(), authDto.getStudentId(), authDto.getMajor(), authDto.getGrade())) {
            return R.fail("注册信息不完整");
        }

        try {
            AppUserInfoVo userInfo = appUserService.register(authDto);

            // 注册成功后自动登录
            Long userId = Long.valueOf(userInfo.getId());
            StpUtil.login(userId);
            userInfo.setTokenValue(StpUtil.getTokenValue());
            userInfo.setTokenName(StpUtil.getTokenName());

            return R.ok(userInfo);
        } catch (Exception e) {
            log.error("用户注册失败：{}", e.getMessage());
            return R.fail(e.getMessage());
        }
    }

    /**
     * 检查手机号是否已注册
     */
    @SaIgnore
    @PostMapping("/check-phone")
    public R<Map<String, Boolean>> checkPhoneExists(@RequestBody @Valid Map<String, String> request) {
        String phone = request.get("phone");
        if (StringUtils.isBlank(phone)) {
            return R.fail("手机号不能为空");
        }

        boolean exists = appUserService.checkPhoneExists(phone);
        Map<String, Boolean> result = new HashMap<>();
        result.put("exists", exists);
        return R.ok(result);
    }

    /**
     * 检查邮箱是否已注册
     */
    @SaIgnore
    @PostMapping("/check-email")
    public R<Map<String, Boolean>> checkEmailExists(@RequestBody @Valid Map<String, String> request) {
        String email = request.get("email");
        if (StringUtils.isBlank(email)) {
            return R.fail("邮箱不能为空");
        }

        boolean exists = appUserService.checkEmailExists(email);
        Map<String, Boolean> result = new HashMap<>();
        result.put("exists", exists);
        return R.ok(result);
    }

    /**
     * 重置密码
     */
    @SaIgnore
    @PostMapping("/reset-password")
    public R<Void> resetPassword(@RequestBody @Valid AppAuthDto authDto) {
        if (StringUtils.isAnyBlank(authDto.getPhone(), authDto.getCode(), authDto.getNewPassword())) {
            return R.fail("手机号、验证码和新密码不能为空");
        }

        try {
            appUserService.resetPassword(authDto.getPhone(), authDto.getCode(), authDto.getNewPassword());
            return R.ok();
        } catch (Exception e) {
            log.error("重置密码失败：{}", e.getMessage());
            return R.fail(e.getMessage());
        }
    }

    /**
     * 第三方登录 (基础实现，可根据需要扩展)
     */
    @SaIgnore
    @PostMapping("/third-party-login")
    public R<AppUserInfoVo> thirdPartyLogin(@RequestBody @Valid AppAuthDto authDto) {
        // TODO: 实现第三方登录逻辑
        return R.fail("第三方登录功能暂未实现");
    }

    /**
     * 刷新Token
     */
    @PostMapping("/refresh-token")
    public R<Map<String, String>> refreshToken(@RequestBody @Valid AppAuthDto authDto) {
        try {
            // 这里使用SaToken的续期功能
            StpUtil.renewTimeout(7200);

            Map<String, String> result = new HashMap<>();
            result.put("token", StpUtil.getTokenValue());
            result.put("refreshToken", StpUtil.getTokenValue());

            return R.ok(result);
        } catch (Exception e) {
            log.error("刷新Token失败：{}", e.getMessage());
            return R.fail("Token刷新失败");
        }
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public R<Void> logout() {
        try {
            StpUtil.logout();
            return R.ok();
        } catch (Exception e) {
            log.error("退出登录失败：{}", e.getMessage());
            return R.fail("退出登录失败");
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/current-user")
    public R<AppUserInfoVo> getCurrentUser() {
        try {
            long userId = StpUtil.getLoginIdAsLong();
            AppUserInfoVo userInfo = appUserService.getUserInfo(userId);
            return R.ok(userInfo);
        } catch (Exception e) {
            log.error("获取用户信息失败：{}", e.getMessage());
            return R.fail("获取用户信息失败");
        }
    }


}
