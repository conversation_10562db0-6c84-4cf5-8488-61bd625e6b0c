{"doc": "\n 视频播放记录缓存服务\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "buildRecordKey", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 构建播放记录缓存键\r\n"}, {"name": "buildUserHistoryKey", "paramTypes": ["java.lang.Long"], "doc": "\n 构建用户历史记录缓存键\r\n"}, {"name": "cachePlayRecord", "paramTypes": ["org.dromara.app.domain.VideoPlayRecord"], "doc": "\n 缓存播放记录\r\n"}, {"name": "getCachedPlayRecord", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 获取播放记录缓存\r\n"}, {"name": "updatePlayRecord", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 更新播放记录缓存\r\n"}, {"name": "getUserVideoHistory", "paramTypes": ["java.lang.Long", "int", "int"], "doc": "\n 获取用户观看历史视频ID列表（按时间倒序）\r\n"}, {"name": "deletePlayRecord", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 删除播放记录缓存\r\n"}, {"name": "preloadUserHistory", "paramTypes": ["java.lang.Long", "java.util.List"], "doc": "\n 批量预热用户历史记录到缓存\r\n"}, {"name": "isRecordCached", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 检查播放记录是否存在于缓存中\r\n"}], "constructors": []}