package org.dromara.app.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 推荐资源视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "推荐资源视图对象")
public class ResourceVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 资源标题
     */
    @Schema(description = "资源标题")
    private String title;

    /**
     * 资源类型
     */
    @Schema(description = "资源类型")
    private String type;

    /**
     * 资源链接
     */
    @Schema(description = "资源链接")
    private String url;

    /**
     * 资源描述
     */
    @Schema(description = "资源描述")
    private String description;
}
