---
description: 
globs: 
alwaysApply: false
---


# UniApp 项目规范文档

## 项目概述

本项目是基于 uniapp 的最佳实践框架项目，使用 Vue3 + Vite5 + Pnpm + TypeScript + UnoCSS 技术栈进行开发。项目支持多平台部署，包括微信小程序、H5、APP、支付宝小程序、钉钉小程序、抖音小程序等。

## 技术栈

- **前端框架**：Vue3
- **开发语言**：TypeScript
- **构建工具**：Vite5
- **包管理器**：Pnpm
- **CSS框架**：UnoCSS
- **跨平台框架**：UniApp
- **Icon 图标库**：(必须使用uni-icons + Material Design Icons 图标库中的图标)

## 注释规则

- 所有注释都要为中文
- 所有方法都要有注释 去解释这个方法是怎么用的
- 例如 :
/**
 * @description 显示通知栏
 * @param message 通知内容
 * @param type 通知类型（info/success/error）
 * @param duration 显示时长（毫秒）
 */
function showNotification(message: string, type = 'info', duration = 3000) {
- 注释必须简明扼要，避免冗长的解释

## 目录结构

``` txt
src/
├── components/        # 公共组件目录
├── hooks/             # 自定义钩子函数
├── interceptors/      # 拦截器目录
├── layouts/           # 布局组件目录
├── pages/             # 页面目录
├── pages-sub/         # 分包页面目录
├── service/           # API服务目录
├── static/            # 静态资源目录
├── store/             # 状态管理目录
├── style/             # 全局样式目录
├── types/             # 类型定义目录
├── uni_modules/       # uni-app模块目录
└── utils/             # 工具函数目录
```

## 页面结构

项目包含以下主要页面：

- 登录页
- 注册页
- 个人中心页
- 面试岗位选择页
- 模拟面试页
- 面试结果页
- 学习资源推荐页
- 历史记录页

## 代码规范

### 命名规范

- **组件**：多单词组件名，使用 PascalCase，如 `UserProfile.vue`
- **变量**：使用 camelCase，如 `userName`
- **函数**：使用 camelCase，如 `getUserInfo()`
- **CSS类名**：使用 kebab-case，如 `user-profile`

### 代码风格

#### Prettier 配置

```json
{
  "singleQuote": true,
  "printWidth": 100,
  "tabWidth": 2,
  "useTabs": false,
  "semi": false,
  "trailingComma": "all",
  "endOfLine": "auto",
  "htmlWhitespaceSensitivity": "ignore"
}
```

#### ESLint 配置

项目使用了多种 ESLint 规则集成，包括：

- eslint:recommended
- plugin:@typescript-eslint/recommended
- plugin:vue/vue3-essential
- plugin:import/recommended
- standard
- prettier
- plugin:prettier/recommended

主要自定义规则包括：

- 关闭未使用变量警告
- 允许使用 console
- 关闭组件命名限制
- 关闭 any 类型限制
- 等等

#### Stylelint 配置

项目使用了以下 Stylelint 配置：

- stylelint-config-recommended
- stylelint-config-recommended-scss
- stylelint-config-recommended-vue/scss
- stylelint-config-html/vue
- stylelint-config-recess-order

自定义规则：

- 支持 rpx 单位（小程序特有单位）
- 支持 page 选择器（小程序特有选择器）
- 支持 v-deep 和 deep 伪类

### Git 提交规范

使用 Commitlint 规范提交信息，类型包括：

- feat: 新功能
- fix: 修复问题
- perf: 性能优化
- style: 代码风格调整
- docs: 文档更新
- test: 测试相关
- refactor: 代码重构
- build: 构建相关
- ci: CI配置相关
- chore: 其他修改
- revert: 回滚
- wip: 开发中
- workflow: 工作流相关
- types: 类型定义
- release: 发布相关

## VS Code 配置

### 推荐扩展

- vue.volar
- stylelint.vscode-stylelint
- esbenp.prettier-vscode
- dbaeumer.vscode-eslint
- antfu.unocss
- antfu.iconify
- evils.uniapp-vscode
- uni-helper.uni-helper-vscode
- uni-helper.uni-app-schemas-vscode
- uni-helper.uni-highlight-vscode
- uni-helper.uni-ui-snippets-vscode
- uni-helper.uni-app-snippets-vscode
- mrmlnc.vscode-json5
- streetsidesoftware.code-spell-checker

### 编辑器设置

```json
{
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit",
    "source.fixAll.eslint": "explicit",
    "source.fixAll.stylelint": "explicit"
  },
  "stylelint.validate": ["css", "scss", "vue", "html"],
  "stylelint.enable": true,
  "css.validate": false,
  "less.validate": false,
  "scss.validate": false,
  "files.associations": {
    "pages.json": "jsonc",
    "manifest.json": "jsonc"
  }
}
```

## 多平台开发注意事项

1. 使用条件编译处理平台差异,根据每个平台的差异进行处理

   ```js
   // #ifdef H5
   // 仅在 H5 平台下编译
   // #endif
   
   // #ifdef MP-WEIXIN
   // 仅在微信小程序下编译
   // #endif
   ```

2. 样式适配
   - 必须使用 rpx 单位进行响应式布局
   - 注意不同平台样式差异，必要时使用条件编译

3. API 兼容
   - 优先使用 uni API 保证多端兼容
   - 平台特有 API 使用条件编译包裹

4. 组件选择
   - 优先使用 uni-ui 等跨平台组件
   - 自定义组件需考虑多端兼容性

5. 注意事项
   - 避免使用浏览器不支持的 API
   - 注意不同平台的差异，如事件处理、组件命名等
   - 及时更新依赖，避免已知问题

## 开发流程

1. 代码提交前会自动执行：
   - prettier 格式化代码
   - eslint 检查并修复 JS/TS/Vue 代码
   - stylelint 检查并修复样式代码

2. Git 提交时：
   - 使用 commitlint 检查提交消息格式
   - 建议使用 `pnpm cz` 命令规范化提交消息

## 编译部署

使用以下命令编译不同平台版本：

- H5: `pnpm build:h5`
- 微信小程序: `pnpm build:mp-weixin`
- APP: 通过 HBuilderX 云打包
- 其他平台: 参考 package.json 中的构建命令

## 页面css样式规范

- 要兼容H5端和微信小程序端
- 这俩端的样式要一致
- 强制要求不要使用 :class='{...}'

## 组件

- 组件要支持H5端和微信小程序端
- 组件的样式要一致
- 组件的props要规范
- 组件的事件要规范
- 组件的插槽要规范
- 组件的文档要规范
- 组件的demo要规范
- src/components 是存放自定义组件的位置
- 组件的props要使用defineProps定义
- 组件的事件要使用defineEmits定义
- 组件的插槽要使用slot定义
- 组件的文档要使用/**/注释定义
- 组件的demo要使用.vue文件定义
- 组件的样式要使用scss定义
- 组件的样式要使用BEM规范定义
- 组件的样式要使用变量定义
- 组件的样式要使用mixin定义
- 组件的样式要使用extends定义
