{"doc": "\n 岗位信息Service接口\r\n\r\n <AUTHOR> Assistant\r\n @date 2025-07-19\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectJobPageWithFavorite", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "org.dromara.app.domain.bo.JobQueryBo"], "doc": "\n 分页查询岗位列表\r\n\r\n @param page    分页参数\r\n @param queryBo 查询条件\r\n @return 岗位列表\r\n"}, {"name": "selectByTechnicalDomain", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": "\n 根据技术领域查询岗位\r\n\r\n @param technicalDomain 技术领域\r\n @param level          岗位级别\r\n @param limit          限制数量\r\n @return 岗位列表\r\n"}, {"name": "selectRecommendedJobs", "paramTypes": ["java.lang.Integer", "java.lang.Long"], "doc": "\n 查询推荐岗位\r\n\r\n @param limit  限制数量\r\n @param userId 用户ID\r\n @return 推荐岗位列表\r\n"}, {"name": "selectHotJobs", "paramTypes": ["java.lang.Integer", "java.lang.Long"], "doc": "\n 查询热门岗位\r\n\r\n @param limit  限制数量\r\n @param userId 用户ID\r\n @return 热门岗位列表\r\n"}, {"name": "selectJobDetail", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 根据ID查询岗位详情\r\n\r\n @param jobId  岗位ID\r\n @param userId 用户ID\r\n @return 岗位详情\r\n"}, {"name": "incrementViewCount", "paramTypes": ["java.lang.Long"], "doc": "\n 增加浏览次数\r\n\r\n @param jobId 岗位ID\r\n @return 是否成功\r\n"}, {"name": "updateFavoriteCount", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 更新收藏次数\r\n\r\n @param jobId     岗位ID\r\n @param increment 增量（1或-1）\r\n @return 是否成功\r\n"}, {"name": "getTechnicalDomainStats", "paramTypes": [], "doc": "\n 查询技术领域统计\r\n\r\n @return 统计信息\r\n"}], "constructors": []}