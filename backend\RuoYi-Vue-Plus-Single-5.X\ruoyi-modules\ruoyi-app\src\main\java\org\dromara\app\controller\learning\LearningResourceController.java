package org.dromara.app.controller.learning;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.LearningResource;
import org.dromara.app.service.ILearningResourceService;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 学习资源控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/learning/resource")
public class LearningResourceController extends BaseController {

    private final ILearningResourceService learningResourceService;

    /**
     * 创建学习资源
     *
     * @param resource 学习资源
     * @return 操作结果
     */
    @Log(title = "创建学习资源", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> createResource(@RequestBody @Validated LearningResource resource) {
        try {
            boolean success = learningResourceService.createResource(resource);
            return success ? R.ok() : R.fail("创建学习资源失败");
        } catch (Exception e) {
            log.error("创建学习资源失败", e);
            return R.fail("创建学习资源失败: " + e.getMessage());
        }
    }

    /**
     * 更新学习资源
     *
     * @param resource 学习资源
     * @return 操作结果
     */
    @Log(title = "更新学习资源", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> updateResource(@RequestBody @Validated LearningResource resource) {
        try {
            boolean success = learningResourceService.updateResource(resource);
            return success ? R.ok() : R.fail("更新学习资源失败");
        } catch (Exception e) {
            log.error("更新学习资源失败", e);
            return R.fail("更新学习资源失败: " + e.getMessage());
        }
    }

    /**
     * 删除学习资源
     *
     * @param resourceId 资源ID
     * @return 操作结果
     */
    @Log(title = "删除学习资源", businessType = BusinessType.DELETE)
    @DeleteMapping("/{resourceId}")
    public R<Void> deleteResource(@PathVariable Long resourceId) {
        try {
            boolean success = learningResourceService.deleteResource(resourceId);
            return success ? R.ok() : R.fail("删除学习资源失败");
        } catch (Exception e) {
            log.error("删除学习资源失败", e);
            return R.fail("删除学习资源失败: " + e.getMessage());
        }
    }

    /**
     * 获取资源详情
     *
     * @param resourceId 资源ID
     * @return 学习资源
     */
    @GetMapping("/{resourceId}")
    public R<LearningResource> getResource(@PathVariable Long resourceId) {
        try {
            LearningResource resource = learningResourceService.getResourceById(resourceId);
            return resource != null ? R.ok(resource) : R.fail("资源不存在");
        } catch (Exception e) {
            log.error("获取学习资源失败", e);
            return R.fail("获取学习资源失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询资源
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param skillArea 技能领域
     * @param type 资源类型
     * @param difficulty 难度等级
     * @return 分页结果
     */
    @GetMapping("/page")
    public R<Page<LearningResource>> getResourcesPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String skillArea,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String difficulty) {
        try {
            Page<LearningResource> page = learningResourceService.getResourcesPage(
                pageNum, pageSize, skillArea, type, difficulty);
            return R.ok(page);
        } catch (Exception e) {
            log.error("分页查询学习资源失败", e);
            return R.fail("分页查询学习资源失败: " + e.getMessage());
        }
    }

    /**
     * 获取推荐资源
     *
     * @param skillArea 技能领域
     * @param difficulty 难度等级
     * @param limit 限制数量
     * @return 资源列表
     */
    @GetMapping("/recommend")
    public R<List<LearningResource>> getRecommendedResources(
            @RequestParam String skillArea,
            @RequestParam(required = false) String difficulty,
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<LearningResource> resources = learningResourceService.getRecommendedResources(
                skillArea, difficulty, limit);
            return R.ok(resources);
        } catch (Exception e) {
            log.error("获取推荐资源失败", e);
            return R.fail("获取推荐资源失败: " + e.getMessage());
        }
    }

    /**
     * 根据类型获取资源
     *
     * @param type 资源类型
     * @param limit 限制数量
     * @return 资源列表
     */
    @GetMapping("/type/{type}")
    public R<List<LearningResource>> getResourcesByType(
            @PathVariable String type,
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<LearningResource> resources = learningResourceService.getResourcesByType(type, limit);
            return R.ok(resources);
        } catch (Exception e) {
            log.error("根据类型获取资源失败", e);
            return R.fail("根据类型获取资源失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门资源
     *
     * @param limit 限制数量
     * @return 资源列表
     */
    @GetMapping("/popular")
    public R<List<LearningResource>> getPopularResources(
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<LearningResource> resources = learningResourceService.getPopularResources(limit);
            return R.ok(resources);
        } catch (Exception e) {
            log.error("获取热门资源失败", e);
            return R.fail("获取热门资源失败: " + e.getMessage());
        }
    }

    /**
     * 获取免费资源
     *
     * @param skillArea 技能领域
     * @param limit 限制数量
     * @return 资源列表
     */
    @GetMapping("/free")
    public R<List<LearningResource>> getFreeResources(
            @RequestParam(required = false) String skillArea,
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<LearningResource> resources = learningResourceService.getFreeResources(skillArea, limit);
            return R.ok(resources);
        } catch (Exception e) {
            log.error("获取免费资源失败", e);
            return R.fail("获取免费资源失败: " + e.getMessage());
        }
    }

    /**
     * 根据标签获取资源
     *
     * @param tag 标签
     * @param limit 限制数量
     * @return 资源列表
     */
    @GetMapping("/tag/{tag}")
    public R<List<LearningResource>> getResourcesByTag(
            @PathVariable String tag,
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<LearningResource> resources = learningResourceService.getResourcesByTag(tag, limit);
            return R.ok(resources);
        } catch (Exception e) {
            log.error("根据标签获取资源失败", e);
            return R.fail("根据标签获取资源失败: " + e.getMessage());
        }
    }

    /**
     * 搜索资源
     *
     * @param keyword 关键词
     * @param limit 限制数量
     * @return 资源列表
     */
    @GetMapping("/search")
    public R<List<LearningResource>> searchResources(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "20") Integer limit) {
        try {
            List<LearningResource> resources = learningResourceService.searchResources(keyword, limit);
            return R.ok(resources);
        } catch (Exception e) {
            log.error("搜索资源失败", e);
            return R.fail("搜索资源失败: " + e.getMessage());
        }
    }

    /**
     * 评估资源质量
     *
     * @param resourceId 资源ID
     * @param qualityAssessment 质量评估
     * @return 操作结果
     */
    @Log(title = "评估资源质量", businessType = BusinessType.UPDATE)
    @PostMapping("/{resourceId}/assess")
    public R<Void> assessResourceQuality(@PathVariable Long resourceId,
                                        @RequestBody LearningResource.QualityAssessment qualityAssessment) {
        try {
            boolean success = learningResourceService.assessResourceQuality(resourceId, qualityAssessment);
            return success ? R.ok() : R.fail("评估资源质量失败");
        } catch (Exception e) {
            log.error("评估资源质量失败", e);
            return R.fail("评估资源质量失败: " + e.getMessage());
        }
    }

    /**
     * 更新资源评分
     *
     * @param resourceId 资源ID
     * @param rating 评分
     * @return 操作结果
     */
    @Log(title = "更新资源评分", businessType = BusinessType.UPDATE)
    @PostMapping("/{resourceId}/rate")
    public R<Void> updateResourceRating(@PathVariable Long resourceId, @RequestParam Double rating) {
        try {
            if (rating < 1.0 || rating > 5.0) {
                return R.fail("评分必须在1-5之间");
            }
            boolean success = learningResourceService.updateResourceRating(resourceId, rating);
            return success ? R.ok() : R.fail("更新资源评分失败");
        } catch (Exception e) {
            log.error("更新资源评分失败", e);
            return R.fail("更新资源评分失败: " + e.getMessage());
        }
    }

    /**
     * 开始学习资源
     *
     * @param resourceId 资源ID
     * @return 操作结果
     */
    @PostMapping("/{resourceId}/start")
    public R<Void> startLearning(@PathVariable Long resourceId) {
        try {
            boolean success = learningResourceService.incrementLearnerCount(resourceId);
            return success ? R.ok() : R.fail("开始学习失败");
        } catch (Exception e) {
            log.error("开始学习失败", e);
            return R.fail("开始学习失败: " + e.getMessage());
        }
    }

    /**
     * 完成学习资源
     *
     * @param resourceId 资源ID
     * @return 操作结果
     */
    @PostMapping("/{resourceId}/complete")
    public R<Void> completeLearning(@PathVariable Long resourceId) {
        try {
            boolean success = learningResourceService.incrementCompletionCount(resourceId);
            return success ? R.ok() : R.fail("完成学习失败");
        } catch (Exception e) {
            log.error("完成学习失败", e);
            return R.fail("完成学习失败: " + e.getMessage());
        }
    }

    /**
     * 获取相似资源
     *
     * @param resourceId 资源ID
     * @param limit 限制数量
     * @return 资源列表
     */
    @GetMapping("/{resourceId}/similar")
    public R<List<LearningResource>> getSimilarResources(
            @PathVariable Long resourceId,
            @RequestParam(defaultValue = "5") Integer limit) {
        try {
            List<LearningResource> resources = learningResourceService.getSimilarResources(resourceId, limit);
            return R.ok(resources);
        } catch (Exception e) {
            log.error("获取相似资源失败", e);
            return R.fail("获取相似资源失败: " + e.getMessage());
        }
    }

    /**
     * 执行质量评估
     *
     * @param resourceId 资源ID
     * @return 质量评估结果
     */
    @PostMapping("/{resourceId}/quality-assessment")
    public R<LearningResource.QualityAssessment> performQualityAssessment(@PathVariable Long resourceId) {
        try {
            LearningResource.QualityAssessment assessment = learningResourceService.performQualityAssessment(resourceId);
            return R.ok(assessment);
        } catch (Exception e) {
            log.error("执行质量评估失败", e);
            return R.fail("执行质量评估失败: " + e.getMessage());
        }
    }

    /**
     * 批量导入资源
     *
     * @param resources 资源列表
     * @return 导入结果
     */
    @Log(title = "批量导入资源", businessType = BusinessType.IMPORT)
    @PostMapping("/batch-import")
    public R<Map<String, Object>> batchImportResources(@RequestBody List<LearningResource> resources) {
        try {
            int successCount = learningResourceService.batchImportResources(resources);
            Map<String, Object> result = Map.of(
                "totalCount", resources.size(),
                "successCount", successCount,
                "failureCount", resources.size() - successCount
            );
            return R.ok(result);
        } catch (Exception e) {
            log.error("批量导入资源失败", e);
            return R.fail("批量导入资源失败: " + e.getMessage());
        }
    }

    // ========== 统计相关接口 ==========

    /**
     * 获取资源统计信息
     *
     * @return 统计信息
     */
    @GetMapping("/statistics")
    public R<Map<String, Object>> getResourceStatistics() {
        try {
            Map<String, Object> statistics = learningResourceService.getResourceStatistics();
            return R.ok(statistics);
        } catch (Exception e) {
            log.error("获取资源统计信息失败", e);
            return R.fail("获取资源统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取技能领域分布
     *
     * @return 技能领域分布
     */
    @GetMapping("/statistics/skill-areas")
    public R<List<Map<String, Object>>> getSkillAreaDistribution() {
        try {
            List<Map<String, Object>> distribution = learningResourceService.getSkillAreaDistribution();
            return R.ok(distribution);
        } catch (Exception e) {
            log.error("获取技能领域分布失败", e);
            return R.fail("获取技能领域分布失败: " + e.getMessage());
        }
    }

    /**
     * 获取资源类型分布
     *
     * @return 资源类型分布
     */
    @GetMapping("/statistics/resource-types")
    public R<List<Map<String, Object>>> getResourceTypeDistribution() {
        try {
            List<Map<String, Object>> distribution = learningResourceService.getResourceTypeDistribution();
            return R.ok(distribution);
        } catch (Exception e) {
            log.error("获取资源类型分布失败", e);
            return R.fail("获取资源类型分布失败: " + e.getMessage());
        }
    }

    /**
     * 获取顶级提供者
     *
     * @param limit 限制数量
     * @return 提供者排行
     */
    @GetMapping("/statistics/top-providers")
    public R<List<Map<String, Object>>> getTopProviders(
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<Map<String, Object>> providers = learningResourceService.getTopProviders(limit);
            return R.ok(providers);
        } catch (Exception e) {
            log.error("获取顶级提供者失败", e);
            return R.fail("获取顶级提供者失败: " + e.getMessage());
        }
    }

    /**
     * 获取资源标签云
     *
     * @param limit 限制数量
     * @return 标签统计
     */
    @GetMapping("/statistics/tag-cloud")
    public R<Map<String, Integer>> getResourceTagCloud(
            @RequestParam(defaultValue = "50") Integer limit) {
        try {
            Map<String, Integer> tagCloud = learningResourceService.getResourceTagCloud(limit);
            return R.ok(tagCloud);
        } catch (Exception e) {
            log.error("获取资源标签云失败", e);
            return R.fail("获取资源标签云失败: " + e.getMessage());
        }
    }

    // ========== 智能推荐相关接口 ==========

    /**
     * 获取个性化推荐资源
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 个性化推荐资源列表
     */
    @GetMapping("/personalized/{userId}")
    public R<List<LearningResource>> getPersonalizedRecommendations(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<LearningResource> resources = learningResourceService.getPersonalizedRecommendations(userId, limit);
            return R.ok(resources);
        } catch (Exception e) {
            log.error("获取个性化推荐资源失败", e);
            return R.fail("获取个性化推荐资源失败: " + e.getMessage());
        }
    }

    /**
     * 根据技能差距推荐资源
     *
     * @param skillAreas 技能领域列表（逗号分隔）
     * @param difficulty 难度等级
     * @param limit 限制数量
     * @return 推荐资源列表
     */
    @GetMapping("/skill-gaps")
    public R<List<LearningResource>> getResourcesBySkillGaps(
            @RequestParam String skillAreas,
            @RequestParam(required = false) String difficulty,
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<String> skillAreaList = List.of(skillAreas.split(","));
            List<LearningResource> resources = learningResourceService.getResourcesBySkillGaps(
                skillAreaList, difficulty, limit);
            return R.ok(resources);
        } catch (Exception e) {
            log.error("根据技能差距推荐资源失败", e);
            return R.fail("根据技能差距推荐资源失败: " + e.getMessage());
        }
    }

    /**
     * 获取协同过滤推荐资源
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 推荐资源列表
     */
    @GetMapping("/collaborative/{userId}")
    public R<List<LearningResource>> getCollaborativeFilteringRecommendations(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<LearningResource> resources = learningResourceService.getCollaborativeFilteringRecommendations(
                userId, limit);
            return R.ok(resources);
        } catch (Exception e) {
            log.error("获取协同过滤推荐资源失败", e);
            return R.fail("获取协同过滤推荐资源失败: " + e.getMessage());
        }
    }

    /**
     * 获取趋势资源
     *
     * @param days 天数
     * @param limit 限制数量
     * @return 趋势资源列表
     */
    @GetMapping("/trending")
    public R<List<LearningResource>> getTrendingResources(
            @RequestParam(defaultValue = "30") Integer days,
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<LearningResource> resources = learningResourceService.getTrendingResources(days, limit);
            return R.ok(resources);
        } catch (Exception e) {
            log.error("获取趋势资源失败", e);
            return R.fail("获取趋势资源失败: " + e.getMessage());
        }
    }

    /**
     * 智能推荐资源排序
     *
     * @param resources 原始资源列表
     * @param userPreferences 用户偏好
     * @return 排序后的资源列表
     */
    @PostMapping("/sort-by-recommendation")
    public R<List<LearningResource>> sortResourcesByRecommendation(
            @RequestBody List<LearningResource> resources,
            @RequestParam(required = false) Map<String, Object> userPreferences) {
        try {
            List<LearningResource> sortedResources = learningResourceService.sortResourcesByRecommendation(
                resources, userPreferences);
            return R.ok(sortedResources);
        } catch (Exception e) {
            log.error("智能推荐资源排序失败", e);
            return R.fail("智能推荐资源排序失败: " + e.getMessage());
        }
    }
}
