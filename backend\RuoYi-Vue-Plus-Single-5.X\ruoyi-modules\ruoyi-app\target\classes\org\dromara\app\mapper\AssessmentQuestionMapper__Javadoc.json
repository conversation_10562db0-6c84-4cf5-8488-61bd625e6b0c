{"doc": "\n 评估问题Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectQuestionsWithOptions", "paramTypes": [], "doc": "\n 查询评估问题列表（包含选项）\r\n\r\n @return 评估问题列表\r\n"}, {"name": "selectQuestionsWithOptionsByStatus", "paramTypes": ["java.lang.String"], "doc": "\n 根据状态查询评估问题列表（包含选项）\r\n\r\n @param status 状态\r\n @return 评估问题列表\r\n"}, {"name": "selectQuestionWithOptionsById", "paramTypes": ["java.lang.Long"], "doc": "\n 根据问题ID查询评估问题（包含选项）\r\n\r\n @param questionId 问题ID\r\n @return 评估问题\r\n"}, {"name": "selectQuestionWithOptionsByCode", "paramTypes": ["java.lang.String"], "doc": "\n 根据问题编码查询评估问题（包含选项）\r\n\r\n @param questionCode 问题编码\r\n @return 评估问题\r\n"}], "constructors": []}