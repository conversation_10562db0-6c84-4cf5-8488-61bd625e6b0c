package org.dromara.common.rabbitmq.exception;

/**
 * RabbitMQ 异常
 *
 * <AUTHOR>
 */
public class RabbitMqException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private final String code;

    /**
     * 错误提示
     */
    private final String message;

    public RabbitMqException(String message) {
        this.code = "500";
        this.message = message;
    }

    public RabbitMqException(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public RabbitMqException(String message, Throwable cause) {
        super(message, cause);
        this.code = "500";
        this.message = message;
    }

    public RabbitMqException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public String getCode() {
        return code;
    }
}
