{"doc": "\n 意见反馈控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.app.domain.bo.FeedbackBo", "<PERSON><PERSON><PERSON><PERSON>"], "doc": "\n 查询意见反馈分页列表\r\n"}, {"name": "getMyFeedbackList", "paramTypes": [], "doc": "\n 查询当前用户的反馈列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取意见反馈详细信息\r\n\r\n @param id 反馈主键\r\n"}, {"name": "add", "paramTypes": ["org.dromara.app.domain.bo.FeedbackBo"], "doc": "\n 新增意见反馈\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.app.domain.bo.FeedbackBo"], "doc": "\n 修改意见反馈（仅限用户修改自己的反馈）\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除意见反馈\r\n\r\n @param ids 反馈主键串\r\n"}, {"name": "getStats", "paramTypes": [], "doc": "\n 获取反馈统计信息\r\n"}, {"name": "cancelFeedback", "paramTypes": ["java.lang.Long"], "doc": "\n 撤销反馈（用户可撤销待处理状态的反馈）\r\n\r\n @param id 反馈ID\r\n"}], "constructors": []}