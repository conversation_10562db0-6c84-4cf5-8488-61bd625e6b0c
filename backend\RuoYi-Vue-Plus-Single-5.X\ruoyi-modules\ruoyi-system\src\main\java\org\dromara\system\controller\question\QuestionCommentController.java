package org.dromara.system.controller.question;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.bo.QuestionCommentBo;
import org.dromara.system.domain.vo.QuestionCommentVo;
import org.dromara.system.service.IQuestionCommentService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 题目评论管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/question/comment")
public class QuestionCommentController extends BaseController {

    private final IQuestionCommentService questionCommentService;

    /**
     * 查询题目评论列表
     */
    @SaCheckPermission("system:questionComment:list")
    @GetMapping("/list")
    public TableDataInfo<QuestionCommentVo> list(QuestionCommentBo bo, PageQuery pageQuery) {
        return questionCommentService.queryPageList(bo, pageQuery);
    }

    /**
     * 根据题目ID查询评论列表
     */
    @SaCheckPermission("system:questionComment:list")
    @GetMapping("/question/{questionId}")
    public TableDataInfo<QuestionCommentVo> listByQuestionId(@PathVariable Long questionId, QuestionCommentBo bo, PageQuery pageQuery) {
        return questionCommentService.queryPageListByQuestionId(questionId, bo, pageQuery);
    }

    /**
     * 导出题目评论列表
     */
    @SaCheckPermission("system:questionComment:export")
    @Log(title = "题目评论管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QuestionCommentBo bo, HttpServletResponse response) {
        List<QuestionCommentVo> list = questionCommentService.exportComment(bo);
        ExcelUtil.exportExcel(list, "题目评论数据", QuestionCommentVo.class, response);
    }

    /**
     * 获取题目评论详细信息
     *
     * @param commentId 评论主键
     */
    @SaCheckPermission("system:questionComment:query")
    @GetMapping("/{commentId}")
    public R<QuestionCommentVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long commentId) {
        return R.ok(questionCommentService.queryById(commentId));
    }

    /**
     * 新增题目评论
     */
    @SaCheckPermission("system:questionComment:add")
    @Log(title = "题目评论管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QuestionCommentBo bo) {
        return toAjax(questionCommentService.insertByBo(bo));
    }

    /**
     * 修改题目评论
     */
    @SaCheckPermission("system:questionComment:edit")
    @Log(title = "题目评论管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QuestionCommentBo bo) {
        return toAjax(questionCommentService.updateByBo(bo));
    }

    /**
     * 删除题目评论
     *
     * @param commentIds 评论主键串
     */
    @SaCheckPermission("system:questionComment:remove")
    @Log(title = "题目评论管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{commentIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] commentIds) {
        return toAjax(questionCommentService.deleteWithValidByIds(List.of(commentIds)));
    }

    /**
     * 根据用户ID查询评论列表
     *
     * @param userId 用户ID
     */
    @SaCheckPermission("system:questionComment:list")
    @GetMapping("/user/{userId}")
    public R<List<QuestionCommentVo>> getByUserId(@PathVariable Long userId) {
        return R.ok(questionCommentService.queryByUserId(userId));
    }

    /**
     * 根据父评论ID查询子评论列表
     *
     * @param parentId 父评论ID
     */
    @SaCheckPermission("system:questionComment:list")
    @GetMapping("/parent/{parentId}")
    public R<List<QuestionCommentVo>> getByParentId(@PathVariable Long parentId) {
        return R.ok(questionCommentService.queryByParentId(parentId));
    }

    /**
     * 查询评论树形结构
     *
     * @param questionId 题目ID
     */
    @SaCheckPermission("system:questionComment:list")
    @GetMapping("/tree/{questionId}")
    public R<List<QuestionCommentVo>> getCommentTree(@PathVariable Long questionId) {
        return R.ok(questionCommentService.queryCommentTree(questionId));
    }

    /**
     * 查询热门评论列表
     *
     * @param questionId 题目ID
     * @param limit      限制数量
     */
    @GetMapping("/hot/{questionId}")
    public R<List<QuestionCommentVo>> getHotComments(@PathVariable Long questionId, @RequestParam(defaultValue = "10") Integer limit) {
        return R.ok(questionCommentService.queryHotComments(questionId, limit));
    }

    /**
     * 查询最新评论列表
     *
     * @param questionId 题目ID
     * @param limit      限制数量
     */
    @GetMapping("/latest/{questionId}")
    public R<List<QuestionCommentVo>> getLatestComments(@PathVariable Long questionId, @RequestParam(defaultValue = "10") Integer limit) {
        return R.ok(questionCommentService.queryLatestComments(questionId, limit));
    }

    /**
     * 点赞/取消点赞评论
     *
     * @param commentId 评论ID
     * @param userId    用户ID
     */
    @SaCheckPermission("system:questionComment:edit")
    @Log(title = "题目评论管理", businessType = BusinessType.UPDATE)
    @PutMapping("/like")
    public R<Void> likeComment(@RequestParam Long commentId, @RequestParam Long userId) {
        return toAjax(questionCommentService.likeComment(commentId, userId));
    }

    /**
     * 回复评论
     */
    @SaCheckPermission("system:questionComment:add")
    @Log(title = "题目评论管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/reply")
    public R<Void> replyComment(@Validated(AddGroup.class) @RequestBody QuestionCommentBo bo) {
        return toAjax(questionCommentService.replyComment(bo));
    }

    /**
     * 统计题目下的评论数量
     *
     * @param questionId 题目ID
     */
    @SaCheckPermission("system:questionComment:list")
    @GetMapping("/count/question/{questionId}")
    public R<Integer> countByQuestionId(@PathVariable Long questionId) {
        return R.ok(questionCommentService.countByQuestionId(questionId));
    }

    /**
     * 统计用户的评论数量
     *
     * @param userId 用户ID
     */
    @SaCheckPermission("system:questionComment:list")
    @GetMapping("/count/user/{userId}")
    public R<Integer> countByUserId(@PathVariable Long userId) {
        return R.ok(questionCommentService.countByUserId(userId));
    }

    /**
     * 审核评论
     *
     * @param commentId 评论ID
     * @param status    审核状态
     */
    @SaCheckPermission("system:questionComment:audit")
    @Log(title = "题目评论管理", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    public R<Void> auditComment(@RequestParam Long commentId, @RequestParam String status) {
        return toAjax(questionCommentService.auditComment(commentId, status));
    }

    /**
     * 批量审核评论
     *
     * @param commentIds 评论ID集合
     * @param status     审核状态
     */
    @SaCheckPermission("system:questionComment:audit")
    @Log(title = "题目评论管理", businessType = BusinessType.UPDATE)
    @PutMapping("/batchAudit")
    public R<Void> batchAuditComments(@RequestParam Long[] commentIds, @RequestParam String status) {
        return toAjax(questionCommentService.batchAuditComments(List.of(commentIds), status));
    }
}
