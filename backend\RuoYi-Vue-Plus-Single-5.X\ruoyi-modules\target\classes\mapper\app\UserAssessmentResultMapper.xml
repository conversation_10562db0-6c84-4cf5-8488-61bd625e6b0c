<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.UserAssessmentResultMapper">

    <resultMap type="org.dromara.app.domain.UserAssessmentResult" id="UserAssessmentResultResult">
        <result property="resultId" column="result_id"/>
        <result property="recordId" column="record_id"/>
        <result property="questionId" column="question_id"/>
        <result property="questionType" column="question_type"/>
        <result property="category" column="category"/>
        <result property="selectedOptionId" column="selected_option_id"/>
        <result property="selectedValue" column="selected_value"/>
        <result property="score" column="score"/>
        <result property="answerTime" column="answer_time"/>
        <result property="timeSpent" column="time_spent"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectUserAssessmentResultVo">
        select result_id,
               record_id,
               question_id,
               question_type,
               category,
               selected_option_id,
               selected_value,
               score,
               answer_time,
               time_spent,
               create_by,
               create_time
        from user_assessment_result
    </sql>

    <select id="selectResultsByRecordId" resultMap="UserAssessmentResultResult">
        <include refid="selectUserAssessmentResultVo"/>
        where record_id = #{recordId}
        order by create_time
    </select>

    <select id="selectResultsByRecordIdAndCategory" resultMap="UserAssessmentResultResult">
        <include refid="selectUserAssessmentResultVo"/>
        where record_id = #{recordId} and category = #{category}
        order by create_time
    </select>

    <select id="selectCategoryAverageScoresByRecordId" resultMap="UserAssessmentResultResult">
        select category, avg(score) as score
        from user_assessment_result
        where record_id = #{recordId}
        group by category
    </select>

    <insert id="insertBatch">
        insert into user_assessment_result(record_id, question_id, question_type, category,
        selected_option_id, selected_value, score, answer_time,
        time_spent, create_by, create_time)
        values
        <foreach collection="results" item="result" separator=",">
            (#{result.recordId}, #{result.questionId}, #{result.questionType}, #{result.category},
            #{result.selectedOptionId}, #{result.selectedValue}, #{result.score}, #{result.answerTime},
            #{result.timeSpent}, #{result.createBy}, #{result.createTime})
        </foreach>
    </insert>

    <delete id="deleteByRecordId">
        delete
        from user_assessment_result
        where record_id = #{recordId}
    </delete>

    <select id="selectResultsByUserId" resultMap="UserAssessmentResultResult">
        select r.result_id,
               r.record_id,
               r.question_id,
               r.question_type,
               r.category,
               r.selected_option_id,
               r.selected_value,
               r.score,
               r.answer_time,
               r.time_spent,
               r.create_by,
               r.create_time
        from user_assessment_result r
                 inner join user_assessment_record rec on r.record_id = rec.record_id
        where rec.user_id = #{userId}
        order by r.create_time desc
    </select>

</mapper>
