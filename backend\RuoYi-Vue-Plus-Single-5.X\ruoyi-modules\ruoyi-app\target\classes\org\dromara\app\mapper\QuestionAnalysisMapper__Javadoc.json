{"doc": "\n 问题分析Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-17\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByResultId", "paramTypes": ["java.lang.String"], "doc": "\n 根据结果ID查询问题分析列表\r\n\r\n @param resultId 结果ID\r\n @return 问题分析列表\r\n"}, {"name": "selectByResultIdAndQuestionId", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据结果ID和问题ID查询问题分析\r\n\r\n @param resultId   结果ID\r\n @param questionId 问题ID\r\n @return 问题分析\r\n"}, {"name": "selectByResultIdAndCategory", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据分类查询问题分析列表\r\n\r\n @param resultId 结果ID\r\n @param category 分类\r\n @return 问题分析列表\r\n"}, {"name": "batchInsert", "paramTypes": ["java.util.List"], "doc": "\n 批量插入问题分析\r\n\r\n @param questionAnalyses 问题分析列表\r\n @return 插入数量\r\n"}, {"name": "deleteByResultId", "paramTypes": ["java.lang.String"], "doc": "\n 根据结果ID删除问题分析\r\n\r\n @param resultId 结果ID\r\n @return 删除数量\r\n"}, {"name": "selectAvgScoreByUserIdAndCategory", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 查询用户在某分类问题上的平均分数\r\n\r\n @param userId   用户ID\r\n @param category 分类\r\n @return 平均分数\r\n"}, {"name": "selectAvgTimeSpentByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户答题时间统计\r\n\r\n @param userId 用户ID\r\n @return 平均答题时间\r\n"}, {"name": "selectHighScoreAnalyses", "paramTypes": ["java.lang.String", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 查询高分问题分析（用于学习参考）\r\n\r\n @param category 分类\r\n @param minScore 最低分数\r\n @param limit    限制数量\r\n @return 问题分析列表\r\n"}], "constructors": []}