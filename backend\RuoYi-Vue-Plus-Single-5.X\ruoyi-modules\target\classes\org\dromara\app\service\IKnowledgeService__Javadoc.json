{"doc": " 知识库服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "createKnowledgeBase", "paramTypes": ["org.dromara.app.domain.KnowledgeBase", "java.lang.Long"], "doc": " 创建知识库\n\n @param knowledgeBase 知识库信息\n @param userId        用户ID\n @return 是否成功\n"}, {"name": "updateKnowledgeBase", "paramTypes": ["org.dromara.app.domain.KnowledgeBase", "java.lang.Long"], "doc": " 更新知识库\n\n @param knowledgeBase 知识库信息\n @param userId        用户ID\n @return 是否成功\n"}, {"name": "deleteKnowledgeBase", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 删除知识库\n\n @param knowledgeBaseId 知识库ID\n @param userId          用户ID\n @return 是否成功\n"}, {"name": "getKnowledgeBase", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 获取知识库详情\n\n @param knowledgeBaseId 知识库ID\n @param userId          用户ID\n @return 知识库详情\n"}, {"name": "getUserKnowledgeBases", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": " 获取用户知识库列表\n\n @param userId   用户ID\n @param pageNum  页码\n @param pageSize 每页大小\n @return 知识库分页结果\n"}, {"name": "getPublicKnowledgeBases", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": " 获取公开知识库列表\n\n @param pageNum  页码\n @param pageSize 每页大小\n @return 知识库分页结果\n"}, {"name": "searchKnowledgeBases", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": " 搜索知识库\n\n @param keyword  搜索关键词\n @param userId   用户ID\n @param pageNum  页码\n @param pageSize 每页大小\n @return 搜索结果\n"}, {"name": "uploadDocument", "paramTypes": ["java.lang.Long", "org.springframework.web.multipart.MultipartFile", "java.lang.Long"], "doc": " 上传文档到知识库\n\n @param knowledgeBaseId 知识库ID\n @param file            文件\n @param userId          用户ID\n @return 文档信息\n"}, {"name": "addTextDocument", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.lang.Long"], "doc": " 添加文本文档\n\n @param knowledgeBaseId 知识库ID\n @param title           文档标题\n @param content         文档内容\n @param userId          用户ID\n @return 文档信息\n"}, {"name": "deleteDocument", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 删除文档\n\n @param documentId 文档ID\n @param userId     用户ID\n @return 是否成功\n"}, {"name": "getKnowledgeBaseDocuments", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": " 获取知识库文档列表\n\n @param knowledgeBaseId 知识库ID\n @param userId          用户ID\n @param pageNum         页码\n @param pageSize        每页大小\n @return 文档分页结果\n"}, {"name": "getDocumentDetail", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 获取文档详情\n\n @param documentId 文档ID\n @param userId     用户ID\n @return 文档详情\n"}, {"name": "reprocessDocument", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 重新处理文档（重新分块和向量化）\n\n @param documentId 文档ID\n @param userId     用户ID\n @return 是否成功\n"}, {"name": "searchKnowledge", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.Long"], "doc": " 在知识库中搜索相关内容\n\n @param knowledgeBaseId 知识库ID\n @param query           查询文本\n @param topK            返回的top结果数\n @param userId          用户ID\n @return 搜索结果\n"}, {"name": "searchMultipleKnowledgeBases", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Integer", "java.lang.Long"], "doc": " 在多个知识库中搜索\n\n @param knowledgeBaseIds 知识库ID列表\n @param query            查询文本\n @param topK             返回的top结果数\n @param userId           用户ID\n @return 搜索结果\n"}, {"name": "hybridSearch", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.Double", "java.lang.Long"], "doc": " 混合搜索（向量搜索 + 关键词搜索）\n\n @param knowledgeBaseId 知识库ID\n @param query           查询文本\n @param topK            返回的top结果数\n @param hybridAlpha     混合权重（0-1，0为纯关键词，1为纯向量）\n @param userId          用户ID\n @return 搜索结果\n"}, {"name": "getDocumentChunks", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 获取文档分块列表\n\n @param documentId 文档ID\n @param userId     用户ID\n @return 分块列表\n"}, {"name": "updateChunkContent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Long"], "doc": " 更新分块内容\n\n @param chunkId 分块ID\n @param content 新内容\n @param userId  用户ID\n @return 是否成功\n"}, {"name": "deleteChunk", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 删除分块\n\n @param chunkId 分块ID\n @param userId  用户ID\n @return 是否成功\n"}, {"name": "getKnowledgeBaseStats", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 获取知识库统计信息\n\n @param knowledgeBaseId 知识库ID\n @param userId          用户ID\n @return 统计信息\n"}, {"name": "getUserKnowledgeStats", "paramTypes": ["java.lang.Long"], "doc": " 获取用户知识库统计\n\n @param userId 用户ID\n @return 统计信息\n"}], "constructors": []}