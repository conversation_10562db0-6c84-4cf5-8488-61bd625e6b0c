{"doc": " 支付状态枚举\n\n <AUTHOR>\n", "fields": [{"name": "code", "doc": " 状态码\n"}, {"name": "description", "doc": " 状态描述\n"}], "enumConstants": [{"name": "UNPAID", "doc": " 未支付\n"}, {"name": "PENDING", "doc": " 待支付\n"}, {"name": "PAYING", "doc": " 支付中\n"}, {"name": "PAID", "doc": " 支付成功\n"}, {"name": "FAILED", "doc": " 支付失败\n"}, {"name": "CANCELLED", "doc": " 已取消\n"}, {"name": "EXPIRED", "doc": " 已过期\n"}, {"name": "REFUNDED", "doc": " 已退款\n"}, {"name": "PARTIAL_REFUNDED", "doc": " 部分退款\n"}], "methods": [{"name": "fromCode", "paramTypes": ["java.lang.String"], "doc": " 根据状态码获取枚举\n\n @param code 状态码\n @return PaymentStatus\n"}, {"name": "isFinalStatus", "paramTypes": [], "doc": " 判断是否为终态\n\n @return true-终态 false-非终态\n"}, {"name": "isSuccess", "paramTypes": [], "doc": " 判断是否为成功状态\n\n @return true-成功 false-非成功\n"}], "constructors": []}