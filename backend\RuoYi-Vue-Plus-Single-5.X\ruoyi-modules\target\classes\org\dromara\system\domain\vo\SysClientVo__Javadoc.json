{"doc": " 授权管理视图对象 sys_client\n\n <AUTHOR>\n @date 2023-05-15\n", "fields": [{"name": "id", "doc": " id\n"}, {"name": "clientId", "doc": " 客户端id\n"}, {"name": "client<PERSON>ey", "doc": " 客户端key\n"}, {"name": "clientSecret", "doc": " 客户端秘钥\n"}, {"name": "grantTypeList", "doc": " 授权类型\n"}, {"name": "grantType", "doc": " 授权类型\n"}, {"name": "deviceType", "doc": " 设备类型\n"}, {"name": "activeTimeout", "doc": " token活跃超时时间\n"}, {"name": "timeout", "doc": " token固定超时时间\n"}, {"name": "status", "doc": " 状态（0正常 1停用）\n"}], "enumConstants": [], "methods": [], "constructors": []}