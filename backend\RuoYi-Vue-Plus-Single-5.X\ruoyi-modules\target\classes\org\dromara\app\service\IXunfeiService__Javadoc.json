{"doc": " 讯飞服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "sparkChat", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": " 星火大模型聊天\n\n @param message 消息内容\n @param context 上下文信息\n @return 响应内容\n"}, {"name": "sparkChatStream", "paramTypes": ["java.lang.String", "java.util.Map", "org.dromara.app.service.IXunfeiService.StreamCallback"], "doc": " 星火大模型流式聊天\n\n @param message  消息内容\n @param context  上下文信息\n @param callback 回调处理器\n"}, {"name": "sparkChatAgent", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map", "org.dromara.app.service.IXunfeiService.StreamCallback"], "doc": " 智能体星火大模型流式聊天\n\n @param message   消息内容\n @param agentType 智能体类型\n @param context   上下文信息\n @param callback  回调处理器\n"}, {"name": "sparkChatAgentSync", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map"], "doc": " 智能体星火大模型聊天(非流式)\n\n @param message   消息内容\n @param agentType 智能体类型\n @param context   上下文信息\n @return 响应内容\n"}, {"name": "speechRecognition", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 语音识别\n\n @param audioFile 音频文件\n @return 语音识别结果\n"}, {"name": "speechRecognition", "paramTypes": ["java.lang.String"], "doc": " 通过URL进行语音识别\n\n @param audioUrl 音频URL\n @return 语音识别结果\n"}, {"name": "emotionAnalysis", "paramTypes": ["java.lang.String"], "doc": " 情感分析\n\n @param text 待分析文本\n @return 情感分析结果\n"}, {"name": "voiceEmotionAnalysis", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 语音情感分析\n\n @param audioFile 音频文件\n @return 语音情感分析结果\n"}, {"name": "imageEmotionAnalysis", "paramTypes": ["java.lang.String"], "doc": " 图像情感分析\n\n @param imageData base64编码的图像数据\n @return 图像情感分析结果\n"}, {"name": "generateInterviewSuggestion", "paramTypes": ["java.util.Map", "java.util.Map"], "doc": " 生成面试智能建议\n\n @param context 面试上下文\n @param analysisData 分析数据\n @return 智能建议\n"}, {"name": "textToSpeech", "paramTypes": ["java.lang.String", "org.dromara.app.service.IXunfeiService.VoiceConfig"], "doc": " 文本转语音\n\n @param text        待合成文本\n @param voiceConfig 语音配置\n @return 语音数据\n"}], "constructors": []}