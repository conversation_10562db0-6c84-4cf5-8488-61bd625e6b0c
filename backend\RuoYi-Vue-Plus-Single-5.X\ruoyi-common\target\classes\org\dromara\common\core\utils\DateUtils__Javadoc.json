{"doc": " 时间工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getNowDate", "paramTypes": [], "doc": " 获取当前日期和时间\n\n @return 当前日期和时间的 Date 对象表示\n"}, {"name": "getDate", "paramTypes": [], "doc": " 获取当前日期的字符串表示，格式为YYYY-MM-DD\n\n @return 当前日期的字符串表示\n"}, {"name": "getCurrentDate", "paramTypes": [], "doc": " 获取当前日期的字符串表示，格式为yyyyMMdd\n\n @return 当前日期的字符串表示\n"}, {"name": "datePath", "paramTypes": [], "doc": " 获取当前日期的路径格式字符串，格式为\"yyyy/MM/dd\"\n\n @return 当前日期的路径格式字符串\n"}, {"name": "getTime", "paramTypes": [], "doc": " 获取当前时间的字符串表示，格式为YYYY-MM-DD HH:MM:SS\n\n @return 当前时间的字符串表示\n"}, {"name": "getTimeWithHourMinuteSecond", "paramTypes": [], "doc": " 获取当前时间的字符串表示，格式为 \"HH:MM:SS\"\n\n @return 当前时间的字符串表示，格式为 \"HH:MM:SS\"\n"}, {"name": "dateTimeNow", "paramTypes": [], "doc": " 获取当前日期和时间的字符串表示，格式为YYYYMMDDHHMMSS\n\n @return 当前日期和时间的字符串表示\n"}, {"name": "dateTimeNow", "paramTypes": ["org.dromara.common.core.enums.FormatsType"], "doc": " 获取当前日期和时间的指定格式的字符串表示\n\n @param format 日期时间格式，例如\"YYYY-MM-DD HH:MM:SS\"\n @return 当前日期和时间的字符串表示\n"}, {"name": "formatDate", "paramTypes": ["java.util.Date"], "doc": " 将指定日期格式化为 YYYY-MM-DD 格式的字符串\n\n @param date 要格式化的日期对象\n @return 格式化后的日期字符串\n"}, {"name": "formatDateTime", "paramTypes": ["java.util.Date"], "doc": " 将指定日期格式化为 YYYY-MM-DD HH:MM:SS 格式的字符串\n\n @param date 要格式化的日期对象\n @return 格式化后的日期时间字符串\n"}, {"name": "parseDateToStr", "paramTypes": ["org.dromara.common.core.enums.FormatsType", "java.util.Date"], "doc": " 将指定日期按照指定格式进行格式化\n\n @param format 要使用的日期时间格式，例如\"YYYY-MM-DD HH:MM:SS\"\n @param date   要格式化的日期对象\n @return 格式化后的日期时间字符串\n"}, {"name": "parseDateTime", "paramTypes": ["org.dromara.common.core.enums.FormatsType", "java.lang.String"], "doc": " 将指定格式的日期时间字符串转换为 Date 对象\n\n @param format 要解析的日期时间格式，例如\"YYYY-MM-DD HH:MM:SS\"\n @param ts     要解析的日期时间字符串\n @return 解析后的 Date 对象\n @throws RuntimeException 如果解析过程中发生异常\n"}, {"name": "parseDate", "paramTypes": ["java.lang.Object"], "doc": " 将对象转换为日期对象\n\n @param str 要转换的对象，通常是字符串\n @return 转换后的日期对象，如果转换失败或输入为null，则返回null\n"}, {"name": "getServerStartDate", "paramTypes": [], "doc": " 获取服务器启动时间\n\n @return 服务器启动时间的 Date 对象表示\n"}, {"name": "difference", "paramTypes": ["java.util.Date", "java.util.Date", "java.util.concurrent.TimeUnit"], "doc": " 计算两个时间之间的时间差，并以指定单位返回（绝对值）\n\n @param start 起始时间\n @param end   结束时间\n @param unit  所需返回的时间单位（DAYS、HOURS、MINUTES、SECONDS、MILLISECONDS、MICROSECONDS、NANOSECONDS）\n @return 时间差的绝对值，以指定单位表示\n"}, {"name": "getDatePoor", "paramTypes": ["java.util.Date", "java.util.Date"], "doc": " 计算两个日期之间的时间差，并以天、小时和分钟的格式返回\n\n @param endDate 结束日期\n @param nowDate 当前日期\n @return 表示时间差的字符串，格式为\"天 小时 分钟\"\n"}, {"name": "getTimeDifference", "paramTypes": ["java.util.Date", "java.util.Date"], "doc": " 计算两个时间点的差值（天、小时、分钟、秒），当值为0时不显示该单位\n\n @param endDate 结束时间\n @param nowDate 当前时间\n @return 时间差字符串，格式为 \"x天 x小时 x分钟 x秒\"，若为 0 则不显示\n"}, {"name": "toDate", "paramTypes": ["java.time.LocalDateTime"], "doc": " 将 LocalDateTime 对象转换为 Date 对象\n\n @param temporalAccessor 要转换的 LocalDateTime 对象\n @return 转换后的 Date 对象\n"}, {"name": "toDate", "paramTypes": ["java.time.LocalDate"], "doc": " 将 LocalDate 对象转换为 Date 对象\n\n @param temporalAccessor 要转换的 LocalDate 对象\n @return 转换后的 Date 对象\n"}, {"name": "validateDateRange", "paramTypes": ["java.util.Date", "java.util.Date", "int", "java.util.concurrent.TimeUnit"], "doc": " 校验日期范围\n\n @param startDate 开始日期\n @param endDate   结束日期\n @param maxValue  最大时间跨度的限制值\n @param unit      时间跨度的单位，可选择 \"DAYS\"、\"HOURS\" 或 \"MINUTES\"\n"}], "constructors": []}