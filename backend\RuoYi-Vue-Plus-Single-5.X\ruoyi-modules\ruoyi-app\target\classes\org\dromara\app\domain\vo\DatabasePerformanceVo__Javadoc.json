{"doc": "\n 数据库性能指标视图对象\r\n\r\n <AUTHOR>\r\n @date 2025-01-19\r\n", "fields": [{"name": "databaseVersion", "doc": "\n 数据库版本\r\n"}, {"name": "uptime", "doc": "\n 数据库运行时间（秒）\r\n"}, {"name": "totalQueries", "doc": "\n 总查询数\r\n"}, {"name": "queriesPerSecond", "doc": "\n 每秒查询数（QPS）\r\n"}, {"name": "slowQueryCount", "doc": "\n 慢查询数量\r\n"}, {"name": "slowQueryRate", "doc": "\n 慢查询比例（百分比）\r\n"}, {"name": "averageQueryTime", "doc": "\n 平均查询时间（毫秒）\r\n"}, {"name": "connectionStats", "doc": "\n 连接数统计\r\n"}, {"name": "bufferPoolHitRate", "doc": "\n 缓冲池命中率（百分比）\r\n"}, {"name": "indexUsageRate", "doc": "\n 索引使用率（百分比）\r\n"}, {"name": "tableLockWaits", "doc": "\n 表锁等待次数\r\n"}, {"name": "rowLockWaits", "doc": "\n 行锁等待次数\r\n"}, {"name": "deadlockCount", "doc": "\n 死锁次数\r\n"}, {"name": "tempTableCount", "doc": "\n 临时表创建次数\r\n"}, {"name": "tempTableOnDiskCount", "doc": "\n 磁盘临时表创建次数\r\n"}, {"name": "databaseSize", "doc": "\n 数据库大小（MB）\r\n"}, {"name": "tablespaceUsage", "doc": "\n 表空间使用情况\r\n"}, {"name": "busiestTables", "doc": "\n 最繁忙的表\r\n"}, {"name": "maxConnections", "doc": "\n 最大连接数\r\n"}, {"name": "currentConnections", "doc": "\n 当前连接数\r\n"}, {"name": "connectionUsageRate", "doc": "\n 连接使用率（百分比）\r\n"}, {"name": "memoryUsage", "doc": "\n 内存使用情况\r\n"}, {"name": "diskIOStats", "doc": "\n 磁盘I/O统计\r\n"}, {"name": "performanceStatus", "doc": "\n 性能状态：excellent/good/fair/poor\r\n"}, {"name": "performanceScore", "doc": "\n 性能评分（0-100）\r\n"}, {"name": "optimizationSuggestions", "doc": "\n 优化建议\r\n"}, {"name": "statisticsTime", "doc": "\n 统计时间\r\n"}], "enumConstants": [], "methods": [], "constructors": []}