{"doc": " AI代理对象 app_agent\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 代理ID\n"}, {"name": "name", "doc": " 代理名称\n"}, {"name": "description", "doc": " 代理描述\n"}, {"name": "icon", "doc": " 代理图标\n"}, {"name": "color", "doc": " 代理颜色\n"}, {"name": "agentType", "doc": " 代理类型：general/interviewer/resume_analyzer/skill_assessor/career_advisor/mock_interviewer/learning_guide\n"}, {"name": "systemPrompt", "doc": " 系统提示词\n"}, {"name": "modelConfig", "doc": " 模型配置（JSON格式）\n"}, {"name": "capabilities", "doc": " 代理能力列表（JSON格式）\n"}, {"name": "quickActions", "doc": " 快速操作列表（JSON格式）\n"}, {"name": "enabled", "doc": " 是否启用：0-禁用，1-启用\n"}, {"name": "sortOrder", "doc": " 排序序号\n"}, {"name": "usageCount", "doc": " 使用次数\n"}, {"name": "averageRating", "doc": " 平均评分\n"}, {"name": "extendConfig", "doc": " 代理配置扩展（JSON格式）\n"}, {"name": "capabilityList", "doc": " 能力列表（不存储到数据库，用于返回给前端）\n"}, {"name": "quickActionList", "doc": " 快速操作列表（不存储到数据库，用于返回给前端）\n"}, {"name": "modelConfigObject", "doc": " 模型配置对象（不存储到数据库）\n"}, {"name": "delFlag", "doc": " delFlag\n"}], "enumConstants": [], "methods": [], "constructors": []}