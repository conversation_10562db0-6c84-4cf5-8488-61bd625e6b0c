{"doc": "\n 学习资源Service接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getMajorList", "paramTypes": [], "doc": "\n 获取专业列表\r\n\r\n @return 专业列表\r\n"}, {"name": "getQuestionBankList", "paramTypes": ["org.dromara.app.domain.dto.QuestionBankQueryDto"], "doc": "\n 获取题库列表\r\n\r\n @param queryDto 查询参数\r\n @return 题库列表\r\n"}, {"name": "toggleBookmark", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Bo<PERSON>an"], "doc": "\n 切换题库收藏状态\r\n\r\n @param userId       用户ID\r\n @param bankId       题库ID\r\n @param isBookmarked 是否收藏\r\n @return 收藏状态\r\n"}, {"name": "searchQuestionBanks", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 搜索题库\r\n\r\n @param keyword 关键词\r\n @param majorId 专业ID\r\n @return 题库列表\r\n"}, {"name": "getQuestionBankDetail", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Long"], "doc": "\n 获取题库详情\r\n\r\n @param bankId  题库ID\r\n @param majorId 专业ID\r\n @param userId  用户ID\r\n @return 题库详情\r\n"}, {"name": "getHotQuestionBanks", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取热门题库\r\n\r\n @param limit 限制数量\r\n @return 题库列表\r\n"}, {"name": "getNewQuestionBanks", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取最新题库\r\n\r\n @param limit 限制数量\r\n @return 题库列表\r\n"}, {"name": "getQuestionBankFullDetail", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 获取题库详细信息\r\n\r\n @param bankId 题库ID\r\n @param userId 用户ID\r\n @return 题库详细信息\r\n"}, {"name": "getQuestionsByCategory", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 获取题库分类题目\r\n\r\n @param bankId 题库ID\r\n @return 按分类组织的题目列表\r\n"}, {"name": "getRecommendedQuestions", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取推荐题目\r\n\r\n @param bankId 题库ID\r\n @param limit  限制数量\r\n @return 推荐题目列表\r\n"}, {"name": "toggleQuestionBankBookmark", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 切换题库收藏状态（新版）\r\n\r\n @param userId 用户ID\r\n @param bankId 题库ID\r\n @return 收藏结果\r\n"}, {"name": "getQuestionList", "paramTypes": ["java.lang.String", "org.dromara.app.domain.dto.QuestionQueryDto"], "doc": "\n 获取题库题目列表（支持筛选和搜索）\r\n\r\n @param bankId   题库ID\r\n @param queryDto 查询参数\r\n @return 题目列表\r\n"}, {"name": "getQuestionDetail", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 获取题目详情\r\n\r\n @param questionId 题目ID\r\n @param userId     用户ID\r\n @return 题目详情\r\n"}, {"name": "searchQuestions", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.Bo<PERSON>an", "java.lang.Long"], "doc": "\n 搜索题目\r\n\r\n @param bankId     题库ID\r\n @param keyword    搜索关键词\r\n @param difficulty 难度等级\r\n @param category   分类\r\n @param completed  完成状态\r\n @param userId     用户ID\r\n @return 题目列表\r\n"}, {"name": "toggleQuestionBookmark", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Bo<PERSON>an"], "doc": "\n 切换题目收藏状态\r\n\r\n @param userId       用户ID\r\n @param questionId   题目ID\r\n @param isBookmarked 是否收藏\r\n @return 收藏状态结果\r\n"}, {"name": "getQuestionComments", "paramTypes": ["java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": "\n 获取题目评论列表\r\n\r\n @param questionId     题目ID\r\n @param page           页码\r\n @param pageSize       每页大小\r\n @param orderBy        排序字段\r\n @param orderDirection 排序方向\r\n @return 评论列表\r\n"}, {"name": "createQuestionComment", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.lang.Long"], "doc": "\n 创建题目评论\r\n\r\n @param userId     用户ID\r\n @param questionId 题目ID\r\n @param content    评论内容\r\n @param parentId   父评论ID\r\n @return 创建的评论\r\n"}, {"name": "deleteQuestionComment", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 删除题目评论\r\n\r\n @param userId    用户ID\r\n @param commentId 评论ID\r\n @return 删除结果\r\n"}, {"name": "likeQuestionComment", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 点赞/取消点赞评论\r\n\r\n @param userId    用户ID\r\n @param commentId 评论ID\r\n @return 点赞结果\r\n"}, {"name": "submitPracticeRecord", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": "\n 提交题目练习记录\r\n\r\n @param userId     用户ID\r\n @param questionId 题目ID\r\n @param userAnswer 用户答案\r\n @param timeSpent  用时（秒）\r\n @return 提交结果\r\n"}, {"name": "getQuestionStats", "paramTypes": ["java.lang.String"], "doc": "\n 获取题目统计信息\r\n\r\n @param questionId 题目ID\r\n @return 统计信息\r\n"}, {"name": "getRelatedQuestions", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取相关题目推荐\r\n\r\n @param questionId 题目ID\r\n @param limit      推荐数量限制\r\n @return 相关题目列表\r\n"}, {"name": "reportContent", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 举报题目或评论\r\n\r\n @param userId      用户ID\r\n @param targetId    目标ID\r\n @param targetType  目标类型（question/comment）\r\n @param reason      举报原因\r\n @param description 详细描述\r\n @return 举报结果\r\n"}, {"name": "getLearningStats", "paramTypes": ["java.lang.Long"], "doc": "\n 获取学习统计数据\r\n\r\n @param userId 用户ID\r\n @return 学习统计数据\r\n"}, {"name": "getTodayRecommendation", "paramTypes": ["java.lang.Long"], "doc": "\n 获取今日推荐内容\r\n\r\n @param userId 用户ID\r\n @return 今日推荐内容\r\n"}, {"name": "getResourceCategoryStats", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 获取资源分类统计数据\r\n\r\n @param majorId 专业ID（可选）\r\n @param userId  用户ID（可选）\r\n @return 资源分类统计数据\r\n"}, {"name": "getQuestionStatistics", "paramTypes": ["java.lang.String"], "doc": "\n 获取题库题目统计信息\r\n\r\n @param bankId 题库ID\r\n @return 统计信息\r\n"}, {"name": "getMajorQuestionBankList", "paramTypes": ["org.dromara.app.domain.dto.QuestionBankQueryDto", "java.lang.Long"], "doc": "\n 获取专业下的所有题库列表（增强版 - 支持更多筛选和排序）\r\n\r\n @param queryDto 查询参数\r\n @param userId   用户ID\r\n @return 题库列表\r\n"}, {"name": "getMajorQuestionBankStatistics", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 获取专业题库统计信息\r\n\r\n @param majorId 专业ID\r\n @param userId  用户ID\r\n @return 统计信息\r\n"}, {"name": "getMajorQuestionBankFilterCounts", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 获取专业题库筛选选项计数\r\n\r\n @param majorId 专业ID\r\n @param userId  用户ID\r\n @return 筛选选项计数\r\n"}], "constructors": []}