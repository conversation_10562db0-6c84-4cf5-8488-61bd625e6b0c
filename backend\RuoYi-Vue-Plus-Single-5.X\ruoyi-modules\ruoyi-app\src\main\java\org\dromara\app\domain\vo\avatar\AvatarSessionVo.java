package org.dromara.app.domain.vo.avatar;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数字人会话信息VO
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
@Schema(description = "数字人会话信息")
public class AvatarSessionVo {

    @Schema(description = "会话ID")
    private String sessionId;

    @Schema(description = "形象ID")
    private String avatarId;

    @Schema(description = "场景ID")
    private String sceneId;

    @Schema(description = "声音ID")
    private String vcn;

    @Schema(description = "视频宽度")
    private Integer width;

    @Schema(description = "视频高度")
    private Integer height;

    @Schema(description = "推流地址")
    private String streamUrl;

    @Schema(description = "连接状态")
    private Boolean connected;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "最后活跃时间")
    private LocalDateTime lastActiveTime;

    @Schema(description = "会话状态描述")
    private String statusDesc;
}
