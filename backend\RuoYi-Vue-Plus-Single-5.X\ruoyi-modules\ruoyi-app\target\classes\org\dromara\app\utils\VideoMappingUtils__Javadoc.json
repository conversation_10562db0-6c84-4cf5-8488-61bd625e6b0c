{"doc": "\n 视频映射工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "map", "paramTypes": ["java.lang.Integer"], "doc": "\n Integer转Boolean映射方法\r\n 0 -> false, 1 -> true\r\n\r\n @param value Integer值\r\n @return Boolean值\r\n"}, {"name": "map", "paramTypes": ["java.lang.Bo<PERSON>an"], "doc": "\n Boolean转Integer映射方法\r\n false -> 0, true -> 1\r\n\r\n @param value Boolean值\r\n @return Integer值\r\n"}, {"name": "map", "paramTypes": ["java.lang.String"], "doc": "\n String转List<String>映射方法\r\n 将JSON字符串转换为字符串列表\r\n\r\n @param value JSON字符串\r\n @return 字符串列表\r\n"}, {"name": "map", "paramTypes": ["java.util.List"], "doc": "\n List<String>转String映射方法\r\n 将字符串列表转换为JSON字符串\r\n\r\n @param value 字符串列表\r\n @return JSON字符串\r\n"}], "constructors": []}