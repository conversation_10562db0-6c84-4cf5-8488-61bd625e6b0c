{"doc": " 视频评论视图对象\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 评论ID\n"}, {"name": "videoId", "doc": " 视频ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "userName", "doc": " 用户名\n"}, {"name": "userAvatar", "doc": " 用户头像\n"}, {"name": "parentId", "doc": " 父评论ID\n"}, {"name": "content", "doc": " 评论内容\n"}, {"name": "likeCount", "doc": " 点赞数\n"}, {"name": "isLiked", "doc": " 是否已点赞\n"}, {"name": "replies", "doc": " 回复列表\n"}, {"name": "createTime", "doc": " 发布时间\n"}, {"name": "publishTime", "doc": " 发布时间格式化\n"}], "enumConstants": [], "methods": [], "constructors": []}