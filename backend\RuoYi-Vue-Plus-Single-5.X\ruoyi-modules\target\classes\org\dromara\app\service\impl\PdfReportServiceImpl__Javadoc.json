{"doc": " PDF报告生成服务实现类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "generateReportContent", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": " 生成报告内容\n"}, {"name": "generateCoverPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": " 生成封面页\n"}, {"name": "generateOverviewPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": " 生成总览页\n"}, {"name": "generateCapabilityAssessmentPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": " 生成能力评估页\n"}, {"name": "generateImprovementSuggestionsPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": " 生成改进建议页\n"}, {"name": "generateLearningPathPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": " 生成学习路径页\n"}, {"name": "addInfoRow", "paramTypes": ["com.itextpdf.layout.element.Table", "java.lang.String", "java.lang.String", "com.itextpdf.kernel.font.PdfFont"], "doc": " 添加信息行到表格\n"}, {"name": "addTableHeader", "paramTypes": ["com.itextpdf.layout.element.Table", "java.lang.String", "com.itextpdf.kernel.font.PdfFont"], "doc": " 添加表格头部\n"}, {"name": "addTableCell", "paramTypes": ["com.itextpdf.layout.element.Table", "java.lang.String", "com.itextpdf.kernel.font.PdfFont"], "doc": " 添加表格单元格\n"}, {"name": "addInfoCell", "paramTypes": ["com.itextpdf.layout.element.Table", "java.lang.String", "java.lang.String", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.colors.Color"], "doc": " 添加信息单元格\n"}, {"name": "getPriorityColor", "paramTypes": ["java.lang.String"], "doc": " 获取优先级颜色\n"}, {"name": "loadChineseFont", "paramTypes": [], "doc": " 加载中文字体\n"}, {"name": "loadBoldChineseFont", "paramTypes": [], "doc": " 加载粗体中文字体\n"}, {"name": "addCoverPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": " 添加封面页\n"}, {"name": "addOverviewPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": " 添加总览页\n"}, {"name": "addDimensionAnalysisPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": " 添加维度分析页\n"}, {"name": "addImprovementSuggestionsPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": " 添加改进建议页\n"}, {"name": "addLearningPathPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": " 添加学习路径页\n"}, {"name": "addAppendixPage", "paramTypes": ["com.itextpdf.layout.Document", "org.dromara.app.domain.InterviewReport", "com.itextpdf.kernel.font.PdfFont", "com.itextpdf.kernel.font.PdfFont"], "doc": " 添加附录页\n"}, {"name": "add<PERSON>ageFooter", "paramTypes": ["com.itextpdf.layout.Document", "com.itextpdf.kernel.font.PdfFont"], "doc": " 添加页脚\n"}, {"name": "addTableRow", "paramTypes": ["com.itextpdf.layout.element.Table", "java.lang.String", "java.lang.String", "com.itextpdf.kernel.font.PdfFont"], "doc": " 添加表格行\n"}, {"name": "createHeaderCell", "paramTypes": ["java.lang.String", "com.itextpdf.kernel.font.PdfFont"], "doc": " 创建表头单元格\n"}, {"name": "createTableCell", "paramTypes": ["java.lang.String", "com.itextpdf.kernel.font.PdfFont"], "doc": " 创建表格单元格\n"}, {"name": "createInfoCell", "paramTypes": ["java.lang.String", "java.lang.String", "com.itextpdf.kernel.font.PdfFont"], "doc": " 创建信息单元格\n"}, {"name": "getScoreColor", "paramTypes": ["java.lang.Integer"], "doc": " 根据分数获取颜色\n"}, {"name": "getLevelText", "paramTypes": ["java.lang.String"], "doc": " 获取等级文本\n"}, {"name": "getScoreLevel", "paramTypes": ["java.lang.Integer"], "doc": " 根据分数获取等级\n"}], "constructors": []}