package org.dromara.common.pay.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付方式枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PaymentMethod {

    /**
     * 支付宝支付
     */
    ALIPAY("alipay", "支付宝支付"),

    /**
     * 微信支付
     */
    WECHAT("wechat", "微信支付"),

    /**
     * 银联支付
     */
    UNIONPAY("unionpay", "银联支付"),

    /**
     * 余额支付
     */
    BALANCE("balance", "余额支付");

    /**
     * 支付方式代码
     */
    private final String code;

    /**
     * 支付方式名称
     */
    private final String name;

    /**
     * 根据代码获取支付方式
     *
     * @param code 支付方式代码
     * @return PaymentMethod
     */
    public static PaymentMethod fromCode(String code) {
        for (PaymentMethod method : values()) {
            if (method.getCode().equals(code)) {
                return method;
            }
        }
        throw new IllegalArgumentException("未知的支付方式: " + code);
    }
}
