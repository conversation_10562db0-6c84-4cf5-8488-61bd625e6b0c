{"doc": "\n 支付token清理定时任务\r\n 定期清理过期的支付token，提高系统安全性\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "cleanupExpiredTokens", "paramTypes": [], "doc": "\n 清理过期的支付token\r\n 每小时执行一次，清理过期超过1小时的token\r\n"}, {"name": "cleanupPaidOrderTokens", "paramTypes": [], "doc": "\n 清理已完成订单的token\r\n 每天凌晨2点执行一次，清理已支付订单的token\r\n"}, {"name": "cleanupCancelledOrderTokens", "paramTypes": [], "doc": "\n 清理已取消订单的token\r\n 每天凌晨3点执行一次，清理已取消订单的token\r\n"}], "constructors": []}