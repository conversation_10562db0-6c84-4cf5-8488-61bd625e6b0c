{"doc": " Excel相关处理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "importExcel", "paramTypes": ["java.io.InputStream", "java.lang.Class"], "doc": " 同步导入(适用于小数据量)\n\n @param is 输入流\n @return 转换后集合\n"}, {"name": "importExcel", "paramTypes": ["java.io.InputStream", "java.lang.Class", "boolean"], "doc": " 使用校验监听器 异步导入 同步返回\n\n @param is         输入流\n @param clazz      对象类型\n @param isValidate 是否 Validator 检验 默认为是\n @return 转换后集合\n"}, {"name": "importExcel", "paramTypes": ["java.io.InputStream", "java.lang.Class", "org.dromara.common.excel.core.ExcelListener"], "doc": " 使用自定义监听器 异步导入 自定义返回\n\n @param is       输入流\n @param clazz    对象类型\n @param listener 自定义监听器\n @return 转换后集合\n"}, {"name": "exportExcel", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Class", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出excel\n\n @param list      导出数据集合\n @param sheetName 工作表的名称\n @param clazz     实体类\n @param response  响应体\n"}, {"name": "exportExcel", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Class", "jakarta.servlet.http.HttpServletResponse", "java.util.List"], "doc": " 导出excel\n\n @param list      导出数据集合\n @param sheetName 工作表的名称\n @param clazz     实体类\n @param response  响应体\n @param options   级联下拉选\n"}, {"name": "exportExcel", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Class", "boolean", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出excel\n\n @param list      导出数据集合\n @param sheetName 工作表的名称\n @param clazz     实体类\n @param merge     是否合并单元格\n @param response  响应体\n"}, {"name": "exportExcel", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Class", "boolean", "jakarta.servlet.http.HttpServletResponse", "java.util.List"], "doc": " 导出excel\n\n @param list      导出数据集合\n @param sheetName 工作表的名称\n @param clazz     实体类\n @param merge     是否合并单元格\n @param response  响应体\n @param options   级联下拉选\n"}, {"name": "exportExcel", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Class", "java.io.OutputStream"], "doc": " 导出excel\n\n @param list      导出数据集合\n @param sheetName 工作表的名称\n @param clazz     实体类\n @param os        输出流\n"}, {"name": "exportExcel", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Class", "java.io.OutputStream", "java.util.List"], "doc": " 导出excel\n\n @param list      导出数据集合\n @param sheetName 工作表的名称\n @param clazz     实体类\n @param os        输出流\n @param options   级联下拉选内容\n"}, {"name": "exportExcel", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Class", "boolean", "java.io.OutputStream", "java.util.List"], "doc": " 导出excel\n\n @param list      导出数据集合\n @param sheetName 工作表的名称\n @param clazz     实体类\n @param merge     是否合并单元格\n @param os        输出流\n"}, {"name": "exportTemplate", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.String", "jakarta.servlet.http.HttpServletResponse"], "doc": " 单表多数据模板导出 模板格式为 {.属性}\n\n @param filename     文件名\n @param templatePath 模板路径 resource 目录下的路径包括模板文件名\n                     例如: excel/temp.xlsx\n                     重点: 模板文件必须放置到启动类对应的 resource 目录下\n @param data         模板需要的数据\n @param response     响应体\n"}, {"name": "exportTemplate", "paramTypes": ["java.util.List", "java.lang.String", "java.io.OutputStream"], "doc": " 单表多数据模板导出 模板格式为 {.属性}\n\n @param templatePath 模板路径 resource 目录下的路径包括模板文件名\n                     例如: excel/temp.xlsx\n                     重点: 模板文件必须放置到启动类对应的 resource 目录下\n @param data         模板需要的数据\n @param os           输出流\n"}, {"name": "exportTemplateMultiList", "paramTypes": ["java.util.Map", "java.lang.String", "java.lang.String", "jakarta.servlet.http.HttpServletResponse"], "doc": " 多表多数据模板导出 模板格式为 {key.属性}\n\n @param filename     文件名\n @param templatePath 模板路径 resource 目录下的路径包括模板文件名\n                     例如: excel/temp.xlsx\n                     重点: 模板文件必须放置到启动类对应的 resource 目录下\n @param data         模板需要的数据\n @param response     响应体\n"}, {"name": "exportTemplateMultiSheet", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.String", "jakarta.servlet.http.HttpServletResponse"], "doc": " 多sheet模板导出 模板格式为 {key.属性}\n\n @param filename     文件名\n @param templatePath 模板路径 resource 目录下的路径包括模板文件名\n                     例如: excel/temp.xlsx\n                     重点: 模板文件必须放置到启动类对应的 resource 目录下\n @param data         模板需要的数据\n @param response     响应体\n"}, {"name": "exportTemplateMultiList", "paramTypes": ["java.util.Map", "java.lang.String", "java.io.OutputStream"], "doc": " 多表多数据模板导出 模板格式为 {key.属性}\n\n @param templatePath 模板路径 resource 目录下的路径包括模板文件名\n                     例如: excel/temp.xlsx\n                     重点: 模板文件必须放置到启动类对应的 resource 目录下\n @param data         模板需要的数据\n @param os           输出流\n"}, {"name": "exportTemplateMultiSheet", "paramTypes": ["java.util.List", "java.lang.String", "java.io.OutputStream"], "doc": " 多sheet模板导出 模板格式为 {key.属性}\n\n @param templatePath 模板路径 resource 目录下的路径包括模板文件名\n                     例如: excel/temp.xlsx\n                     重点: 模板文件必须放置到启动类对应的 resource 目录下\n @param data         模板需要的数据\n @param os           输出流\n"}, {"name": "resetResponse", "paramTypes": ["java.lang.String", "jakarta.servlet.http.HttpServletResponse"], "doc": " 重置响应体\n"}, {"name": "convertByExp", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 解析导出值 0=男,1=女,2=未知\n\n @param propertyValue 参数值\n @param converterExp  翻译注解\n @param separator     分隔符\n @return 解析后值\n"}, {"name": "reverseByExp", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 反向解析值 男=0,女=1,未知=2\n\n @param propertyValue 参数值\n @param converterExp  翻译注解\n @param separator     分隔符\n @return 解析后值\n"}, {"name": "encodingFilename", "paramTypes": ["java.lang.String"], "doc": " 编码文件名\n"}], "constructors": []}