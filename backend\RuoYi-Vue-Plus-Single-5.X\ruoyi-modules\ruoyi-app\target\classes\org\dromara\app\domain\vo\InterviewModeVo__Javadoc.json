{"doc": "\n 面试模式视图对象 app_interview_mode\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [{"name": "id", "doc": "\n 模式ID\r\n"}, {"name": "name", "doc": "\n 模式名称\r\n"}, {"name": "description", "doc": "\n 模式描述\r\n"}, {"name": "icon", "doc": "\n 模式图标\r\n"}, {"name": "color", "doc": "\n 模式颜色\r\n"}, {"name": "duration", "doc": "\n 默认时长（分钟）\r\n"}, {"name": "difficulty", "doc": "\n 难度等级（1-5）\r\n"}, {"name": "features", "doc": "\n 模式特性\r\n"}, {"name": "sortOrder", "doc": "\n 排序号\r\n"}, {"name": "status", "doc": "\n 状态（0正常 1停用）\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}], "enumConstants": [], "methods": [], "constructors": []}