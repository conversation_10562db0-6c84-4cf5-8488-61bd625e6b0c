{"doc": " 书籍章节Service接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryChaptersByBookId", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 根据书籍ID查询章节列表\n\n @param bookId 书籍ID\n @param userId 用户ID（用于判断解锁状态和完成状态）\n @return 章节列表\n"}, {"name": "query<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 根据章节ID查询章节内容\n\n @param chapterId 章节ID\n @param userId    用户ID（用于权限校验）\n @return 章节内容\n"}, {"name": "queryChapterByOrder", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Long"], "doc": " 根据书籍ID和章节序号查询章节\n\n @param bookId       书籍ID\n @param chapterOrder 章节序号\n @param userId       用户ID\n @return 章节信息\n"}, {"name": "queryPreviewChapters", "paramTypes": ["java.lang.Long"], "doc": " 查询试读章节列表\n\n @param bookId 书籍ID\n @return 试读章节列表\n"}, {"name": "insertChapter", "paramTypes": ["org.dromara.app.domain.BookChapter"], "doc": " 新增章节\n\n @param chapter 章节信息\n @return 是否成功\n"}, {"name": "updateChapter", "paramTypes": ["org.dromara.app.domain.BookChapter"], "doc": " 修改章节\n\n @param chapter 章节信息\n @return 是否成功\n"}, {"name": "deleteChapter", "paramTypes": ["java.lang.String"], "doc": " 删除章节\n\n @param chapterId 章节ID\n @return 是否成功\n"}, {"name": "deleteChaptersByBookId", "paramTypes": ["java.lang.Long"], "doc": " 根据书籍ID删除所有章节\n\n @param bookId 书籍ID\n @return 是否成功\n"}, {"name": "checkChapterAccess", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 检查用户是否有权限访问章节\n\n @param chapterId 章节ID\n @param userId    用户ID\n @return 是否有权限\n"}, {"name": "updateChapterUnlockStatus", "paramTypes": ["java.lang.String", "java.lang.Bo<PERSON>an"], "doc": " 更新章节解锁状态\n\n @param chapterId  章节ID\n @param isUnlocked 是否解锁\n @return 是否成功\n"}], "constructors": []}