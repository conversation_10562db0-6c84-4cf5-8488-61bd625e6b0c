package org.dromara.common.rabbitmq.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MessageType {

    /**
     * 普通消息
     */
    NORMAL("normal", "普通消息"),

    /**
     * 延迟消息
     */
    DELAY("delay", "延迟消息"),

    /**
     * 广播消息
     */
    BROADCAST("broadcast", "广播消息"),

    /**
     * RPC消息
     */
    RPC("rpc", "RPC消息"),

    /**
     * 事务消息
     */
    TRANSACTION("transaction", "事务消息");

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举对象
     */
    public static MessageType getByCode(String code) {
        for (MessageType type : MessageType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
