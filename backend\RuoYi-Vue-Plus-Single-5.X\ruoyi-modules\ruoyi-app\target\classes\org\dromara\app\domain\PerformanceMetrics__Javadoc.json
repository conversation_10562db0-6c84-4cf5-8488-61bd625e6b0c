{"doc": "\n 性能指标对象 app_performance_metrics\r\n\r\n <AUTHOR>\r\n @date 2025-07-17\r\n", "fields": [{"name": "id", "doc": "\n 性能指标ID\r\n"}, {"name": "resultId", "doc": "\n 结果ID\r\n"}, {"name": "technical", "doc": "\n 技术能力\r\n"}, {"name": "communication", "doc": "\n 沟通能力\r\n"}, {"name": "problemSolving", "doc": "\n 问题解决\r\n"}, {"name": "teamwork", "doc": "\n 团队合作\r\n"}, {"name": "leadership", "doc": "\n 领导力\r\n"}, {"name": "creativity", "doc": "\n 创造力\r\n"}, {"name": "detailedMetrics", "doc": "\n 详细指标（JSON对象）\r\n"}, {"name": "industryAverage", "doc": "\n 行业平均值（JSON对象）\r\n"}, {"name": "skillGaps", "doc": "\n 技能差距（JSON数组）\r\n"}, {"name": "skillStrengths", "doc": "\n 技能优势（JSON数组）\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}], "enumConstants": [], "methods": [], "constructors": []}