{"doc": " 成就视图对象 app_achievement\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 主键ID\n"}, {"name": "achievementCode", "doc": " 成就代码(唯一标识)\n"}, {"name": "achievementName", "doc": " 成就名称\n"}, {"name": "achievementDesc", "doc": " 成就描述\n"}, {"name": "achievementIcon", "doc": " 成就图标URL\n"}, {"name": "achievementType", "doc": " 成就类型\n"}, {"name": "triggerCondition", "doc": " 触发条件(JSON格式)\n"}, {"name": "rewardPoints", "doc": " 奖励积分\n"}, {"name": "isActive", "doc": " 是否激活(0否 1是)\n"}, {"name": "sortOrder", "doc": " 排序\n"}, {"name": "createByName", "doc": " 创建者\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}, {"name": "remark", "doc": " 备注\n"}], "enumConstants": [], "methods": [], "constructors": []}