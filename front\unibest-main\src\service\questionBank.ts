/**
 * 题库相关API服务
 */
import { httpGet, httpPost, httpPut, httpDelete } from '@/utils/http'
import type { ApiResponse } from '@/types/learning'

/**
 * 题库数据类型定义
 */
export interface QuestionBank {
  bankId: number
  bankCode: string
  title: string
  description?: string
  majorId?: number
  majorName?: string
  icon?: string
  color?: string
  difficulty: 1 | 2 | 3 // 1-简单 2-中等 3-困难
  totalQuestions: number
  practiceCount: number
  categories?: string
  categoryList?: string[]
  sort: number
  status: '0' | '1' // 0-正常 1-停用
  remark?: string
  createTime: string
  updateTime: string
  isBookmarked?: boolean
  progress?: number
  completedQuestions?: number
  correctRate?: number
}

export interface QuestionBankQuery {
  bankCode?: string
  title?: string
  majorId?: number
  difficulty?: number
  status?: string
  page?: number
  pageSize?: number
}

export interface QuestionBankCreate {
  bankCode: string
  title: string
  description?: string
  majorId?: number
  icon?: string
  color?: string
  difficulty: 1 | 2 | 3
  categories?: string
  sort?: number
  status?: '0' | '1'
  remark?: string
}

export interface QuestionBankUpdate extends QuestionBankCreate {
  bankId: number
}

export interface PageResult<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

/**
 * 题库API服务类
 */
export const questionBankApi = {
  /**
   * 分页查询题库列表
   * @param params 查询参数
   * @returns 题库分页列表
   */
  getList(params: QuestionBankQuery): Promise<ApiResponse<PageResult<QuestionBank>>> {
    return httpGet<PageResult<QuestionBank>>('/system/question/bank/list', params)
  },

  /**
   * 获取题库详情
   * @param bankId 题库ID
   * @returns 题库详情
   */
  getDetail(bankId: number): Promise<ApiResponse<QuestionBank>> {
    return httpGet<QuestionBank>(`/system/question/bank/${bankId}`)
  },

  /**
   * 新增题库
   * @param data 题库数据
   * @returns 创建结果
   */
  create(data: QuestionBankCreate): Promise<ApiResponse<void>> {
    return httpPost<void>('/system/question/bank', data)
  },

  /**
   * 修改题库
   * @param data 题库数据
   * @returns 修改结果
   */
  update(data: QuestionBankUpdate): Promise<ApiResponse<void>> {
    return httpPut<void>('/system/question/bank', data)
  },

  /**
   * 删除题库
   * @param bankIds 题库ID数组
   * @returns 删除结果
   */
  delete(bankIds: number[]): Promise<ApiResponse<void>> {
    return httpDelete<void>(`/system/question/bank/${bankIds.join(',')}`)
  },

  /**
   * 根据题库编码查询题库
   * @param bankCode 题库编码
   * @returns 题库信息
   */
  getByCode(bankCode: string): Promise<ApiResponse<QuestionBank>> {
    return httpGet<QuestionBank>(`/system/question/bank/code/${bankCode}`)
  },

  /**
   * 根据专业ID查询题库列表
   * @param majorId 专业ID
   * @returns 题库列表
   */
  getByMajorId(majorId: number): Promise<ApiResponse<QuestionBank[]>> {
    return httpGet<QuestionBank[]>(`/system/question/bank/major/${majorId}`)
  },

  /**
   * 查询用户收藏的题库列表
   * @param userId 用户ID
   * @returns 收藏题库列表
   */
  getBookmarkedBanks(userId: number): Promise<ApiResponse<QuestionBank[]>> {
    return httpGet<QuestionBank[]>(`/system/question/bank/bookmarked/${userId}`)
  },

  /**
   * 查询热门题库列表
   * @param limit 限制数量
   * @returns 热门题库列表
   */
  getHotBanks(limit = 10): Promise<ApiResponse<QuestionBank[]>> {
    return httpGet<QuestionBank[]>('/system/question/bank/hot', { limit })
  },

  /**
   * 更新题库练习次数
   * @param bankId 题库ID
   * @returns 更新结果
   */
  updatePracticeCount(bankId: number): Promise<ApiResponse<void>> {
    return httpPut<void>(`/system/question/bank/practice/${bankId}`)
  },

  /**
   * 更新题库题目总数
   * @param bankId 题库ID
   * @returns 更新结果
   */
  updateTotalQuestions(bankId: number): Promise<ApiResponse<void>> {
    return httpPut<void>(`/system/question/bank/total/${bankId}`)
  },

  /**
   * 启用/停用题库
   * @param bankId 题库ID
   * @param status 状态
   * @returns 操作结果
   */
  changeStatus(bankId: number, status: '0' | '1'): Promise<ApiResponse<void>> {
    return httpPut<void>('/system/question/bank/status', null, { bankId, status })
  },

  /**
   * 复制题库
   * @param bankId 源题库ID
   * @param title 新题库标题
   * @returns 复制结果
   */
  copyQuestionBank(bankId: number, title: string): Promise<ApiResponse<void>> {
    return httpPost<void>('/system/question/bank/copy', null, { bankId, title })
  },

  /**
   * 获取题库统计信息
   * @param bankId 题库ID
   * @returns 统计信息
   */
  getStatistics(bankId: number): Promise<ApiResponse<QuestionBank>> {
    return httpGet<QuestionBank>(`/system/question/bank/statistics/${bankId}`)
  },

  /**
   * 导出题库数据
   * @param params 查询参数
   * @returns 导出结果
   */
  exportData(params: QuestionBankQuery): Promise<ApiResponse<void>> {
    return httpPost<void>('/system/question/bank/export', params)
  },

  /**
   * 导入题库数据
   * @param file 文件
   * @returns 导入结果
   */
  importData(file: File): Promise<ApiResponse<string>> {
    const formData = new FormData()
    formData.append('file', file)
    return httpPost<string>('/system/question/bank/importData', formData)
  },

  /**
   * 获取导入模板
   * @returns 模板文件
   */
  getImportTemplate(): Promise<ApiResponse<void>> {
    return httpPost<void>('/system/question/bank/importTemplate')
  },
}
