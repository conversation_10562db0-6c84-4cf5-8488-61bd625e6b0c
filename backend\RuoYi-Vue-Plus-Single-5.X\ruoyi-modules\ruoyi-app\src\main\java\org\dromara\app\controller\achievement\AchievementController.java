package org.dromara.app.controller.achievement;

import cn.dev33.satoken.stp.StpUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.vo.AchievementStatsVo;
import org.dromara.app.domain.vo.AchievementVo;
import org.dromara.app.domain.vo.BadgeVo;
import org.dromara.app.domain.vo.UserAchievementVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.app.service.IAchievementService;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.ratelimiter.annotation.RateLimiter;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 成就系统控制器
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/achievement")
@Tag(name = "成就系统管理", description = "成就与徽章相关接口")
public class AchievementController extends BaseController {

    private final IAchievementService achievementService;

    /**
     * 获取用户所有徽章
     */
    @Operation(summary = "获取用户所有徽章", description = "获取当前用户的所有徽章，包括已解锁和未解锁")
    @RateLimiter(key = "app:achievement:badges", count = 10)
    @GetMapping("/badges")
    public R<List<BadgeVo>> getAllBadges(
        @Parameter(description = "徽章类别") @RequestParam(required = false) String category,
        @Parameter(description = "解锁状态") @RequestParam(required = false) Boolean unlocked,
        @Parameter(description = "稀有度") @RequestParam(required = false) String rarity) {

        String userId = StpUtil.getLoginIdAsString();
        log.info("获取用户徽章, userId: {}, category: {}, unlocked: {}, rarity: {}",
            userId, category, unlocked, rarity);

        List<BadgeVo> badges = achievementService.getUserBadges(userId, category, unlocked, rarity);
        return R.ok("获取成功", badges);
    }

    /**
     * 获取徽章详情
     */
    @Operation(summary = "获取徽章详情", description = "获取指定徽章的详细信息")
    @RateLimiter(key = "app:achievement:badge:detail", count = 10)
    @GetMapping("/badge/detail")
    public R<BadgeVo> getBadgeDetail(
        @Parameter(description = "徽章ID", required = true)
        @NotBlank(message = "徽章ID不能为空")
        @RequestParam String badgeId) {

        String userId = StpUtil.getLoginIdAsString();
        log.info("获取徽章详情, userId: {}, badgeId: {}", userId, badgeId);

        BadgeVo badge = achievementService.getBadgeDetail(userId, badgeId);
        return R.ok("获取成功", badge);
    }

    /**
     * 设置徽章置顶状态
     */
    @Operation(summary = "设置徽章置顶状态", description = "设置指定徽章的置顶状态")
    @Log(title = "设置徽章置顶", businessType = BusinessType.UPDATE)
    @RateLimiter(key = "app:achievement:badge:pin", count = 5)
    @PostMapping("/badge/pin")
    public R<Void> pinBadge(@RequestBody @Valid PinBadgeRequest request) {
        String userId = StpUtil.getLoginIdAsString();
        log.info("设置徽章置顶状态, userId: {}, badgeId: {}, isPinned: {}",
            userId, request.getBadgeId(), request.getIsPinned());

        achievementService.setPinStatus(userId, request.getBadgeId(), request.getIsPinned());

        String message = request.getIsPinned() ? "置顶成功" : "取消置顶成功";
        return R.ok(message);
    }

    /**
     * 获取置顶徽章列表
     */
    @Operation(summary = "获取置顶徽章列表", description = "获取当前用户的所有置顶徽章")
    @RateLimiter(key = "app:achievement:badges:pinned", count = 10)
    @GetMapping("/badges/pinned")
    public R<List<BadgeVo>> getPinnedBadges() {
        String userId = StpUtil.getLoginIdAsString();
        log.info("获取用户置顶徽章, userId: {}", userId);

        List<BadgeVo> badges = achievementService.getPinnedBadges(userId);
        return R.ok("获取成功", badges);
    }

    /**
     * 获取成就统计信息
     */
    @Operation(summary = "获取成就统计信息", description = "获取当前用户的成就统计信息")
    @RateLimiter(key = "app:achievement:stats", count = 10)
    @GetMapping("/stats")
    public R<AchievementStatsVo> getAchievementStats() {
        String userId = StpUtil.getLoginIdAsString();
        log.info("获取用户成就统计信息, userId: {}", userId);

        AchievementStatsVo stats = achievementService.getAchievementStats(userId);
        return R.ok("获取成功", stats);
    }

    /**
     * 获取最近解锁的成就
     */
    @Operation(summary = "获取最近解锁的成就", description = "获取当前用户最近解锁的成就")
    @RateLimiter(key = "app:achievement:recent", count = 10)
    @GetMapping("/recent")
    public R<List<UserAchievementVo>> getRecentAchievements(
        @Parameter(description = "数量限制") @RequestParam(required = false, defaultValue = "5") Integer limit) {

        String userId = StpUtil.getLoginIdAsString();
        log.info("获取用户最近解锁成就, userId: {}, limit: {}", userId, limit);

        List<UserAchievementVo> achievements = achievementService.getRecentAchievements(userId, limit);
        return R.ok("获取成功", achievements);
    }

    /**
     * 获取成就分类列表
     */
    @Operation(summary = "获取成就分类列表", description = "获取系统中所有成就分类")
    @RateLimiter(key = "app:achievement:categories", count = 10)
    @GetMapping("/categories")
    public R<Map<String, String>> getCategories() {
        log.info("获取成就分类列表");

        Map<String, String> categories = achievementService.getCategories();
        return R.ok("获取成功", categories);
    }

    /**
     * 查询用户进行中的成就
     */
    @Operation(summary = "查询用户进行中的成就", description = "获取当前用户正在进行中的成就，包括进度信息")
    @RateLimiter(key = "app:achievement:in-progress", count = 10)
    @GetMapping("/in-progress")
    public R<List<UserAchievementVo>> getInProgressAchievements() {
        String userId = StpUtil.getLoginIdAsString();
        log.info("获取用户进行中成就, userId: {}", userId);

        List<UserAchievementVo> achievements = achievementService.getInProgressAchievements(userId);
        return R.ok("获取成功", achievements);
    }

    /**
     * 获取用户成就详情
     */
    @Operation(summary = "获取用户成就详情", description = "获取指定成就的详细信息，包括用户进度")
    @RateLimiter(key = "app:achievement:detail", count = 10)
    @GetMapping("/detail")
    public R<UserAchievementVo> getAchievementDetail(
        @Parameter(description = "成就ID", required = true)
        @NotBlank(message = "成就ID不能为空")
        @RequestParam String achievementId) {

        String userId = StpUtil.getLoginIdAsString();
        log.info("获取用户成就详情, userId: {}, achievementId: {}", userId, achievementId);

        UserAchievementVo achievement = achievementService.getUserAchievementDetail(userId, achievementId);
        return R.ok("获取成功", achievement);
    }

    /**
     * 手动检查成就进度
     */
    @Operation(summary = "手动检查成就进度", description = "手动触发检查用户成就进度")
    @Log(title = "检查成就进度", businessType = BusinessType.OTHER)
    @RateLimiter(key = "app:achievement:check", count = 2)
    @PostMapping("/check")
    public R<List<AchievementVo>> checkAchievements() {
        String userId = StpUtil.getLoginIdAsString();
        log.info("手动检查用户成就进度, userId: {}", userId);

        List<AchievementVo> newlyUnlocked = achievementService.checkAndUpdateAchievements(userId);
        if (newlyUnlocked.isEmpty()) {
            return R.ok("暂无新解锁成就");
        }
        return R.ok("发现新解锁成就", newlyUnlocked);
    }

    /**
     * 分享成就墙
     */
    @Operation(summary = "分享成就墙", description = "分享用户的成就墙到指定平台")
    @Log(title = "分享成就墙", businessType = BusinessType.OTHER)
    @RateLimiter(key = "app:achievement:share", count = 5)
    @PostMapping("/share")
    public R<Map<String, Object>> shareAchievements(@RequestBody @Valid ShareRequest request) {
        String userId = StpUtil.getLoginIdAsString();
        log.info("分享成就墙, userId: {}, platform: {}", userId, request.getPlatform());

        Map<String, Object> shareResult = achievementService.shareAchievements(userId, request.getPlatform());
        return R.ok("分享成功", shareResult);
    }

    /**
     * 获取推荐成就
     */
    @Operation(summary = "获取推荐成就", description = "获取推荐用户接下来完成的成就")
    @RateLimiter(key = "app:achievement:recommended", count = 10)
    @GetMapping("/recommended")
    public R<List<UserAchievementVo>> getRecommendedAchievements(
        @Parameter(description = "数量限制") @RequestParam(required = false, defaultValue = "5") Integer limit) {

        String userId = StpUtil.getLoginIdAsString();
        log.info("获取推荐成就, userId: {}, limit: {}", userId, limit);

        List<UserAchievementVo> achievements = achievementService.getRecommendedAchievements(userId, limit);
        return R.ok("获取成功", achievements);
    }

    /**
     * 获取成就排行榜
     */
    @Operation(summary = "获取成就排行榜", description = "获取成就排行榜信息")
    @RateLimiter(key = "app:achievement:leaderboard", count = 10)
    @GetMapping("/leaderboard")
    public R<List<AchievementStatsVo.LeaderboardEntry>> getLeaderboard(
        @Parameter(description = "类别") @RequestParam(required = false) String category,
        @Parameter(description = "数量限制") @RequestParam(required = false, defaultValue = "10") Integer limit) {

        log.info("获取成就排行榜, category: {}, limit: {}", category, limit);

        List<AchievementStatsVo.LeaderboardEntry> leaderboard = achievementService.getLeaderboard(category, limit);
        return R.ok("获取成功", leaderboard);
    }

    /**
     * 获取用户排名信息
     */
    @Operation(summary = "获取用户排名信息", description = "获取当前用户在排行榜中的位置")
    @RateLimiter(key = "app:achievement:ranking", count = 10)
    @GetMapping("/ranking")
    public R<AchievementStatsVo.LeaderboardInfo> getUserRanking(
        @Parameter(description = "类别") @RequestParam(required = false) String category) {

        String userId = StpUtil.getLoginIdAsString();
        log.info("获取用户排名信息, userId: {}, category: {}", userId, category);

        AchievementStatsVo.LeaderboardInfo rankingInfo = achievementService.getUserRankingInfo(userId, category);
        return R.ok("获取成功", rankingInfo);
    }

    /**
     * 记录用户行为事件
     */
    @Operation(summary = "记录用户行为事件", description = "记录用户行为事件，用于触发成就检查")
    @Log(title = "记录用户行为事件", businessType = BusinessType.OTHER)
    @RateLimiter(key = "app:achievement:event", count = 20)
    @PostMapping("/event")
    public R<Boolean> recordEvent(@RequestBody @Valid EventRequest request) {
        String userId = StpUtil.getLoginIdAsString();
        log.info("记录用户行为事件, userId: {}, eventType: {}", userId, request.getEventType());

        boolean triggered = achievementService.recordEvent(userId, request.getEventType(),
            request.getEventData(), request.getEventValue(), request.getRelatedId(), request.getRelatedType());

        return R.ok("记录成功", triggered);
    }

    /**
     * 获取用户成就完成度
     */
    @Operation(summary = "获取用户成就完成度", description = "获取用户成就完成度统计信息")
    @RateLimiter(key = "app:achievement:completion", count = 10)
    @GetMapping("/completion")
    public R<Map<String, Object>> getUserAchievementCompletion() {
        String userId = StpUtil.getLoginIdAsString();
        log.info("获取用户成就完成度, userId: {}", userId);

        Map<String, Object> completion = achievementService.getUserAchievementCompletion(userId);
        return R.ok("获取成功", completion);
    }

    /**
     * 获取事件统计信息
     */
    @Operation(summary = "获取事件统计信息", description = "获取用户事件统计信息")
    @RateLimiter(key = "app:achievement:event-stats", count = 10)
    @GetMapping("/event-stats")
    public R<Map<String, Object>> getEventStatistics(
        @Parameter(description = "事件类型") @RequestParam(required = false) String eventType,
        @Parameter(description = "统计天数") @RequestParam(required = false, defaultValue = "30") Integer days) {

        String userId = StpUtil.getLoginIdAsString();
        log.info("获取事件统计信息, userId: {}, eventType: {}, days: {}", userId, eventType, days);

        Map<String, Object> statistics = achievementService.getEventStatistics(userId, eventType, days);
        return R.ok("获取成功", statistics);
    }

    /**
     * 手动解锁成就
     */
    @Operation(summary = "手动解锁成就", description = "手动解锁指定成就（管理员功能）")
    @Log(title = "手动解锁成就", businessType = BusinessType.OTHER)
    @RateLimiter(key = "app:achievement:unlock", count = 5)
    @PostMapping("/unlock")
    public R<Boolean> unlockAchievement(@RequestBody @Valid UnlockRequest request) {
        String userId = StpUtil.getLoginIdAsString();
        log.info("手动解锁成就, userId: {}, achievementId: {}", userId, request.getAchievementId());

        boolean success = achievementService.unlockAchievement(userId, request.getAchievementId(), "manual");
        return R.ok(success ? "解锁成功" : "解锁失败", success);
    }

    /**
     * 重新计算用户进度
     */
    @Operation(summary = "重新计算用户进度", description = "重新计算用户所有成就进度")
    @Log(title = "重新计算用户进度", businessType = BusinessType.OTHER)
    @RateLimiter(key = "app:achievement:recalculate", count = 2)
    @PostMapping("/recalculate")
    public R<Integer> recalculateUserProgress() {
        String userId = StpUtil.getLoginIdAsString();
        log.info("重新计算用户进度, userId: {}", userId);

        int recalculatedCount = achievementService.recalculateUserProgress(userId);
        return R.ok("重新计算完成", recalculatedCount);
    }

    /**
     * 徽章置顶请求
     */
    @lombok.Data
    public static class PinBadgeRequest {
        @NotBlank(message = "徽章ID不能为空")
        private String badgeId;

        @NotNull(message = "置顶状态不能为空")
        private Boolean isPinned;
    }

    /**
     * 分享请求
     */
    @lombok.Data
    public static class ShareRequest {
        @NotBlank(message = "分享平台不能为空")
        private String platform; // wechat, qq, weibo, link
    }

    /**
     * 事件记录请求
     */
    @lombok.Data
    public static class EventRequest {
        @NotBlank(message = "事件类型不能为空")
        private String eventType;

        private Map<String, Object> eventData;

        private Integer eventValue;

        private String relatedId;

        private String relatedType;
    }

    /**
     * 解锁成就请求
     */
    @lombok.Data
    public static class UnlockRequest {
        @NotBlank(message = "成就ID不能为空")
        private String achievementId;

        private String source = "manual";
    }

    // ==================== 新增的成就系统核心接口 ====================

    /**
     * 查询成就列表（分页）
     */
    @Operation(summary = "查询成就列表", description = "分页查询所有成就信息")
    @GetMapping("/list")
    public R<TableDataInfo<AchievementVo>> list(PageQuery pageQuery) {
        TableDataInfo<AchievementVo> result = achievementService.queryPageList(pageQuery);
        return R.ok(result);
    }

    /**
     * 查询激活的成就列表
     */
    @Operation(summary = "查询激活的成就列表", description = "获取所有激活状态的成就")
    @GetMapping("/active")
    public R<List<AchievementVo>> getActiveAchievements() {
        List<AchievementVo> result = achievementService.queryActiveAchievements();
        return R.ok(result);
    }

    /**
     * 根据成就类型查询成就列表
     */
    @Operation(summary = "根据类型查询成就", description = "根据成就类型获取成就列表")
    @GetMapping("/type/{achievementType}")
    public R<List<AchievementVo>> getAchievementsByType(
        @Parameter(description = "成就类型") @PathVariable String achievementType) {
        List<AchievementVo> result = achievementService.queryByAchievementType(achievementType);
        return R.ok(result);
    }

    /**
     * 初始化用户成就进度
     */
    @Operation(summary = "初始化用户成就", description = "为用户初始化所有成就的进度记录")
    @Log(title = "初始化用户成就", businessType = BusinessType.INSERT)
    @PostMapping("/user/init")
    public R<Void> initUserAchievements() {
        String userId = StpUtil.getLoginIdAsString();
        achievementService.initializeUserAchievements(userId);
        return R.ok();
    }

    /**
     * 手动检查用户成就
     */
    @Operation(summary = "手动检查用户成就", description = "手动触发用户成就检查，返回新解锁的成就")
    @Log(title = "手动检查用户成就", businessType = BusinessType.UPDATE)
    @PostMapping("/user/check")
    public R<List<AchievementVo>> checkUserAchievements() {
        String userId = StpUtil.getLoginIdAsString();
        List<AchievementVo> result = achievementService.checkAndUpdateAchievements(userId);
        return R.ok(result);
    }
}
