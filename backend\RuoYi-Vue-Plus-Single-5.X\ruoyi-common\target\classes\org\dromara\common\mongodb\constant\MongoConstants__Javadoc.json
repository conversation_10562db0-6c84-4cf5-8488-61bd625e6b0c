{"doc": " MongoDB常量\n\n <AUTHOR>\n", "fields": [{"name": "DEFAULT_DATABASE", "doc": " 默认数据库名称\n"}, {"name": "ID_FIELD", "doc": " ID字段名\n"}, {"name": "CREATE_TIME_FIELD", "doc": " 创建时间字段名\n"}, {"name": "UPDATE_TIME_FIELD", "doc": " 更新时间字段名\n"}, {"name": "CREATE_BY_FIELD", "doc": " 创建者字段名\n"}, {"name": "UPDATE_BY_FIELD", "doc": " 更新者字段名\n"}, {"name": "VERSION_FIELD", "doc": " 版本字段名\n"}, {"name": "DEL_FLAG_FIELD", "doc": " 删除标志字段名\n"}, {"name": "DEL_FLAG_NORMAL", "doc": " 未删除标志\n"}, {"name": "DEL_FLAG_DELETED", "doc": " 已删除标志\n"}, {"name": "DEFAULT_PAGE_SIZE", "doc": " 默认分页大小\n"}, {"name": "MAX_PAGE_SIZE", "doc": " 最大分页大小\n"}], "enumConstants": [], "methods": [], "constructors": []}