{"doc": "\n 高级RAG服务接口\r\n 提供更智能的检索和增强功能\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "smartRetrieval", "paramTypes": ["java.lang.String", "java.util.List", "org.dromara.app.service.AdvancedRagService.RetrievalOptions"], "doc": "\n 智能检索\r\n 结合多种检索策略，提供最相关的结果\r\n\r\n @param query            查询文本\r\n @param knowledgeBaseIds 知识库ID列表\r\n @param options          检索选项\r\n @return 检索结果\r\n"}, {"name": "hybridRetrieval", "paramTypes": ["java.lang.String", "java.util.List", "double", "double", "int"], "doc": "\n 混合检索\r\n 结合向量检索和关键词检索\r\n\r\n @param query            查询文本\r\n @param knowledgeBaseIds 知识库ID列表\r\n @param vectorWeight     向量检索权重\r\n @param keywordWeight    关键词检索权重\r\n @param topK             返回数量\r\n @return 检索结果\r\n"}, {"name": "rerank", "paramTypes": ["java.lang.String", "java.util.List", "int"], "doc": "\n 重排序检索结果\r\n 使用重排序模型对检索结果进行重新排序\r\n\r\n @param query      查询文本\r\n @param candidates 候选结果\r\n @param topK       返回数量\r\n @return 重排序后的结果\r\n"}, {"name": "expandQuery", "paramTypes": ["java.lang.String", "org.dromara.app.service.AdvancedRagService.QueryExpansionType"], "doc": "\n 查询扩展\r\n 扩展用户查询以提高检索效果\r\n\r\n @param query         原始查询\r\n @param expansionType 扩展类型\r\n @return 扩展后的查询\r\n"}, {"name": "compressContext", "paramTypes": ["java.lang.String", "java.util.List", "int"], "doc": "\n 上下文压缩\r\n 压缩检索到的上下文，保留最重要的信息\r\n\r\n @param query     查询文本\r\n @param contexts  上下文列表\r\n @param maxLength 最大长度\r\n @return 压缩后的上下文\r\n"}, {"name": "generateEnhancedPrompt", "paramTypes": ["java.lang.String", "java.util.List", "java.lang.String"], "doc": "\n 生成增强提示词\r\n 基于检索结果生成优化的提示词\r\n\r\n @param query            用户查询\r\n @param retrievalResults 检索结果\r\n @param promptTemplate   提示词模板\r\n @return 增强后的提示词\r\n"}], "constructors": []}