package org.dromara.common.chat.service;

import org.dromara.common.chat.agent.AgentContext;
import org.dromara.common.chat.agent.AgentType;
import org.dromara.common.chat.agent.factory.AgentFactory;
import org.dromara.common.chat.agent.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 基于LangChain4j的聊天服务
 *
 * <AUTHOR>
 */
@Service
public class LangChain4jChatService {

    @Autowired
    private AgentFactory agentFactory;

    /**
     * 通用聊天
     */
    public String generalChat(AgentContext context, String message) {
        GeneralChatAgent agent = agentFactory.createAgent(AgentType.GENERAL_CHAT, context);
        return agent.chat(message);
    }

    /**
     * 面试对话
     */
    public String conductInterview(AgentContext context, String candidateResponse) {
        InterviewerAgent agent = agentFactory.createAgent(AgentType.INTERVIEWER, context);
        return agent.conductInterview(candidateResponse);
    }

    /**
     * 生成面试问题
     */
    public String generateInterviewQuestions(AgentContext context, String position, int count) {
        InterviewerAgent agent = agentFactory.createAgent(AgentType.INTERVIEWER, context);
        return agent.generateQuestions(position, count);
    }

    /**
     * 评估面试回答
     */
    public String evaluateInterviewAnswer(AgentContext context, String question, String answer) {
        InterviewerAgent agent = agentFactory.createAgent(AgentType.INTERVIEWER, context);
        return agent.evaluateAnswer(question, answer);
    }

    /**
     * 分析简历
     */
    public String analyzeResume(AgentContext context, String resumeContent) {
        ResumeAnalyzerAgent agent = agentFactory.createAgent(AgentType.RESUME_ANALYZER, context);
        return agent.analyzeResume(resumeContent);
    }

    /**
     * 简历与职位匹配
     */
    public String matchResumeWithJob(AgentContext context, String resume, String jobRequirements) {
        ResumeAnalyzerAgent agent = agentFactory.createAgent(AgentType.RESUME_ANALYZER, context);
        return agent.matchWithJob(resume, jobRequirements);
    }

    /**
     * 提取简历关键信息
     */
    public String extractResumeKeyInfo(AgentContext context, String resume) {
        ResumeAnalyzerAgent agent = agentFactory.createAgent(AgentType.RESUME_ANALYZER, context);
        return agent.extractKeyInfo(resume);
    }

    /**
     * 优化简历
     */
    public String optimizeResumeForPosition(AgentContext context, String resume, String targetPosition) {
        ResumeAnalyzerAgent agent = agentFactory.createAgent(AgentType.RESUME_ANALYZER, context);
        return agent.optimizeForPosition(resume, targetPosition);
    }

    /**
     * 技能评估
     */
    public String assessSkill(AgentContext context, String skillArea, String candidateAnswer) {
        SkillAssessorAgent agent = agentFactory.createAgent(AgentType.SKILL_ASSESSOR, context);
        return agent.assessSkill(skillArea, candidateAnswer);
    }

    /**
     * 生成技能测试
     */
    public String generateSkillTest(AgentContext context, String skillArea, String level, int count) {
        SkillAssessorAgent agent = agentFactory.createAgent(AgentType.SKILL_ASSESSOR, context);
        return agent.generateSkillTest(skillArea, level, count);
    }

    /**
     * 评估技能答案
     */
    public String evaluateSkillAnswer(AgentContext context, String skill, String question, String answer) {
        SkillAssessorAgent agent = agentFactory.createAgent(AgentType.SKILL_ASSESSOR, context);
        return agent.evaluateSkillAnswer(skill, question, answer);
    }

    /**
     * 生成技能报告
     */
    public String generateSkillReport(AgentContext context, String assessmentData) {
        SkillAssessorAgent agent = agentFactory.createAgent(AgentType.SKILL_ASSESSOR, context);
        return agent.generateSkillReport(assessmentData);
    }

    /**
     * 创建学习路径
     */
    public String createLearningPath(AgentContext context, String targetSkill, String currentLevel, String timeFrame) {
        SkillAssessorAgent agent = agentFactory.createAgent(AgentType.SKILL_ASSESSOR, context);
        return agent.createLearningPath(targetSkill, currentLevel, timeFrame);
    }

    /**
     * 职业建议
     */
    public String provideCareerAdvice(AgentContext context, String careerQuery) {
        CareerAdvisorAgent agent = agentFactory.createAgent(AgentType.CAREER_ADVISOR, context);
        return agent.provideCareerAdvice(careerQuery);
    }

    /**
     * 制定职业规划
     */
    public String createCareerPlan(AgentContext context, String background, String goals, String timeFrame) {
        CareerAdvisorAgent agent = agentFactory.createAgent(AgentType.CAREER_ADVISOR, context);
        return agent.createCareerPlan(background, goals, timeFrame);
    }

    /**
     * 分析行业趋势
     */
    public String analyzeIndustryTrends(AgentContext context, String industry) {
        CareerAdvisorAgent agent = agentFactory.createAgent(AgentType.CAREER_ADVISOR, context);
        return agent.analyzeIndustryTrends(industry);
    }

    /**
     * 职业转换指导
     */
    public String guideCareerTransition(AgentContext context, String currentCareer, String targetCareer, String personalSituation) {
        CareerAdvisorAgent agent = agentFactory.createAgent(AgentType.CAREER_ADVISOR, context);
        return agent.guideCareerTransition(currentCareer, targetCareer, personalSituation);
    }

    /**
     * 薪资谈判建议
     */
    public String provideSalaryNegotiationAdvice(AgentContext context, String position, String experience, String location) {
        CareerAdvisorAgent agent = agentFactory.createAgent(AgentType.CAREER_ADVISOR, context);
        return agent.provideSalaryNegotiationAdvice(position, experience, location);
    }

    /**
     * 模拟面试
     */
    public String conductMockInterview(AgentContext context, String candidateResponse) {
        MockInterviewerAgent agent = agentFactory.createAgent(AgentType.MOCK_INTERVIEWER, context);
        return agent.conductMockInterview(candidateResponse);
    }

    /**
     * 设计面试流程
     */
    public String designInterviewFlow(AgentContext context, String position, String duration, String focus) {
        MockInterviewerAgent agent = agentFactory.createAgent(AgentType.MOCK_INTERVIEWER, context);
        return agent.designInterviewFlow(position, duration, focus);
    }

    /**
     * 压力面试
     */
    public String conductStressInterview(AgentContext context, String scenario, String response) {
        MockInterviewerAgent agent = agentFactory.createAgent(AgentType.MOCK_INTERVIEWER, context);
        return agent.conductStressInterview(scenario, response);
    }

    /**
     * 面试反馈
     */
    public String provideMockInterviewFeedback(AgentContext context, String interviewRecord) {
        MockInterviewerAgent agent = agentFactory.createAgent(AgentType.MOCK_INTERVIEWER, context);
        return agent.provideFeedback(interviewRecord);
    }

    /**
     * 问题类型准备
     */
    public String prepareQuestionType(AgentContext context, String questionType, String question) {
        MockInterviewerAgent agent = agentFactory.createAgent(AgentType.MOCK_INTERVIEWER, context);
        return agent.prepareQuestionType(questionType, question);
    }

    /**
     * 学习指导
     */
    public String provideLearningGuidance(AgentContext context, String learningQuery) {
        LearningGuideAgent agent = agentFactory.createAgent(AgentType.LEARNING_GUIDE, context);
        return agent.provideLearningGuidance(learningQuery);
    }

    /**
     * 制定学习计划
     */
    public String createLearningPlan(AgentContext context, String goal, String currentLevel, String timeAvailable, String preference) {
        LearningGuideAgent agent = agentFactory.createAgent(AgentType.LEARNING_GUIDE, context);
        return agent.createLearningPlan(goal, currentLevel, timeAvailable, preference);
    }

    /**
     * 推荐学习资源
     */
    public String recommendLearningResources(AgentContext context, String topic, String level, String resourceType) {
        LearningGuideAgent agent = agentFactory.createAgent(AgentType.LEARNING_GUIDE, context);
        return agent.recommendResources(topic, level, resourceType);
    }

    /**
     * 分析学习进度
     */
    public String analyzeLearningProgress(AgentContext context, String learningRecord, String challenges) {
        LearningGuideAgent agent = agentFactory.createAgent(AgentType.LEARNING_GUIDE, context);
        return agent.analyzeProgress(learningRecord, challenges);
    }

    /**
     * 提供学习方法
     */
    public String provideLearningMethods(AgentContext context, String content, String difficulties, String environment) {
        LearningGuideAgent agent = agentFactory.createAgent(AgentType.LEARNING_GUIDE, context);
        return agent.provideLearningMethods(content, difficulties, environment);
    }

    /**
     * 构建知识图谱
     */
    public String buildKnowledgeMap(AgentContext context, String domain, String knownConcepts) {
        LearningGuideAgent agent = agentFactory.createAgent(AgentType.LEARNING_GUIDE, context);
        return agent.buildKnowledgeMap(domain, knownConcepts);
    }

    /**
     * 清理会话缓存
     */
    public void clearSessionCache(String sessionId) {
        agentFactory.clearAgentCache(sessionId);
    }
}
