<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Annotation profile for RuoYi-Vue-Plus" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$MAVEN_REPOSITORY$/com/github/therapi/therapi-runtime-javadoc-scribe/0.15.0/therapi-runtime-javadoc-scribe-0.15.0.jar" />
          <entry name="$MAVEN_REPOSITORY$/com/github/therapi/therapi-runtime-javadoc/0.15.0/therapi-runtime-javadoc-0.15.0.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/springframework/boot/spring-boot-configuration-processor/3.4.6/spring-boot-configuration-processor-3.4.6.jar" />
          <entry name="$MAVEN_REPOSITORY$/io/github/linpeilie/mapstruct-plus-processor/1.4.8/mapstruct-plus-processor-1.4.8.jar" />
          <entry name="$MAVEN_REPOSITORY$/io/github/linpeilie/mapstruct-plus/1.4.8/mapstruct-plus-1.4.8.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
          <entry name="$MAVEN_REPOSITORY$/io/github/linpeilie/mapstruct-plus-object-convert/1.4.8/mapstruct-plus-object-convert-1.4.8.jar" />
          <entry name="$MAVEN_REPOSITORY$/cn/easii/tutelary-repackage-javapoet/1.0.5/tutelary-repackage-javapoet-1.0.5.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/mapstruct/tools/gem/gem-api/1.0.0.Alpha3/gem-api-1.0.0.Alpha3.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/projectlombok/lombok-mapstruct-binding/0.2.0/lombok-mapstruct-binding-0.2.0.jar" />
        </processorPath>
        <module name="ruoyi-common-job" />
        <module name="ruoyi-system" />
        <module name="ruoyi-common-redis" />
        <module name="ruoyi-common-tenant" />
        <module name="ruoyi-common-doc" />
        <module name="ruoyi-common-satoken" />
        <module name="ruoyi-common-security" />
        <module name="ruoyi-common-rabbitmq" />
        <module name="ruoyi-common-oss" />
        <module name="ruoyi-common-websocket" />
        <module name="ruoyi-common-encrypt" />
        <module name="ruoyi-common-sensitive" />
        <module name="ruoyi-common-excel" />
        <module name="ruoyi-common-sse" />
        <module name="ruoyi-common-mongodb" />
        <module name="ruoyi-common-log" />
        <module name="ruoyi-admin" />
        <module name="ruoyi-common-es" />
        <module name="ruoyi-common-mybatis" />
        <module name="ruoyi-monitor-admin" />
        <module name="ruoyi-common-sms" />
        <module name="ruoyi-common-json" />
        <module name="ruoyi-common-mail" />
        <module name="ruoyi-common-core" />
        <module name="ruoyi-common-translation" />
        <module name="ruoyi-common-web" />
        <module name="ruoyi-common-ratelimiter" />
        <module name="ruoyi-common-idempotent" />
        <module name="ruoyi-common-chat" />
        <module name="ruoyi-common-pay" />
        <module name="ruoyi-common-social" />
        <module name="ruoyi-snailjob-server" />
        <module name="ruoyi-app" />
        <module name="ruoyi-common-caffeine" />
      </profile>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="ruoyi-mcp-server" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="ruoyi-admin" options="-parameters" />
      <module name="ruoyi-app" options="-parameters" />
      <module name="ruoyi-common" options="-parameters" />
      <module name="ruoyi-common-caffeine" options="-parameters" />
      <module name="ruoyi-common-chat" options="-parameters" />
      <module name="ruoyi-common-core" options="-parameters" />
      <module name="ruoyi-common-doc" options="-parameters" />
      <module name="ruoyi-common-encrypt" options="-parameters" />
      <module name="ruoyi-common-es" options="-parameters" />
      <module name="ruoyi-common-excel" options="-parameters" />
      <module name="ruoyi-common-idempotent" options="-parameters" />
      <module name="ruoyi-common-job" options="-parameters" />
      <module name="ruoyi-common-json" options="-parameters" />
      <module name="ruoyi-common-log" options="-parameters" />
      <module name="ruoyi-common-mail" options="-parameters" />
      <module name="ruoyi-common-mongodb" options="-parameters" />
      <module name="ruoyi-common-mybatis" options="-parameters" />
      <module name="ruoyi-common-oss" options="-parameters" />
      <module name="ruoyi-common-pay" options="-parameters" />
      <module name="ruoyi-common-rabbitmq" options="-parameters" />
      <module name="ruoyi-common-ratelimiter" options="-parameters" />
      <module name="ruoyi-common-redis" options="-parameters" />
      <module name="ruoyi-common-satoken" options="-parameters" />
      <module name="ruoyi-common-security" options="-parameters" />
      <module name="ruoyi-common-sensitive" options="-parameters" />
      <module name="ruoyi-common-sms" options="-parameters" />
      <module name="ruoyi-common-social" options="-parameters" />
      <module name="ruoyi-common-sse" options="-parameters" />
      <module name="ruoyi-common-tenant" options="-parameters" />
      <module name="ruoyi-common-translation" options="-parameters" />
      <module name="ruoyi-common-web" options="-parameters" />
      <module name="ruoyi-common-websocket" options="-parameters" />
      <module name="ruoyi-extend" options="-parameters" />
      <module name="ruoyi-mcp-server" options="-parameters" />
      <module name="ruoyi-modules" options="-parameters" />
      <module name="ruoyi-monitor-admin" options="-parameters" />
      <module name="ruoyi-snailjob-server" options="-parameters" />
      <module name="ruoyi-system" options="-parameters" />
      <module name="ruoyi-vue-plus" options="-parameters" />
    </option>
  </component>
</project>