{"doc": " 用户成就视图对象 app_user_achievement\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 主键ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "achievementId", "doc": " 成就ID\n"}, {"name": "achievementCode", "doc": " 成就代码\n"}, {"name": "achievementName", "doc": " 成就名称\n"}, {"name": "achievementDesc", "doc": " 成就描述\n"}, {"name": "achievementIcon", "doc": " 成就图标URL\n"}, {"name": "achievementType", "doc": " 成就类型\n"}, {"name": "rewardPoints", "doc": " 奖励积分\n"}, {"name": "unlockTime", "doc": " 解锁时间\n"}, {"name": "progress", "doc": " 进度百分比(0-100)\n"}, {"name": "currentValue", "doc": " 当前数值\n"}, {"name": "targetValue", "doc": " 目标数值\n"}, {"name": "isCompleted", "doc": " 是否完成(0否 1是)\n"}, {"name": "isNotified", "doc": " 是否已通知(0否 1是)\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}, {"name": "createByName", "doc": " 创建者名称\n"}, {"name": "updateByName", "doc": " 更新者名称\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "completionStatusDesc", "doc": " 成就完成状态描述\n"}, {"name": "progressDesc", "doc": " 进度描述\n"}, {"name": "achievementTypeDesc", "doc": " 成就类型描述\n"}, {"name": "remainingValue", "doc": " 距离完成还需要的数值\n"}, {"name": "estimatedCompletionTime", "doc": " 预计完成时间（基于当前进度）\n"}, {"name": "difficultyLevel", "doc": " 成就难度等级\n"}, {"name": "isRecommended", "doc": " 是否为推荐成就\n"}, {"name": "sortOrder", "doc": " 成就排序\n"}, {"name": "rarity", "doc": " 获取成就稀有度（基于完成用户数量，需要外部设置）\n"}], "enumConstants": [], "methods": [{"name": "getCompletionStatusDesc", "paramTypes": [], "doc": " 获取完成状态描述\n"}, {"name": "getProgressDesc", "paramTypes": [], "doc": " 获取进度描述\n"}, {"name": "getAchievementTypeDesc", "paramTypes": [], "doc": " 获取成就类型描述\n"}, {"name": "getRemainingValue", "paramTypes": [], "doc": " 获取距离完成还需要的数值\n"}, {"name": "isNearCompletion", "paramTypes": [], "doc": " 判断是否接近完成（进度超过80%）\n"}, {"name": "isCompleted", "paramTypes": [], "doc": " 判断是否已完成\n"}, {"name": "isNotified", "paramTypes": [], "doc": " 判断是否已通知\n"}, {"name": "getCompletionDays", "paramTypes": [], "doc": " 获取成就完成天数（如果已完成）\n"}], "constructors": []}