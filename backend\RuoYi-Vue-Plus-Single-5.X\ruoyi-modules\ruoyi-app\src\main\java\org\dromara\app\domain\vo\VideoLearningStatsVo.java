package org.dromara.app.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 视频学习统计视图对象
 *
 * <AUTHOR>
 */
@Data
public class VideoLearningStatsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 总视频数
     */
    private Integer totalVideos;

    /**
     * 已完成视频数
     */
    private Integer completedVideos;

    /**
     * 总学习时长
     */
    private BigDecimal totalHours;

    /**
     * 今日学习时长
     */
    private BigDecimal studiedToday;

    /**
     * 周学习目标
     */
    private BigDecimal weeklyGoal;

    /**
     * 周学习进度
     */
    private BigDecimal weeklyProgress;

    /**
     * 分类统计
     */
    private List<CategoryStatsVo> categoryStats;

    /**
     * 分类统计视图对象
     */
    @Data
    public static class CategoryStatsVo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 分类名称
         */
        private String category;

        /**
         * 分类中文名
         */
        private String categoryName;

        /**
         * 视频数量
         */
        private Integer count;

        /**
         * 完成数量
         */
        private Integer completed;

        /**
         * 完成率
         */
        private BigDecimal completionRate;
    }
}
