package org.dromara.common.chat.agent.service;

import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;

/**
 * 技能评估Agent
 *
 * <AUTHOR>
 */
public interface SkillAssessorAgent {

    @SystemMessage({
        "你是一位专业的技能评估专家，具有深厚的技术背景和评估经验。",
        "你的任务是：",
        "1. 设计针对性的技能测试题目",
        "2. 评估候选人的技术能力水平",
        "3. 生成详细的技能评估报告",
        "4. 提供技能提升的学习建议",
        "请确保评估标准客观、准确，建议具有实操性。"
    })
    String assessSkill(String skillArea, String candidateAnswer);

    @SystemMessage({
        "作为技能评估专家，请根据指定的技术领域生成合适的测试题目。",
        "题目应该覆盖基础知识、实际应用、问题解决等方面。",
        "难度要适中，能够准确评估候选人的真实水平。"
    })
    String generateSkillTest(@UserMessage("技术领域：{skillArea}，难度等级：{level}，题目数量：{count}") String skillArea, String level, int count);

    @SystemMessage({
        "作为技能评估专家，请对候选人的技术答案进行详细评估。",
        "评估维度包括：技术准确性、深度理解、实践经验、解决方案等。",
        "请给出具体的评分和改进建议。"
    })
    String evaluateSkillAnswer(@UserMessage("技能领域：{skill}\n问题：{question}\n候选人答案：{answer}") String skill, String question, String answer);

    @SystemMessage({
        "作为技能评估专家，请生成个人技能评估报告。",
        "报告应包括：技能水平分析、优势与不足、学习建议、职业发展方向等。",
        "请提供具体、可操作的建议。"
    })
    String generateSkillReport(@UserMessage("评估数据：{assessmentData}") String assessmentData);

    @SystemMessage({
        "作为技能评估专家，请为指定技能制定学习路径规划。",
        "包括：学习目标、学习阶段、推荐资源、实践项目等。",
        "请确保路径具有可操作性和渐进性。"
    })
    String createLearningPath(@UserMessage("目标技能：{targetSkill}，当前水平：{currentLevel}，学习时间：{timeFrame}") String targetSkill, String currentLevel, String timeFrame);
}
