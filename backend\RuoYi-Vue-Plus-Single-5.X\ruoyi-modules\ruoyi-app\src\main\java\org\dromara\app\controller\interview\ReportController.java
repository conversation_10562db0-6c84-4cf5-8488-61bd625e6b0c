package org.dromara.app.controller.interview;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.service.IReportGenerationService;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;

import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 面试报告控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/report")
public class ReportController extends BaseController {

    private final IReportGenerationService reportGenerationService;

    /**
     * 生成面试报告
     *
     * @param sessionId 面试会话ID
     * @return 面试报告
     */
    @Log(title = "生成面试报告", businessType = BusinessType.INSERT)
    @PostMapping("/generate/{sessionId}")
    public R<IReportGenerationService.InterviewReportData> generateReport(@PathVariable String sessionId) {
        try {
            IReportGenerationService.InterviewReportData report = reportGenerationService.generateInterviewReport(sessionId);
            return R.ok(report);
        } catch (Exception e) {
            log.error("生成面试报告失败", e);
            return R.fail("生成面试报告失败: " + e.getMessage());
        }
    }

    /**
     * 生成PDF报告
     *
     * @param sessionId 面试会话ID
     * @return PDF文件路径
     */
    @Log(title = "生成PDF报告", businessType = BusinessType.INSERT)
    @PostMapping("/pdf/{sessionId}")
    public R<String> generatePdfReport(@PathVariable String sessionId) {
        try {
            String pdfPath = reportGenerationService.generatePdfReport(sessionId);
            return R.ok(pdfPath);
        } catch (Exception e) {
            log.error("生成PDF报告失败", e);
            return R.fail("生成PDF报告失败: " + e.getMessage());
        }
    }

    /**
     * 下载PDF报告
     *
     * @param sessionId 面试会话ID
     * @param response HTTP响应
     */
    @GetMapping("/download/{sessionId}")
    public void downloadPdfReport(@PathVariable String sessionId, HttpServletResponse response) {
        try {
            // 生成PDF报告
            String pdfPath = reportGenerationService.generatePdfReport(sessionId);

            // 读取PDF文件
            byte[] pdfBytes = Files.readAllBytes(Paths.get(pdfPath));

            // 设置响应头
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition",
                "attachment; filename=\"interview_report_" + sessionId + ".pdf\"");
            response.setContentLength(pdfBytes.length);

            // 写入响应
            response.getOutputStream().write(pdfBytes);
            response.getOutputStream().flush();

        } catch (Exception e) {
            log.error("下载PDF报告失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取雷达图数据
     *
     * @param sessionId 面试会话ID
     * @return 雷达图数据
     */
    @GetMapping("/radar/{sessionId}")
    public R<IReportGenerationService.RadarChartData> getRadarChartData(@PathVariable String sessionId) {
        try {
            // 先生成报告获取数据
            IReportGenerationService.InterviewReportData reportData = reportGenerationService.generateInterviewReport(sessionId);
            return R.ok(reportData.getRadarChartData());
        } catch (Exception e) {
            log.error("获取雷达图数据失败", e);
            return R.fail("获取雷达图数据失败: " + e.getMessage());
        }
    }
}
