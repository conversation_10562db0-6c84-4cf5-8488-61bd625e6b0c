{"doc": " SSE 控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "connect", "paramTypes": [], "doc": " 建立 SSE 连接\n"}, {"name": "close", "paramTypes": [], "doc": " 关闭 SSE 连接\n"}, {"name": "send", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 向特定用户发送消息\n\n @param userId 目标用户的 ID\n @param msg    要发送的消息内容\n"}, {"name": "send", "paramTypes": ["java.lang.String"], "doc": " 向所有用户发送消息\n\n @param msg 要发送的消息内容\n"}, {"name": "destroy", "paramTypes": [], "doc": " 清理资源。此方法目前不执行任何操作，但避免因未实现而导致错误\n"}], "constructors": []}