{"doc": " 题目评论视图对象\n\n <AUTHOR>\n", "fields": [{"name": "commentId", "doc": " 评论ID\n"}, {"name": "questionId", "doc": " 题目ID\n"}, {"name": "questionTitle", "doc": " 题目标题（关联查询）\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "userName", "doc": " 用户昵称（关联查询）\n"}, {"name": "userAvatar", "doc": " 用户头像（关联查询）\n"}, {"name": "parentId", "doc": " 父评论ID（回复时使用）\n"}, {"name": "content", "doc": " 评论内容\n"}, {"name": "likeCount", "doc": " 点赞数\n"}, {"name": "replyCount", "doc": " 回复数\n"}, {"name": "status", "doc": " 状态（0正常 1删除）\n"}, {"name": "sort", "doc": " 排序\n"}, {"name": "ip<PERSON><PERSON><PERSON>", "doc": " IP地址\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}, {"name": "children", "doc": " 子评论列表（非数据库字段）\n"}, {"name": "isLiked", "doc": " 是否点赞（非数据库字段）\n"}, {"name": "level", "doc": " 评论层级（非数据库字段）\n"}], "enumConstants": [], "methods": [], "constructors": []}