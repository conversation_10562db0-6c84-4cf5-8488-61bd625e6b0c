{"doc": "\n 面试结果Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-17\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectBySessionId", "paramTypes": ["java.lang.String"], "doc": "\n 根据会话ID查询面试结果\r\n\r\n @param sessionId 会话ID\r\n @return 面试结果\r\n"}, {"name": "selectByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 根据用户ID查询面试结果列表\r\n\r\n @param userId 用户ID\r\n @param limit  限制数量\r\n @return 面试结果列表\r\n"}, {"name": "selectByUserIdAndStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 根据用户ID和状态查询面试结果列表\r\n\r\n @param userId 用户ID\r\n @param status 状态\r\n @return 面试结果列表\r\n"}, {"name": "selectStatsByJobId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据岗位ID查询面试结果统计\r\n\r\n @param jobId 岗位ID\r\n @return 统计数据\r\n"}, {"name": "selectRecentByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询用户最近的面试结果\r\n\r\n @param userId 用户ID\r\n @param days   天数\r\n @return 面试结果列表\r\n"}, {"name": "selectByScoreRange", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": "\n 根据分数范围查询面试结果\r\n\r\n @param minScore 最低分数\r\n @param maxScore 最高分数\r\n @return 面试结果列表\r\n"}, {"name": "selectAvgScoreByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户平均分数\r\n\r\n @param userId 用户ID\r\n @return 平均分数\r\n"}, {"name": "selectCountByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户面试次数\r\n\r\n @param userId 用户ID\r\n @return 面试次数\r\n"}, {"name": "selectHistoryPageWithJobInfo", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.Long", "java.lang.String", "java.lang.String"], "doc": "\n 分页查询用户面试历史记录（包含岗位和分类信息）\r\n\r\n @param page     分页参数\r\n @param userId   用户ID\r\n @param category 分类名称（可选）\r\n @param status   状态（可选）\r\n @return 面试结果列表\r\n"}], "constructors": []}