{"doc": "\n 面试结果对象 app_interview_result\r\n\r\n <AUTHOR>\r\n @date 2025-07-17\r\n", "fields": [{"name": "id", "doc": "\n 结果ID\r\n"}, {"name": "sessionId", "doc": "\n 会话ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "jobId", "doc": "\n 岗位ID\r\n"}, {"name": "job<PERSON>ame", "doc": "\n 岗位名称\r\n"}, {"name": "company", "doc": "\n 公司名称\r\n"}, {"name": "mode", "doc": "\n 面试模式\r\n"}, {"name": "date", "doc": "\n 面试日期\r\n"}, {"name": "duration", "doc": "\n 面试时长\r\n"}, {"name": "totalScore", "doc": "\n 总分\r\n"}, {"name": "rank", "doc": "\n 等级（excellent,good,average,poor）\r\n"}, {"name": "rankText", "doc": "\n 等级文本\r\n"}, {"name": "percentile", "doc": "\n 百分位数\r\n"}, {"name": "answeredQuestions", "doc": "\n 已回答问题数\r\n"}, {"name": "totalQuestions", "doc": "\n 总问题数\r\n"}, {"name": "status", "doc": "\n 状态（completed,partial,cancelled）\r\n"}, {"name": "topStrengths", "doc": "\n 主要优势（JSON数组）\r\n"}, {"name": "topWeaknesses", "doc": "\n 主要劣势（JSON数组）\r\n"}, {"name": "overallFeedback", "doc": "\n 总体反馈\r\n"}, {"name": "delFlag", "doc": "\n 删除标志（0代表存在 1代表删除）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}