package org.dromara.common.chat.sse;

import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.model.chat.response.ChatResponse;
import dev.langchain4j.model.chat.response.StreamingChatResponseHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.boot.configurationprocessor.json.JSONObject;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * SSE事件处理器 - 处理流式AI响应
 *
 * <AUTHOR>
 */
@Slf4j
public class SseEventHandler implements StreamingChatResponseHandler {

    private final SseEmitter emitter;
    private final String sessionId;
    private final Long userId;
    private final String agentType;
    private final AtomicBoolean completed = new AtomicBoolean(false);
    private final AtomicReference<String> fullResponse = new AtomicReference<>("");

    public SseEventHandler(SseEmitter emitter, String sessionId, Long userId, String agentType) {
        this.emitter = emitter;
        this.sessionId = sessionId;
        this.userId = userId;
        this.agentType = agentType;
    }

    @Override
    public void onPartialResponse(String token) {
        if (completed.get()) {
            return;
        }

        try {
            // 构建SSE消息
            JSONObject sseMessage = new JSONObject();
            sseMessage.put("type", "token");
            sseMessage.put("sessionId", sessionId);
            sseMessage.put("userId", userId);
            sseMessage.put("agentType", agentType);
            sseMessage.put("token", token);
            sseMessage.put("timestamp", System.currentTimeMillis());

            // 发送SSE事件
            SseEmitter.SseEventBuilder event = SseEmitter.event()
                .name("message")
                .data(sseMessage.toString());

            emitter.send(event);

            // 累积完整响应
            fullResponse.updateAndGet(current -> current + token);

            log.debug("发送SSE token: {}, 会话: {}", token, sessionId);

        } catch (IOException | JSONException e) {
            log.error("发送SSE消息失败, 会话: {}, 错误: {}", sessionId, e.getMessage());
            handleError(e);
        }
    }

    @Override
    public void onCompleteResponse(ChatResponse chatResponse) {
        if (completed.getAndSet(true)) {
            return;
        }

        try {
            AiMessage aiMessage = chatResponse.aiMessage();

            // 构建完成消息
            JSONObject completeMessage = new JSONObject();
            completeMessage.put("type", "complete");
            completeMessage.put("sessionId", sessionId);
            completeMessage.put("userId", userId);
            completeMessage.put("agentType", agentType);
            completeMessage.put("fullMessage", fullResponse.get());
            completeMessage.put("finishReason", chatResponse.finishReason());
            completeMessage.put("tokenUsage", chatResponse.tokenUsage());
            completeMessage.put("timestamp", System.currentTimeMillis());
            completeMessage.put("completedAt", LocalDateTime.now());

            // 发送完成事件
            SseEmitter.SseEventBuilder event = SseEmitter.event()
                .name("complete")
                .data(completeMessage.toString());

            emitter.send(event);

            log.info("AI响应完成, 会话: {}, 响应长度: {}", sessionId, fullResponse.get().length());

            // 完成SSE连接
            emitter.complete();

        } catch (IOException | JSONException e) {
            log.error("发送SSE完成消息失败, 会话: {}, 错误: {}", sessionId, e.getMessage());
            handleError(e);
        }
    }

    @Override
    public void onError(Throwable error) {
        if (completed.getAndSet(true)) {
            return;
        }

        log.error("AI响应流式处理出错, 会话: {}, 错误: {}", sessionId, error.getMessage(), error);
        handleError(error);
    }

    /**
     * 处理错误
     */
    private void handleError(Throwable error) {
        try {
            // 构建错误消息
            JSONObject errorMessage = new JSONObject();
            errorMessage.put("type", "error");
            errorMessage.put("sessionId", sessionId);
            errorMessage.put("userId", userId);
            errorMessage.put("agentType", agentType);
            errorMessage.put("error", error.getMessage());
            errorMessage.put("timestamp", System.currentTimeMillis());

            // 发送错误事件
            SseEmitter.SseEventBuilder event = SseEmitter.event()
                .name("error")
                .data(errorMessage.toString());

            emitter.send(event);

        } catch (IOException e) {
            log.error("发送SSE错误消息失败, 会话: {}", sessionId, e);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        } finally {
            // 完成或关闭SSE连接
            emitter.completeWithError(error);
        }
    }

    /**
     * 发送开始事件
     */
    public void sendStartEvent() {
        try {
            JSONObject startMessage = new JSONObject();
            startMessage.put("type", "start");
            startMessage.put("sessionId", sessionId);
            startMessage.put("userId", userId);
            startMessage.put("agentType", agentType);
            startMessage.put("timestamp", System.currentTimeMillis());
            startMessage.put("startedAt", LocalDateTime.now());

            SseEmitter.SseEventBuilder event = SseEmitter.event()
                .name("start")
                .data(startMessage.toString());

            emitter.send(event);

            log.info("AI响应开始, 会话: {}", sessionId);

        } catch (IOException | JSONException e) {
            log.error("发送SSE开始消息失败, 会话: {}", sessionId, e);
            handleError(e);
        }
    }

}
