{"doc": "\n 活动类型枚举\r\n\r\n <AUTHOR>\r\n @date 2025-07-16\r\n", "fields": [{"name": "code", "doc": "\n 活动类型代码\r\n"}, {"name": "description", "doc": "\n 活动类型描述\r\n"}], "enumConstants": [{"name": "COURSE", "doc": "\n 课程学习\r\n"}, {"name": "INTERVIEW", "doc": "\n 面试练习\r\n"}, {"name": "BOOK", "doc": "\n 书籍阅读\r\n"}, {"name": "VIDEO", "doc": "\n 视频学习\r\n"}, {"name": "EXERCISE", "doc": "\n 习题练习\r\n"}, {"name": "DOCUMENT", "doc": "\n 文档阅读\r\n"}, {"name": "OTHER", "doc": "\n 其他活动\r\n"}], "methods": [{"name": "fromCode", "paramTypes": ["java.lang.String"], "doc": "\n 根据代码获取活动类型\r\n\r\n @param code 活动类型代码\r\n @return 活动类型枚举\r\n"}, {"name": "isValidCode", "paramTypes": ["java.lang.String"], "doc": "\n 检查代码是否有效\r\n\r\n @param code 活动类型代码\r\n @return 是否有效\r\n"}], "constructors": []}