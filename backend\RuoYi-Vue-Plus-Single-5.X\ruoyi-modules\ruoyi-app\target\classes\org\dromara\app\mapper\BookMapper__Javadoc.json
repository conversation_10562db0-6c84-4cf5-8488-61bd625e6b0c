{"doc": "\n 面试书籍Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectBookPageWithUserInfo", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.String", "java.lang.String", "java.lang.Long"], "doc": "\n 分页查询书籍列表（支持分类筛选和搜索）\r\n\r\n @param page        分页参数\r\n @param category    分类筛选\r\n @param searchQuery 搜索关键词\r\n @param userId      用户ID（用于查询购买状态和阅读进度）\r\n @return 书籍列表\r\n"}, {"name": "selectBookByIdWithUserInfo", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 根据ID查询书籍详情（包含用户阅读信息）\r\n\r\n @param id     书籍ID\r\n @param userId 用户ID\r\n @return 书籍详情\r\n"}, {"name": "incrementReadCount", "paramTypes": ["java.lang.Long"], "doc": "\n 增加书籍阅读次数\r\n\r\n @param bookId 书籍ID\r\n @return 影响行数\r\n"}, {"name": "selectHotBooks", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询热门书籍列表\r\n\r\n @param limit 限制数量\r\n @return 热门书籍列表\r\n"}, {"name": "selectRecommendedBooks", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询推荐书籍列表（基于用户阅读历史）\r\n\r\n @param userId 用户ID\r\n @param limit  限制数量\r\n @return 推荐书籍列表\r\n"}, {"name": "selectCategoryStats", "paramTypes": [], "doc": "\n 根据分类查询书籍数量统计\r\n\r\n @return 分类统计信息\r\n"}], "constructors": []}