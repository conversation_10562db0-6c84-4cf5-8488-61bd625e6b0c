package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__162;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.UserResumeBoToUserResumeMapper;
import org.dromara.app.domain.vo.UserResumeVo;
import org.dromara.app.domain.vo.UserResumeVoToUserResumeMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__162.class,
    uses = {UserResumeVoToUserResumeMapper.class,UserResumeBoToUserResumeMapper.class},
    imports = {}
)
public interface UserResumeToUserResumeVoMapper extends BaseMapper<UserResume, UserResumeVo> {
}
