{"doc": "\n 成就业务对象 app_achievement\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 主键ID\r\n"}, {"name": "achievementCode", "doc": "\n 成就代码(唯一标识)\r\n"}, {"name": "achievementName", "doc": "\n 成就名称\r\n"}, {"name": "achievementDesc", "doc": "\n 成就描述\r\n"}, {"name": "achievementIcon", "doc": "\n 成就图标URL\r\n"}, {"name": "achievementType", "doc": "\n 成就类型\r\n"}, {"name": "triggerCondition", "doc": "\n 触发条件(JSON格式)\r\n"}, {"name": "rewardPoints", "doc": "\n 奖励积分\r\n"}, {"name": "isActive", "doc": "\n 是否激活(0否 1是)\r\n"}, {"name": "sortOrder", "doc": "\n 排序\r\n"}], "enumConstants": [], "methods": [], "constructors": []}