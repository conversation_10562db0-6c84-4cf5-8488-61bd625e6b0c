package org.dromara.app.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.service.IXunfeiService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 讯飞AI测试控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/app/xunfei/test")
@RequiredArgsConstructor
@Tag(name = "讯飞AI测试", description = "讯飞AI服务测试接口")
public class XunfeiTestController {

    private final IXunfeiService xunfeiService;

    /**
     * 测试星火大模型聊天（非流式）
     */
    @PostMapping("/chat")
    @Operation(summary = "测试星火大模型聊天", description = "测试讯飞星火大模型非流式聊天功能")
    public Map<String, Object> testChat(
            @Parameter(description = "用户消息") @RequestParam String message,
            @Parameter(description = "系统提示") @RequestParam(required = false) String systemPrompt) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("测试星火大模型聊天，消息: {}", message);
            
            // 构建上下文
            Map<String, Object> context = new HashMap<>();
            if (systemPrompt != null && !systemPrompt.trim().isEmpty()) {
                context.put("systemPrompt", systemPrompt);
            }
            context.put("userId", "test_user");
            
            // 调用服务
            String response = xunfeiService.sparkChat(message, context);
            
            result.put("success", true);
            result.put("message", "调用成功");
            result.put("data", Map.of(
                "userMessage", message,
                "aiResponse", response,
                "timestamp", System.currentTimeMillis()
            ));
            
        } catch (Exception e) {
            log.error("测试星火大模型聊天失败", e);
            result.put("success", false);
            result.put("message", "调用失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试星火大模型流式聊天
     */
    @GetMapping(value = "/chat/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "测试星火大模型流式聊天", description = "测试讯飞星火大模型流式聊天功能")
    public SseEmitter testChatStream(
            @Parameter(description = "用户消息") @RequestParam String message,
            @Parameter(description = "系统提示") @RequestParam(required = false) String systemPrompt) {
        
        SseEmitter emitter = new SseEmitter(60000L); // 60秒超时
        
        CompletableFuture.runAsync(() -> {
            try {
                log.info("测试星火大模型流式聊天，消息: {}", message);
                
                // 构建上下文
                Map<String, Object> context = new HashMap<>();
                if (systemPrompt != null && !systemPrompt.trim().isEmpty()) {
                    context.put("systemPrompt", systemPrompt);
                }
                context.put("userId", "test_user");
                
                // 发送开始事件
                emitter.send(SseEmitter.event()
                    .name("start")
                    .data(Map.of("message", "开始生成回复...")));
                
                // 调用流式服务
                xunfeiService.sparkChatStream(message, context, new IXunfeiService.StreamCallback() {
                    @Override
                    public void onToken(String token) {
                        try {
                            emitter.send(SseEmitter.event()
                                .name("token")
                                .data(Map.of("token", token)));
                        } catch (IOException e) {
                            log.error("发送流式数据失败", e);
                        }
                    }

                    @Override
                    public void onComplete(String fullResponse) {
                        try {
                            emitter.send(SseEmitter.event()
                                .name("complete")
                                .data(Map.of(
                                    "fullResponse", fullResponse,
                                    "timestamp", System.currentTimeMillis()
                                )));
                            emitter.complete();
                        } catch (IOException e) {
                            log.error("发送完成事件失败", e);
                            emitter.completeWithError(e);
                        }
                    }

                    @Override
                    public void onError(Throwable error) {
                        try {
                            emitter.send(SseEmitter.event()
                                .name("error")
                                .data(Map.of("error", error.getMessage())));
                        } catch (IOException e) {
                            log.error("发送错误事件失败", e);
                        }
                        emitter.completeWithError(error);
                    }
                });
                
            } catch (Exception e) {
                log.error("流式聊天测试失败", e);
                emitter.completeWithError(e);
            }
        });
        
        return emitter;
    }

    /**
     * 测试配置信息
     */
    @GetMapping("/config")
    @Operation(summary = "查看配置信息", description = "查看当前讯飞配置信息（脱敏）")
    public Map<String, Object> getConfig() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 这里只返回非敏感配置信息
            result.put("success", true);
            result.put("message", "配置信息获取成功");
            result.put("data", Map.of(
                "baseUrl", "已配置",
                "model", "已配置", 
                "temperature", "已配置",
                "maxTokens", "已配置",
                "apiPasswordConfigured", "检查中...",
                "timestamp", System.currentTimeMillis()
            ));
            
        } catch (Exception e) {
            log.error("获取配置信息失败", e);
            result.put("success", false);
            result.put("message", "获取配置失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查讯飞服务是否正常")
    public Map<String, Object> healthCheck() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 简单的健康检查
            String testMessage = "你好";
            Map<String, Object> context = new HashMap<>();
            context.put("userId", "health_check");
            
            String response = xunfeiService.sparkChat(testMessage, context);
            
            result.put("success", true);
            result.put("message", "服务正常");
            result.put("data", Map.of(
                "status", "healthy",
                "testResponse", response != null && !response.isEmpty(),
                "timestamp", System.currentTimeMillis()
            ));
            
        } catch (Exception e) {
            log.error("健康检查失败", e);
            result.put("success", false);
            result.put("message", "服务异常: " + e.getMessage());
            result.put("data", Map.of(
                "status", "unhealthy",
                "error", e.getClass().getSimpleName(),
                "timestamp", System.currentTimeMillis()
            ));
        }
        
        return result;
    }
}
