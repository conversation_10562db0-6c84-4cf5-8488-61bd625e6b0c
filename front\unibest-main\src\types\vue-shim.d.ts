// Vue类型定义补充
declare module 'vue' {
  export const ref: any
  export const reactive: any
  export const computed: any
  export const watch: any
  export const onMounted: any
  export const onUnmounted: any
  export const nextTick: any
  export const provide: any
  export const inject: any
  export const toRefs: any
  export const toRef: any
  export const defineComponent: any
  export const defineAsyncComponent: any
  export const h: any
  export const createApp: any
  export const getCurrentInstance: any
  export const useAttrs: any
  export const useSlots: any
  export const markRaw: any
  export const isRef: any
  export const isReactive: any
  export const isReadonly: any
  export const isProxy: any
  export const unref: any
  export const toRaw: any
  export const readonly: any
  export const shallowRef: any
  export const shallowReactive: any
  export const shallowReadonly: any
  export const triggerRef: any
  export const customRef: any
  export const watchEffect: any
  export const watchPostEffect: any
  export const watchSyncEffect: any
  export const onBeforeMount: any
  export const onBeforeUnmount: any
  export const onBeforeUpdate: any
  export const onUpdated: any
  export const onErrorCaptured: any
  export const onRenderTracked: any
  export const onRenderTriggered: any
  export const onActivated: any
  export const onDeactivated: any
  export const onServerPrefetch: any
  export const effectScope: any
  export const getCurrentScope: any
  export const onScopeDispose: any
  export type UnwrapRef<T> = any
}
