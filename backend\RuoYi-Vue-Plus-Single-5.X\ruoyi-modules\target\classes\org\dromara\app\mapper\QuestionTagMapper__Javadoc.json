{"doc": " 问题标签Mapper接口\n\n <AUTHOR> Assistant\n @date 2025-07-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByCategory", "paramTypes": ["java.lang.String"], "doc": " 根据分类查询标签\n\n @param category 标签分类\n @return 标签列表\n"}, {"name": "selectHotTags", "paramTypes": ["java.lang.Integer"], "doc": " 查询热门标签\n\n @param limit 限制数量\n @return 热门标签列表\n"}, {"name": "incrementUsageCount", "paramTypes": ["java.lang.String"], "doc": " 增加标签使用次数\n\n @param tagName 标签名称\n @return 影响行数\n"}, {"name": "selectByNames", "paramTypes": ["java.util.List"], "doc": " 根据名称查询标签\n\n @param names 标签名称列表\n @return 标签列表\n"}], "constructors": []}