{"doc": "\n 面试问题管理控制器\r\n\r\n <AUTHOR> Assistant\r\n @date 2025-07-19\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.String"], "doc": "\n 分页查询问题列表\r\n"}, {"name": "getQuestionsByDomain", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": "\n 根据技术领域查询问题\r\n"}, {"name": "getMultimodalQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 获取多模态问题\r\n"}, {"name": "getGradedQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 根据难度分级获取问题\r\n"}, {"name": "getQuestionsByTags", "paramTypes": ["java.util.List", "java.lang.Integer"], "doc": "\n 根据标签查询问题\r\n"}, {"name": "getTags", "paramTypes": ["java.lang.String"], "doc": "\n 获取问题标签\r\n"}, {"name": "getHotTags", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取热门标签\r\n"}, {"name": "getTagsByCategory", "paramTypes": [], "doc": "\n 获取所有分类的标签\r\n"}, {"name": "getDifficultySuggestion", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取问题难度分级建议\r\n"}, {"name": "getQuestionTypeStats", "paramTypes": ["java.lang.String"], "doc": "\n 获取问题类型统计\r\n"}, {"name": "calculateDifficultyDistribution", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 计算难度分布建议\r\n"}, {"name": "getQuestionTypeStatistics", "paramTypes": ["java.lang.String"], "doc": "\n 获取问题类型统计\r\n"}], "constructors": []}