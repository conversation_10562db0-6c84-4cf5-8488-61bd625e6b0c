package org.dromara.system.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.mybatis.core.domain.QuestionBank;

import java.io.Serial;
import java.io.Serializable;

/**
 * 题库业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = QuestionBank.class, reverseConvertGenerate = false)
public class QuestionBankBo extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 题库ID
     */
    @NotNull(message = "题库ID不能为空", groups = { EditGroup.class })
    private Long bankId;

    /**
     * 题库编码
     */
    @NotBlank(message = "题库编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bankCode;

    /**
     * 题库标题
     */
    @NotBlank(message = "题库标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String title;

    /**
     * 题库描述
     */
    private String description;

    /**
     * 专业ID
     */
    private Long majorId;

    /**
     * 题库图标
     */
    private String icon;

    /**
     * 题库颜色
     */
    private String color;

    /**
     * 难度（1-简单 2-中等 3-困难）
     */
    private Integer difficulty;

    /**
     * 题目总数
     */
    private Integer totalQuestions;

    /**
     * 练习次数
     */
    private Integer practiceCount;

    /**
     * 分类标签（JSON格式）
     */
    private String categories;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;
}
