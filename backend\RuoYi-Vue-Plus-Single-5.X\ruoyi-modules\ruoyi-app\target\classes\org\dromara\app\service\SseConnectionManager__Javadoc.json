{"doc": "\n SSE连接管理器\r\n 负责管理SSE连接的生命周期，防止连接泄露\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "DEFAULT_TIMEOUT", "doc": "\n 默认连接超时时间（毫秒）\r\n"}, {"name": "MAX_CONNECTIONS", "doc": "\n 最大连接数\r\n"}, {"name": "CLEANUP_INTERVAL_MINUTES", "doc": "\n 清理任务执行间隔（分钟）\r\n"}, {"name": "connections", "doc": "\n 连接缓存，存储连接ID到连接包装器的映射\r\n"}, {"name": "scheduler", "doc": "\n 定时清理任务执行器\r\n"}], "enumConstants": [], "methods": [{"name": "createConnection", "paramTypes": ["java.lang.String"], "doc": "\n 创建SSE连接\r\n\r\n @param connectionId 连接ID\r\n @return SSE发射器\r\n"}, {"name": "createConnection", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 创建SSE连接\r\n\r\n @param connectionId 连接ID\r\n @param timeout      超时时间（毫秒）\r\n @return SSE发射器\r\n"}, {"name": "getConnection", "paramTypes": ["java.lang.String"], "doc": "\n 获取SSE连接\r\n\r\n @param connectionId 连接ID\r\n @return SSE发射器，如果不存在则返回null\r\n"}, {"name": "removeConnection", "paramTypes": ["java.lang.String"], "doc": "\n 移除SSE连接\r\n\r\n @param connectionId 连接ID\r\n"}, {"name": "sendData", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 向指定连接发送数据\r\n\r\n @param connectionId 连接ID\r\n @param data         数据\r\n @return 是否发送成功\r\n"}, {"name": "cleanExpiredConnections", "paramTypes": [], "doc": "\n 清理过期连接\r\n"}, {"name": "cleanOldestConnection", "paramTypes": [], "doc": "\n 清理最旧的连接（当连接数超限时）\r\n"}, {"name": "getStats", "paramTypes": [], "doc": "\n 获取连接统计信息\r\n\r\n @return 统计信息\r\n"}], "constructors": []}