package org.dromara.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.common.mybatis.core.domain.QuestionComment;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T11:27:37+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.11 (Amazon.com Inc.)"
)
@Component
public class QuestionCommentBoToQuestionCommentMapperImpl implements QuestionCommentBoToQuestionCommentMapper {

    @Override
    public QuestionComment convert(QuestionCommentBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        QuestionComment questionComment = new QuestionComment();

        questionComment.setSearchValue( arg0.getSearchValue() );
        questionComment.setCreateDept( arg0.getCreateDept() );
        questionComment.setCreateBy( arg0.getCreateBy() );
        questionComment.setCreateTime( arg0.getCreateTime() );
        questionComment.setUpdateBy( arg0.getUpdateBy() );
        questionComment.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            questionComment.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        questionComment.setCommentId( arg0.getCommentId() );
        questionComment.setQuestionId( arg0.getQuestionId() );
        questionComment.setUserId( arg0.getUserId() );
        questionComment.setParentId( arg0.getParentId() );
        questionComment.setContent( arg0.getContent() );
        questionComment.setLikeCount( arg0.getLikeCount() );
        questionComment.setReplyCount( arg0.getReplyCount() );
        questionComment.setStatus( arg0.getStatus() );
        questionComment.setSort( arg0.getSort() );
        questionComment.setIpAddress( arg0.getIpAddress() );
        questionComment.setRemark( arg0.getRemark() );

        return questionComment;
    }

    @Override
    public QuestionComment convert(QuestionCommentBo arg0, QuestionComment arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCommentId( arg0.getCommentId() );
        arg1.setQuestionId( arg0.getQuestionId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setContent( arg0.getContent() );
        arg1.setLikeCount( arg0.getLikeCount() );
        arg1.setReplyCount( arg0.getReplyCount() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setSort( arg0.getSort() );
        arg1.setIpAddress( arg0.getIpAddress() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
