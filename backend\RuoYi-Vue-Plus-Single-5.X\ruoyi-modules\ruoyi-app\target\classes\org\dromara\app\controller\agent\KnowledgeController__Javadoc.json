{"doc": "\n 知识库管理Controller\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeBaseBo", "<PERSON><PERSON><PERSON><PERSON>"], "doc": "\n 分页查询知识库列表\r\n\r\n @param bo        查询条件\r\n @param pageQuery 分页参数\r\n @return 分页结果\r\n"}, {"name": "getKnowledgeBases", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeBaseBo"], "doc": "\n 获取知识库列表（不分页）\r\n\r\n @param bo 查询条件\r\n @return 知识库列表\r\n"}, {"name": "getKnowledgeBase", "paramTypes": ["java.lang.Long"], "doc": "\n 获取知识库详情\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @return 知识库详情\r\n"}, {"name": "createKnowledgeBase", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeBaseBo"], "doc": "\n 创建知识库\r\n\r\n @param bo 知识库信息\r\n @return 操作结果\r\n"}, {"name": "updateKnowledgeBase", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeBaseBo"], "doc": "\n 更新知识库\r\n\r\n @param bo 知识库信息\r\n @return 操作结果\r\n"}, {"name": "deleteKnowledgeBase", "paramTypes": ["java.lang.Long"], "doc": "\n 删除知识库\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @return 操作结果\r\n"}, {"name": "getKnowledgeBaseStats", "paramTypes": ["java.lang.Long"], "doc": "\n 获取知识库统计信息\r\n"}, {"name": "rebuildIndex", "paramTypes": ["java.lang.Long"], "doc": "\n 重建知识库索引\r\n"}, {"name": "documentList", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeDocumentBo", "<PERSON><PERSON><PERSON><PERSON>"], "doc": "\n 分页查询知识库文档列表\r\n\r\n @param bo        查询条件\r\n @param pageQuery 分页参数\r\n @return 分页结果\r\n"}, {"name": "getDocuments", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.bo.KnowledgeDocumentBo"], "doc": "\n 获取知识库文档列表（不分页）\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @param bo              查询条件\r\n @return 文档列表\r\n"}, {"name": "getDocument", "paramTypes": ["java.lang.Long"], "doc": "\n 获取文档详情\r\n\r\n @param documentId 文档ID\r\n @return 文档详情\r\n"}, {"name": "addDocument", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeDocumentBo"], "doc": "\n 添加文档到知识库\r\n\r\n @param bo 文档信息\r\n @return 操作结果\r\n"}, {"name": "updateDocument", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeDocumentBo"], "doc": "\n 更新文档信息\r\n\r\n @param bo 文档信息\r\n @return 操作结果\r\n"}, {"name": "deleteDocument", "paramTypes": ["java.lang.Long"], "doc": "\n 删除文档\r\n\r\n @param documentId 文档ID\r\n @return 操作结果\r\n"}, {"name": "processDocument", "paramTypes": ["java.lang.Long"], "doc": "\n 处理文档（重新向量化）\r\n\r\n @param documentId 文档ID\r\n @return 操作结果\r\n"}, {"name": "searchKnowledge", "paramTypes": ["org.dromara.app.controller.agent.KnowledgeController.SearchRequest"], "doc": "\n 向量搜索知识库\r\n\r\n @param request 搜索请求\r\n @return 搜索结果\r\n"}, {"name": "hybridSearch", "paramTypes": ["org.dromara.app.controller.agent.KnowledgeController.SearchRequest"], "doc": "\n 混合搜索知识库（向量 + 关键词）\r\n\r\n @param request 搜索请求\r\n @return 搜索结果\r\n"}], "constructors": []}