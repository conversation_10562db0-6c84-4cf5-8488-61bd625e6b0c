package org.dromara.app.domain.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.io.Serial;
import java.io.Serializable;

/**
 * 题目查询参数DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QuestionQueryDto extends PageQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 搜索关键词
     */
    private String keyword;

    /**
     * 难度等级（easy/medium/hard）
     */
    private String difficulty;

    /**
     * 分类
     */
    private String category;

    /**
     * 完成状态（true/false/null）
     */
    private Boolean completed;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向（asc/desc）
     */
    private String orderDirection;
}
