{"doc": " 面试模块全局异常处理器\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleInterviewException", "paramTypes": ["org.dromara.app.exception.InterviewException", "jakarta.servlet.http.HttpServletRequest"], "doc": " 面试业务异常\n"}, {"name": "handleJobNotFoundException", "paramTypes": ["org.dromara.app.exception.InterviewException.JobNotFoundException", "jakarta.servlet.http.HttpServletRequest"], "doc": " 岗位不存在异常\n"}, {"name": "handleInterviewModeNotFoundException", "paramTypes": ["org.dromara.app.exception.InterviewException.InterviewModeNotFoundException", "jakarta.servlet.http.HttpServletRequest"], "doc": " 面试模式不存在异常\n"}, {"name": "handleSessionExpiredException", "paramTypes": ["org.dromara.app.exception.InterviewException.SessionExpiredException", "jakarta.servlet.http.HttpServletRequest"], "doc": " 会话已过期异常\n"}, {"name": "handleDeviceCheckFailedException", "paramTypes": ["org.dromara.app.exception.InterviewException.DeviceCheckFailedException", "jakarta.servlet.http.HttpServletRequest"], "doc": " 设备检测失败异常\n"}, {"name": "handleUserNotLoginException", "paramTypes": ["org.dromara.app.exception.InterviewException.UserNotLoginException", "jakarta.servlet.http.HttpServletRequest"], "doc": " 用户未登录异常\n"}], "constructors": []}