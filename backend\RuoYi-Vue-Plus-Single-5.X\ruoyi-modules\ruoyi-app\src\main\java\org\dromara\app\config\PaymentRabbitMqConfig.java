package org.dromara.app.config;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * 支付订单RabbitMQ配置
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(prefix = "spring.rabbitmq", name = "enabled", havingValue = "true")
public class PaymentRabbitMqConfig {

    /**
     * 支付订单延迟交换机
     */
    public static final String PAYMENT_DELAY_EXCHANGE = "payment.delay.exchange";

    /**
     * 支付订单延迟队列
     */
    public static final String PAYMENT_DELAY_QUEUE = "payment.delay.queue";

    /**
     * 支付订单延迟路由键
     */
    public static final String PAYMENT_DELAY_ROUTING_KEY = "payment.delay.routing.key";

    /**
     * 支付订单死信交换机
     */
    public static final String PAYMENT_DEAD_EXCHANGE = "payment.dead.exchange";

    /**
     * 支付订单死信队列
     */
    public static final String PAYMENT_DEAD_QUEUE = "payment.dead.queue";

    /**
     * 支付订单死信路由键
     */
    public static final String PAYMENT_DEAD_ROUTING_KEY = "payment.dead.routing.key";

    /**
     * 创建延迟交换机
     */
    @Bean
    public DirectExchange paymentDelayExchange() {
        return new DirectExchange(PAYMENT_DELAY_EXCHANGE, true, false);
    }

    /**
     * 创建延迟队列
     * 设置30分钟过期时间，过期后转发到死信队列
     */
    @Bean
    public Queue paymentDelayQueue() {
        Map<String, Object> args = new HashMap<>();
        // 设置死信交换机
        args.put("x-dead-letter-exchange", PAYMENT_DEAD_EXCHANGE);
        // 设置死信路由键
        args.put("x-dead-letter-routing-key", PAYMENT_DEAD_ROUTING_KEY);
        // 设置消息过期时间（30分钟）
        args.put("x-message-ttl", 30 * 60 * 1000);

        return new Queue(PAYMENT_DELAY_QUEUE, true, false, false, args);
    }

    /**
     * 绑定延迟队列到延迟交换机
     */
    @Bean
    public Binding paymentDelayBinding() {
        return BindingBuilder.bind(paymentDelayQueue())
            .to(paymentDelayExchange())
            .with(PAYMENT_DELAY_ROUTING_KEY);
    }

    /**
     * 创建死信交换机
     */
    @Bean
    public DirectExchange paymentDeadExchange() {
        return new DirectExchange(PAYMENT_DEAD_EXCHANGE, true, false);
    }

    /**
     * 创建死信队列
     */
    @Bean
    public Queue paymentDeadQueue() {
        return new Queue(PAYMENT_DEAD_QUEUE, true);
    }

    /**
     * 绑定死信队列到死信交换机
     */
    @Bean
    public Binding paymentDeadBinding() {
        return BindingBuilder.bind(paymentDeadQueue())
            .to(paymentDeadExchange())
            .with(PAYMENT_DEAD_ROUTING_KEY);
    }
}
