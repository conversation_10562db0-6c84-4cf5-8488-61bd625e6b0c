{"doc": "\n Interview Result Service Implementation\r\n\r\n <AUTHOR>\r\n @date 2025-07-17\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "parseResultData", "paramTypes": ["java.lang.Object"], "doc": "\n 解析结果数据\r\n"}, {"name": "convertMapToResultData", "paramTypes": ["java.util.Map"], "doc": "\n 将Map转换为InterviewResultData\r\n"}, {"name": "createRelatedData", "paramTypes": ["java.lang.String", "org.dromara.app.service.impl.InterviewResultServiceImpl.InterviewResultData"], "doc": "\n 创建关联数据\r\n"}, {"name": "updateRelatedData", "paramTypes": ["java.lang.String", "org.dromara.app.service.impl.InterviewResultServiceImpl.InterviewResultData"], "doc": "\n 更新关联数据\r\n"}, {"name": "deleteRelatedData", "paramTypes": ["java.lang.String"], "doc": "\n 删除关联数据\r\n"}], "constructors": []}