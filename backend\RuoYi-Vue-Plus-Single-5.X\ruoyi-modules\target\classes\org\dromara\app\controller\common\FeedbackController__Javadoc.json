{"doc": " 意见反馈控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.app.domain.bo.FeedbackBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询意见反馈分页列表\n"}, {"name": "getMyFeedbackList", "paramTypes": [], "doc": " 查询当前用户的反馈列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取意见反馈详细信息\n\n @param id 反馈主键\n"}, {"name": "add", "paramTypes": ["org.dromara.app.domain.bo.FeedbackBo"], "doc": " 新增意见反馈\n"}, {"name": "edit", "paramTypes": ["org.dromara.app.domain.bo.FeedbackBo"], "doc": " 修改意见反馈（仅限用户修改自己的反馈）\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除意见反馈\n\n @param ids 反馈主键串\n"}, {"name": "getStats", "paramTypes": [], "doc": " 获取反馈统计信息\n"}, {"name": "cancelFeedback", "paramTypes": ["java.lang.Long"], "doc": " 撤销反馈（用户可撤销待处理状态的反馈）\n\n @param id 反馈ID\n"}], "constructors": []}