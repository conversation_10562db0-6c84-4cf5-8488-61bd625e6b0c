package org.dromara.common.mongodb.config;

import cn.hutool.core.util.StrUtil;
import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.MongoCredential;
import com.mongodb.ServerAddress;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.factory.YmlPropertySourceFactory;
import org.dromara.common.mongodb.properties.MongoProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.MongoTransactionManager;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;
import org.springframework.data.mongodb.core.convert.DefaultDbRefResolver;
import org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;

import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * MongoDB配置类
 *
 * <AUTHOR>
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(MongoProperties.class)
@PropertySource(value = "classpath:common-mongodb.yml", factory = YmlPropertySourceFactory.class)
public class MongoConfig {

    @Autowired
    private MongoProperties mongoProperties;

    @Bean
    @ConditionalOnMissingBean
    public MongoClient mongoClient() {
        MongoClientSettings.Builder builder = MongoClientSettings.builder();

        // 如果有URI，优先使用URI配置
        if (StrUtil.isNotBlank(mongoProperties.getUri())) {
            builder.applyConnectionString(new ConnectionString(mongoProperties.getUri()));
        } else {
            // 使用单独配置
            ServerAddress serverAddress = new ServerAddress(mongoProperties.getHost(), mongoProperties.getPort());
            builder.applyToClusterSettings(clusterBuilder ->
                clusterBuilder.hosts(Collections.singletonList(serverAddress)));

            // 设置认证
            if (StrUtil.isNotBlank(mongoProperties.getUsername()) &&
                StrUtil.isNotBlank(mongoProperties.getPassword())) {
                String authDatabase = StrUtil.isNotBlank(mongoProperties.getAuthenticationDatabase())
                    ? mongoProperties.getAuthenticationDatabase()
                    : mongoProperties.getDatabase();
                MongoCredential credential = MongoCredential.createCredential(
                    mongoProperties.getUsername(),
                    authDatabase,
                    mongoProperties.getPassword().toCharArray()
                );
                builder.credential(credential);
            }
        }

        // 连接池配置
        MongoProperties.Pool pool = mongoProperties.getPool();
        builder.applyToConnectionPoolSettings(poolBuilder -> {
            poolBuilder.maxSize(pool.getMaxSize())
                .minSize(pool.getMinSize())
                .maxConnectionIdleTime(pool.getMaxConnectionIdleTimeMs(), TimeUnit.MILLISECONDS)
                .maxConnectionLifeTime(pool.getMaxConnectionLifeTimeMs(), TimeUnit.MILLISECONDS)
                .maxWaitTime(pool.getMaxWaitTimeMs(), TimeUnit.MILLISECONDS);
        });

        // 超时配置
        builder.applyToSocketSettings(socketBuilder -> {
            socketBuilder.connectTimeout((int) pool.getMaxConnectionTimeoutMs(), TimeUnit.MILLISECONDS)
                .readTimeout((int) pool.getMaxReadTimeoutMs(), TimeUnit.MILLISECONDS);
        });

        log.info("初始化 MongoDB 配置");
        return MongoClients.create(builder.build());
    }

    /**
     * MongoDB数据库工厂
     */
    @Bean
    @ConditionalOnMissingBean
    public MongoDatabaseFactory mongoDatabaseFactory(MongoClient mongoClient) {
        return new SimpleMongoClientDatabaseFactory(mongoClient, mongoProperties.getDatabase());
    }

    /**
     * MongoDB模板
     */
    @Bean
    @ConditionalOnMissingBean
    public MongoTemplate mongoTemplate(MongoDatabaseFactory mongoDatabaseFactory) {
        MappingMongoConverter converter = new MappingMongoConverter(
            new DefaultDbRefResolver(mongoDatabaseFactory),
            new MongoMappingContext()
        );
        // 移除_class字段
        converter.setTypeMapper(new DefaultMongoTypeMapper(null));
        converter.afterPropertiesSet();

        return new MongoTemplate(mongoDatabaseFactory, converter);
    }

    /**
     * MongoDB事务管理器
     */
    @Bean
    @ConditionalOnMissingBean
    public MongoTransactionManager transactionManager(MongoDatabaseFactory mongoDatabaseFactory) {
        return new MongoTransactionManager(mongoDatabaseFactory);
    }
}
