{"doc": " 成就系统RabbitMQ配置\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "achievementExchange", "paramTypes": [], "doc": " 成就系统主交换机\n"}, {"name": "achievement<PERSON><PERSON><PERSON><PERSON><PERSON>ue", "paramTypes": [], "doc": " 成就检查队列\n"}, {"name": "achievementNotificationQueue", "paramTypes": [], "doc": " 成就通知队列\n"}, {"name": "achievementDlxExchange", "paramTypes": [], "doc": " 死信交换机\n"}, {"name": "achievementDlxQueue", "paramTypes": [], "doc": " 死信队列\n"}, {"name": "achievement<PERSON><PERSON>ck<PERSON><PERSON>ing", "paramTypes": [], "doc": " 绑定成就检查队列到交换机\n"}, {"name": "achievementNotificationBinding", "paramTypes": [], "doc": " 绑定成就通知队列到交换机\n"}, {"name": "achievementDlxBinding", "paramTypes": [], "doc": " 绑定死信队列到死信交换机\n"}], "constructors": []}