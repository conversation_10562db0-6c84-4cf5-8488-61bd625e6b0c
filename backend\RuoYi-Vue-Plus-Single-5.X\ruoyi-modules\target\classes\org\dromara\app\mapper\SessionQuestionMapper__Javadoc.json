{"doc": " 面试会话问题Mapper接口\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectBySessionIdOrderByOrder", "paramTypes": ["java.lang.String"], "doc": " 根据会话ID查询所有问题（按顺序）\n\n @param sessionId 会话ID\n @return 问题列表\n"}, {"name": "selectBySessionIdAndQuestionId", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据会话ID和问题ID查询问题\n\n @param sessionId  会话ID\n @param questionId 问题ID\n @return 问题\n"}, {"name": "selectBySessionIdAndOrder", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 根据会话ID和顺序查询问题\n\n @param sessionId     会话ID\n @param questionOrder 问题顺序\n @return 问题\n"}, {"name": "selectNextQuestion", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 查询会话的下一个问题\n\n @param sessionId      会话ID\n @param currentOrder   当前问题顺序\n @return 下一个问题\n"}, {"name": "selectCurrentQuestion", "paramTypes": ["java.lang.String"], "doc": " 查询会话的当前问题\n\n @param sessionId 会话ID\n @return 当前问题\n"}, {"name": "countBySessionId", "paramTypes": ["java.lang.String"], "doc": " 统计会话问题总数\n\n @param sessionId 会话ID\n @return 问题总数\n"}, {"name": "countBySessionIdAndStatus", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据状态统计问题数量\n\n @param sessionId 会话ID\n @param status    状态\n @return 问题数量\n"}, {"name": "batchInsert", "paramTypes": ["java.util.List"], "doc": " 批量插入问题\n\n @param questions 问题列表\n @return 插入数量\n"}, {"name": "updateStatusBySessionIdAndQuestionId", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 更新问题状态\n\n @param sessionId  会话ID\n @param questionId 问题ID\n @param status     新状态\n @return 更新数量\n"}], "constructors": []}