{"doc": " 分析回调接口\n", "fields": [], "enumConstants": [], "methods": [{"name": "onProgress", "paramTypes": ["int", "java.lang.String"], "doc": " 分析进度更新\n\n @param progress 进度百分比\n @param stage 当前阶段\n"}, {"name": "onComplete", "paramTypes": ["java.lang.Object"], "doc": " 分析完成\n\n @param result 分析结果\n"}, {"name": "onError", "paramTypes": ["java.lang.Throwable"], "doc": " 分析失败\n\n @param error 错误信息\n"}], "constructors": []}