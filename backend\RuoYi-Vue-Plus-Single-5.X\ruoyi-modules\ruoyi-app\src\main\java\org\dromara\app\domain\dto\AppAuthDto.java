package org.dromara.app.domain.dto;

import jakarta.validation.constraints.Email;
import lombok.Data;

/**
 * 应用认证DTO
 *
 * <AUTHOR>
 */
@Data
public class AppAuthDto {

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 授权类型
     */
    private String grantType;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 验证码
     */
    private String code;

    /**
     * 密码
     */
    private String password;

    /**
     * 新密码
     */
    private String newPassword;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 学号
     */
    private String studentId;

    /**
     * 专业
     */
    private String major;

    /**
     * 年级
     */
    private String grade;

    /**
     * 第三方登录来源
     */
    private String source;

    /**
     * 第三方登录平台
     */
    private String platform;

    /**
     * 第三方登录code
     */
    private String thirdPartyCode;

    /**
     * 社交登录授权码
     */
    private String socialCode;

    /**
     * 社交登录状态码
     */
    private String socialState;

    /**
     * 第三方登录openId
     */
    private String openId;

    /**
     * 刷新令牌
     */
    private String refreshToken;
}
