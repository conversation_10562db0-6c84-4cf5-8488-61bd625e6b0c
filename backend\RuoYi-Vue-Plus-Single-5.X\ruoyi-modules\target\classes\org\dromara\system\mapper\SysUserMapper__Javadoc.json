{"doc": " 用户表 数据层\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectPageUserList", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 分页查询用户列表，并进行数据权限控制\n\n @param page         分页参数\n @param queryWrapper 查询条件\n @return 分页的用户信息\n"}, {"name": "selectUserList", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 查询用户列表，并进行数据权限控制\n\n @param queryWrapper 查询条件\n @return 用户信息集合\n"}, {"name": "selectUserExportList", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 根据条件分页查询用户列表\n\n @param queryWrapper 查询条件\n @return 用户信息集合信息\n"}, {"name": "selectAllocatedList", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 根据条件分页查询已配用户角色列表\n\n @param queryWrapper 查询条件\n @return 用户信息集合信息\n"}, {"name": "selectUnallocatedList", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 根据条件分页查询未分配用户角色列表\n\n @param queryWrapper 查询条件\n @return 用户信息集合信息\n"}, {"name": "countUserById", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID统计用户数量\n\n @param userId 用户ID\n @return 用户数量\n"}, {"name": "update", "paramTypes": ["org.dromara.system.domain.SysUser", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 根据条件更新用户数据\n\n @param user          要更新的用户实体\n @param updateWrapper 更新条件封装器\n @return 更新操作影响的行数\n"}, {"name": "updateById", "paramTypes": ["org.dromara.system.domain.SysUser"], "doc": " 根据用户ID更新用户数据\n\n @param user 要更新的用户实体\n @return 更新操作影响的行数\n"}], "constructors": []}