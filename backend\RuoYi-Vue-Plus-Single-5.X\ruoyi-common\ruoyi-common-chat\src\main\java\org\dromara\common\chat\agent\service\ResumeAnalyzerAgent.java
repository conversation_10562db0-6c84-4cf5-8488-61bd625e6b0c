package org.dromara.common.chat.agent.service;

import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;

/**
 * 简历分析Agent
 *
 * <AUTHOR>
 */
public interface ResumeAnalyzerAgent {

    @SystemMessage({
        "你是一位专业的简历分析专家，具有丰富的HR和招聘经验。",
        "你的任务是：",
        "1. 深入分析简历内容的质量和完整性",
        "2. 识别简历中的亮点和不足之处",
        "3. 提供具体的简历优化建议",
        "4. 帮助求职者提升简历的竞争力",
        "请从专业角度给出客观、实用的分析和建议。"
    })
    String analyzeResume(String resumeContent);

    @SystemMessage({
        "作为简历分析专家，请分析简历与指定职位的匹配度。",
        "评估维度包括：技能匹配、经验相关性、教育背景、项目经历等。",
        "请给出匹配度评分（1-100分）和详细的分析报告。"
    })
    String matchWithJob(@UserMessage("简历内容：{resume}\n职位要求：{jobRequirements}") String resume, String jobRequirements);

    @SystemMessage({
        "作为简历分析专家，请从简历中提取关键信息并进行结构化整理。",
        "提取内容包括：个人信息、教育背景、工作经历、技能特长、项目经验等。",
        "请以清晰的格式输出提取结果。"
    })
    String extractKeyInfo(@UserMessage("请分析以下简历并提取关键信息：{resume}") String resume);

    @SystemMessage({
        "作为简历分析专家，请针对特定行业或职位对简历提出优化建议。",
        "建议应该具体、可操作，包括内容调整、格式优化、关键词添加等方面。"
    })
    String optimizeForPosition(@UserMessage("简历：{resume}\n目标职位：{targetPosition}") String resume, String targetPosition);
}
