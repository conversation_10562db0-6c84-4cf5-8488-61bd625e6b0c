{"doc": " 敏感信息检测结果视图对象\n\n <AUTHOR>\n @date 2025-01-19\n", "fields": [{"name": "detectionId", "doc": " 检测ID\n"}, {"name": "originalTextLength", "doc": " 原始文本长度\n"}, {"name": "hasSensitiveInfo", "doc": " 是否包含敏感信息\n"}, {"name": "sensitiveInfoCount", "doc": " 敏感信息总数\n"}, {"name": "riskLevel", "doc": " 风险等级：low/medium/high/critical\n"}, {"name": "riskScore", "doc": " 风险评分（0-100）\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "doc": " 检测到的敏感词列表\n"}, {"name": "personalInfo", "doc": " 检测到的个人信息\n"}, {"name": "filteredText", "doc": " 过滤后的文本\n"}, {"name": "maskedText", "doc": " 脱敏后的文本\n"}, {"name": "detectionTime", "doc": " 检测耗时（毫秒）\n"}, {"name": "detectionDateTime", "doc": " 检测时间\n"}, {"name": "recommendations", "doc": " 建议处理方式\n"}, {"name": "needs<PERSON><PERSON><PERSON><PERSON><PERSON>iew", "doc": " 是否需要人工审核\n"}], "enumConstants": [], "methods": [], "constructors": []}