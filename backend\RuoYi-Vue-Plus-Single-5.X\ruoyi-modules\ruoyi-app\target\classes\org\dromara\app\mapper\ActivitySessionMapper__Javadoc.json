{"doc": "\n 用户活动会话Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-16\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectBySessionId", "paramTypes": ["java.lang.String"], "doc": "\n 根据会话ID查询活动会话\r\n\r\n @param sessionId 会话ID\r\n @return 活动会话\r\n"}, {"name": "selectActiveSessionsByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户的活跃会话\r\n\r\n @param userId 用户ID\r\n @return 活跃会话列表\r\n"}, {"name": "updateSessionToInactive", "paramTypes": ["java.lang.String", "java.time.LocalDateTime", "java.lang.Long"], "doc": "\n 更新会话状态为非活跃\r\n\r\n @param sessionId 会话ID\r\n @param endTime   结束时间\r\n @param duration  持续时长\r\n @return 更新行数\r\n"}, {"name": "updateSessionDuration", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 更新会话持续时长\r\n\r\n @param sessionId 会话ID\r\n @param duration  持续时长\r\n @return 更新行数\r\n"}, {"name": "selectUserActivityHistory", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.Long", "org.dromara.app.domain.enums.ActivityType", "java.time.LocalDateTime", "java.time.LocalDateTime"], "doc": "\n 分页查询用户活动历史记录\r\n\r\n @param page         分页对象\r\n @param userId       用户ID\r\n @param activityType 活动类型\r\n @param startDate    开始时间\r\n @param endDate      结束时间\r\n @return 分页结果\r\n"}, {"name": "selectTodayDuration", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType"], "doc": "\n 查询用户今日活动时长\r\n\r\n @param userId       用户ID\r\n @param activityType 活动类型\r\n @return 今日活动时长(毫秒)\r\n"}, {"name": "selectWeekDuration", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType"], "doc": "\n 查询用户本周活动时长\r\n\r\n @param userId       用户ID\r\n @param activityType 活动类型\r\n @return 本周活动时长(毫秒)\r\n"}, {"name": "selectMonthDuration", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType"], "doc": "\n 查询用户本月活动时长\r\n\r\n @param userId       用户ID\r\n @param activityType 活动类型\r\n @return 本月活动时长(毫秒)\r\n"}, {"name": "selectTotalDuration", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType"], "doc": "\n 查询用户总活动时长\r\n\r\n @param userId       用户ID\r\n @param activityType 活动类型\r\n @return 总活动时长(毫秒)\r\n"}, {"name": "deleteUserActivityRecords", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType"], "doc": "\n 删除用户指定类型的活动记录\r\n\r\n @param userId       用户ID\r\n @param activityType 活动类型\r\n @return 删除行数\r\n"}], "constructors": []}