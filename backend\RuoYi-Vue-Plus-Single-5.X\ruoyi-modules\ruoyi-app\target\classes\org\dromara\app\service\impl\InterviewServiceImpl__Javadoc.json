{"doc": "\n 面试服务实现类\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "calculateRemainingTime", "paramTypes": ["org.dromara.app.domain.InterviewSession"], "doc": "\n 计算剩余时间（秒）\r\n"}, {"name": "getScoreLevel", "paramTypes": ["java.lang.Double"], "doc": "\n 根据分数获取等级\r\n"}, {"name": "generateAndSaveSessionQuestions", "paramTypes": ["org.dromara.app.domain.InterviewSession"], "doc": "\n 生成并保存会话问题\r\n"}, {"name": "createEmptyHistoryResponse", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": "\n 创建空的历史记录响应\r\n"}, {"name": "batchConvertToInterviewRecords", "paramTypes": ["java.util.List"], "doc": "\n 批量转换InterviewResult为InterviewRecord，优化数据库查询\r\n"}, {"name": "convertToInterviewRecordOptimized", "paramTypes": ["org.dromara.app.domain.InterviewResult", "java.util.Map", "java.util.Map"], "doc": "\n 转换InterviewResult为InterviewRecord（优化版本）\r\n"}, {"name": "getJobIconByCategory", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": "\n 根据分类ID获取岗位图标（优化版本）\r\n"}, {"name": "convertToInterviewRecord", "paramTypes": ["org.dromara.app.domain.InterviewResult"], "doc": "\n 转换InterviewResult为InterviewRecord（兼容旧版本）\r\n"}, {"name": "getDifficultyText", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据分数获取难度文本\r\n"}, {"name": "convertStatus", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 转换状态\r\n"}, {"name": "getTimeAgo", "paramTypes": ["java.time.LocalDateTime"], "doc": "\n 获取时间差描述\r\n"}, {"name": "getJobIcon", "paramTypes": ["java.lang.Long"], "doc": "\n 获取岗位图标\r\n"}, {"name": "getJobCategory", "paramTypes": ["java.lang.Long"], "doc": "\n 获取岗位分类\r\n"}, {"name": "createInterviewStep", "paramTypes": ["int", "java.lang.String", "int", "java.lang.String"], "doc": "\n 创建面试流程步骤\r\n"}, {"name": "createSkillPoint", "paramTypes": ["java.lang.String", "int", "java.lang.String"], "doc": "\n 创建技能考查点\r\n"}], "constructors": []}