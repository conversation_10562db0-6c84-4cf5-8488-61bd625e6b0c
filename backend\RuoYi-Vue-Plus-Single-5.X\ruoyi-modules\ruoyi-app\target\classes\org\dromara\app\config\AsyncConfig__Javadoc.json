{"doc": "\n 异步处理配置\r\n 配置多模态数据分析的异步处理线程池\r\n\r\n <AUTHOR>\r\n @date 2025-01-19\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "analysisTaskExecutor", "paramTypes": [], "doc": "\n 分析任务执行器\r\n 用于执行多模态分析任务\r\n"}, {"name": "reportGenerationExecutor", "paramTypes": [], "doc": "\n 报告生成执行器\r\n 用于异步生成报告\r\n"}, {"name": "cacheOptimizationExecutor", "paramTypes": [], "doc": "\n 缓存优化执行器\r\n 用于异步执行缓存优化任务\r\n"}, {"name": "dataCleanupExecutor", "paramTypes": [], "doc": "\n 数据清理执行器\r\n 用于异步执行数据清理任务\r\n"}], "constructors": []}