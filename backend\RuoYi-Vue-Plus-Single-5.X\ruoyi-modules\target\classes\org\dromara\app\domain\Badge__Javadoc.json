{"doc": " 徽章实体\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [{"name": "id", "doc": " 徽章ID\n"}, {"name": "icon", "doc": " 徽章图标\n"}, {"name": "color", "doc": " 徽章颜色\n"}, {"name": "title", "doc": " 徽章标题\n"}, {"name": "description", "doc": " 徽章描述\n"}, {"name": "category", "doc": " 徽章类别\n"}, {"name": "rarity", "doc": " 稀有度\n"}, {"name": "achievementId", "doc": " 关联成就ID\n"}, {"name": "sort", "doc": " 排序\n"}, {"name": "isEnabled", "doc": " 是否启用\n"}, {"name": "unlockCriteria", "doc": " 解锁条件（单独徽章可能不关联成就）\n"}, {"name": "effect", "doc": " 徽章特效\n"}, {"name": "specialFlag", "doc": " 特殊徽章标志（如限时、活动等）\n"}, {"name": "tags", "doc": " 徽章标签（用逗号分隔）\n"}], "enumConstants": [], "methods": [], "constructors": []}