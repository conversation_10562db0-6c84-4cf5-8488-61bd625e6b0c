{"doc": " RabbitMQ 消息实体\n\n <AUTHOR>\n", "fields": [{"name": "messageId", "doc": " 消息ID\n"}, {"name": "content", "doc": " 消息内容\n"}, {"name": "messageType", "doc": " 消息类型\n"}, {"name": "sendTime", "doc": " 发送时间\n"}, {"name": "retryCount", "doc": " 重试次数\n"}, {"name": "source", "doc": " 消息来源\n"}, {"name": "extras", "doc": " 附加信息\n"}], "enumConstants": [], "methods": [{"name": "create", "paramTypes": ["java.lang.Object"], "doc": " 创建消息\n\n @param content 消息内容\n @param <T>     内容类型\n @return 消息对象\n"}], "constructors": []}