{"doc": "\n 题目工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getDifficultyDescription", "paramTypes": ["java.lang.Integer"], "doc": "\n 将难度代码转换为中文描述\r\n\r\n @param difficulty 难度代码（1-简单 2-中等 3-困难）\r\n @return 难度描述\r\n"}, {"name": "getDifficultyCode", "paramTypes": ["java.lang.String"], "doc": "\n 将难度描述转换为代码\r\n\r\n @param difficultyDescription 难度描述\r\n @return 难度代码\r\n"}, {"name": "getDifficultyEnum", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据代码获取难度枚举实例\r\n\r\n @param difficulty 难度代码\r\n @return 难度枚举实例\r\n"}, {"name": "getTypeDescription", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取题目类型描述\r\n\r\n @param typeCode 类型代码\r\n @return 类型描述\r\n"}, {"name": "getTypeCode", "paramTypes": ["java.lang.String"], "doc": "\n 获取题目类型代码\r\n\r\n @param typeDescription 类型描述\r\n @return 类型代码\r\n"}, {"name": "getTypeEnum", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据代码获取题目类型枚举实例\r\n\r\n @param typeCode 类型代码\r\n @return 类型枚举实例\r\n"}, {"name": "calculateEstimatedTime", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": "\n 根据难度和题目类型计算预计完成时间\r\n\r\n @param difficulty 难度代码（1-简单 2-中等 3-困难）\r\n @param typeCode   题目类型代码\r\n @return 预计时间（分钟）\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Integer"], "doc": "\n 验证难度代码是否有效\r\n\r\n @param difficulty 难度代码\r\n @return 是否有效\r\n"}, {"name": "isValidType", "paramTypes": ["java.lang.Integer"], "doc": "\n 验证题目类型代码是否有效\r\n\r\n @param typeCode 类型代码\r\n @return 是否有效\r\n"}, {"name": "getAllDifficulties", "paramTypes": [], "doc": "\n 获取所有难度选项\r\n\r\n @return 难度枚举数组\r\n"}, {"name": "getAllTypes", "paramTypes": [], "doc": "\n 获取所有题目类型选项\r\n\r\n @return 类型枚举数组\r\n"}], "constructors": []}