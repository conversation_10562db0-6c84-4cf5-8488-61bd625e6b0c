package org.dromara.common.rabbitmq.utils;

import jakarta.annotation.PostConstruct;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * RabbitMQ 工具类
 *
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(prefix = "spring.rabbitmq", name = "enabled", havingValue = "true")
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RabbitMqUtils {

    private static RabbitAdmin rabbitAdmin;

    @Autowired
    private RabbitAdmin admin;

    /**
     * 创建交换机
     *
     * @param exchangeName 交换机名称
     * @param exchangeType 交换机类型
     * @param durable      是否持久化
     * @param autoDelete   是否自动删除
     */
    public static void createExchange(String exchangeName, String exchangeType, boolean durable, boolean autoDelete) {
        AbstractExchange exchange = switch (exchangeType) {
            case "direct" -> new DirectExchange(exchangeName, durable, autoDelete);
            case "topic" -> new TopicExchange(exchangeName, durable, autoDelete);
            case "fanout" -> new FanoutExchange(exchangeName, durable, autoDelete);
            case "headers" -> new HeadersExchange(exchangeName, durable, autoDelete);
            default -> throw new IllegalArgumentException("不支持的交换机类型: " + exchangeType);
        };
        rabbitAdmin.declareExchange(exchange);
    }

    /**
     * 创建队列
     *
     * @param queueName  队列名称
     * @param durable    是否持久化
     * @param exclusive  是否独占
     * @param autoDelete 是否自动删除
     * @return Queue对象
     */
    public static Queue createQueue(String queueName, boolean durable, boolean exclusive, boolean autoDelete) {
        Queue queue = new Queue(queueName, durable, exclusive, autoDelete);
        rabbitAdmin.declareQueue(queue);
        return queue;
    }

    /**
     * 创建队列（带参数）
     *
     * @param queueName  队列名称
     * @param durable    是否持久化
     * @param exclusive  是否独占
     * @param autoDelete 是否自动删除
     * @param args       队列参数
     * @return Queue对象
     */
    public static Queue createQueue(String queueName, boolean durable, boolean exclusive,
                                    boolean autoDelete, Map<String, Object> args) {
        Queue queue = new Queue(queueName, durable, exclusive, autoDelete, args);
        rabbitAdmin.declareQueue(queue);
        return queue;
    }

    /**
     * 创建死信队列
     *
     * @param queueName      队列名称
     * @param deadExchange   死信交换机
     * @param deadRoutingKey 死信路由键
     * @param messageTtl     消息过期时间（毫秒）
     * @return Queue对象
     */
    public static Queue createDeadLetterQueue(String queueName, String deadExchange,
                                              String deadRoutingKey, long messageTtl) {
        Map<String, Object> args = new HashMap<>();
        args.put("x-dead-letter-exchange", deadExchange);
        args.put("x-dead-letter-routing-key", deadRoutingKey);
        args.put("x-message-ttl", messageTtl);

        return createQueue(queueName, true, false, false, args);
    }

    /**
     * 创建延迟队列
     *
     * @param queueName 队列名称
     * @return Queue对象
     */
    public static Queue createDelayQueue(String queueName) {
        Map<String, Object> args = new HashMap<>();
        args.put("x-delayed-type", "direct");

        return createQueue(queueName, true, false, false, args);
    }

    /**
     * 绑定队列到交换机
     *
     * @param queueName    队列名称
     * @param exchangeName 交换机名称
     * @param routingKey   路由键
     */
    public static void bindQueue(String queueName, String exchangeName, String routingKey) {
        Binding binding = BindingBuilder
            .bind(new Queue(queueName))
            .to(new DirectExchange(exchangeName))
            .with(routingKey);
        rabbitAdmin.declareBinding(binding);
    }

    /**
     * 解除队列绑定
     *
     * @param queueName    队列名称
     * @param exchangeName 交换机名称
     * @param routingKey   路由键
     */
    public static void unbindQueue(String queueName, String exchangeName, String routingKey) {
        Binding binding = BindingBuilder
            .bind(new Queue(queueName))
            .to(new DirectExchange(exchangeName))
            .with(routingKey);
        rabbitAdmin.removeBinding(binding);
    }

    /**
     * 删除交换机
     *
     * @param exchangeName 交换机名称
     */
    public static void deleteExchange(String exchangeName) {
        rabbitAdmin.deleteExchange(exchangeName);
    }

    /**
     * 删除队列
     *
     * @param queueName 队列名称
     */
    public static void deleteQueue(String queueName) {
        rabbitAdmin.deleteQueue(queueName);
    }

    /**
     * 清空队列消息
     *
     * @param queueName 队列名称
     */
    public static void purgeQueue(String queueName) {
        rabbitAdmin.purgeQueue(queueName);
    }

    /**
     * 获取队列消息数量
     *
     * @param queueName 队列名称
     * @return 消息数量
     */
    public static Integer getQueueMessageCount(String queueName) {
        QueueInformation queueInfo = rabbitAdmin.getQueueInfo(queueName);
        return queueInfo.getMessageCount();
    }

    /**
     * 检查队列是否存在
     *
     * @param queueName 队列名称
     * @return 是否存在
     */
    public static boolean queueExists(String queueName) {
        return rabbitAdmin.getQueueInfo(queueName) != null;
    }

    @PostConstruct
    public void init() {
        RabbitMqUtils.rabbitAdmin = admin;
    }
}
