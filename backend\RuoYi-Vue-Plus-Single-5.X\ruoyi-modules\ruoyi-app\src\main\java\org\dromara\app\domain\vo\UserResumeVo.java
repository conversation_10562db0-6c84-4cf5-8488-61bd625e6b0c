package org.dromara.app.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.app.domain.UserResume;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户简历视图对象 app_user_resume
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = UserResume.class)
public class UserResumeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 简历主键
     */
    private Long resumeId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 简历名称
     */
    private String resumeName;

    /**
     * 原始文件名
     */
    private String originalName;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件URL地址
     */
    private String fileUrl;

    /**
     * 文件大小(字节)
     */
    private Long fileSize;

    /**
     * 文件大小(格式化后)
     */
    private String fileSizeStr;

    /**
     * 文件后缀名
     */
    private String fileSuffix;

    /**
     * 是否默认简历(0否 1是)
     */
    private Integer isDefault;

    /**
     * 状态(0正常 1停用)
     */
    private String status;

    /**
     * 对象存储ID
     */
    private Long ossId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建人名称
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "createBy")
    private String createByName;

    /**
     * 更新时间
     */
    private Date updateTime;

}
