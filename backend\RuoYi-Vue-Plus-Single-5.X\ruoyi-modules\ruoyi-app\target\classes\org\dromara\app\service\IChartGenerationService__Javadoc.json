{"doc": "\n 图表生成服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "generateRadarChart", "paramTypes": ["java.util.List", "java.lang.String"], "doc": "\n 生成雷达图\r\n\r\n @param dimensionScores 维度评分列表\r\n @param title 图表标题\r\n @return 雷达图图像\r\n"}, {"name": "generateRadarChartWithComparison", "paramTypes": ["java.util.List", "java.util.List", "java.lang.String"], "doc": "\n 生成雷达图（带对比数据）\r\n\r\n @param dimensionScores 维度评分列表\r\n @param industryAverages 行业平均分\r\n @param title 图表标题\r\n @return 雷达图图像\r\n"}, {"name": "generateBarChart", "paramTypes": ["java.util.Map", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 生成柱状图\r\n\r\n @param data 数据\r\n @param title 图表标题\r\n @param xAxisLabel X轴标签\r\n @param yAxisLabel Y轴标签\r\n @return 柱状图图像\r\n"}, {"name": "generateLineChart", "paramTypes": ["java.util.Map", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 生成折线图\r\n\r\n @param data 数据\r\n @param title 图表标题\r\n @param xAxisLabel X轴标签\r\n @param yAxisLabel Y轴标签\r\n @return 折线图图像\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.util.Map", "java.lang.String"], "doc": "\n 生成饼图\r\n\r\n @param data 数据\r\n @param title 图表标题\r\n @return 饼图图像\r\n"}, {"name": "generateCapabilityDistributionChart", "paramTypes": ["java.util.List"], "doc": "\n 生成能力分布图\r\n\r\n @param dimensionScores 维度评分列表\r\n @return 能力分布图图像\r\n"}, {"name": "generateTrendChart", "paramTypes": ["java.util.Map", "java.lang.String"], "doc": "\n 生成趋势分析图\r\n\r\n @param historicalData 历史数据\r\n @param title 图表标题\r\n @return 趋势分析图图像\r\n"}, {"name": "generateDashboard", "paramTypes": ["org.dromara.app.domain.InterviewReport"], "doc": "\n 生成综合评估仪表盘\r\n\r\n @param report 面试报告\r\n @return 仪表盘图像\r\n"}, {"name": "imageToByteArray", "paramTypes": ["java.awt.image.BufferedImage", "java.lang.String"], "doc": "\n 将图像转换为字节数组\r\n\r\n @param image 图像\r\n @param format 格式（PNG, JPEG等）\r\n @return 字节数组\r\n"}, {"name": "saveImageToFile", "paramTypes": ["java.awt.image.BufferedImage", "java.lang.String", "java.lang.String"], "doc": "\n 保存图像到文件\r\n\r\n @param image 图像\r\n @param filePath 文件路径\r\n @param format 格式\r\n @return 是否保存成功\r\n"}], "constructors": []}