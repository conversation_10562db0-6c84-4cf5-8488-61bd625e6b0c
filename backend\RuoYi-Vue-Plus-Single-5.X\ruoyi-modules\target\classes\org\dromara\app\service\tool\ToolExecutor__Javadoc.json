{"doc": " 工具执行器接口\n 所有工具实现都需要实现此接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getToolName", "paramTypes": [], "doc": " 获取工具名称\n\n @return 工具名称\n"}, {"name": "getDisplayName", "paramTypes": [], "doc": " 获取工具显示名称\n\n @return 显示名称\n"}, {"name": "getDescription", "paramTypes": [], "doc": " 获取工具描述\n\n @return 工具描述\n"}, {"name": "getCategory", "paramTypes": [], "doc": " 获取工具分类\n\n @return 工具分类\n"}, {"name": "getParameterSchema", "paramTypes": [], "doc": " 获取参数Schema\n\n @return 参数Schema\n"}, {"name": "validateParameters", "paramTypes": ["java.util.Map"], "doc": " 验证参数\n\n @param parameters 参数\n @return 验证结果\n"}, {"name": "execute", "paramTypes": ["java.util.Map", "org.dromara.app.service.tool.ToolExecutor.ExecutionContext"], "doc": " 执行工具\n\n @param parameters 参数\n @param context    执行上下文\n @return 执行结果\n"}, {"name": "supportsAsync", "paramTypes": [], "doc": " 是否支持异步执行\n\n @return 是否支持异步\n"}, {"name": "getTimeoutSeconds", "paramTypes": [], "doc": " 获取执行超时时间（秒）\n\n @return 超时时间\n"}, {"name": "getRequiredPermissions", "paramTypes": [], "doc": " 获取所需权限\n\n @return 权限列表\n"}], "constructors": []}