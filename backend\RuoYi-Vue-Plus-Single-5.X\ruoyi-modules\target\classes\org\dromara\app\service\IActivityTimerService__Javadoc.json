{"doc": " 活动时长记录服务接口\n\n <AUTHOR>\n @date 2025-07-16\n", "fields": [], "enumConstants": [], "methods": [{"name": "startActivity", "paramTypes": ["org.dromara.app.domain.dto.ActivityStartRequest"], "doc": " 开始活动记录\n\n @param request 开始活动请求\n @return 是否成功\n"}, {"name": "pauseActivity", "paramTypes": ["org.dromara.app.domain.dto.ActivityPauseRequest"], "doc": " 暂停活动记录\n\n @param request 暂停活动请求\n @return 是否成功\n"}, {"name": "endActivity", "paramTypes": ["org.dromara.app.domain.dto.ActivityEndRequest"], "doc": " 结束活动记录\n\n @param request 结束活动请求\n @return 是否成功\n"}, {"name": "syncSession", "paramTypes": ["org.dromara.app.domain.dto.ActivitySyncRequest"], "doc": " 同步活动会话\n\n @param request 同步请求\n @return 是否成功\n"}, {"name": "getStatistics", "paramTypes": ["org.dromara.app.domain.dto.ActivityStatisticsRequest"], "doc": " 获取活动统计数据\n\n @param request 统计请求\n @return 统计数据\n"}, {"name": "getHistory", "paramTypes": ["org.dromara.app.domain.dto.ActivityHistoryRequest"], "doc": " 获取活动历史记录\n\n @param request 历史记录请求\n @return 历史记录\n"}, {"name": "clearRecords", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType"], "doc": " 清除活动记录\n\n @param userId       用户ID\n @param activityType 活动类型(可选)\n @return 是否成功\n"}, {"name": "getStatsByType", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType"], "doc": " 获取用户指定类型的活动统计\n\n @param userId       用户ID\n @param activityType 活动类型\n @return 统计数据\n"}, {"name": "initializeUserSummary", "paramTypes": ["java.lang.Long"], "doc": " 初始化用户活动总览\n\n @param userId 用户ID\n @return 是否成功\n"}], "constructors": []}