{"groups": [{"name": "spring.rabbitmq", "type": "org.dromara.common.rabbitmq.properties.RabbitMqProperties", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties"}, {"name": "spring.rabbitmq.listener", "type": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Listener", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties", "sourceMethod": "getListener()"}, {"name": "spring.rabbitmq.listener.retry", "type": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Listener", "sourceMethod": "getRetry()"}, {"name": "spring.rabbitmq.retry", "type": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties", "sourceMethod": "getRetry()"}], "properties": [{"name": "spring.rabbitmq.channel-cache-size", "type": "java.lang.Integer", "description": "通道缓存大小", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties", "defaultValue": 50}, {"name": "spring.rabbitmq.connection-timeout", "type": "java.lang.Integer", "description": "连接超时时间（毫秒）", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties", "defaultValue": 60000}, {"name": "spring.rabbitmq.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用 RabbitMQ", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties", "defaultValue": false}, {"name": "spring.rabbitmq.host", "type": "java.lang.String", "description": "主机地址", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties", "defaultValue": "localhost"}, {"name": "spring.rabbitmq.listener.concurrency", "type": "java.lang.Integer", "description": "并发消费者数量", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Listener", "defaultValue": 1}, {"name": "spring.rabbitmq.listener.max-concurrency", "type": "java.lang.Integer", "description": "最大并发消费者数量", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Listener", "defaultValue": 10}, {"name": "spring.rabbitmq.listener.prefetch", "type": "java.lang.Integer", "description": "预取数量", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Listener", "defaultValue": 1}, {"name": "spring.rabbitmq.listener.retry.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用重试", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry", "defaultValue": true}, {"name": "spring.rabbitmq.listener.retry.initial-interval", "type": "java.lang.Long", "description": "初始重试间隔（毫秒）", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry", "defaultValue": 1000}, {"name": "spring.rabbitmq.listener.retry.max-attempts", "type": "java.lang.Integer", "description": "最大重试次数", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry", "defaultValue": 3}, {"name": "spring.rabbitmq.listener.retry.max-interval", "type": "java.lang.Long", "description": "最大重试间隔（毫秒）", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry", "defaultValue": 10000}, {"name": "spring.rabbitmq.listener.retry.multiplier", "type": "java.lang.Double", "description": "重试间隔倍数", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry", "defaultValue": 2}, {"name": "spring.rabbitmq.password", "type": "java.lang.String", "description": "密码", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties", "defaultValue": "guest"}, {"name": "spring.rabbitmq.port", "type": "java.lang.Integer", "description": "端口", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties", "defaultValue": 5672}, {"name": "spring.rabbitmq.retry.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用重试", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry", "defaultValue": true}, {"name": "spring.rabbitmq.retry.initial-interval", "type": "java.lang.Long", "description": "初始重试间隔（毫秒）", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry", "defaultValue": 1000}, {"name": "spring.rabbitmq.retry.max-attempts", "type": "java.lang.Integer", "description": "最大重试次数", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry", "defaultValue": 3}, {"name": "spring.rabbitmq.retry.max-interval", "type": "java.lang.Long", "description": "最大重试间隔（毫秒）", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry", "defaultValue": 10000}, {"name": "spring.rabbitmq.retry.multiplier", "type": "java.lang.Double", "description": "重试间隔倍数", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry", "defaultValue": 2}, {"name": "spring.rabbitmq.username", "type": "java.lang.String", "description": "用户名", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties", "defaultValue": "guest"}, {"name": "spring.rabbitmq.virtual-host", "type": "java.lang.String", "description": "虚拟主机", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties", "defaultValue": "/"}], "hints": []}