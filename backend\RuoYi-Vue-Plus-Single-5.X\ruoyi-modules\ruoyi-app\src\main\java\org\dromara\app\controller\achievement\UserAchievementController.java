package org.dromara.app.controller.achievement;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.vo.AchievementStatsVo;
import org.dromara.app.domain.vo.AchievementVo;
import org.dromara.app.domain.vo.UserAchievementVo;
import org.dromara.app.service.IAchievementService;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户成就控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/user/achievement")
@Tag(name = "用户成就管理", description = "用户成就相关接口")
public class UserAchievementController {

    private final IAchievementService achievementService;

    /**
     * 获取当前用户成就列表
     */
    @Operation(summary = "获取用户成就列表")
    @SaCheckPermission("app:user:achievement:list")
    @GetMapping("/list")
    public R<List<UserAchievementVo>> getUserAchievements() {
        String userId = LoginHelper.getUserId().toString();
        List<UserAchievementVo> result = achievementService.getUserAchievements(userId);
        return R.ok(result);
    }

    /**
     * 获取用户成就统计
     */
    @Operation(summary = "获取用户成就统计")
    @SaCheckPermission("app:user:achievement:stats")
    @GetMapping("/stats")
    public R<AchievementStatsVo> getAchievementStats() {
        String userId = LoginHelper.getUserId().toString();
        AchievementStatsVo result = achievementService.getAchievementStats(userId);
        return R.ok(result);
    }

    /**
     * 获取最近解锁的成就
     */
    @Operation(summary = "获取最近解锁的成就")
    @SaCheckPermission("app:user:achievement:recent")
    @GetMapping("/recent")
    public R<List<UserAchievementVo>> getRecentAchievements(
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "10") Integer limit) {
        String userId = LoginHelper.getUserId().toString();
        List<UserAchievementVo> result = achievementService.getRecentUserAchievements(userId, limit);
        return R.ok(result);
    }

    /**
     * 获取进行中的成就
     */
    @Operation(summary = "获取进行中的成就")
    @SaCheckPermission("app:user:achievement:progress")
    @GetMapping("/progress")
    public R<List<UserAchievementVo>> getInProgressAchievements() {
        String userId = LoginHelper.getUserId().toString();
        List<UserAchievementVo> result = achievementService.getInProgressAchievements(userId);
        return R.ok(result);
    }

    /**
     * 获取推荐成就
     */
    @Operation(summary = "获取推荐成就")
    @SaCheckPermission("app:user:achievement:recommend")
    @GetMapping("/recommend")
    public R<List<UserAchievementVo>> getRecommendedAchievements(
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "5") Integer limit) {
        String userId = LoginHelper.getUserId().toString();
        List<UserAchievementVo> result = achievementService.getRecommendedAchievements(userId, limit);
        return R.ok(result);
    }

    /**
     * 获取成就详情
     */
    @Operation(summary = "获取成就详情")
    @SaCheckPermission("app:user:achievement:detail")
    @GetMapping("/{achievementId}")
    public R<UserAchievementVo> getAchievementDetail(
            @Parameter(description = "成就ID") @PathVariable String achievementId) {
        String userId = LoginHelper.getUserId().toString();
        UserAchievementVo result = achievementService.getUserAchievementDetail(userId, achievementId);
        return R.ok(result);
    }

    /**
     * 手动检查并更新成就
     */
    @Operation(summary = "手动检查并更新成就")
    @SaCheckPermission("app:user:achievement:check")
    @Log(title = "手动检查成就", businessType = BusinessType.UPDATE)
    @PostMapping("/check")
    public R<List<AchievementVo>> checkAndUpdateAchievements() {
        String userId = LoginHelper.getUserId().toString();
        List<AchievementVo> result = achievementService.checkAndUpdateAchievements(userId);
        return R.ok(result);
    }

    /**
     * 初始化用户成就
     */
    @Operation(summary = "初始化用户成就")
    @SaCheckPermission("app:user:achievement:init")
    @Log(title = "初始化用户成就", businessType = BusinessType.INSERT)
    @PostMapping("/init")
    public R<Integer> initializeUserAchievements() {
        String userId = LoginHelper.getUserId().toString();
        int result = achievementService.initializeUserAchievements(userId);
        return R.ok(result);
    }

    /**
     * 分享成就墙
     */
    @Operation(summary = "分享成就墙")
    @SaCheckPermission("app:user:achievement:share")
    @Log(title = "分享成就墙", businessType = BusinessType.OTHER)
    @PostMapping("/share")
    public R<Map<String, Object>> shareAchievements(
            @Parameter(description = "分享平台") @RequestParam String platform) {
        String userId = LoginHelper.getUserId().toString();
        Map<String, Object> result = achievementService.shareAchievements(userId, platform);
        return R.ok(result);
    }

    /**
     * 获取成就分类列表
     */
    @Operation(summary = "获取成就分类列表")
    @SaCheckPermission("app:user:achievement:categories")
    @GetMapping("/categories")
    public R<Map<String, String>> getCategories() {
        Map<String, String> result = achievementService.getCategories();
        return R.ok(result);
    }

    /**
     * 获取用户成就完成度
     */
    @Operation(summary = "获取用户成就完成度")
    @SaCheckPermission("app:user:achievement:completion")
    @GetMapping("/completion")
    public R<Map<String, Object>> getUserAchievementCompletion() {
        String userId = LoginHelper.getUserId().toString();
        Map<String, Object> result = achievementService.getUserAchievementCompletion(userId);
        return R.ok(result);
    }

    /**
     * 重新计算用户成就进度
     */
    @Operation(summary = "重新计算用户成就进度")
    @SaCheckPermission("app:user:achievement:recalculate")
    @Log(title = "重新计算成就进度", businessType = BusinessType.UPDATE)
    @PostMapping("/recalculate")
    public R<Integer> recalculateUserProgress() {
        String userId = LoginHelper.getUserId().toString();
        int result = achievementService.recalculateUserProgress(userId);
        return R.ok(result);
    }

    /**
     * 获取事件统计
     */
    @Operation(summary = "获取事件统计")
    @SaCheckPermission("app:user:achievement:events")
    @GetMapping("/events/stats")
    public R<Map<String, Object>> getEventStatistics(
            @Parameter(description = "事件类型") @RequestParam(required = false) String eventType,
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        String userId = LoginHelper.getUserId().toString();
        Map<String, Object> result = achievementService.getEventStatistics(userId, eventType, days);
        return R.ok(result);
    }

    /**
     * 手动解锁成就（管理员功能）
     */
    @Operation(summary = "手动解锁成就")
    @SaCheckPermission("app:admin:achievement:unlock")
    @Log(title = "手动解锁成就", businessType = BusinessType.UPDATE)
    @PostMapping("/{achievementId}/unlock")
    public R<Boolean> unlockAchievement(
            @Parameter(description = "成就ID") @PathVariable String achievementId,
            @Parameter(description = "解锁来源") @RequestParam(defaultValue = "manual") String source) {
        String userId = LoginHelper.getUserId().toString();
        boolean result = achievementService.unlockAchievement(userId, achievementId, source);
        return R.ok(result);
    }

    /**
     * 记录用户事件（测试用）
     */
    @Operation(summary = "记录用户事件")
    @SaCheckPermission("app:user:achievement:event")
    @Log(title = "记录用户事件", businessType = BusinessType.INSERT)
    @PostMapping("/event")
    public R<Boolean> recordEvent(
            @Parameter(description = "事件类型") @RequestParam String eventType,
            @Parameter(description = "事件数据") @RequestBody(required = false) Map<String, Object> eventData,
            @Parameter(description = "事件值") @RequestParam(required = false) Integer eventValue,
            @Parameter(description = "关联对象ID") @RequestParam(required = false) String relatedId,
            @Parameter(description = "关联对象类型") @RequestParam(required = false) String relatedType) {
        String userId = LoginHelper.getUserId().toString();
        boolean result = achievementService.recordEvent(userId, eventType, eventData, eventValue, relatedId, relatedType);
        return R.ok(result);
    }

}
