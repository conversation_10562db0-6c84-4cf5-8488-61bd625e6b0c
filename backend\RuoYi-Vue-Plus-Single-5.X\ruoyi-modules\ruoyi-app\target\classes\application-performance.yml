# 性能优化配置
spring:
  # 数据库连接池优化
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      # 连接池配置
      maximum-pool-size: 20          # 最大连接数
      minimum-idle: 5                # 最小空闲连接数
      connection-timeout: 30000      # 连接超时时间(毫秒)
      idle-timeout: 600000           # 空闲连接超时时间(毫秒)
      max-lifetime: 1800000          # 连接最大生存时间(毫秒)
      validation-timeout: 5000       # 连接验证超时时间(毫秒)
      leak-detection-threshold: 60000 # 连接泄露检测阈值(毫秒)
      
      # 连接池性能配置
      pool-name: "HikariCP-Agent"
      connection-test-query: "SELECT 1"
      auto-commit: true
      
      # 数据库特定配置
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
        useLocalSessionState: true
        rewriteBatchedStatements: true
        cacheResultSetMetadata: true
        cacheServerConfiguration: true
        elideSetAutoCommits: true
        maintainTimeStats: false

  # JPA优化配置
  jpa:
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        # 查询优化
        jdbc:
          batch_size: 50
          fetch_size: 50
        # 缓存配置
        cache:
          use_second_level_cache: true
          use_query_cache: true
          region:
            factory_class: org.hibernate.cache.jcache.JCacheRegionFactory
        # 统计信息
        generate_statistics: false
        format_sql: false
        show_sql: false

  # Redis缓存优化
  data:
    redis:
      # 连接池配置
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 2
          max-wait: 5000ms
      # 超时配置
      timeout: 3000ms
      connect-timeout: 3000ms
      # 序列化配置
      serialization:
        key-serializer: org.springframework.data.redis.serializer.StringRedisSerializer
        value-serializer: org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer

# MyBatis Plus优化配置
mybatis-plus:
  configuration:
    # 缓存配置
    cache-enabled: true
    local-cache-scope: statement
    # 延迟加载
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
    # 执行器类型
    default-executor-type: reuse
    # 超时配置
    default-statement-timeout: 30
  global-config:
    db-config:
      # 逻辑删除
      logic-delete-field: delFlag
      logic-delete-value: 2
      logic-not-delete-value: 0

# 线程池配置
thread-pool:
  # 核心线程池
  core:
    core-pool-size: 10
    maximum-pool-size: 50
    keep-alive-time: 60
    queue-capacity: 200
    thread-name-prefix: "agent-core-"
  
  # 异步任务线程池
  async:
    core-pool-size: 5
    maximum-pool-size: 20
    keep-alive-time: 60
    queue-capacity: 100
    thread-name-prefix: "agent-async-"

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5,0.9,0.95,0.99

# 日志配置
logging:
  level:
    org.dromara.app: INFO
    org.springframework.web: WARN
    com.zaxxer.hikari: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 应用性能配置
app:
  performance:
    # 内存管理
    memory:
      chat-memory-expire-hours: 2
      max-chat-memory-count: 1000
      cleanup-interval-minutes: 30
    
    # SSE连接管理
    sse:
      max-connections: 500
      default-timeout-minutes: 30
      cleanup-interval-minutes: 5
    
    # 缓存配置
    cache:
      agent-cache-minutes: 10
      agent-type-cache-minutes: 30
      quick-actions-cache-minutes: 30
    
    # 限流配置
    rate-limit:
      enabled: true
      requests-per-minute: 60
      burst-capacity: 100
