{"doc": "\n 学习推荐服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "generateLearningPaths", "paramTypes": ["org.dromara.app.domain.InterviewReport"], "doc": "\n 基于面试报告生成学习路径推荐\r\n\r\n @param report 面试报告\r\n @return 学习路径推荐列表\r\n"}, {"name": "generatePathsByWeaknesses", "paramTypes": ["java.util.List", "java.lang.String"], "doc": "\n 基于弱项生成学习推荐\r\n\r\n @param weaknesses 弱项列表\r\n @param jobPosition 岗位信息\r\n @return 学习路径推荐列表\r\n"}, {"name": "generatePathsByJobPosition", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 基于岗位生成学习推荐\r\n\r\n @param jobPosition 岗位信息\r\n @param userLevel 用户水平\r\n @return 学习路径推荐列表\r\n"}, {"name": "generatePathsByDimensionScores", "paramTypes": ["java.util.List", "java.lang.String"], "doc": "\n 基于维度评分生成学习推荐\r\n\r\n @param dimensionScores 维度评分列表\r\n @param jobPosition 岗位信息\r\n @return 学习路径推荐列表\r\n"}, {"name": "getRecommendedResources", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 获取学习资源推荐\r\n\r\n @param skillArea 技能领域\r\n @param difficulty 难度等级\r\n @param resourceType 资源类型\r\n @return 学习资源列表\r\n"}, {"name": "calculateLearningPriorities", "paramTypes": ["java.util.List", "java.util.List", "java.lang.String"], "doc": "\n 计算学习路径优先级\r\n\r\n @param weaknesses 弱项列表\r\n @param dimensionScores 维度评分\r\n @param jobPosition 岗位信息\r\n @return 优先级映射\r\n"}, {"name": "personalizelearningPaths", "paramTypes": ["java.util.List", "org.dromara.app.service.ILearningRecommendationService.UserProfile"], "doc": "\n 个性化学习路径调整\r\n\r\n @param basePaths 基础学习路径\r\n @param userProfile 用户画像\r\n @return 调整后的学习路径\r\n"}, {"name": "getLearningPathDetail", "paramTypes": ["java.lang.String"], "doc": "\n 获取学习路径详情\r\n\r\n @param pathId 路径ID\r\n @return 学习路径详情\r\n"}], "constructors": []}