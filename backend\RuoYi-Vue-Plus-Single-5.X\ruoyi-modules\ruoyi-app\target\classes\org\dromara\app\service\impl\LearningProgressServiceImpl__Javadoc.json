{"doc": "\n 学习进度服务实现类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "updateLearningStatistics", "paramTypes": ["org.dromara.app.domain.LearningProgress", "java.lang.Integer"], "doc": "\n 更新学习统计\r\n"}, {"name": "updateLearningStatisticsOnCompletion", "paramTypes": ["org.dromara.app.domain.LearningProgress"], "doc": "\n 完成时更新学习统计\r\n"}, {"name": "calculateEfficiencyScore", "paramTypes": ["org.dromara.app.domain.LearningProgress"], "doc": "\n 计算学习效率评分\r\n"}, {"name": "calculateFinalEfficiencyScore", "paramTypes": ["org.dromara.app.domain.LearningProgress"], "doc": "\n 计算最终效率评分\r\n"}, {"name": "analyzeLearningPatterns", "paramTypes": ["java.lang.Long"], "doc": "\n 分析学习模式\r\n"}, {"name": "analyzeEfficiency", "paramTypes": ["java.lang.Long"], "doc": "\n 分析学习效率\r\n"}, {"name": "generateImprovementSuggestions", "paramTypes": ["java.lang.Long"], "doc": "\n 生成改进建议\r\n"}, {"name": "analyzeAchievements", "paramTypes": ["java.lang.Long"], "doc": "\n 分析学习成就\r\n"}], "constructors": []}