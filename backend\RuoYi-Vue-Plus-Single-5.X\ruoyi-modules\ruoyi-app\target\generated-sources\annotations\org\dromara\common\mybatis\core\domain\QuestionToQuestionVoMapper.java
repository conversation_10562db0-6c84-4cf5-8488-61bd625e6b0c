package org.dromara.common.mybatis.core.domain;

import io.github.linpeilie.AutoMapperConfig__162;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.QuestionBoToQuestionMapper;
import org.dromara.system.domain.vo.QuestionVo;
import org.dromara.system.domain.vo.QuestionVoToQuestionMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__162.class,
    uses = {QuestionBoToQuestionMapper.class,QuestionVoToQuestionMapper.class},
    imports = {}
)
public interface QuestionToQuestionVoMapper extends BaseMapper<Question, QuestionVo> {
}
