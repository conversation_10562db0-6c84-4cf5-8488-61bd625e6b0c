{"doc": " 用户活动总览Mapper接口\n\n <AUTHOR>\n @date 2025-07-16\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询活动总览\n\n @param userId 用户ID\n @return 活动总览\n"}, {"name": "updateSummary", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType", "java.lang.Long"], "doc": " 更新用户活动总览\n\n @param userId       用户ID\n @param activityType 活动类型\n @param duration     时长\n @return 更新行数\n"}, {"name": "resetSummary", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType"], "doc": " 重置用户活动总览\n\n @param userId       用户ID\n @param activityType 活动类型(可选，为空时重置所有)\n @return 更新行数\n"}], "constructors": []}