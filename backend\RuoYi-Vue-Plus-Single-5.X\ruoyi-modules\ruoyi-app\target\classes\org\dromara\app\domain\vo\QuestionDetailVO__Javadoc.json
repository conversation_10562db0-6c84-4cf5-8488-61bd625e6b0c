{"doc": "\n 题目详情VO\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 题目ID\r\n"}, {"name": "title", "doc": "\n 题目标题\r\n"}, {"name": "description", "doc": "\n 题目描述\r\n"}, {"name": "content", "doc": "\n 题目内容（Markdown格式）\r\n"}, {"name": "answer", "doc": "\n 参考答案（Markdown格式）\r\n"}, {"name": "analysis", "doc": "\n 答案解析（Markdown格式）\r\n"}, {"name": "difficulty", "doc": "\n 难度等级（简单/中等/困难）\r\n"}, {"name": "tags", "doc": "\n 标签列表\r\n"}, {"name": "acceptanceRate", "doc": "\n 通过率（百分比）\r\n"}, {"name": "isCompleted", "doc": "\n 是否已完成\r\n"}, {"name": "practiceCount", "doc": "\n 练习次数\r\n"}, {"name": "correctRate", "doc": "\n 正确率（百分比）\r\n"}, {"name": "commentCount", "doc": "\n 评论数\r\n"}, {"name": "isBookmarked", "doc": "\n 是否收藏\r\n"}, {"name": "questionType", "doc": "\n 题目类型（single-单选，multiple-多选，judge-判断，essay-简答，code-编程）\r\n"}, {"name": "options", "doc": "\n 题目选项（JSON格式，用于选择题）\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "doc": "\n 正确答案\r\n"}, {"name": "category", "doc": "\n 分类\r\n"}, {"name": "bankId", "doc": "\n 题库ID\r\n"}, {"name": "bankTitle", "doc": "\n 题库标题\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}], "enumConstants": [], "methods": [], "constructors": []}