<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.VectorEmbeddingMapper">

    <!-- 批量插入向量嵌入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO app_vector_embedding (
        id, tenant_id, knowledge_base_id, document_id, content, embedding,
        title, summary, position, content_length, chunk_type, model_name,
        dimension, metadata, tags, sort_order, remark, create_dept,
        create_by, create_time, update_by, update_time, del_flag, version
        ) VALUES
        <foreach collection="embeddings" item="item" separator=",">
            (
            #{item.id}, #{item.tenantId}, #{item.knowledgeBaseId}, #{item.documentId},
            #{item.content}, #{item.embedding}, #{item.title}, #{item.summary},
            #{item.position}, #{item.contentLength}, #{item.chunkType}, #{item.modelName},
            #{item.dimension}, #{item.metadata}, #{item.tags}, #{item.sortOrder},
            #{item.remark}, #{item.createDept}, #{item.createBy}, #{item.createTime},
            #{item.updateBy}, #{item.updateTime}, #{item.delFlag}, #{item.version}
            )
        </foreach>
    </insert>

    <!-- 根据文档ID删除向量 -->
    <delete id="deleteByDocumentId">
        UPDATE app_vector_embedding
        SET del_flag    = '2',
            update_time = CURRENT_TIMESTAMP
        WHERE document_id = #{documentId}
          AND del_flag = '0'
    </delete>

    <!-- 根据知识库ID删除向量 -->
    <delete id="deleteByKnowledgeBaseId">
        UPDATE app_vector_embedding
        SET del_flag    = '2',
            update_time = CURRENT_TIMESTAMP
        WHERE knowledge_base_id = #{knowledgeBaseId}
          AND del_flag = '0'
    </delete>

    <!-- 批量更新文档状态 -->
    <update id="updateStatusByIds">
        UPDATE app_knowledge_document
        SET status = #{status}, update_time = CURRENT_TIMESTAMP
        WHERE id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND del_flag = '0'
    </update>

</mapper>
