{"doc": "\n AI工具对象 app_ai_tool\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 工具ID\r\n"}, {"name": "name", "doc": "\n 工具名称\r\n"}, {"name": "displayName", "doc": "\n 工具显示名称\r\n"}, {"name": "description", "doc": "\n 工具描述\r\n"}, {"name": "category", "doc": "\n 工具分类：system/web/file/calculation/database/api\r\n"}, {"name": "icon", "doc": "\n 工具图标\r\n"}, {"name": "color", "doc": "\n 工具颜色\r\n"}, {"name": "functionDefinition", "doc": "\n 函数定义（JSON格式）\r\n"}, {"name": "parameterSchema", "doc": "\n 参数schema（JSON格式）\r\n"}, {"name": "implementationClass", "doc": "\n 实现类名\r\n"}, {"name": "toolConfig", "doc": "\n 工具配置（JSON格式）\r\n"}, {"name": "enabled", "doc": "\n 是否启用：0-禁用，1-启用\r\n"}, {"name": "isSystem", "doc": "\n 是否系统工具：0-自定义，1-系统内置\r\n"}, {"name": "permissionLevel", "doc": "\n 权限级别：0-公开，1-登录用户，2-特定权限\r\n"}, {"name": "requiredPermissions", "doc": "\n 所需权限\r\n"}, {"name": "usageCount", "doc": "\n 使用次数\r\n"}, {"name": "avgExecutionTime", "doc": "\n 平均执行时间（毫秒）\r\n"}, {"name": "successRate", "doc": "\n 成功率\r\n"}, {"name": "lastUsed", "doc": "\n 最后使用时间\r\n"}, {"name": "sortOrder", "doc": "\n 排序序号\r\n"}, {"name": "tags", "doc": "\n 标签，逗号分隔\r\n"}, {"name": "version", "doc": "\n 工具版本\r\n"}, {"name": "author", "doc": "\n 作者\r\n"}, {"name": "delFlag", "doc": "\n 删除标志\r\n"}, {"name": "functionDefinitionObject", "doc": "\n 函数定义对象（不存储到数据库）\r\n"}, {"name": "parameterSchemaObject", "doc": "\n 参数Schema对象（不存储到数据库）\r\n"}, {"name": "toolConfigObject", "doc": "\n 工具配置对象（不存储到数据库）\r\n"}, {"name": "tagList", "doc": "\n 标签列表（不存储到数据库）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}