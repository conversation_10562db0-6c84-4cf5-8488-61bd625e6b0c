{"doc": "\n 向量嵌入对象，用于知识库检索\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 唯一标识\r\n"}, {"name": "tenantId", "doc": "\n 租户ID\r\n"}, {"name": "knowledgeBaseId", "doc": "\n 知识库ID\r\n"}, {"name": "documentId", "doc": "\n 文档ID\r\n"}, {"name": "content", "doc": "\n 文档内容\r\n"}, {"name": "embedding", "doc": "\n 嵌入向量（PGvector格式）\r\n"}, {"name": "title", "doc": "\n 文档标题\r\n"}, {"name": "summary", "doc": "\n 摘要\r\n"}, {"name": "position", "doc": "\n 位置\r\n"}, {"name": "contentLength", "doc": "\n 内容长度\r\n"}, {"name": "chunkType", "doc": "\n 分块类型\r\n"}, {"name": "modelName", "doc": "\n 模型名称\r\n"}, {"name": "dimension", "doc": "\n 向量维度\r\n"}, {"name": "metadata", "doc": "\n 元数据\r\n"}, {"name": "tags", "doc": "\n 标签\r\n"}, {"name": "sortOrder", "doc": "\n 排序\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}, {"name": "createDept", "doc": "\n 创建部门\r\n"}, {"name": "createBy", "doc": "\n 创建者\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateBy", "doc": "\n 更新者\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}, {"name": "delFlag", "doc": "\n 删除标志\r\n"}, {"name": "version", "doc": "\n 版本号\r\n"}, {"name": "similarity", "doc": "\n 相似度得分（查询时使用）\r\n"}, {"name": "source", "doc": "\n 来源\r\n"}, {"name": "documentType", "doc": "\n 文档类型\r\n"}], "enumConstants": [], "methods": [], "constructors": []}