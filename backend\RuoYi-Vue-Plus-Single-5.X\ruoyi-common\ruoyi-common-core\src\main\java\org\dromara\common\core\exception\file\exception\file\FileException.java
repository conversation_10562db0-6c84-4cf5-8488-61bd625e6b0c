package org.dromara.common.core.exception.file.exception.file;

import org.dromara.common.core.exception.file.exception.base.BaseException;

import java.io.Serial;

/**
 * 文件信息异常类
 *
 * <AUTHOR>
 */
public class FileException extends BaseException {

    @Serial
    private static final long serialVersionUID = 1L;

    public FileException(String code, Object[] args) {
        super("file", code, args, null);
    }

}
