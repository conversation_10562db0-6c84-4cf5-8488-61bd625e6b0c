<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.UserGrowthProfileMapper">

    <resultMap type="org.dromara.app.domain.UserGrowthProfile" id="UserGrowthProfileResult">
        <result property="profileId" column="profile_id"/>
        <result property="userId" column="user_id"/>
        <result property="currentStage" column="current_stage"/>
        <result property="joinDate" column="join_date"/>
        <result property="lastActiveDate" column="last_active_date"/>
        <result property="totalInterviews" column="total_interviews"/>
        <result property="initialOverallScore" column="initial_overall_score"/>
        <result property="initialProfessionalKnowledge" column="initial_professional_knowledge"/>
        <result property="initialLogicalThinking" column="initial_logical_thinking"/>
        <result property="initialLanguageExpression" column="initial_language_expression"/>
        <result property="initialStressResistance" column="initial_stress_resistance"/>
        <result property="initialTeamCollaboration" column="initial_team_collaboration"/>
        <result property="initialInnovation" column="initial_innovation"/>
        <result property="currentOverallScore" column="current_overall_score"/>
        <result property="currentProfessionalKnowledge" column="current_professional_knowledge"/>
        <result property="currentLogicalThinking" column="current_logical_thinking"/>
        <result property="currentLanguageExpression" column="current_language_expression"/>
        <result property="currentStressResistance" column="current_stress_resistance"/>
        <result property="currentTeamCollaboration" column="current_team_collaboration"/>
        <result property="currentInnovation" column="current_innovation"/>
        <result property="improvementRate" column="improvement_rate"/>
        <result property="targetPosition" column="target_position"/>
        <result property="learningGoals" column="learning_goals"/>
        <result property="achievements" column="achievements"/>
        <result property="continuousLearningDays" column="continuous_learning_days"/>
        <result property="completedCourses" column="completed_courses"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectUserGrowthProfileVo">
        select profile_id,
               user_id,
               current_stage,
               join_date,
               last_active_date,
               total_interviews,
               initial_overall_score,
               initial_professional_knowledge,
               initial_logical_thinking,
               initial_language_expression,
               initial_stress_resistance,
               initial_team_collaboration,
               initial_innovation,
               current_overall_score,
               current_professional_knowledge,
               current_logical_thinking,
               current_language_expression,
               current_stress_resistance,
               current_team_collaboration,
               current_innovation,
               improvement_rate,
               target_position,
               learning_goals,
               achievements,
               continuous_learning_days,
               completed_courses,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from user_growth_profile
    </sql>

    <select id="selectProfileByUserId" resultMap="UserGrowthProfileResult">
        <include refid="selectUserGrowthProfileVo"/>
        where user_id = #{userId}
    </select>

    <update id="updateProfileByUserId" parameterType="org.dromara.app.domain.UserGrowthProfile">
        update user_growth_profile
        <trim prefix="SET" suffixOverrides=",">
            <if test="currentStage != null">current_stage = #{currentStage},</if>
            <if test="lastActiveDate != null">last_active_date = #{lastActiveDate},</if>
            <if test="totalInterviews != null">total_interviews = #{totalInterviews},</if>
            <if test="currentOverallScore != null">current_overall_score = #{currentOverallScore},</if>
            <if test="currentProfessionalKnowledge != null">current_professional_knowledge =
                #{currentProfessionalKnowledge},
            </if>
            <if test="currentLogicalThinking != null">current_logical_thinking = #{currentLogicalThinking},</if>
            <if test="currentLanguageExpression != null">current_language_expression = #{currentLanguageExpression},
            </if>
            <if test="currentStressResistance != null">current_stress_resistance = #{currentStressResistance},</if>
            <if test="currentTeamCollaboration != null">current_team_collaboration = #{currentTeamCollaboration},</if>
            <if test="currentInnovation != null">current_innovation = #{currentInnovation},</if>
            <if test="improvementRate != null">improvement_rate = #{improvementRate},</if>
            <if test="targetPosition != null">target_position = #{targetPosition},</if>
            <if test="learningGoals != null">learning_goals = #{learningGoals},</if>
            <if test="achievements != null">achievements = #{achievements},</if>
            <if test="continuousLearningDays != null">continuous_learning_days = #{continuousLearningDays},</if>
            <if test="completedCourses != null">completed_courses = #{completedCourses},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deleteByUserId">
        delete
        from user_growth_profile
        where user_id = #{userId}
    </delete>

    <select id="selectProfilesByCurrentStage" resultMap="UserGrowthProfileResult">
        <include refid="selectUserGrowthProfileVo"/>
        where current_stage = #{currentStage}
        order by create_time desc
    </select>

    <select id="selectProfilesStatistics" resultMap="UserGrowthProfileResult">
        <include refid="selectUserGrowthProfileVo"/>
        order by create_time desc
    </select>

    <update id="updateLastActiveTimeByUserId">
        update user_growth_profile
        set last_active_date = now(),
            update_time      = now()
        where user_id = #{userId}
    </update>

    <update id="incrementInterviewCountByUserId">
        update user_growth_profile
        set total_interviews = total_interviews + #{increment},
            update_time      = now()
        where user_id = #{userId}
    </update>

    <update id="updateContinuousLearningDaysByUserId">
        update user_growth_profile
        set continuous_learning_days = #{days},
            update_time              = now()
        where user_id = #{userId}
    </update>

    <update id="incrementCompletedCoursesByUserId">
        update user_growth_profile
        set completed_courses = completed_courses + #{increment},
            update_time       = now()
        where user_id = #{userId}
    </update>

</mapper>
