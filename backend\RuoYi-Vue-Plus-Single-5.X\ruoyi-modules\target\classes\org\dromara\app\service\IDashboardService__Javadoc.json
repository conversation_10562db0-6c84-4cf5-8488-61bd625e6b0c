{"doc": " 首页仪表盘服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getDashboardSummary", "paramTypes": ["java.lang.Long"], "doc": " 获取首页仪表盘汇总数据\n\n @param userId 用户ID\n @return 仪表盘汇总数据\n"}, {"name": "getUserAbilities", "paramTypes": ["java.lang.Long"], "doc": " 获取用户能力评估数据\n\n @param userId 用户ID\n @return 用户能力评估数据\n"}, {"name": "getStudyStats", "paramTypes": ["java.lang.Long"], "doc": " 获取学习统计数据\n\n @param userId 用户ID\n @return 学习统计数据\n"}, {"name": "getSmartTasks", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.String"], "doc": " 获取智能推荐任务\n\n @param userId 用户ID\n @param limit  任务数量限制\n @param type   任务类型\n @return 智能推荐任务列表\n"}, {"name": "getRecentInterviews", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": " 获取最近面试记录\n\n @param userId 用户ID\n @param limit  记录数量限制\n @param page   页码\n @return 最近面试记录列表\n"}, {"name": "updateTargetPosition", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 更新用户目标岗位\n\n @param userId         用户ID\n @param targetPosition 目标岗位\n @return 更新结果\n"}, {"name": "completeTask", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 标记任务为已完成\n\n @param userId 用户ID\n @param taskId 任务ID\n @return 更新结果\n"}, {"name": "getDashboardData", "paramTypes": ["java.lang.Long"], "doc": " 获取首页所有数据（聚合接口）\n\n @param userId 用户ID\n @return 所有数据\n"}], "constructors": []}