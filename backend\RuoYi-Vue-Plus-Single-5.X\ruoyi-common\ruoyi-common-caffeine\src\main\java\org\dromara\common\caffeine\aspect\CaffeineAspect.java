package org.dromara.common.caffeine.aspect;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.dromara.common.caffeine.annotation.CaffeineCache;
import org.dromara.common.caffeine.annotation.CaffeineEvict;
import org.dromara.common.caffeine.annotation.CaffeinePut;
import org.dromara.common.caffeine.annotation.TimeUnit;
import org.dromara.common.caffeine.core.CaffeineClient;
import org.dromara.common.caffeine.utils.CacheKeyGenerator;
import org.dromara.common.caffeine.utils.CaffeineSpelExpressionParser;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;

/**
 * Caffeine缓存注解切面
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@Order(1)
@RequiredArgsConstructor
public class CaffeineAspect {

    private final CaffeineClient caffeineClient;
    private final CacheKeyGenerator cacheKeyGenerator;
    private final CaffeineSpelExpressionParser spelExpressionParser;

    /**
     * @CaffeineCache注解处理
     */
    @Around("@annotation(caffeineCache)")
    public Object handleCaffeineCache(ProceedingJoinPoint joinPoint, CaffeineCache caffeineCache) throws Throwable {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        Object[] args = joinPoint.getArgs();
        Object target = joinPoint.getTarget();

        // 检查条件
        if (!checkCondition(caffeineCache.condition(), target, method, args)) {
            return joinPoint.proceed();
        }

        // 生成缓存键
        String cacheKey = generateCacheKey(caffeineCache.key(), caffeineCache.keyPrefix(), target, method, args);

        // 先尝试从缓存获取
        Object cachedResult = caffeineClient.get(cacheKey);
        if (cachedResult != null) {
            log.debug("缓存命中: {}", cacheKey);
            return cachedResult;
        }

        // 执行方法
        Object result = joinPoint.proceed();

        // 检查unless条件
        if (!checkCondition(caffeineCache.unless(), target, method, args, result)) {
            // 获取过期时间
            long ttlSeconds = parseTtl(caffeineCache.expire(), caffeineCache.timeUnit(), target, method, args, result);

            // 缓存结果
            if (ttlSeconds > 0) {
                caffeineClient.put(cacheKey, result, ttlSeconds);
                log.debug("缓存存储(TTL): {} -> {}, ttl={}s", cacheKey, result, ttlSeconds);
            } else {
                caffeineClient.put(cacheKey, result);
                log.debug("缓存存储: {} -> {}", cacheKey, result);
            }
        }

        return result;
    }

    /**
     * @CaffeinePut注解处理
     */
    @Around("@annotation(caffeinePut)")
    public Object handleCaffeinePut(ProceedingJoinPoint joinPoint, CaffeinePut caffeinePut) throws Throwable {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        Object[] args = joinPoint.getArgs();
        Object target = joinPoint.getTarget();

        // 执行方法
        Object result = joinPoint.proceed();

        // 检查条件
        if (checkCondition(caffeinePut.condition(), target, method, args, result)) {
            // 检查unless条件
            if (!checkCondition(caffeinePut.unless(), target, method, args, result)) {
                // 生成缓存键
                String cacheKey = generateCacheKey(caffeinePut.key(), caffeinePut.keyPrefix(), target, method, args);

                // 获取过期时间
                long ttlSeconds = parseTtl(caffeinePut.expire(), caffeinePut.timeUnit(), target, method, args, result);

                // 更新缓存
                if (ttlSeconds > 0) {
                    caffeineClient.put(cacheKey, result, ttlSeconds);
                    log.debug("缓存更新(TTL): {} -> {}, ttl={}s", cacheKey, result, ttlSeconds);
                } else {
                    caffeineClient.put(cacheKey, result);
                    log.debug("缓存更新: {} -> {}", cacheKey, result);
                }
            }
        }

        return result;
    }

    /**
     * @CaffeineEvict注解处理
     */
    @Around("@annotation(caffeineEvict)")
    public Object handleCaffeineEvict(ProceedingJoinPoint joinPoint, CaffeineEvict caffeineEvict) throws Throwable {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        Object[] args = joinPoint.getArgs();
        Object target = joinPoint.getTarget();

        try {
            // 如果是方法执行前清除缓存
            if (caffeineEvict.beforeInvocation()) {
                evictCache(caffeineEvict, target, method, args);
            }

            // 执行方法
            Object result = joinPoint.proceed();

            // 如果是方法执行后清除缓存
            if (!caffeineEvict.beforeInvocation()) {
                evictCache(caffeineEvict, target, method, args);
            }

            return result;
        } catch (Exception e) {
            // 如果方法执行失败，但是beforeInvocation=false，仍然清除缓存
            if (!caffeineEvict.beforeInvocation()) {
                evictCache(caffeineEvict, target, method, args);
            }
            throw e;
        }
    }

    /**
     * 清除缓存
     */
    private void evictCache(CaffeineEvict caffeineEvict, Object target, Method method, Object[] args) {
        // 检查条件
        if (!checkCondition(caffeineEvict.condition(), target, method, args)) {
            return;
        }

        if (caffeineEvict.allEntries()) {
            // 清除所有缓存
            caffeineClient.clear();
            log.debug("清除所有缓存");
        } else {
            // 清除指定键的缓存
            String cacheKey = generateCacheKey(caffeineEvict.key(), caffeineEvict.keyPrefix(), target, method, args);
            caffeineClient.evict(cacheKey);
            log.debug("清除缓存: {}", cacheKey);
        }
    }

    /**
     * 生成缓存键
     */
    private String generateCacheKey(String keyExpression, String keyPrefix, Object target, Method method, Object[] args) {
        String key;

        if (StringUtils.hasText(keyExpression)) {
            // 使用SpEL表达式生成键
            key = spelExpressionParser.parseExpression(keyExpression, target, method, args);
        } else {
            // 使用默认键生成器
            key = cacheKeyGenerator.generate(method, args);
        }

        // 添加前缀
        if (StringUtils.hasText(keyPrefix)) {
            key = keyPrefix + ":" + key;
        }

        return key;
    }

    /**
     * 检查条件
     */
    private boolean checkCondition(String condition, Object target, Method method, Object[] args) {
        return checkCondition(condition, target, method, args, null);
    }

    /**
     * 检查条件
     */
    private boolean checkCondition(String condition, Object target, Method method, Object[] args, Object result) {
        if (!StringUtils.hasText(condition)) {
            return true;
        }

        try {
            return spelExpressionParser.parseCondition(condition, target, method, args, result);
        } catch (Exception e) {
            log.warn("条件表达式解析失败: {}", condition, e);
            return true;
        }
    }

    /**
     * 解析过期时间
     *
     * @param expireExpression 过期时间表达式
     * @param timeUnit         时间单位
     * @param method           方法
     * @param args             参数
     * @param result           返回值
     * @return 过期时间（秒），-1表示使用默认配置，0表示永不过期
     */
    private long parseTtl(String expireExpression, TimeUnit timeUnit, Object target, Method method, Object[] args, Object result) {
        if (!StringUtils.hasText(expireExpression) || "-1".equals(expireExpression)) {
            return -1; // 使用默认配置
        }

        try {
            // 解析SpEL表达式
            String expireStr = spelExpressionParser.parseExpression(expireExpression, target, method, args, result);
            long duration = Long.parseLong(expireStr);

            if (duration <= 0) {
                return 0; // 永不过期
            }

            // 转换为秒
            return timeUnit.toSeconds(duration);
        } catch (Exception e) {
            log.warn("解析过期时间失败: {}, 使用默认配置", expireExpression, e);
            return -1;
        }
    }

}
