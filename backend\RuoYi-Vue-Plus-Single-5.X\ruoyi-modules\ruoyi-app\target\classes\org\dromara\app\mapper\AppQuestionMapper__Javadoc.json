{"doc": "\n 题目Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectQuestionsByCategory", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 根据题库ID和分类查询题目列表\r\n\r\n @param bankId   题库ID\r\n @param category 分类\r\n @return 题目列表\r\n"}, {"name": "selectRecommendedQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 根据题库ID查询推荐题目\r\n\r\n @param bankId 题库ID\r\n @param limit  数量限制\r\n @return 推荐题目列表\r\n"}, {"name": "selectCategoriesByBankId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据题库ID查询所有分类\r\n\r\n @param bankId 题库ID\r\n @return 分类列表\r\n"}], "constructors": []}