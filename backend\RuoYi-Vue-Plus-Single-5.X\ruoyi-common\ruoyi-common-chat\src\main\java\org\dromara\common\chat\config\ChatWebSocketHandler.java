package org.dromara.common.chat.config;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.chat.agent.AgentContext;
import org.dromara.common.chat.agent.AgentType;
import org.dromara.common.chat.service.LangChain4jChatService;
import org.dromara.common.chat.service.StreamingChatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * AI Agent聊天WebSocket处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ChatWebSocketHandler extends TextWebSocketHandler {

    // 会话管理
    private final ConcurrentMap<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, AgentContext> contexts = new ConcurrentHashMap<>();
    @Autowired
    private LangChain4jChatService chatService;
    @Autowired
    private StreamingChatService streamingChatService;

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        sessions.put(sessionId, session);
        log.info("WebSocket连接建立，会话ID: {}", sessionId);

        // 发送连接成功消息
        JSONObject response = new JSONObject();
        response.put("type", "connection");
        response.put("message", "连接成功");
        response.put("sessionId", sessionId);
        response.put("timestamp", System.currentTimeMillis());
        session.sendMessage(new TextMessage(response.toString()));
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String sessionId = session.getId();
        String payload = message.getPayload();

        if (StrUtil.isBlank(payload)) {
            return;
        }

        try {
            JSONObject request = JSONUtil.parseObj(payload);
            String type = request.getStr("type");
            String agentType = request.getStr("agentType", "GENERAL_CHAT");
            String userMessage = request.getStr("message");
            Long userId = request.getLong("userId", 1L);
            Boolean streaming = request.getBool("streaming", false);
            String provider = request.getStr("provider");

            // 获取或创建Agent上下文
            AgentContext context = contexts.computeIfAbsent(sessionId, k -> {
                AgentContext ctx = new AgentContext(sessionId, userId, AgentType.getByCode(agentType));
                // 设置额外参数
                if (request.containsKey("parameters")) {
                    JSONObject params = request.getJSONObject("parameters");
                    for (String key : params.keySet()) {
                        ctx.setParameter(key, params.get(key));
                    }
                }
                return ctx;
            });

            // 检查是否使用流式响应
            if (streaming != null && streaming) {
                // 处理流式响应
                handleStreamingMessage(context, userMessage, provider, session);
            } else {
                // 处理非流式响应
                String response = processMessage(context, type, request);

                // 发送响应
                JSONObject responseObj = new JSONObject();
                responseObj.put("type", "response");
                responseObj.put("agentType", agentType);
                responseObj.put("message", response);
                responseObj.put("timestamp", System.currentTimeMillis());

                session.sendMessage(new TextMessage(responseObj.toString()));
            }

        } catch (Exception e) {
            log.error("处理WebSocket消息出错: {}", e.getMessage(), e);
            sendErrorMessage(session, "处理消息出错: " + e.getMessage());
        }
    }

    /**
     * 处理流式消息
     */
    private void handleStreamingMessage(AgentContext context, String message, String provider, WebSocketSession session) {
        try {
            // 创建WebSocket流式处理器
            WebSocketStreamingHandler handler = new WebSocketStreamingHandler(session, context);

            // 发送开始事件
            handler.onStart();

            // 在新线程中处理流式响应
            new Thread(() -> {
                try {
                    // 模拟流式响应（实际应用中需要集成真正的LangChain4j流式API）
                    String response = processStreamingMessage(context, message, provider);

                    // 模拟逐token发送
                    String[] tokens = response.split("(?<=\\p{Punct})|(?=\\p{Punct})|\\s+");
                    for (String token : tokens) {
                        if (StrUtil.isNotBlank(token)) {
                            handler.onNext(token);
                            Thread.sleep(50); // 模拟流式延迟
                        }
                    }

                    handler.onComplete(response);

                } catch (Exception e) {
                    handler.onError(e);
                }
            }).start();

        } catch (Exception e) {
            log.error("处理流式消息失败: {}", e.getMessage(), e);
            sendErrorMessage(session, "流式处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理流式消息内容
     */
    private String processStreamingMessage(AgentContext context, String message, String provider) {
        // 根据Agent类型调用相应的服务方法
        switch (context.getAgentType()) {
            case INTERVIEWER:
                return chatService.conductInterview(context, message);
            case RESUME_ANALYZER:
                return chatService.analyzeResume(context, message);
            case SKILL_ASSESSOR:
                return chatService.assessSkill(context, "通用技能", message);
            case CAREER_ADVISOR:
                return chatService.provideCareerAdvice(context, message);
            case MOCK_INTERVIEWER:
                return chatService.conductMockInterview(context, message);
            case LEARNING_GUIDE:
                return chatService.provideLearningGuidance(context, message);
            case GENERAL_CHAT:
            default:
                return chatService.generalChat(context, message);
        }
    }

    /**
     * 处理非流式消息
     */
    private String processMessage(AgentContext context, String type, JSONObject request) {
        try {
            switch (type) {
                case "general_chat":
                    return chatService.generalChat(context, request.getStr("message"));

                case "interview":
                    return chatService.conductInterview(context, request.getStr("message"));

                case "generate_questions":
                    return chatService.generateInterviewQuestions(context,
                        request.getStr("position"), request.getInt("count", 5));

                case "evaluate_answer":
                    return chatService.evaluateInterviewAnswer(context,
                        request.getStr("question"), request.getStr("answer"));

                case "analyze_resume":
                    return chatService.analyzeResume(context, request.getStr("resume"));

                case "match_job":
                    return chatService.matchResumeWithJob(context,
                        request.getStr("resume"), request.getStr("jobRequirements"));

                case "skill_assessment":
                    return chatService.assessSkill(context,
                        request.getStr("skillArea"), request.getStr("answer"));

                case "generate_skill_test":
                    return chatService.generateSkillTest(context,
                        request.getStr("skillArea"), request.getStr("level"), request.getInt("count", 5));

                case "career_advice":
                    return chatService.provideCareerAdvice(context, request.getStr("query"));

                case "create_career_plan":
                    return chatService.createCareerPlan(context,
                        request.getStr("background"), request.getStr("goals"), request.getStr("timeFrame"));

                case "mock_interview":
                    return chatService.conductMockInterview(context, request.getStr("response"));

                case "design_interview_flow":
                    return chatService.designInterviewFlow(context,
                        request.getStr("position"), request.getStr("duration"), request.getStr("focus"));

                case "learning_guidance":
                    return chatService.provideLearningGuidance(context, request.getStr("query"));

                case "create_learning_plan":
                    return chatService.createLearningPlan(context,
                        request.getStr("goal"), request.getStr("currentLevel"),
                        request.getStr("timeAvailable"), request.getStr("preference"));

                default:
                    return chatService.generalChat(context, request.getStr("message"));
            }
        } catch (Exception e) {
            log.error("处理消息失败, 类型: {}, 错误: {}", type, e.getMessage(), e);
            return "抱歉，处理您的请求时出现了错误: " + e.getMessage();
        }
    }

    /**
     * 发送错误消息
     */
    private void sendErrorMessage(WebSocketSession session, String errorMessage) {
        try {
            JSONObject response = new JSONObject();
            response.put("type", "error");
            response.put("message", errorMessage);
            response.put("timestamp", System.currentTimeMillis());
            session.sendMessage(new TextMessage(response.toString()));
        } catch (IOException e) {
            log.error("发送错误消息失败: {}", e.getMessage());
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        String sessionId = session.getId();
        sessions.remove(sessionId);

        // 清理Agent上下文和缓存
        AgentContext context = contexts.remove(sessionId);
        if (context != null) {
            // 清理流式服务的会话内存
            if (streamingChatService != null) {
                streamingChatService.clearSessionMemory(sessionId);
            }
        }

        log.info("WebSocket连接关闭，会话ID: {}, 状态: {}", sessionId, status);
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String sessionId = session.getId();
        log.error("WebSocket传输错误，会话ID: {}, 错误: {}", sessionId, exception.getMessage(), exception);

        // 清理资源
        sessions.remove(sessionId);
        AgentContext context = contexts.remove(sessionId);
        if (context != null && streamingChatService != null) {
            streamingChatService.clearSessionMemory(sessionId);
        }
    }

    /**
     * 获取当前活跃连接数
     */
    public int getActiveConnectionCount() {
        return sessions.size();
    }

    /**
     * 获取指定会话
     */
    public WebSocketSession getSession(String sessionId) {
        return sessions.get(sessionId);
    }

    /**
     * 广播消息到所有连接
     */
    public void broadcastMessage(String message) {
        JSONObject broadcastMsg = new JSONObject();
        broadcastMsg.put("type", "broadcast");
        broadcastMsg.put("message", message);
        broadcastMsg.put("timestamp", System.currentTimeMillis());

        String msgText = broadcastMsg.toString();
        sessions.values().forEach(session -> {
            try {
                if (session.isOpen()) {
                    session.sendMessage(new TextMessage(msgText));
                }
            } catch (IOException e) {
                log.error("广播消息失败，会话: {}", session.getId(), e);
            }
        });
    }

    /**
     * WebSocket流式处理器
     */
    private class WebSocketStreamingHandler {
        private final WebSocketSession session;
        private final AgentContext context;

        public WebSocketStreamingHandler(WebSocketSession session, AgentContext context) {
            this.session = session;
            this.context = context;
        }

        public void onStart() {
            try {
                JSONObject startMsg = new JSONObject();
                startMsg.put("type", "stream_start");
                startMsg.put("sessionId", context.getSessionId());
                startMsg.put("agentType", context.getAgentType().getCode());
                startMsg.put("timestamp", System.currentTimeMillis());
                session.sendMessage(new TextMessage(startMsg.toString()));
                log.debug("发送流式开始消息, 会话: {}", context.getSessionId());
            } catch (IOException e) {
                log.error("发送流式开始消息失败: {}", e.getMessage());
            }
        }

        public void onNext(String token) {
            try {
                JSONObject tokenMsg = new JSONObject();
                tokenMsg.put("type", "stream_token");
                tokenMsg.put("sessionId", context.getSessionId());
                tokenMsg.put("token", token);
                tokenMsg.put("timestamp", System.currentTimeMillis());
                session.sendMessage(new TextMessage(tokenMsg.toString()));
                log.debug("发送流式token: {}, 会话: {}", token, context.getSessionId());
            } catch (IOException e) {
                log.error("发送流式token消息失败: {}", e.getMessage());
            }
        }

        public void onComplete(String fullMessage) {
            try {
                JSONObject completeMsg = new JSONObject();
                completeMsg.put("type", "stream_complete");
                completeMsg.put("sessionId", context.getSessionId());
                completeMsg.put("fullMessage", fullMessage);
                completeMsg.put("timestamp", System.currentTimeMillis());
                session.sendMessage(new TextMessage(completeMsg.toString()));
                log.info("流式响应完成, 会话: {}, 消息长度: {}", context.getSessionId(), fullMessage.length());
            } catch (IOException e) {
                log.error("发送流式完成消息失败: {}", e.getMessage());
            }
        }

        public void onError(Throwable error) {
            try {
                JSONObject errorMsg = new JSONObject();
                errorMsg.put("type", "stream_error");
                errorMsg.put("sessionId", context.getSessionId());
                errorMsg.put("error", error.getMessage());
                errorMsg.put("timestamp", System.currentTimeMillis());
                session.sendMessage(new TextMessage(errorMsg.toString()));
                log.error("流式响应错误, 会话: {}, 错误: {}", context.getSessionId(), error.getMessage());
            } catch (IOException e) {
                log.error("发送流式错误消息失败: {}", e.getMessage());
            }
        }
    }
}
