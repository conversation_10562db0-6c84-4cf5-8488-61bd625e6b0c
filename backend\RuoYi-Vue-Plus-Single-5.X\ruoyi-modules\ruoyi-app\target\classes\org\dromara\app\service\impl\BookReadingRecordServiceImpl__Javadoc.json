{"doc": "\n 书籍阅读记录Service业务层处理\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryByUserIdAndBookId", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 根据用户ID和书籍ID查询阅读记录\r\n"}, {"name": "queryUserReadingHistory", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.Long"], "doc": "\n 查询用户的阅读历史（分页）\r\n"}, {"name": "queryRecentReading", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询用户最近阅读的书籍\r\n"}, {"name": "queryUserReadingStats", "paramTypes": ["java.lang.Long"], "doc": "\n 统计用户阅读数据\r\n"}, {"name": "saveOrUpdateReadingRecord", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.util.Map"], "doc": "\n 保存或更新阅读记录\r\n"}, {"name": "mark<PERSON><PERSON><PERSON>erCompleted", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.String"], "doc": "\n 标记章节已完成\r\n"}, {"name": "markBookFinished", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 标记书籍已读完\r\n"}, {"name": "addReadingTime", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Integer"], "doc": "\n 增加阅读时长\r\n"}, {"name": "deleteReadingRecord", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 删除阅读记录\r\n"}, {"name": "processReadingRecordData", "paramTypes": ["org.dromara.app.domain.BookReadingRecord"], "doc": "\n 处理阅读记录数据转换\r\n"}, {"name": "convertMapToJson", "paramTypes": ["java.util.Map"], "doc": "\n 将Map转换为JSON字符串\r\n"}, {"name": "calculateReadingStreak", "paramTypes": ["java.util.List"], "doc": "\n 计算连续阅读天数\r\n"}], "constructors": []}