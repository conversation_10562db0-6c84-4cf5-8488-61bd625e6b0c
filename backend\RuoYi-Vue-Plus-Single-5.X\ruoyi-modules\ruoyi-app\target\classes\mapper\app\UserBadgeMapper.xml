<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.UserBadgeMapper">

    <resultMap type="org.dromara.app.domain.UserBadge" id="UserBadgeResult">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="badgeId" column="badge_id"/>
        <result property="unlocked" column="unlocked"/>
        <result property="unlockedAt" column="unlocked_at"/>
        <result property="isPinned" column="is_pinned"/>
        <result property="pinnedAt" column="pinned_at"/>
        <result property="pinnedSort" column="pinned_sort"/>
        <result property="notificationStatus" column="notification_status"/>
        <result property="isViewed" column="is_viewed"/>
        <result property="viewedAt" column="viewed_at"/>
        <result property="unlockChannel" column="unlock_channel"/>
        <result property="userAchievementId" column="user_achievement_id"/>
        <result property="extraData" column="extra_data"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <resultMap type="org.dromara.app.domain.vo.BadgeVo" id="BadgeVoResult">
        <id property="id" column="id"/>
        <result property="icon" column="icon"/>
        <result property="color" column="color"/>
        <result property="title" column="title"/>
        <result property="desc" column="description"/>
        <result property="unlocked" column="unlocked"/>
        <result property="unlockedAt" column="unlocked_at"/>
        <result property="isPinned" column="is_pinned"/>
        <result property="pinnedAt" column="pinned_at"/>
        <result property="category" column="category"/>
        <result property="rarity" column="rarity"/>
        <result property="achievementId" column="achievement_id"/>
        <result property="sort" column="sort"/>
    </resultMap>

    <sql id="selectUserBadgeVo">
        select
            ub.id, ub.user_id, ub.badge_id, ub.unlocked, ub.unlocked_at,
            ub.is_pinned, ub.pinned_at, ub.pinned_sort, ub.notification_status,
            ub.is_viewed, ub.viewed_at, ub.unlock_channel, ub.user_achievement_id,
            ub.extra_data, ub.create_by, ub.create_time, ub.update_by, ub.update_time, ub.remark
        from app_user_badge ub
    </sql>

    <!-- 获取用户所有徽章 -->
    <select id="selectUserBadges" parameterType="String" resultMap="BadgeVoResult">
        select
            b.id, b.icon, b.color, b.title, b.description, b.category, b.rarity,
            b.achievement_id, b.sort,
            coalesce(ub.unlocked, false) as unlocked,
            ub.unlocked_at,
            coalesce(ub.is_pinned, false) as is_pinned,
            ub.pinned_at
        from app_badge b
        left join app_user_badge ub on b.id = ub.badge_id and ub.user_id = #{userId}
        where b.is_enabled = true
        order by b.sort asc, b.create_time desc
    </select>

    <!-- 获取用户徽章详情 -->
    <select id="selectUserBadgeById" resultMap="BadgeVoResult">
        select
            b.id, b.icon, b.color, b.title, b.description, b.category, b.rarity,
            b.achievement_id, b.sort,
            coalesce(ub.unlocked, false) as unlocked,
            ub.unlocked_at,
            coalesce(ub.is_pinned, false) as is_pinned,
            ub.pinned_at
        from app_badge b
        left join app_user_badge ub on b.id = ub.badge_id and ub.user_id = #{userId}
        where b.id = #{badgeId} and b.is_enabled = true
    </select>

    <!-- 获取用户置顶徽章数量 -->
    <select id="countPinnedBadges" parameterType="String" resultType="long">
        select count(1) from app_user_badge
        where user_id = #{userId} and is_pinned = true
    </select>

    <!-- 更新徽章置顶状态 -->
    <update id="updatePinStatus">
        update app_user_badge
        set is_pinned = #{isPinned},
            pinned_at = #{pinnedAt},
            update_time = now()
        where user_id = #{userId} and badge_id = #{badgeId}
    </update>

    <!-- 获取用户置顶徽章 -->
    <select id="selectPinnedBadges" parameterType="String" resultMap="BadgeVoResult">
        select
            b.id, b.icon, b.color, b.title, b.description, b.category, b.rarity,
            b.achievement_id, b.sort,
            ub.unlocked,
            ub.unlocked_at,
            ub.is_pinned,
            ub.pinned_at
        from app_user_badge ub
        join app_badge b on ub.badge_id = b.id
        where ub.user_id = #{userId} and ub.is_pinned = true and ub.unlocked = true
        order by ub.pinned_at desc, ub.pinned_sort asc
    </select>

    <!-- 获取总徽章数 -->
    <select id="countTotalBadges" resultType="int">
        select count(1) from app_badge where is_enabled = true
    </select>

    <!-- 获取用户已解锁徽章数 -->
    <select id="countUnlockedBadges" parameterType="String" resultType="int">
        select count(1) from app_user_badge
        where user_id = #{userId} and unlocked = true
    </select>

    <!-- 根据用户ID和徽章ID获取用户徽章 -->
    <select id="selectByUserIdAndBadgeId" resultMap="UserBadgeResult">
        <include refid="selectUserBadgeVo"/>
        where ub.user_id = #{userId} and ub.badge_id = #{badgeId}
    </select>

    <!-- 获取用户最近解锁的徽章 -->
    <select id="selectRecentUnlockedBadges" resultMap="BadgeVoResult">
        select
            b.id, b.icon, b.color, b.title, b.description, b.category, b.rarity,
            b.achievement_id, b.sort,
            ub.unlocked,
            ub.unlocked_at,
            ub.is_pinned,
            ub.pinned_at
        from app_user_badge ub
        join app_badge b on ub.badge_id = b.id
        where ub.user_id = #{userId} and ub.unlocked = true
        order by ub.unlocked_at desc
        limit #{limit}
    </select>

    <!-- 更新徽章通知状态 -->
    <update id="updateNotificationStatus">
        update app_user_badge
        set notification_status = #{status},
            update_time = now()
        where user_id = #{userId} and unlocked = true and notification_status != #{status}
    </update>

    <!-- 标记徽章为已查看 -->
    <update id="markAsViewed">
        update app_user_badge
        set is_viewed = true,
            viewed_at = now(),
            update_time = now()
        where user_id = #{userId} and badge_id = #{badgeId}
    </update>

    <!-- 获取用户某类别已解锁徽章数 -->
    <select id="countUnlockedBadgesByCategory" resultType="int">
        select count(1)
        from app_user_badge ub
        join app_badge b on ub.badge_id = b.id
        where ub.user_id = #{userId} and ub.unlocked = true and b.category = #{category}
    </select>

    <!-- 获取用户某稀有度已解锁徽章数 -->
    <select id="countUnlockedBadgesByRarity" resultType="int">
        select count(1)
        from app_user_badge ub
        join app_badge b on ub.badge_id = b.id
        where ub.user_id = #{userId} and ub.unlocked = true and b.rarity = #{rarity}
    </select>

</mapper>
