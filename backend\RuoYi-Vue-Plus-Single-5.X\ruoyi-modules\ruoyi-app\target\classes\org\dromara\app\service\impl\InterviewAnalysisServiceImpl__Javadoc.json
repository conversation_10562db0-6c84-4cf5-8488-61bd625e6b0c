{"doc": "\n 面试分析服务实现\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "generateMockEmotionData", "paramTypes": [], "doc": "\n 生成模拟情绪数据（用于测试）\r\n"}, {"name": "findPrimaryEmotion", "paramTypes": ["cn.hutool.json.JSONObject"], "doc": "\n 找出主要情绪\r\n"}, {"name": "generateEmotionSuggestionText", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 根据情绪生成建议文本\r\n"}, {"name": "determineEmotionSuggestionLevel", "paramTypes": ["java.lang.String"], "doc": "\n 确定情绪建议的优先级\r\n"}, {"name": "buildSpeechSuggestionPrompt", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": "\n 构建语音建议的prompt\r\n"}, {"name": "buildComprehensiveSuggestionPrompt", "paramTypes": ["cn.hutool.json.JSONObject"], "doc": "\n 构建综合建议的prompt\r\n"}, {"name": "parseSuggestionFromAI", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 从AI响应中解析建议\r\n"}, {"name": "createErrorResult", "paramTypes": ["java.lang.String"], "doc": "\n 创建错误结果\r\n"}], "constructors": []}