package org.dromara.app.domain.vo;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 任务队列状态视图对象
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskQueueStatusVo {

    /**
     * 队列是否运行中
     */
    private Boolean isRunning;

    /**
     * 队列是否暂停
     */
    private Boolean isPaused;

    /**
     * 待处理任务数量
     */
    private Integer pendingTaskCount;

    /**
     * 正在执行任务数量
     */
    private Integer runningTaskCount;

    /**
     * 已完成任务数量
     */
    private Integer completedTaskCount;

    /**
     * 失败任务数量
     */
    private Integer failedTaskCount;

    /**
     * 队列最大容量
     */
    private Integer maxQueueCapacity;

    /**
     * 当前队列使用率（百分比）
     */
    private Double queueUsageRate;

    /**
     * 最大并发数
     */
    private Integer maxConcurrency;

    /**
     * 当前并发数
     */
    private Integer currentConcurrency;

    /**
     * 平均任务执行时间（秒）
     */
    private Double averageExecutionTime;

    /**
     * 任务成功率（百分比）
     */
    private Double taskSuccessRate;

    /**
     * 队列启动时间
     */
    private LocalDateTime startTime;

    /**
     * 最后活动时间
     */
    private LocalDateTime lastActivityTime;

    /**
     * 总处理任务数量
     */
    private Long totalProcessedTasks;

    /**
     * 今日处理任务数量
     */
    private Integer todayProcessedTasks;

    /**
     * 系统负载状态：low/medium/high
     */
    private String systemLoad;

    /**
     * 内存使用情况
     */
    private String memoryUsage;

    /**
     * CPU使用情况
     */
    private String cpuUsage;
}
