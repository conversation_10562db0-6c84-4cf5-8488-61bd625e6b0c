{"doc": "\n 书籍章节对象 book_chapters (MongoDB)\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 章节ID（MongoDB ObjectId）\r\n"}, {"name": "bookId", "doc": "\n 书籍ID\r\n"}, {"name": "title", "doc": "\n 章节标题\r\n"}, {"name": "content", "doc": "\n 章节内容（Markdown格式）\r\n"}, {"name": "chapterOrder", "doc": "\n 章节排序序号\r\n"}, {"name": "wordCount", "doc": "\n 字数统计\r\n"}, {"name": "readTime", "doc": "\n 预估阅读时间（分钟）\r\n"}, {"name": "isUnlocked", "doc": "\n 是否已解锁：0-未解锁，1-已解锁\r\n"}, {"name": "isPreview", "doc": "\n 是否为试读章节：0-否，1-是\r\n"}, {"name": "status", "doc": "\n 章节状态：0-草稿，1-已发布\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}, {"name": "createBy", "doc": "\n 创建者\r\n"}, {"name": "updateBy", "doc": "\n 更新者\r\n"}, {"name": "isCompleted", "doc": "\n 是否已完成（用户维度，不存储到数据库）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}