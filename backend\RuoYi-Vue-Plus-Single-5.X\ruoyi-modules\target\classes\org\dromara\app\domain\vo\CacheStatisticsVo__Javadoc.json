{"doc": " 缓存统计信息视图对象\n\n <AUTHOR>\n @date 2025-01-19\n", "fields": [{"name": "totalCacheCount", "doc": " 总缓存数量\n"}, {"name": "hitCount", "doc": " 缓存命中次数\n"}, {"name": "missCount", "doc": " 缓存未命中次数\n"}, {"name": "hitRate", "doc": " 缓存命中率（百分比）\n"}, {"name": "totalCacheSize", "doc": " 缓存总大小（字节）\n"}, {"name": "usedCacheSize", "doc": " 已使用缓存大小（字节）\n"}, {"name": "cacheUsageRate", "doc": " 缓存使用率（百分比）\n"}, {"name": "expiredCacheCount", "doc": " 过期缓存数量\n"}, {"name": "hotCacheCount", "doc": " 热点缓存数量\n"}, {"name": "coldCacheCount", "doc": " 冷缓存数量\n"}, {"name": "averageAccessTime", "doc": " 平均缓存访问时间（毫秒）\n"}, {"name": "writeCount", "doc": " 缓存写入次数\n"}, {"name": "deleteCount", "doc": " 缓存删除次数\n"}, {"name": "updateCount", "doc": " 缓存更新次数\n"}, {"name": "evictionCount", "doc": " 缓存驱逐次数\n"}, {"name": "lastStatisticsTime", "doc": " 最后统计时间\n"}, {"name": "categoryStatistics", "doc": " 缓存分类统计\n"}, {"name": "sizeDistribution", "doc": " 缓存大小分布\n"}, {"name": "accessFrequencyDistribution", "doc": " 缓存访问频率分布\n"}, {"name": "memoryUsage", "doc": " 内存使用情况\n"}, {"name": "healthStatus", "doc": " 缓存健康状态：healthy/warning/critical\n"}, {"name": "performanceMetrics", "doc": " 性能指标\n"}], "enumConstants": [], "methods": [], "constructors": []}