<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.ChatMessageMapper">

    <resultMap type="org.dromara.app.domain.ChatMessage" id="ChatMessageResult">
        <result property="id" column="id"/>
        <result property="sessionId" column="session_id"/>
        <result property="userId" column="user_id"/>
        <result property="role" column="role"/>
        <result property="content" column="content"/>
        <result property="messageType" column="message_type"/>
        <result property="attachments" column="attachments"/>
        <result property="status" column="status"/>
        <result property="errorMessage" column="error_message"/>
        <result property="metadata" column="metadata"/>
        <result property="parentMessageId" column="parent_message_id"/>
        <result property="messageIndex" column="message_index"/>
        <result property="isRead" column="is_read"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectChatMessageVo">
        select id,
               session_id,
               user_id,
               role,
               content,
               message_type,
               attachments,
               status,
               error_message,
               metadata,
               parent_message_id,
               message_index,
               is_read,
               create_by,
               create_time,
               update_by,
               update_time,
               del_flag
        from app_chat_message
    </sql>

    <select id="selectMessagesBySession" parameterType="String" resultMap="ChatMessageResult">
        <include refid="selectChatMessageVo"/>
        where session_id = #{sessionId} and del_flag = '0'
        order by create_time asc
        <if test="limit != null and limit > 0">
            limit #{limit}
        </if>
    </select>

    <select id="selectSessionMessages" resultMap="ChatMessageResult">
        <include refid="selectChatMessageVo"/>
        where session_id = #{sessionId} and user_id = #{userId} and del_flag = '0'
        order by create_time desc
    </select>

    <select id="selectRecentMessages" resultMap="ChatMessageResult">
        <include refid="selectChatMessageVo"/>
        where session_id = #{sessionId} and user_id = #{userId} and del_flag = '0'
        order by create_time desc
        limit #{limit}
    </select>

    <select id="selectMessagesByUser" parameterType="Long" resultMap="ChatMessageResult">
        <include refid="selectChatMessageVo"/>
        where user_id = #{userId} and del_flag = '0'
        <if test="startTime != null">
            and create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime}
        </if>
        order by create_time desc
        <if test="limit != null and limit > 0">
            limit #{limit}
        </if>
    </select>

    <select id="countSessionMessages" resultType="java.lang.Integer">
        select count(*)
        from app_chat_message
        where session_id = #{sessionId}
          and user_id = #{userId}
          and del_flag = '0'
    </select>

    <select id="searchMessages" resultMap="ChatMessageResult">
        <include refid="selectChatMessageVo"/>
        where del_flag = '0'
        <if test="userId != null">
            and user_id = #{userId}
        </if>
        <if test="sessionId != null and sessionId != ''">
            and session_id = #{sessionId}
        </if>
        <if test="keyword != null and keyword != ''">
            and content like concat('%', #{keyword}, '%')
        </if>
        <if test="startTime != null">
            and create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime}
        </if>
        order by create_time desc
    </select>

    <select id="getMessageStats" parameterType="Long" resultType="java.util.Map">
        select
        count(*) as totalMessages,
        sum(case when role = 'user' then 1 else 0 end) as userMessages,
        sum(case when role = 'assistant' then 1 else 0 end) as assistantMessages
        from app_chat_message
        where user_id = #{userId} and del_flag = '0'
        <if test="startTime != null">
            and create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime}
        </if>
    </select>

    <insert id="batchInsertMessages">
        insert into app_chat_message
        (id, session_id, user_id, role, content, message_type, attachments, status, error_message, metadata,
        parent_message_id, message_index, is_read, create_by, create_time, update_by, update_time, del_flag)
        values
        <foreach item="message" collection="messages" separator=",">
            (#{message.id}, #{message.sessionId}, #{message.userId}, #{message.role}, #{message.content},
            #{message.messageType}, #{message.attachments}, #{message.status}, #{message.errorMessage},
            #{message.metadata}, #{message.parentMessageId}, #{message.messageIndex}, #{message.isRead},
            #{message.createBy}, #{message.createTime}, #{message.updateBy}, #{message.updateTime}, #{message.delFlag})
        </foreach>
    </insert>

    <select id="searchMessages" resultMap="ChatMessageResult">
        <include refid="selectChatMessageVo"/>
        where del_flag = '0'
        <if test="userId != null">
            and user_id = #{userId}
        </if>
        <if test="sessionId != null and sessionId != ''">
            and session_id = #{sessionId}
        </if>
        <if test="keyword != null and keyword != ''">
            and content like concat('%', #{keyword}, '%')
        </if>
        <if test="startTime != null">
            and create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime}
        </if>
        order by create_time desc
    </select>

    <select id="getMessageStats" parameterType="Long" resultType="java.util.Map">
        select
        count(*) as totalMessages,
        sum(case when role = 'user' then 1 else 0 end) as userMessages,
        sum(case when role = 'assistant' then 1 else 0 end) as assistantMessages
        from app_chat_message
        where user_id = #{userId} and del_flag = '0'
        <if test="startTime != null">
            and create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime}
        </if>
    </select>

    <update id="markMessagesAsDeleted">
        update app_chat_message
        set is_deleted = 1, update_time = now()
        where session_id = #{sessionId}
        <if test="messageIds != null and messageIds.size() > 0">
            and id in
            <foreach item="messageId" collection="messageIds" open="(" separator="," close=")">
                #{messageId}
            </foreach>
        </if>
    </update>

    <delete id="deleteMessagesBySession" parameterType="String">
        delete
        from app_chat_message
        where session_id = #{sessionId}
    </delete>

</mapper>
