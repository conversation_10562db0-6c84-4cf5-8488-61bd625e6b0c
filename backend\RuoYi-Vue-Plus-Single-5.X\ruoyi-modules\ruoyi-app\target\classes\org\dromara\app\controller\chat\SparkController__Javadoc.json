{"doc": "\n 讯飞星火大模型聊天控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "chat", "paramTypes": ["org.dromara.app.controller.chat.SparkController.ChatRequest"], "doc": "\n 聊天接口\r\n"}, {"name": "chatStream", "paramTypes": ["org.dromara.app.controller.chat.SparkController.ChatRequest"], "doc": "\n 流式聊天接口\r\n"}, {"name": "agent<PERSON><PERSON>", "paramTypes": ["org.dromara.app.controller.chat.SparkController.AgentChatRequest"], "doc": "\n 智能体聊天接口\r\n"}, {"name": "agentChatStream", "paramTypes": ["org.dromara.app.controller.chat.SparkController.AgentChatRequest"], "doc": "\n 智能体流式聊天接口\r\n"}, {"name": "invokeStreamingAgentService", "paramTypes": ["org.dromara.app.service.impl.XunfeiServiceImpl.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String", "org.dromara.app.service.impl.XunfeiServiceImpl.StreamingChatResponseHandler"], "doc": "\n 调用流式Agent服务\r\n"}, {"name": "buildContext", "paramTypes": ["org.dromara.app.controller.chat.SparkController.ChatRequest"], "doc": "\n 构建上下文\r\n"}], "constructors": []}