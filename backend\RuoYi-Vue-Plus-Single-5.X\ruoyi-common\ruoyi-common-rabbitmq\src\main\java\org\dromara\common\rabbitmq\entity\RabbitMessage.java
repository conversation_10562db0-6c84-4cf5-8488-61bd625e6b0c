package org.dromara.common.rabbitmq.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * RabbitMQ 消息实体
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RabbitMessage<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 消息内容
     */
    private T content;

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 消息来源
     */
    private String source;

    /**
     * 附加信息
     */
    private Map<String, Object> extras;

    /**
     * 创建消息
     *
     * @param content 消息内容
     * @param <T>     内容类型
     * @return 消息对象
     */
    public static <T> RabbitMessage<T> create(T content) {
        return new RabbitMessage<T>()
            .setContent(content)
            .setSendTime(new Date())
            .setRetryCount(0);
    }
}
