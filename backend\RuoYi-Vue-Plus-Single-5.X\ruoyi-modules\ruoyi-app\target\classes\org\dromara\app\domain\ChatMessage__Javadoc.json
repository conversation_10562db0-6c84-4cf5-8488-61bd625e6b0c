{"doc": "\n 聊天消息对象 app_chat_message\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 消息ID\r\n"}, {"name": "sessionId", "doc": "\n 会话ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "role", "doc": "\n 消息角色：user/assistant/system\r\n"}, {"name": "content", "doc": "\n 消息内容\r\n"}, {"name": "messageType", "doc": "\n 消息类型：text/image/file/voice\r\n"}, {"name": "attachments", "doc": "\n 附件信息（JSON格式）\r\n"}, {"name": "status", "doc": "\n 消息状态：0-发送中，1-发送成功，2-发送失败\r\n"}, {"name": "errorMessage", "doc": "\n 错误信息（如果发送失败）\r\n"}, {"name": "metadata", "doc": "\n 消息元数据（JSON格式，存储额外信息如模型名称、token消耗等）\r\n"}, {"name": "parentMessageId", "doc": "\n 父消息ID（用于消息回复链）\r\n"}, {"name": "messageIndex", "doc": "\n 消息序号（在会话中的顺序）\r\n"}, {"name": "isRead", "doc": "\n 是否已读\r\n"}, {"name": "attachmentList", "doc": "\n 附件列表（不存储到数据库，用于返回给前端）\r\n"}, {"name": "metadataObject", "doc": "\n 消息元数据对象（不存储到数据库）\r\n"}, {"name": "delFlag", "doc": "\n 删除标志（0-正常，1-删除）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}