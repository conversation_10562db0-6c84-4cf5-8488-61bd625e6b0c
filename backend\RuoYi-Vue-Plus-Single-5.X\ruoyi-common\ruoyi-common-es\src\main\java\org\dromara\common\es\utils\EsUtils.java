package org.dromara.common.es.utils;

import cn.hutool.core.collection.CollUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.es.core.EsClient;
import org.dromara.common.es.entity.EsPage;
import org.dromara.common.es.entity.SearchRequest;

import java.util.List;
import java.util.Map;

/**
 * ES工具类
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class EsUtils {

    private static final EsClient ES_CLIENT = SpringUtils.getBean(EsClient.class);

    /**
     * 创建索引
     *
     * @param indexName 索引名称
     * @return 是否成功
     */
    public static boolean createIndex(String indexName) {
        return createIndex(indexName, null);
    }

    /**
     * 创建索引
     *
     * @param indexName 索引名称
     * @param mapping   映射配置
     * @return 是否成功
     */
    public static boolean createIndex(String indexName, String mapping) {
        return ES_CLIENT.createIndex(indexName, mapping);
    }

    /**
     * 删除索引
     *
     * @param indexName 索引名称
     * @return 是否成功
     */
    public static boolean deleteIndex(String indexName) {
        return ES_CLIENT.deleteIndex(indexName);
    }

    /**
     * 判断索引是否存在
     *
     * @param indexName 索引名称
     * @return 是否存在
     */
    public static boolean existsIndex(String indexName) {
        return ES_CLIENT.existsIndex(indexName);
    }

    /**
     * 添加文档
     *
     * @param indexName 索引名称
     * @param document  文档内容
     * @return 是否成功
     */
    public static boolean addDocument(String indexName, Object document) {
        return addDocument(indexName, null, document);
    }

    /**
     * 添加文档
     *
     * @param indexName 索引名称
     * @param id        文档ID
     * @param document  文档内容
     * @return 是否成功
     */
    public static boolean addDocument(String indexName, String id, Object document) {
        return ES_CLIENT.addDocument(indexName, id, document);
    }

    /**
     * 批量添加文档
     *
     * @param indexName 索引名称
     * @param documents 文档列表
     * @return 是否成功
     */
    public static boolean batchAddDocuments(String indexName, List<Map<String, Object>> documents) {
        return ES_CLIENT.batchAddDocuments(indexName, documents);
    }

    /**
     * 更新文档
     *
     * @param indexName 索引名称
     * @param id        文档ID
     * @param document  更新内容
     * @return 是否成功
     */
    public static boolean updateDocument(String indexName, String id, Object document) {
        return ES_CLIENT.updateDocument(indexName, id, document);
    }

    /**
     * 删除文档
     *
     * @param indexName 索引名称
     * @param id        文档ID
     * @return 是否成功
     */
    public static boolean deleteDocument(String indexName, String id) {
        return ES_CLIENT.deleteDocument(indexName, id);
    }

    /**
     * 根据ID获取文档
     *
     * @param indexName 索引名称
     * @param id        文档ID
     * @return 文档内容
     */
    public static Map<String, Object> getDocument(String indexName, String id) {
        return ES_CLIENT.getDocument(indexName, id);
    }

    /**
     * 搜索文档
     *
     * @param indexName 索引名称
     * @param keyword   关键字
     * @return 搜索结果
     */
    public static EsPage<Map<String, Object>> search(String indexName, String keyword) {
        SearchRequest searchRequest = new SearchRequest()
            .setIndexName(indexName)
            .setKeyword(keyword);
        return ES_CLIENT.search(searchRequest);
    }

    /**
     * 搜索文档
     *
     * @param indexName 索引名称
     * @param keyword   关键字
     * @param pageNum   页码
     * @param pageSize  页大小
     * @return 搜索结果
     */
    public static EsPage<Map<String, Object>> search(String indexName, String keyword,
                                                     Integer pageNum, Integer pageSize) {
        SearchRequest searchRequest = new SearchRequest()
            .setIndexName(indexName)
            .setKeyword(keyword)
            .setPageNum(pageNum)
            .setPageSize(pageSize);
        return ES_CLIENT.search(searchRequest);
    }

    /**
     * 搜索文档
     *
     * @param indexName 索引名称
     * @param keyword   关键字
     * @param fields    查询字段
     * @return 搜索结果
     */
    public static EsPage<Map<String, Object>> search(String indexName, String keyword, List<String> fields) {
        SearchRequest searchRequest = new SearchRequest()
            .setIndexName(indexName)
            .setKeyword(keyword)
            .setFields(fields);
        return ES_CLIENT.search(searchRequest);
    }

    /**
     * 搜索文档
     *
     * @param searchRequest 搜索请求
     * @return 搜索结果
     */
    public static EsPage<Map<String, Object>> search(SearchRequest searchRequest) {
        return ES_CLIENT.search(searchRequest);
    }

    /**
     * 高亮搜索
     *
     * @param indexName       索引名称
     * @param keyword         关键字
     * @param highlightFields 高亮字段
     * @return 搜索结果
     */
    public static EsPage<Map<String, Object>> searchWithHighlight(String indexName, String keyword,
                                                                  List<String> highlightFields) {
        SearchRequest searchRequest = new SearchRequest()
            .setIndexName(indexName)
            .setKeyword(keyword)
            .setHighlightFields(highlightFields);
        return ES_CLIENT.search(searchRequest);
    }

    /**
     * 过滤搜索
     *
     * @param indexName 索引名称
     * @param filters   过滤条件
     * @return 搜索结果
     */
    public static EsPage<Map<String, Object>> searchWithFilters(String indexName,
                                                                Map<String, Object> filters) {
        SearchRequest searchRequest = new SearchRequest()
            .setIndexName(indexName)
            .setFilters(filters);
        return ES_CLIENT.search(searchRequest);
    }

    /**
     * 聚合搜索
     *
     * @param indexName    索引名称
     * @param aggregations 聚合配置
     * @return 搜索结果
     */
    public static EsPage<Map<String, Object>> searchWithAggregations(String indexName,
                                                                     List<SearchRequest.AggregationConfig> aggregations) {
        SearchRequest searchRequest = new SearchRequest()
            .setIndexName(indexName)
            .setAggregations(aggregations);
        return ES_CLIENT.search(searchRequest);
    }

    /**
     * 构建搜索请求
     *
     * @param indexName 索引名称
     * @return 搜索请求构建器
     */
    public static SearchRequestBuilder builder(String indexName) {
        return new SearchRequestBuilder(indexName);
    }

    /**
     * 搜索请求构建器
     */
    public static class SearchRequestBuilder {
        private final SearchRequest searchRequest;

        public SearchRequestBuilder(String indexName) {
            this.searchRequest = new SearchRequest().setIndexName(indexName);
        }

        public SearchRequestBuilder keyword(String keyword) {
            this.searchRequest.setKeyword(keyword);
            return this;
        }

        public SearchRequestBuilder fields(List<String> fields) {
            this.searchRequest.setFields(fields);
            return this;
        }

        public SearchRequestBuilder filters(Map<String, Object> filters) {
            this.searchRequest.setFilters(filters);
            return this;
        }

        public SearchRequestBuilder highlight(List<String> highlightFields) {
            this.searchRequest.setHighlightFields(highlightFields);
            return this;
        }

        public SearchRequestBuilder page(Integer pageNum, Integer pageSize) {
            this.searchRequest.setPageNum(pageNum).setPageSize(pageSize);
            return this;
        }

        public SearchRequestBuilder sort(String field, String order) {
            List<SearchRequest.SortField> sorts = this.searchRequest.getSorts();
            if (sorts == null) {
                sorts = CollUtil.newArrayList();
                this.searchRequest.setSorts(sorts);
            }
            sorts.add(new SearchRequest.SortField().setField(field).setOrder(order));
            return this;
        }

        public SearchRequestBuilder include(List<String> includeFields) {
            this.searchRequest.setIncludeFields(includeFields);
            return this;
        }

        public SearchRequestBuilder exclude(List<String> excludeFields) {
            this.searchRequest.setExcludeFields(excludeFields);
            return this;
        }

        public EsPage<Map<String, Object>> execute() {
            return ES_CLIENT.search(this.searchRequest);
        }
    }

}
