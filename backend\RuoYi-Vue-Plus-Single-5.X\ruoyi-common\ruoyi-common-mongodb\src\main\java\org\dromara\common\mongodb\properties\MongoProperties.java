package org.dromara.common.mongodb.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * MongoDB配置属性
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "spring.data.mongodb")
public class MongoProperties {

    /**
     * MongoDB连接字符串
     */
    private String uri = "mongodb://localhost:27017/dromara";

    /**
     * 数据库名称
     */
    private String database = "dromara";

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 主机地址
     */
    private String host = "localhost";

    /**
     * 端口号
     */
    private int port = 27017;

    /**
     * 认证数据库
     */
    private String authenticationDatabase;

    /**
     * 连接池配置
     */
    private Pool pool = new Pool();

    /**
     * 连接池配置
     */
    @Data
    public static class Pool {
        /**
         * 最大连接数
         */
        private int maxSize = 100;

        /**
         * 最小连接数
         */
        private int minSize = 10;

        /**
         * 连接超时时间(毫秒)
         */
        private long maxConnectionTimeoutMs = 30000;

        /**
         * 读取超时时间(毫秒)
         */
        private long maxReadTimeoutMs = 15000;

        /**
         * 最大等待时间(毫秒)
         */
        private long maxWaitTimeMs = 30000;

        /**
         * 连接最大空闲时间(毫秒)
         */
        private long maxConnectionIdleTimeMs = 600000;

        /**
         * 连接最大生存时间(毫秒)
         */
        private long maxConnectionLifeTimeMs = 0;
    }
}
