package org.dromara.app.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 面试系统配置
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app.interview")
public class InterviewConfig {

    /**
     * 会话配置
     */
    private Session session = new Session();

    /**
     * 搜索配置
     */
    private Search search = new Search();

    /**
     * 设备检测配置
     */
    private Device device = new Device();

    /**
     * 排序配置
     */
    private Sort sort = new Sort();

    /**
     * 会话配置
     */
    @Data
    public static class Session {
        /**
         * 默认过期时间（小时）
         */
        private Integer defaultExpirationHours = 2;

        /**
         * 最大会话数量（每个用户）
         */
        private Integer maxSessionsPerUser = 5;

        /**
         * 会话清理间隔（分钟）
         */
        private Integer cleanupIntervalMinutes = 30;
    }

    /**
     * 搜索配置
     */
    @Data
    public static class Search {
        /**
         * 默认搜索建议数量
         */
        private Integer defaultSuggestionLimit = 5;

        /**
         * 热门关键词数量
         */
        private Integer hotKeywordLimit = 10;

        /**
         * 最近搜索记录数量
         */
        private Integer recentSearchLimit = 5;

        /**
         * 关键词最小长度
         */
        private Integer minKeywordLength = 2;

        /**
         * 关键词最大长度
         */
        private Integer maxKeywordLength = 50;
    }

    /**
     * 设备检测配置
     */
    @Data
    public static class Device {
        /**
         * 是否启用设备检测
         */
        private Boolean enabled = true;

        /**
         * 检测超时时间（秒）
         */
        private Integer timeoutSeconds = 10;

        /**
         * 模拟检测失败概率（用于测试）
         */
        private Double mockFailureRate = 0.1;
    }

    /**
     * 排序配置
     */
    @Data
    public static class Sort {
        /**
         * 智能排序权重配置
         */
        private SmartSort smartSort = new SmartSort();

        /**
         * 默认排序方式
         */
        private String defaultSortBy = "smart";

        /**
         * 默认排序顺序
         */
        private String defaultSortOrder = "desc";
    }

    /**
     * 智能排序权重配置
     */
    @Data
    public static class SmartSort {
        /**
         * 热度权重
         */
        private Double hotWeight = 0.3;

        /**
         * 通过率权重
         */
        private Double passRateWeight = 0.25;

        /**
         * 难度权重
         */
        private Double difficultyWeight = 0.2;

        /**
         * 时长权重
         */
        private Double durationWeight = 0.15;

        /**
         * 题目数量权重
         */
        private Double questionCountWeight = 0.1;

        /**
         * 最佳通过率
         */
        private Double optimalPassRate = 65.0;

        /**
         * 最佳难度
         */
        private Integer optimalDifficulty = 3;

        /**
         * 最佳时长
         */
        private Double optimalDuration = 37.5;

        /**
         * 最佳题目数量
         */
        private Double optimalQuestionCount = 12.5;
    }

}
