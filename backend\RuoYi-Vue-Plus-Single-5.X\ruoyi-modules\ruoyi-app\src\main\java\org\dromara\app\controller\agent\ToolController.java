package org.dromara.app.controller.agent;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.AiTool;
import org.dromara.app.domain.ToolCall;
import org.dromara.app.service.IToolService;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * AI工具管理控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/tool")
@Tag(name = "AI工具管理", description = "AI工具管理相关接口")
public class ToolController extends BaseController {

    private final IToolService toolService;

    /**
     * 获取可用工具列表
     */
    @Operation(summary = "获取可用工具列表")
    @GetMapping("/available")
    public R<List<AiTool>> getAvailableTools(
        @Parameter(description = "工具分类") @RequestParam(required = false) String category) {
        Long userId = LoginHelper.getUserId();
        List<AiTool> tools = toolService.getAvailableTools(category, userId);
        return R.ok(tools);
    }

    /**
     * 获取工具详情
     */
    @Operation(summary = "获取工具详情")
    @GetMapping("/{toolId}")
    public R<AiTool> getToolDetail(@PathVariable String toolId) {
        Long userId = LoginHelper.getUserId();
        AiTool tool = toolService.getToolDetail(toolId, userId);
        if (tool == null) {
            return R.fail("工具不存在或无权限访问");
        }
        return R.ok(tool);
    }

    /**
     * 执行工具调用
     */
    @Operation(summary = "执行工具调用")
    @PostMapping("/execute")
    public R<Object> executeTool(@RequestBody ToolExecuteRequest request) {
        Long userId = LoginHelper.getUserId();

        // 创建调用上下文
        IToolService.ToolCallContext context = new IToolService.ToolCallContext();
        context.setSessionId(request.getSessionId());
        context.setMessageId(request.getMessageId());
        context.setUserQuery(request.getUserQuery());
        context.setEnvironment(request.getEnvironment());

        IToolService.ToolCallResult result = toolService.executeTool(
            request.getToolId(),
            request.getParameters(),
            context,
            userId
        );

        if (result.isSuccess()) {
            return R.ok(result.getData());
        } else {
            return R.fail(result.getMessage());
        }
    }

    /**
     * 异步执行工具调用
     */
    @Operation(summary = "异步执行工具调用")
    @PostMapping("/execute/async")
    public R<String> executeToolAsync(@RequestBody ToolExecuteRequest request) {
        Long userId = LoginHelper.getUserId();

        // 创建调用上下文
        IToolService.ToolCallContext context = new IToolService.ToolCallContext();
        context.setSessionId(request.getSessionId());
        context.setMessageId(request.getMessageId());
        context.setUserQuery(request.getUserQuery());
        context.setEnvironment(request.getEnvironment());

        String callId = toolService.executeToolAsync(
            request.getToolId(),
            request.getParameters(),
            context,
            userId
        );

        if (StrUtil.isNotBlank(callId)) {
            return R.ok(callId, "异步调用已启动");
        } else {
            return R.fail("启动异步调用失败");
        }
    }

    /**
     * 批量执行工具调用
     */
    @Operation(summary = "批量执行工具调用")
    @PostMapping("/execute/batch")
    public R<List<IToolService.ToolCallResult>> executeBatchTools(@RequestBody BatchToolExecuteRequest request) {
        Long userId = LoginHelper.getUserId();

        List<IToolService.BatchToolCall> batchCalls = request.getToolCalls().stream()
            .map(req -> {
                IToolService.BatchToolCall batchCall = new IToolService.BatchToolCall();
                batchCall.setToolId(req.getToolId());
                batchCall.setParameters(req.getParameters());

                IToolService.ToolCallContext context = new IToolService.ToolCallContext();
                context.setSessionId(req.getSessionId());
                context.setMessageId(req.getMessageId());
                context.setUserQuery(req.getUserQuery());
                context.setEnvironment(req.getEnvironment());
                batchCall.setContext(context);

                return batchCall;
            })
            .collect(java.util.stream.Collectors.toList());

        List<IToolService.ToolCallResult> results = toolService.executeBatchTools(batchCalls, userId);
        return R.ok(results);
    }

    /**
     * 验证工具调用参数
     */
    @Operation(summary = "验证工具调用参数")
    @PostMapping("/validate")
    public R<IToolService.ParameterValidationResult> validateParameters(@RequestBody ParameterValidationRequest request) {
        IToolService.ParameterValidationResult result = toolService.validateParameters(
            request.getToolId(),
            request.getParameters()
        );
        return R.ok(result);
    }

    /**
     * 获取工具调用历史
     */
    @Operation(summary = "获取工具调用历史")
    @GetMapping("/history")
    public TableDataInfo<ToolCall> getToolCallHistory(PageQuery pageQuery) {
        Long userId = LoginHelper.getUserId();
        Page<ToolCall> page = toolService.getToolCallHistory(userId, pageQuery.getPageNum(), pageQuery.getPageSize());
        return TableDataInfo.build(page);
    }

    /**
     * 获取会话工具调用记录
     */
    @Operation(summary = "获取会话工具调用记录")
    @GetMapping("/session/{sessionId}/calls")
    public R<List<ToolCall>> getSessionToolCalls(@PathVariable String sessionId) {
        Long userId = LoginHelper.getUserId();
        List<ToolCall> calls = toolService.getSessionToolCalls(sessionId, userId);
        return R.ok(calls);
    }

    /**
     * 获取工具调用详情
     */
    @Operation(summary = "获取工具调用详情")
    @GetMapping("/call/{callId}")
    public R<ToolCall> getToolCallDetail(@PathVariable String callId) {
        Long userId = LoginHelper.getUserId();
        ToolCall toolCall = toolService.getToolCallDetail(callId, userId);
        if (toolCall == null) {
            return R.fail("调用记录不存在或无权限访问");
        }
        return R.ok(toolCall);
    }

    /**
     * 重试工具调用
     */
    @Operation(summary = "重试工具调用")
    @PostMapping("/call/{callId}/retry")
    public R<Object> retryToolCall(@PathVariable String callId) {
        Long userId = LoginHelper.getUserId();
        IToolService.ToolCallResult result = toolService.retryToolCall(callId, userId);

        if (result.isSuccess()) {
            return R.ok(result.getData());
        } else {
            return R.fail(result.getMessage());
        }
    }

    /**
     * 获取工具使用统计
     */
    @Operation(summary = "获取工具使用统计")
    @GetMapping("/stats/usage")
    public R<Map<String, Object>> getToolUsageStats() {
        Long userId = LoginHelper.getUserId();
        Map<String, Object> stats = toolService.getToolUsageStats(userId);
        return R.ok(stats);
    }

    /**
     * 获取工具性能统计
     */
    @Operation(summary = "获取工具性能统计")
    @GetMapping("/{toolId}/stats")
    public R<Map<String, Object>> getToolPerformanceStats(@PathVariable String toolId) {
        Map<String, Object> stats = toolService.getToolPerformanceStats(toolId);
        return R.ok(stats);
    }

    // ========== 管理员功能 ==========

    /**
     * 注册工具
     */
    @Operation(summary = "注册工具")
    @SaCheckPermission("app:tool:add")
    @Log(title = "注册工具", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> registerTool(@RequestBody @Validated AiTool tool) {
        boolean success = toolService.registerTool(tool);
        return success ? R.ok() : R.fail("注册工具失败");
    }

    /**
     * 更新工具
     */
    @Operation(summary = "更新工具")
    @SaCheckPermission("app:tool:edit")
    @Log(title = "更新工具", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> updateTool(@RequestBody @Validated AiTool tool) {
        boolean success = toolService.updateTool(tool);
        return success ? R.ok() : R.fail("更新工具失败");
    }

    /**
     * 删除工具
     */
    @Operation(summary = "删除工具")
    @SaCheckPermission("app:tool:remove")
    @Log(title = "删除工具", businessType = BusinessType.DELETE)
    @DeleteMapping("/{toolId}")
    public R<Void> deleteTool(@PathVariable String toolId) {
        boolean success = toolService.deleteTool(toolId);
        return success ? R.ok() : R.fail("删除工具失败");
    }

    /**
     * 启用/禁用工具
     */
    @Operation(summary = "启用/禁用工具")
    @SaCheckPermission("app:tool:edit")
    @Log(title = "切换工具状态", businessType = BusinessType.UPDATE)
    @PutMapping("/{toolId}/toggle")
    public R<Void> toggleTool(@PathVariable String toolId, @RequestParam Boolean enabled) {
        boolean success = toolService.toggleTool(toolId, enabled);
        return success ? R.ok() : R.fail("切换工具状态失败");
    }

    /**
     * 初始化系统工具
     */
    @Operation(summary = "初始化系统工具")
    @SaCheckPermission("app:tool:init")
    @Log(title = "初始化系统工具", businessType = BusinessType.OTHER)
    @PostMapping("/init")
    public R<Void> initSystemTools() {
        toolService.initSystemTools();
        return R.ok();
    }

    // ========== 内部类 ==========

    /**
     * 工具执行请求
     */
    public static class ToolExecuteRequest {
        private String toolId;
        private Map<String, Object> parameters;
        private String sessionId;
        private String messageId;
        private String userQuery;
        private Map<String, Object> environment;

        // getters and setters
        public String getToolId() {
            return toolId;
        }

        public void setToolId(String toolId) {
            this.toolId = toolId;
        }

        public Map<String, Object> getParameters() {
            return parameters;
        }

        public void setParameters(Map<String, Object> parameters) {
            this.parameters = parameters;
        }

        public String getSessionId() {
            return sessionId;
        }

        public void setSessionId(String sessionId) {
            this.sessionId = sessionId;
        }

        public String getMessageId() {
            return messageId;
        }

        public void setMessageId(String messageId) {
            this.messageId = messageId;
        }

        public String getUserQuery() {
            return userQuery;
        }

        public void setUserQuery(String userQuery) {
            this.userQuery = userQuery;
        }

        public Map<String, Object> getEnvironment() {
            return environment;
        }

        public void setEnvironment(Map<String, Object> environment) {
            this.environment = environment;
        }
    }

    /**
     * 批量工具执行请求
     */
    public static class BatchToolExecuteRequest {
        private List<ToolExecuteRequest> toolCalls;

        public List<ToolExecuteRequest> getToolCalls() {
            return toolCalls;
        }

        public void setToolCalls(List<ToolExecuteRequest> toolCalls) {
            this.toolCalls = toolCalls;
        }
    }

    /**
     * 参数验证请求
     */
    public static class ParameterValidationRequest {
        private String toolId;
        private Map<String, Object> parameters;

        public String getToolId() {
            return toolId;
        }

        public void setToolId(String toolId) {
            this.toolId = toolId;
        }

        public Map<String, Object> getParameters() {
            return parameters;
        }

        public void setParameters(Map<String, Object> parameters) {
            this.parameters = parameters;
        }
    }
}
