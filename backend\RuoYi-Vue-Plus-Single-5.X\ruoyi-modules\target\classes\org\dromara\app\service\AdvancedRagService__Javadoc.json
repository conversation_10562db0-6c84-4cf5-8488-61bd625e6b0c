{"doc": " 高级RAG服务接口\n 提供更智能的检索和增强功能\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "smartRetrieval", "paramTypes": ["java.lang.String", "java.util.List", "org.dromara.app.service.AdvancedRagService.RetrievalOptions"], "doc": " 智能检索\n 结合多种检索策略，提供最相关的结果\n\n @param query            查询文本\n @param knowledgeBaseIds 知识库ID列表\n @param options          检索选项\n @return 检索结果\n"}, {"name": "hybridRetrieval", "paramTypes": ["java.lang.String", "java.util.List", "double", "double", "int"], "doc": " 混合检索\n 结合向量检索和关键词检索\n\n @param query            查询文本\n @param knowledgeBaseIds 知识库ID列表\n @param vectorWeight     向量检索权重\n @param keywordWeight    关键词检索权重\n @param topK             返回数量\n @return 检索结果\n"}, {"name": "rerank", "paramTypes": ["java.lang.String", "java.util.List", "int"], "doc": " 重排序检索结果\n 使用重排序模型对检索结果进行重新排序\n\n @param query      查询文本\n @param candidates 候选结果\n @param topK       返回数量\n @return 重排序后的结果\n"}, {"name": "expandQuery", "paramTypes": ["java.lang.String", "org.dromara.app.service.AdvancedRagService.QueryExpansionType"], "doc": " 查询扩展\n 扩展用户查询以提高检索效果\n\n @param query         原始查询\n @param expansionType 扩展类型\n @return 扩展后的查询\n"}, {"name": "compressContext", "paramTypes": ["java.lang.String", "java.util.List", "int"], "doc": " 上下文压缩\n 压缩检索到的上下文，保留最重要的信息\n\n @param query     查询文本\n @param contexts  上下文列表\n @param maxLength 最大长度\n @return 压缩后的上下文\n"}, {"name": "generateEnhancedPrompt", "paramTypes": ["java.lang.String", "java.util.List", "java.lang.String"], "doc": " 生成增强提示词\n 基于检索结果生成优化的提示词\n\n @param query            用户查询\n @param retrievalResults 检索结果\n @param promptTemplate   提示词模板\n @return 增强后的提示词\n"}], "constructors": []}