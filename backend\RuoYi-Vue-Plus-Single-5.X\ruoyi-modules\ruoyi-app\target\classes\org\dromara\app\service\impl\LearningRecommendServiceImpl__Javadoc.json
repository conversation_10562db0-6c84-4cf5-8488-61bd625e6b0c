{"doc": "\n 学习推荐服务实现类\r\n 基于用户能力评估和学习历史，提供个性化的学习资源推荐\r\n\r\n <AUTHOR>\r\n @date 2025-07-27\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "convertVideoToRecommendationItem", "paramTypes": ["org.dromara.app.domain.Video", "java.lang.Long", "java.util.Map"], "doc": "\n 将视频转换为推荐项VO\r\n"}, {"name": "convertQuestionBankToRecommendationItem", "paramTypes": ["QuestionBank", "java.lang.Long", "java.util.Map"], "doc": "\n 将题库转换为推荐项VO\r\n"}, {"name": "convertBookToRecommendationItem", "paramTypes": ["org.dromara.app.domain.Book", "java.lang.Long", "java.util.Map"], "doc": "\n 将书籍转换为推荐项VO\r\n"}, {"name": "getPriorityLevel", "paramTypes": ["java.lang.Double"], "doc": "\n 根据优先级分数获取优先级等级\r\n"}, {"name": "setTargetCapabilityAndReason", "paramTypes": ["org.dromara.app.domain.vo.RecommendationResponseVo.RecommendationItemVo", "java.util.Map"], "doc": "\n 设置目标能力和推荐原因\r\n"}, {"name": "calculateEstimatedTime", "paramTypes": ["java.lang.Integer"], "doc": "\n 计算题库预估时间\r\n"}, {"name": "calculateBookEstimatedTime", "paramTypes": ["java.lang.Integer"], "doc": "\n 计算书籍预估阅读时间\r\n"}, {"name": "convertToLocalDateTime", "paramTypes": ["java.util.Date"], "doc": "\n 转换Date到LocalDateTime\r\n"}, {"name": "convertDifficultyLevel", "paramTypes": ["java.lang.Integer"], "doc": "\n 转换难度等级（数字转文字）\r\n"}], "constructors": []}