{"doc": "\n 用户评估记录Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectRecordsByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询评估记录列表\r\n\r\n @param userId 用户ID\r\n @return 评估记录列表\r\n"}, {"name": "selectRecordsByUserIdAndType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 根据用户ID和评估类型查询评估记录\r\n\r\n @param userId         用户ID\r\n @param assessmentType 评估类型\r\n @return 评估记录列表\r\n"}, {"name": "selectLatestRecordByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询最新的评估记录\r\n\r\n @param userId 用户ID\r\n @return 最新评估记录\r\n"}, {"name": "selectLatestRecordByUserIdAndType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 根据用户ID和评估类型查询最新的评估记录\r\n\r\n @param userId         用户ID\r\n @param assessmentType 评估类型\r\n @return 最新评估记录\r\n"}, {"name": "countCompletedRecordsByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询已完成的评估记录数量\r\n\r\n @param userId 用户ID\r\n @return 已完成评估记录数量\r\n"}, {"name": "selectRecordsByUserIdAndStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 根据用户ID和状态查询评估记录\r\n\r\n @param userId 用户ID\r\n @param status 状态\r\n @return 评估记录列表\r\n"}], "constructors": []}