{"doc": "\n 结果分享记录Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-17\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByResultId", "paramTypes": ["java.lang.String"], "doc": "\n 根据结果ID查询分享记录列表\r\n\r\n @param resultId 结果ID\r\n @return 分享记录列表\r\n"}, {"name": "selectByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 根据用户ID查询分享记录列表\r\n\r\n @param userId 用户ID\r\n @param limit  限制数量\r\n @return 分享记录列表\r\n"}, {"name": "selectByShareUrl", "paramTypes": ["java.lang.String"], "doc": "\n 根据分享链接查询分享记录\r\n\r\n @param shareUrl 分享链接\r\n @return 分享记录\r\n"}, {"name": "selectByPlatform", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 根据平台查询分享记录\r\n\r\n @param platform 平台\r\n @param limit    限制数量\r\n @return 分享记录列表\r\n"}, {"name": "updateViewCount", "paramTypes": ["java.lang.Long"], "doc": "\n 更新查看次数\r\n\r\n @param id 分享记录ID\r\n @return 更新数量\r\n"}, {"name": "selectByStatus", "paramTypes": ["java.lang.String"], "doc": "\n 根据状态查询分享记录\r\n\r\n @param status 状态\r\n @return 分享记录列表\r\n"}, {"name": "selectExpiredShares", "paramTypes": [], "doc": "\n 查询过期的分享记录\r\n\r\n @return 分享记录列表\r\n"}, {"name": "batchUpdateExpiredStatus", "paramTypes": ["java.util.List"], "doc": "\n 批量更新过期状态\r\n\r\n @param ids 分享记录ID列表\r\n @return 更新数量\r\n"}, {"name": "deleteByResultId", "paramTypes": ["java.lang.String"], "doc": "\n 根据结果ID删除分享记录\r\n\r\n @param resultId 结果ID\r\n @return 删除数量\r\n"}], "constructors": []}