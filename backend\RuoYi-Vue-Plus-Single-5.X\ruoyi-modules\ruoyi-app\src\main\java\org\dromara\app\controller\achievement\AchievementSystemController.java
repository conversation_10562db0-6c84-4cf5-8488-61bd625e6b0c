package org.dromara.app.controller.achievement;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.util.AchievementSystemChecker;
import org.dromara.common.core.domain.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 成就系统状态控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/system/achievement")
@Tag(name = "成就系统状态", description = "成就系统状态检查和管理")
public class AchievementSystemController {

    private final AchievementSystemChecker systemChecker;

    /**
     * 获取系统健康状态
     */
    @Operation(summary = "获取系统健康状态")
    @SaCheckPermission("app:system:achievement:health")
    @GetMapping("/health")
    public R<Map<String, Object>> getSystemHealth() {
        Map<String, Object> healthReport = systemChecker.generateHealthReport();
        return R.ok(healthReport);
    }

    /**
     * 检查系统完整性
     */
    @Operation(summary = "检查系统完整性")
    @SaCheckPermission("app:system:achievement:integrity")
    @GetMapping("/integrity")
    public R<Map<String, Object>> checkSystemIntegrity() {
        Map<String, Object> integrityCheck = systemChecker.checkSystemIntegrity();
        return R.ok(integrityCheck);
    }

    /**
     * 检查数据库表
     */
    @Operation(summary = "检查数据库表")
    @SaCheckPermission("app:system:achievement:database")
    @GetMapping("/database")
    public R<Map<String, Object>> checkDatabase() {
        Map<String, Object> databaseCheck = systemChecker.checkDatabaseTables();
        return R.ok(databaseCheck);
    }

    /**
     * 检查RabbitMQ配置
     */
    @Operation(summary = "检查RabbitMQ配置")
    @SaCheckPermission("app:system:achievement:rabbitmq")
    @GetMapping("/rabbitmq")
    public R<Map<String, Object>> checkRabbitMQ() {
        Map<String, Object> rabbitMQCheck = systemChecker.checkRabbitMQConfig();
        return R.ok(rabbitMQCheck);
    }

    /**
     * 打印系统状态到日志
     */
    @Operation(summary = "打印系统状态到日志")
    @SaCheckPermission("app:system:achievement:log")
    @PostMapping("/log-status")
    public R<String> logSystemStatus() {
        systemChecker.printSystemStatus();
        return R.ok("系统状态已打印到日志");
    }

    /**
     * 获取系统信息概览
     */
    @Operation(summary = "获取系统信息概览")
    @SaCheckPermission("app:system:achievement:info")
    @GetMapping("/info")
    public R<Map<String, Object>> getSystemInfo() {
        Map<String, Object> info = new HashMap<>();

        info.put("systemName", "成就系统");
        info.put("version", "1.0.0");
        info.put("description", "完整的用户行为追踪和成就解锁系统");
        info.put("features", new String[]{
            "实时埋点数据收集",
            "异步成就检查",
            "灵活的规则引擎",
            "完善的管理功能",
            "消息队列支持",
            "多种成就类型",
            "统计和排行榜",
            "通知系统"
        });

        info.put("supportedAchievementTypes", new String[]{
            "LOGIN - 登录类成就",
            "LEARNING - 学习类成就",
            "SOCIAL - 社交类成就",
            "TIME - 时长类成就",
            "CUSTOM - 自定义成就"
        });

        info.put("coreComponents", new String[]{
            "TrackController - 埋点数据接收",
            "AchievementService - 成就业务服务",
            "AchievementRuleEngine - 规则引擎",
            "AchievementMessageListener - 消息监听器",
            "UserBehaviorService - 行为处理服务"
        });

        return R.ok(info);
    }

}
