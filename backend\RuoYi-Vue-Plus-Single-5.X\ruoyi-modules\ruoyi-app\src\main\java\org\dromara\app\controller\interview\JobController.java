package org.dromara.app.controller.interview;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.InterviewQuestion;
import org.dromara.app.domain.bo.JobQueryBo;
import org.dromara.app.domain.vo.JobVo;
import org.dromara.app.service.IInterviewQuestionService;
import org.dromara.app.service.IJobService;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 岗位管理控制器
 *
 * <AUTHOR> Assistant
 * @date 2025-07-19
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/job")
public class JobController extends BaseController {

    private final IJobService jobService;
    private final IInterviewQuestionService questionService;

    /**
     * 分页查询岗位列表
     */
    @GetMapping("/list")
    public R<Page<JobVo>> list(JobQueryBo queryBo) {
        Page<JobVo> page = new Page<>(queryBo.getPageNum(), queryBo.getPageSize());
        return R.ok(jobService.selectJobPageWithFavorite(page, queryBo));
    }

    /**
     * 根据技术领域查询岗位
     */
    @GetMapping("/domain/{technicalDomain}")
    public R<List<JobVo>> getJobsByDomain(@PathVariable String technicalDomain,
                                         @RequestParam(required = false) String level,
                                         @RequestParam(defaultValue = "10") Integer limit) {
        List<JobVo> jobs = jobService.selectByTechnicalDomain(technicalDomain, level, limit);
        return R.ok(jobs);
    }

    /**
     * 获取岗位详情
     */
    @GetMapping("/{jobId}")
    public R<JobVo> getJobDetail(@PathVariable Long jobId,
                                @RequestParam(required = false) Long userId) {
        JobVo jobDetail = jobService.selectJobDetail(jobId, userId);
        if (jobDetail == null) {
            return R.fail("岗位不存在");
        }

        // 增加浏览次数
        jobService.incrementViewCount(jobId);

        return R.ok(jobDetail);
    }

    /**
     * 获取推荐岗位
     */
    @GetMapping("/recommended")
    public R<List<JobVo>> getRecommendedJobs(@RequestParam(defaultValue = "10") Integer limit,
                                            @RequestParam(required = false) Long userId) {
        List<JobVo> jobs = jobService.selectRecommendedJobs(limit, userId);
        return R.ok(jobs);
    }

    /**
     * 获取热门岗位
     */
    @GetMapping("/hot")
    public R<List<JobVo>> getHotJobs(@RequestParam(defaultValue = "10") Integer limit,
                                    @RequestParam(required = false) Long userId) {
        List<JobVo> jobs = jobService.selectHotJobs(limit, userId);
        return R.ok(jobs);
    }

    /**
     * 获取技术领域统计
     */
    @GetMapping("/domain/stats")
    public R<List<IJobService.TechnicalDomainStats>> getTechnicalDomainStats() {
        List<IJobService.TechnicalDomainStats> stats = jobService.getTechnicalDomainStats();
        return R.ok(stats);
    }

    /**
     * 根据岗位获取面试问题
     */
    @GetMapping("/{jobId}/questions")
    public R<List<InterviewQuestion>> getJobQuestions(@PathVariable Long jobId,
                                                    @RequestParam(required = false) Integer difficulty,
                                                    @RequestParam(defaultValue = "20") Integer limit) {
        List<InterviewQuestion> questions = questionService.selectByJobId(jobId, difficulty, limit);
        return R.ok(questions);
    }

    /**
     * 根据技术领域获取面试问题
     */
    @GetMapping("/domain/{technicalDomain}/questions")
    public R<List<InterviewQuestion>> getDomainQuestions(@PathVariable String technicalDomain,
                                                       @RequestParam(required = false) String questionType,
                                                       @RequestParam(defaultValue = "20") Integer limit) {
        List<InterviewQuestion> questions = questionService.selectByTechnicalDomain(technicalDomain, questionType, limit);
        return R.ok(questions);
    }

    /**
     * 获取多模态面试问题
     */
    @GetMapping("/questions/multimodal")
    public R<List<InterviewQuestion>> getMultimodalQuestions(@RequestParam(required = false) Long jobId,
                                                           @RequestParam(defaultValue = "10") Integer limit) {
        List<InterviewQuestion> questions = questionService.selectMultimodalQuestions(jobId, limit);
        return R.ok(questions);
    }

    /**
     * 根据标签获取面试问题
     */
    @GetMapping("/questions/by-tags")
    public R<List<InterviewQuestion>> getQuestionsByTags(@RequestParam List<String> tags,
                                                       @RequestParam(defaultValue = "20") Integer limit) {
        List<InterviewQuestion> questions = questionService.selectByTags(tags, limit);
        return R.ok(questions);
    }

    /**
     * 获取分级面试问题
     */
    @GetMapping("/{jobId}/questions/graded")
    public R<IInterviewQuestionService.QuestionsByDifficulty> getGradedQuestions(
            @PathVariable Long jobId,
            @RequestParam(defaultValue = "5") Integer easyCount,
            @RequestParam(defaultValue = "10") Integer mediumCount,
            @RequestParam(defaultValue = "5") Integer hardCount) {

        IInterviewQuestionService.QuestionsByDifficulty questions =
            questionService.getQuestionsByDifficulty(jobId, easyCount, mediumCount, hardCount);
        return R.ok(questions);
    }

}
