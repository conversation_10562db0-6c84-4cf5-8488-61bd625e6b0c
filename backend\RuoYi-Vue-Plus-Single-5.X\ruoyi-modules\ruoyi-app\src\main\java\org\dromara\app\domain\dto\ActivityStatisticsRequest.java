package org.dromara.app.domain.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.app.domain.enums.ActivityType;

/**
 * 查询活动统计请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class ActivityStatisticsRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 活动类型(可选，为空时查询所有类型)
     */
    private ActivityType type;
}
