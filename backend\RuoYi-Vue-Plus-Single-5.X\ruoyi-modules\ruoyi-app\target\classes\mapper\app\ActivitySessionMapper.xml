<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.ActivitySessionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.dromara.app.domain.ActivitySession">
        <id column="id" property="id"/>
        <result column="session_id" property="sessionId"/>
        <result column="user_id" property="userId"/>
        <result column="activity_type" property="activityType"/>
        <result column="activity_id" property="activityId"/>
        <result column="activity_name" property="activityName"/>
        <result column="category_id" property="categoryId"/>
        <result column="category_name" property="categoryName"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="duration" property="duration"/>
        <result column="is_active" property="isActive"/>
        <result column="metadata" property="metadata"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="selectVo">
        SELECT id,
               session_id,
               user_id,
               activity_type,
               activity_id,
               activity_name,
               category_id,
               category_name,
               start_time,
               end_time,
               duration,
               is_active,
               metadata,
               create_time,
               update_time,
               create_by,
               update_by,
               del_flag
        FROM app_activity_session
    </sql>

    <!-- 分页查询用户活动历史记录 -->
    <select id="selectUserActivityHistory" resultMap="BaseResultMap">
        <include refid="selectVo"/>
        <where>
            user_id = #{userId}
            AND del_flag = '0'
            <if test="activityType != null">
                AND activity_type = #{activityType}
            </if>
            <if test="startDate != null">
                AND start_time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND start_time &lt;= #{endDate}
            </if>
        </where>
        ORDER BY start_time DESC
    </select>

    <!-- 查询用户活动会话统计信息 -->
    <select id="selectUserActivityStats" resultType="java.util.Map">
        SELECT
        activity_type,
        COUNT(*) as session_count,
        COALESCE(SUM(duration), 0) as total_duration,
        COALESCE(AVG(duration), 0) as avg_duration,
        COALESCE(MAX(duration), 0) as max_duration
        FROM app_activity_session
        WHERE user_id = #{userId}
        AND del_flag = '0'
        <if test="activityType != null">
            AND activity_type = #{activityType}
        </if>
        <if test="startDate != null">
            AND start_time >= #{startDate}
        </if>
        <if test="endDate != null">
            AND start_time &lt;= #{endDate}
        </if>
        GROUP BY activity_type
        ORDER BY total_duration DESC
    </select>

    <!-- 查询用户每日活动时长统计 -->
    <select id="selectDailyActivityStats" resultType="java.util.Map">
        SELECT
        DATE(start_time) as activity_date,
        activity_type,
        COUNT(*) as session_count,
        COALESCE(SUM(duration), 0) as total_duration
        FROM app_activity_session
        WHERE user_id = #{userId}
        AND del_flag = '0'
        <if test="activityType != null">
            AND activity_type = #{activityType}
        </if>
        <if test="startDate != null">
            AND DATE(start_time) >= #{startDate}
        </if>
        <if test="endDate != null">
            AND DATE(start_time) &lt;= #{endDate}
        </if>
        GROUP BY DATE(start_time), activity_type
        ORDER BY activity_date DESC, total_duration DESC
    </select>

    <!-- 查询用户最近的活动会话 -->
    <select id="selectRecentSessions" resultMap="BaseResultMap">
        <include refid="selectVo"/>
        WHERE user_id = #{userId}
        AND del_flag = '0'
        <if test="activityType != null">
            AND activity_type = #{activityType}
        </if>
        ORDER BY start_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询用户活跃时间段统计 -->
    <select id="selectActiveTimeStats" resultType="java.util.Map">
        SELECT
        HOUR(start_time) as hour_of_day,
        COUNT(*) as session_count,
        COALESCE(SUM(duration), 0) as total_duration,
        COALESCE(AVG(duration), 0) as avg_duration
        FROM app_activity_session
        WHERE user_id = #{userId}
        AND del_flag = '0'
        AND start_time >= #{startDate}
        AND start_time &lt;= #{endDate}
        <if test="activityType != null">
            AND activity_type = #{activityType}
        </if>
        GROUP BY HOUR(start_time)
        ORDER BY hour_of_day
    </select>

    <!-- 批量更新会话状态 -->
    <update id="batchUpdateSessionStatus">
        UPDATE app_activity_session
        SET is_active = #{isActive},
        update_time = NOW()
        WHERE user_id = #{userId}
        AND session_id IN
        <foreach collection="sessionIds" item="sessionId" open="(" separator="," close=")">
            #{sessionId}
        </foreach>
    </update>

    <!-- 清理过期的活跃会话 -->
    <update id="cleanupExpiredActiveSessions">
        UPDATE app_activity_session
        SET is_active   = 0,
            end_time    = NOW(),
            duration    = TIMESTAMPDIFF(MICROSECOND, start_time, NOW()) / 1000,
            update_time = NOW()
        WHERE is_active = 1
          AND start_time &lt; #{expireTime}
          AND del_flag = '0'
    </update>

</mapper>
