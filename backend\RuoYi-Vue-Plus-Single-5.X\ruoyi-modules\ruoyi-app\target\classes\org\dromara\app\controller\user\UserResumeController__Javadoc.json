{"doc": "\n 用户简历管理控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo", "<PERSON><PERSON><PERSON><PERSON>"], "doc": "\n 查询用户简历分页列表\r\n"}, {"name": "getMyResumeList", "paramTypes": [], "doc": "\n 查询当前用户的所有简历列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.String"], "doc": "\n 获取用户简历详细信息\r\n\r\n @param resumeId 简历主键\r\n"}, {"name": "uploadResume", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String", "java.lang.String"], "doc": "\n 上传用户简历文件\r\n\r\n @param file     简历文件\r\n @param fileName 原始文件名（可选）\r\n @param fileType 文件类型（可选）\r\n"}, {"name": "add", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo"], "doc": "\n 新增用户简历\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo"], "doc": "\n 修改用户简历\r\n"}, {"name": "renameResume", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": "\n 重命名简历\r\n\r\n @param resumeId 简历ID\r\n @param request  重命名请求参数\r\n"}, {"name": "setDefaultResume", "paramTypes": ["java.lang.Long"], "doc": "\n 设置默认简历\r\n\r\n @param resumeId 简历ID\r\n"}, {"name": "cancelDefaultResume", "paramTypes": ["java.lang.Long"], "doc": "\n 取消默认简历\r\n\r\n @param resumeId 简历ID\r\n"}, {"name": "getDefaultResume", "paramTypes": [], "doc": "\n 获取当前用户的默认简历\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除用户简历\r\n\r\n @param resumeIds 简历主键串\r\n"}, {"name": "downloadResume", "paramTypes": ["java.lang.Long", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 下载用户简历文件\r\n\r\n @param resumeId 简历ID\r\n"}, {"name": "previewResumeContent", "paramTypes": ["java.lang.Long"], "doc": "\n 预览简历文件内容\r\n\r\n @param resumeId 简历ID\r\n @return 预览内容响应\r\n"}, {"name": "getStructuredResumeContent", "paramTypes": ["java.lang.Long"], "doc": "\n 获取简历结构化预览内容\r\n\r\n <p>将简历文件内容解析为结构化数据，包括：</p>\r\n <ul>\r\n   <li>基本信息：简历名称、创建时间等</li>\r\n   <li>个人信息：姓名、联系方式、求职意向等</li>\r\n   <li>教育经历：学校、专业、学历等</li>\r\n   <li>工作经验：公司、职位、工作内容等</li>\r\n   <li>技能特长：技能名称、熟练度等</li>\r\n   <li>其他信息：项目经历、获奖情况、证书等</li>\r\n </ul>\r\n\r\n @param resumeId 简历ID，必须为正整数\r\n @return 结构化简历内容响应，包含完整的简历数据结构\r\n @throws ServiceException 当简历不存在、已禁用或解析失败时抛出\r\n"}, {"name": "getResumePreviewImage", "paramTypes": ["java.lang.Long"], "doc": "\n 获取简历预览图片\r\n\r\n <p>生成简历的预览图片，用于快速预览简历内容。</p>\r\n <p>返回的数据包含：</p>\r\n <ul>\r\n   <li>imageUrl: 完整尺寸预览图URL</li>\r\n   <li>thumbnailUrl: 缩略图URL（可选）</li>\r\n   <li>resumeId: 简历ID</li>\r\n   <li>resumeName: 简历名称</li>\r\n </ul>\r\n\r\n <p>注意：目前返回占位图片，后续可扩展为真实的图片生成功能。</p>\r\n\r\n @param resumeId 简历ID，必须为正整数\r\n @return 预览图片数据响应，包含图片URL和相关信息\r\n @throws ServiceException 当简历不存在、已禁用或生成失败时抛出\r\n"}], "constructors": []}