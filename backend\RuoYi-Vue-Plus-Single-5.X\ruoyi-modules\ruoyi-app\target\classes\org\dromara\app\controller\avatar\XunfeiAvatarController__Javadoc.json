{"doc": "\n 讯飞数字人控制器\r\n\r\n <AUTHOR>\r\n @date 2025-07-20\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "startSession", "paramTypes": ["org.dromara.app.domain.dto.avatar.AvatarStartDto"], "doc": "\n 启动数字人会话\r\n"}, {"name": "sendTextDriver", "paramTypes": ["java.lang.String", "org.dromara.app.domain.dto.avatar.AvatarTextDto"], "doc": "\n 发送文本驱动\r\n"}, {"name": "sendTextInteract", "paramTypes": ["java.lang.String", "org.dromara.app.domain.dto.avatar.AvatarTextDto"], "doc": "\n 发送文本交互\r\n"}, {"name": "sendAudioDriver", "paramTypes": ["java.lang.String", "java.lang.String", "int"], "doc": "\n 发送音频驱动\r\n"}, {"name": "resetAvatar", "paramTypes": ["java.lang.String"], "doc": "\n 重置数字人\r\n"}, {"name": "stopSession", "paramTypes": ["java.lang.String"], "doc": "\n 停止数字人会话\r\n"}, {"name": "sendHeartbeat", "paramTypes": ["java.lang.String"], "doc": "\n 发送心跳\r\n"}, {"name": "sendAction", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 发送动作指令\r\n"}, {"name": "getSessionInfo", "paramTypes": ["java.lang.String"], "doc": "\n 获取会话信息\r\n"}, {"name": "checkSessionStatus", "paramTypes": ["java.lang.String"], "doc": "\n 检查会话状态\r\n"}, {"name": "getStreamUrl", "paramTypes": ["java.lang.String"], "doc": "\n 获取推流地址\r\n"}, {"name": "getStreamStatus", "paramTypes": ["java.lang.String"], "doc": "\n 检查推流地址状态\r\n"}, {"name": "updateStreamUrl", "paramTypes": ["java.lang.String"], "doc": "\n 更新推流地址\r\n"}], "constructors": []}