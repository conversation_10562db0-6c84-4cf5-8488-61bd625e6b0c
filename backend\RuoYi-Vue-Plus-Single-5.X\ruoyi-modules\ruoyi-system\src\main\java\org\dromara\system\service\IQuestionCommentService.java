package org.dromara.system.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.domain.bo.QuestionCommentBo;
import org.dromara.system.domain.vo.QuestionCommentVo;

import java.util.Collection;
import java.util.List;

/**
 * 题目评论Service接口
 *
 * <AUTHOR>
 */
public interface IQuestionCommentService {

    /**
     * 查询题目评论
     *
     * @param commentId 评论主键
     * @return 题目评论
     */
    QuestionCommentVo queryById(Long commentId);

    /**
     * 查询题目评论列表
     *
     * @param bo 题目评论查询条件
     * @return 题目评论集合
     */
    List<QuestionCommentVo> queryList(QuestionCommentBo bo);

    /**
     * 分页查询题目评论列表
     *
     * @param bo        题目评论查询条件
     * @param pageQuery 分页参数
     * @return 题目评论分页集合
     */
    TableDataInfo<QuestionCommentVo> queryPageList(QuestionCommentBo bo, PageQuery pageQuery);

    /**
     * 根据题目ID分页查询评论列表
     *
     * @param questionId 题目ID
     * @param bo         评论查询条件
     * @param pageQuery  分页参数
     * @return 评论分页集合
     */
    TableDataInfo<QuestionCommentVo> queryPageListByQuestionId(Long questionId, QuestionCommentBo bo, PageQuery pageQuery);

    /**
     * 新增题目评论
     *
     * @param bo 题目评论信息
     * @return 新增结果
     */
    Boolean insertByBo(QuestionCommentBo bo);

    /**
     * 修改题目评论
     *
     * @param bo 题目评论信息
     * @return 修改结果
     */
    Boolean updateByBo(QuestionCommentBo bo);

    /**
     * 校验并批量删除题目评论信息
     *
     * @param ids 需要删除的题目评论主键集合
     * @return 删除结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids);

    /**
     * 根据题目ID查询评论列表
     *
     * @param questionId 题目ID
     * @return 评论集合
     */
    List<QuestionCommentVo> queryByQuestionId(Long questionId);

    /**
     * 根据用户ID查询评论列表
     *
     * @param userId 用户ID
     * @return 评论集合
     */
    List<QuestionCommentVo> queryByUserId(Long userId);

    /**
     * 根据父评论ID查询子评论列表
     *
     * @param parentId 父评论ID
     * @return 子评论集合
     */
    List<QuestionCommentVo> queryByParentId(Long parentId);

    /**
     * 查询评论树形结构
     *
     * @param questionId 题目ID
     * @return 评论树形集合
     */
    List<QuestionCommentVo> queryCommentTree(Long questionId);

    /**
     * 查询热门评论列表
     *
     * @param questionId 题目ID
     * @param limit      限制数量
     * @return 评论集合
     */
    List<QuestionCommentVo> queryHotComments(Long questionId, Integer limit);

    /**
     * 查询最新评论列表
     *
     * @param questionId 题目ID
     * @param limit      限制数量
     * @return 评论集合
     */
    List<QuestionCommentVo> queryLatestComments(Long questionId, Integer limit);

    /**
     * 更新评论点赞数
     *
     * @param commentId 评论ID
     * @param increment 增量（可为负数）
     * @return 更新结果
     */
    Boolean updateLikeCount(Long commentId, Integer increment);

    /**
     * 更新评论回复数
     *
     * @param commentId 评论ID
     * @param increment 增量（可为负数）
     * @return 更新结果
     */
    Boolean updateReplyCount(Long commentId, Integer increment);

    /**
     * 点赞/取消点赞评论
     *
     * @param commentId 评论ID
     * @param userId    用户ID
     * @return 操作结果
     */
    Boolean likeComment(Long commentId, Long userId);

    /**
     * 回复评论
     *
     * @param bo 评论信息
     * @return 回复结果
     */
    Boolean replyComment(QuestionCommentBo bo);

    /**
     * 根据题目ID删除评论
     *
     * @param questionId 题目ID
     * @return 删除结果
     */
    Boolean deleteByQuestionId(Long questionId);

    /**
     * 根据用户ID删除评论
     *
     * @param userId 用户ID
     * @return 删除结果
     */
    Boolean deleteByUserId(Long userId);

    /**
     * 统计题目下的评论数量
     *
     * @param questionId 题目ID
     * @return 评论数量
     */
    Integer countByQuestionId(Long questionId);

    /**
     * 统计用户的评论数量
     *
     * @param userId 用户ID
     * @return 评论数量
     */
    Integer countByUserId(Long userId);

    /**
     * 审核评论
     *
     * @param commentId 评论ID
     * @param status    审核状态
     * @return 审核结果
     */
    Boolean auditComment(Long commentId, String status);

    /**
     * 批量审核评论
     *
     * @param commentIds 评论ID集合
     * @param status     审核状态
     * @return 审核结果
     */
    Boolean batchAuditComments(Collection<Long> commentIds, String status);

    /**
     * 导出评论数据
     *
     * @param bo 评论查询条件
     * @return 评论集合
     */
    List<QuestionCommentVo> exportComment(QuestionCommentBo bo);
}
