{"doc": " MongoDB异常处理器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleMongoException", "paramTypes": ["com.mongodb.MongoException"], "doc": " MongoDB通用异常\n"}, {"name": "handleMongoTimeoutException", "paramTypes": ["com.mongodb.MongoTimeoutException"], "doc": " MongoDB超时异常\n"}, {"name": "handleMongoWriteException", "paramTypes": ["com.mongodb.MongoWriteException"], "doc": " MongoDB写入异常\n"}, {"name": "handleDataAccessException", "paramTypes": ["org.springframework.dao.DataAccessException"], "doc": " 数据访问异常\n"}, {"name": "handleDuplicateKeyException", "paramTypes": ["org.springframework.dao.DuplicateKeyException"], "doc": " 重复键异常\n"}], "constructors": []}