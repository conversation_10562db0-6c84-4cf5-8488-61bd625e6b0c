<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.ToolCallMapper">

    <resultMap type="org.dromara.app.domain.ToolCall" id="ToolCallResult">
        <result property="id" column="id"/>
        <result property="messageId" column="message_id"/>
        <result property="sessionId" column="session_id"/>
        <result property="userId" column="user_id"/>
        <result property="toolId" column="tool_id"/>
        <result property="toolName" column="tool_name"/>
        <result property="callId" column="call_id"/>
        <result property="functionName" column="function_name"/>
        <result property="parameters" column="parameters"/>
        <result property="result" column="result"/>
        <result property="executionTime" column="execution_time"/>
        <result property="status" column="status"/>
        <result property="errorMessage" column="error_message"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="retryCount" column="retry_count"/>
        <result property="metadata" column="metadata"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectToolCallVo">
        select id,
               message_id,
               session_id,
               user_id,
               tool_id,
               tool_name,
               call_id,
               function_name,
               parameters,
               result,
               execution_time,
               status,
               error_message,
               start_time,
               end_time,
               retry_count,
               metadata,
               create_by,
               create_time,
               update_by,
               update_time,
               del_flag
        from app_tool_call
    </sql>

    <select id="selectToolCallsByMessage" parameterType="String" resultMap="ToolCallResult">
        <include refid="selectToolCallVo"/>
        where message_id = #{messageId} and del_flag = '0'
        order by create_time asc
    </select>

    <select id="selectToolCallsBySession" parameterType="String" resultMap="ToolCallResult">
        <include refid="selectToolCallVo"/>
        where session_id = #{sessionId} and del_flag = '0'
        <if test="status != null and status != ''">
            and status = #{status}
        </if>
        order by create_time desc
        <if test="limit != null and limit > 0">
            limit #{limit}
        </if>
    </select>

    <select id="selectToolCallsByUser" parameterType="Long" resultMap="ToolCallResult">
        <include refid="selectToolCallVo"/>
        where user_id = #{userId} and del_flag = '0'
        <if test="toolId != null and toolId != ''">
            and tool_id = #{toolId}
        </if>
        <if test="startTime != null">
            and start_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and end_time &lt;= #{endTime}
        </if>
        order by create_time desc
        <if test="limit != null and limit > 0">
            limit #{limit}
        </if>
    </select>

    <select id="selectToolCallStats" parameterType="String" resultType="java.util.Map">
        select
        tool_id as toolId,
        tool_name as toolName,
        count(*) as totalCalls,
        sum(case when status = 'success' then 1 else 0 end) as successCalls,
        sum(case when status = 'failed' then 1 else 0 end) as failedCalls,
        avg(execution_time) as avgExecutionTime,
        max(execution_time) as maxExecutionTime,
        min(execution_time) as minExecutionTime
        from app_tool_call
        where del_flag = '0'
        <if test="toolId != null and toolId != ''">
            and tool_id = #{toolId}
        </if>
        <if test="startTime != null">
            and start_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and end_time &lt;= #{endTime}
        </if>
        group by tool_id, tool_name
        order by totalCalls desc
    </select>

    <select id="getToolUsageRanking" parameterType="int" resultType="java.util.Map">
        select tool_id                                                                  as toolId,
               tool_name                                                                as toolName,
               count(*)                                                                 as usageCount,
               avg(execution_time)                                                      as avgExecutionTime,
               (sum(case when status = 'success' then 1 else 0 end) * 100.0 / count(*)) as successRate
        from app_tool_call
        where del_flag = '0'
        group by tool_id, tool_name
        order by usageCount desc
        limit #{limit}
    </select>

    <select id="getUserToolStats" parameterType="Long" resultType="java.util.Map">
        select
        count(*) as totalCalls,
        count(distinct tool_id) as uniqueTools,
        sum(case when status = 'success' then 1 else 0 end) as successCalls,
        sum(case when status = 'failed' then 1 else 0 end) as failedCalls,
        avg(execution_time) as avgExecutionTime
        from app_tool_call
        where user_id = #{userId} and del_flag = '0'
        <if test="startTime != null">
            and start_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and end_time &lt;= #{endTime}
        </if>
    </select>

    <delete id="deleteToolCallsBySession" parameterType="String">
        delete
        from app_tool_call
        where session_id = #{sessionId}
    </delete>

    <!-- 查询指定用户的历史调用记录 -->
    <select id="selectUserHistory" resultMap="ToolCallResult">
        <include refid="selectToolCallVo"/>
        where user_id = #{userId} and del_flag = '0'
        order by create_time desc
        limit #{offset}, #{pageSize}
    </select>

    <!-- 根据会话ID查询工具调用记录 -->
    <select id="selectBySessionId" parameterType="String" resultMap="ToolCallResult">
        <include refid="selectToolCallVo"/>
        where session_id = #{sessionId} and del_flag = '0'
        order by create_time asc
    </select>

    <delete id="deleteToolCallsByMessage" parameterType="String">
        delete
        from app_tool_call
        where message_id = #{messageId}
    </delete>

</mapper>
