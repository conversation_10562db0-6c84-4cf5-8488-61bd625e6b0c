package org.dromara.common.caffeine.config;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.caffeine.core.CaffeineClient;
import org.dromara.common.caffeine.properties.CaffeineProperties;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.time.Duration;

/**
 * Caffeine缓存配置
 *
 * <AUTHOR>
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(CaffeineProperties.class)
@ConditionalOnProperty(prefix = "caffeine", name = "enabled", havingValue = "true", matchIfMissing = true)
public class CaffeineConfig {

    /**
     * Caffeine缓存客户端
     */
    @Bean
    @Primary
    public Cache<String, Object> caffeineCache(CaffeineProperties caffeineProperties) {
        Caffeine<Object, Object> builder = Caffeine.newBuilder();

        // 设置初始容量
        if (caffeineProperties.getInitialCapacity() != null) {
            builder.initialCapacity(caffeineProperties.getInitialCapacity());
        }

        // 设置最大条目数
        if (caffeineProperties.getMaximumSize() != null) {
            builder.maximumSize(caffeineProperties.getMaximumSize());
        }

        // 设置写入后过期时间
        if (caffeineProperties.getExpireAfterWrite() != null) {
            builder.expireAfterWrite(Duration.ofSeconds(caffeineProperties.getExpireAfterWrite()));
        }

        // 设置访问后过期时间
        if (caffeineProperties.getExpireAfterAccess() != null) {
            builder.expireAfterAccess(Duration.ofSeconds(caffeineProperties.getExpireAfterAccess()));
        }

        // 设置刷新时间
        if (caffeineProperties.getRefreshAfterWrite() != null) {
            builder.refreshAfterWrite(Duration.ofSeconds(caffeineProperties.getRefreshAfterWrite()));
        }

        // 设置引用类型
        if (Boolean.TRUE.equals(caffeineProperties.getWeakKeys())) {
            builder.weakKeys();
        }

        if (Boolean.TRUE.equals(caffeineProperties.getWeakValues())) {
            builder.weakValues();
        }

        if (Boolean.TRUE.equals(caffeineProperties.getSoftValues())) {
            builder.softValues();
        }

        // 启用统计
        if (Boolean.TRUE.equals(caffeineProperties.getRecordStats())) {
            builder.recordStats();
        }

        // 创建缓存
        Cache<String, Object> cache = builder.build();

        log.info("初始化Caffeine缓存成功，最大容量: {}, 过期时间: {}秒",
            caffeineProperties.getMaximumSize(),
            caffeineProperties.getExpireAfterWrite());

        return cache;
    }

    /**
     * Caffeine客户端封装
     */
    @Bean
    public CaffeineClient caffeineClient(Cache<String, Object> caffeineCache, CaffeineProperties caffeineProperties) {
        return new CaffeineClient(caffeineCache, caffeineProperties);
    }

}
