{"doc": " 面试事件监听器\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleInterviewStarted", "paramTypes": ["org.dromara.app.event.InterviewEvents.InterviewStartedEvent"], "doc": " 监听面试开始事件\n"}, {"name": "handleQuestionAnswered", "paramTypes": ["org.dromara.app.event.InterviewEvents.QuestionAnsweredEvent"], "doc": " 监听问题回答事件\n"}, {"name": "handleQuestionSkipped", "paramTypes": ["org.dromara.app.event.InterviewEvents.QuestionSkippedEvent"], "doc": " 监听问题跳过事件\n"}, {"name": "handleInterviewCompleted", "paramTypes": ["org.dromara.app.event.InterviewEvents.InterviewCompletedEvent"], "doc": " 监听面试完成事件\n"}, {"name": "handleAiEvaluation", "paramTypes": ["org.dromara.app.event.InterviewEvents.AiEvaluationEvent"], "doc": " 监听AI评估事件\n"}, {"name": "handleSystemError", "paramTypes": ["org.dromara.app.event.InterviewEvents.SystemErrorEvent"], "doc": " 监听系统错误事件\n"}], "constructors": []}