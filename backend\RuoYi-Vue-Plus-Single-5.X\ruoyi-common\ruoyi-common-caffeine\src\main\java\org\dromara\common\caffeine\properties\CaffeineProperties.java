package org.dromara.common.caffeine.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Caffeine缓存配置属性
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "caffeine")
public class CaffeineProperties {

    /**
     * 是否开启
     */
    private Boolean enabled = true;

    /**
     * 默认缓存名称
     */
    private String defaultCacheName = "default";

    /**
     * 缓存最大条目数
     */
    private Long maximumSize = 1000L;

    /**
     * 写入后过期时间（秒）
     */
    private Long expireAfterWrite = 3600L;

    /**
     * 访问后过期时间（秒）
     */
    private Long expireAfterAccess;

    /**
     * 刷新时间（秒）
     */
    private Long refreshAfterWrite;

    /**
     * 初始容量
     */
    private Integer initialCapacity = 16;

    /**
     * 是否启用统计
     */
    private Boolean recordStats = false;

    /**
     * 是否启用软引用
     */
    private Boolean weakKeys = false;

    /**
     * 是否启用弱引用值
     */
    private Boolean weakValues = false;

    /**
     * 是否启用软引用值
     */
    private Boolean softValues = false;

    /**
     * 是否启用调试日志
     */
    private Boolean debugEnabled = false;

}
