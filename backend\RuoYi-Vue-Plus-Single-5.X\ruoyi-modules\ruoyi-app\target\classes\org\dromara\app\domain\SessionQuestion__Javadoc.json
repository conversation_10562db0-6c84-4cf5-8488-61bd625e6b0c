{"doc": "\n 面试会话问题对象 app_session_question\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [{"name": "id", "doc": "\n 记录ID\r\n"}, {"name": "sessionId", "doc": "\n 会话ID\r\n"}, {"name": "questionId", "doc": "\n 问题ID（唯一标识）\r\n"}, {"name": "content", "doc": "\n 问题内容\r\n"}, {"name": "type", "doc": "\n 问题类型（technical, behavioral, project, case）\r\n"}, {"name": "difficulty", "doc": "\n 难度等级（easy, medium, hard）\r\n"}, {"name": "category", "doc": "\n 问题分类\r\n"}, {"name": "subcategory", "doc": "\n 子分类\r\n"}, {"name": "timeLimit", "doc": "\n 时间限制（秒）\r\n"}, {"name": "questionOrder", "doc": "\n 问题顺序\r\n"}, {"name": "tags", "doc": "\n 标签（JSON数组）\r\n"}, {"name": "status", "doc": "\n 问题状态（pending, current, answered, skipped）\r\n"}, {"name": "answerPoints", "doc": "\n 参考答案要点（JSON数组）\r\n"}, {"name": "hints", "doc": "\n 提示信息（JSON数组）\r\n"}, {"name": "scoringCriteria", "doc": "\n 评分标准（JSON格式）\r\n"}, {"name": "isCustom", "doc": "\n 是否为自定义问题\r\n"}, {"name": "source", "doc": "\n 问题来源（system, custom, ai_generated）\r\n"}, {"name": "metadata", "doc": "\n 元数据（JSON格式）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}