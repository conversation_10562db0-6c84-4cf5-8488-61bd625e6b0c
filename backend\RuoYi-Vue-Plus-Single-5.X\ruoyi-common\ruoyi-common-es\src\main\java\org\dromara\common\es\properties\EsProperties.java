package org.dromara.common.es.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * ES配置属性
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "elasticsearch")
public class EsProperties {

    /**
     * 是否开启
     */
    private Boolean enabled;

    /**
     * 集群节点地址
     */
    private List<String> hosts;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectTimeout = 5000;

    /**
     * 响应超时时间（毫秒）
     */
    private Integer socketTimeout = 30000;

    /**
     * 连接请求超时时间（毫秒）
     */
    private Integer connectionRequestTimeout = 1000;

    /**
     * 最大连接数
     */
    private Integer maxConnections = 100;

    /**
     * 每个路由的最大连接数
     */
    private Integer maxConnectionsPerRoute = 10;

    /**
     * 索引默认分片数
     */
    private Integer defaultShards = 1;

    /**
     * 索引默认副本数
     */
    private Integer defaultReplicas = 1;

    /**
     * 是否启用SSL
     */
    private Boolean sslEnabled = false;

    /**
     * 是否启用调试日志
     */
    private Boolean debugEnabled = false;

}
