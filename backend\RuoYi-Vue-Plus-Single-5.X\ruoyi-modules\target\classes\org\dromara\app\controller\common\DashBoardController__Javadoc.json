{"doc": " 首页仪表盘控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getDashboardSummary", "paramTypes": [], "doc": " 获取首页仪表盘汇总数据\n"}, {"name": "getUserAbilities", "paramTypes": [], "doc": " 获取用户能力评估数据\n"}, {"name": "getStudyStats", "paramTypes": [], "doc": " 获取学习统计数据\n"}, {"name": "getSmartTasks", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": " 获取智能推荐任务\n"}, {"name": "getRecentInterviews", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": " 获取最近面试记录\n"}, {"name": "updateTargetPosition", "paramTypes": ["java.util.Map"], "doc": " 更新用户目标岗位\n"}, {"name": "completeTask", "paramTypes": ["java.util.Map"], "doc": " 标记任务为已完成\n"}, {"name": "getDashboardData", "paramTypes": [], "doc": " 获取首页所有数据（聚合接口）\n"}], "constructors": []}