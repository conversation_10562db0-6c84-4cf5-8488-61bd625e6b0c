package org.dromara.app.controller.agent;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.KnowledgeBase;
import org.dromara.app.domain.KnowledgeDocument;
import org.dromara.app.domain.VectorEmbedding;
import org.dromara.app.domain.bo.KnowledgeBaseBo;
import org.dromara.app.domain.bo.KnowledgeDocumentBo;
import org.dromara.app.domain.vo.KnowledgeBaseVo;
import org.dromara.app.domain.vo.KnowledgeDocumentVo;
import org.dromara.app.service.IRagService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.core.validate.QueryGroup;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 知识库管理Controller
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/knowledge")
@Tag(name = "知识库管理", description = "知识库管理相关接口")
public class KnowledgeController extends BaseController {

    private final IRagService ragService;

    // ========== 知识库管理接口 ==========

    /**
     * 分页查询知识库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    @Operation(summary = "分页查询知识库列表", description = "支持按名称、类型、状态等条件查询知识库")
    @Parameters({
        @Parameter(name = "bo", description = "查询条件", required = true),
        @Parameter(name = "pageQuery", description = "分页参数", required = true)
    })
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功",
            content = @Content(schema = @Schema(implementation = TableDataInfo.class))),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "500", description = "系统错误")
    })
    @GetMapping("/bases/list")
    public TableDataInfo<KnowledgeBaseVo> list(@Validated(QueryGroup.class) KnowledgeBaseBo bo, PageQuery pageQuery) {
        // 将分页参数设置到bo中
        bo.setPageNum(pageQuery.getPageNum());
        bo.setPageSize(pageQuery.getPageSize());
        bo.setOrderByColumn(pageQuery.getOrderByColumn());
        bo.setIsAsc(pageQuery.getIsAsc());

        return ragService.queryPageList(bo);
    }

    /**
     * 获取知识库列表（不分页）
     *
     * @param bo 查询条件
     * @return 知识库列表
     */
    @GetMapping("/bases")
    public R<List<KnowledgeBaseVo>> getKnowledgeBases(@Validated(QueryGroup.class) KnowledgeBaseBo bo) {
        List<KnowledgeBaseVo> list = ragService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 获取知识库详情
     *
     * @param knowledgeBaseId 知识库ID
     * @return 知识库详情
     */
    @GetMapping("/bases/{knowledgeBaseId}")
    public R<KnowledgeBaseVo> getKnowledgeBase(@PathVariable Long knowledgeBaseId) {
        if (knowledgeBaseId == null) {
            return R.fail("知识库ID不能为空");
        }

        KnowledgeBaseVo knowledgeBase = ragService.queryById(knowledgeBaseId);
        if (knowledgeBase == null) {
            return R.fail("知识库不存在");
        }
        return R.ok(knowledgeBase);
    }

    /**
     * 创建知识库
     *
     * @param bo 知识库信息
     * @return 操作结果
     */
    @Operation(summary = "创建知识库", description = "创建新的知识库")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "创建成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "500", description = "创建失败")
    })
    @Log(title = "知识库管理", businessType = BusinessType.INSERT)
    @PostMapping("/bases")
    public R<Void> createKnowledgeBase(@RequestBody @Validated(AddGroup.class) KnowledgeBaseBo bo) {
        // 转换为实体对象
        KnowledgeBase knowledgeBase = new KnowledgeBase();
        knowledgeBase.setName(bo.getName());
        knowledgeBase.setDescription(bo.getDescription());
        knowledgeBase.setType(bo.getType());
        knowledgeBase.setStatus(bo.getStatus());
        knowledgeBase.setVectorDimension(bo.getVectorDimension());
        knowledgeBase.setIndexConfig(bo.getIndexConfig());
        knowledgeBase.setExtendConfig(bo.getExtendConfig());
        knowledgeBase.setSortOrder(bo.getSortOrder());
        knowledgeBase.setRemark(bo.getRemark());

        boolean success = ragService.createKnowledgeBase(knowledgeBase);
        return toAjax(success);
    }

    /**
     * 更新知识库
     *
     * @param bo 知识库信息
     * @return 操作结果
     */
    @Log(title = "知识库管理", businessType = BusinessType.UPDATE)
    @PutMapping("/bases")
    public R<Void> updateKnowledgeBase(@RequestBody @Validated(EditGroup.class) KnowledgeBaseBo bo) {
        // 转换为实体对象
        KnowledgeBase knowledgeBase = new KnowledgeBase();
        knowledgeBase.setId(bo.getId());
        knowledgeBase.setName(bo.getName());
        knowledgeBase.setDescription(bo.getDescription());
        knowledgeBase.setType(bo.getType());
        knowledgeBase.setStatus(bo.getStatus());
        knowledgeBase.setVectorDimension(bo.getVectorDimension());
        knowledgeBase.setIndexConfig(bo.getIndexConfig());
        knowledgeBase.setExtendConfig(bo.getExtendConfig());
        knowledgeBase.setSortOrder(bo.getSortOrder());
        knowledgeBase.setRemark(bo.getRemark());

        boolean success = ragService.updateKnowledgeBase(knowledgeBase);
        return toAjax(success);
    }

    /**
     * 删除知识库
     *
     * @param knowledgeBaseId 知识库ID
     * @return 操作结果
     */
    @Log(title = "知识库管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/bases/{knowledgeBaseId}")
    public R<Void> deleteKnowledgeBase(@PathVariable Long knowledgeBaseId) {
        if (knowledgeBaseId == null) {
            return R.fail("知识库ID不能为空");
        }

        boolean success = ragService.deleteKnowledgeBase(knowledgeBaseId);
        return toAjax(success);
    }

    /**
     * 获取知识库统计信息
     */
    @GetMapping("/bases/{knowledgeBaseId}/stats")
    public R<IRagService.KnowledgeBaseStats> getKnowledgeBaseStats(@PathVariable Long knowledgeBaseId) {
        IRagService.KnowledgeBaseStats stats = ragService.getKnowledgeBaseStats(knowledgeBaseId);
        return R.ok(stats);
    }

    /**
     * 重建知识库索引
     */
    @Log(title = "知识库管理", businessType = BusinessType.UPDATE)
    @PostMapping("/bases/{knowledgeBaseId}/rebuild")
    public R<Void> rebuildIndex(@PathVariable Long knowledgeBaseId) {
        boolean success = ragService.rebuildIndex(knowledgeBaseId);
        return success ? R.ok() : R.fail("重建索引失败");
    }

    // ========== 文档管理接口 ==========

    /**
     * 分页查询知识库文档列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    @GetMapping("/documents/list")
    public TableDataInfo<KnowledgeDocumentVo> documentList(@Validated(QueryGroup.class) KnowledgeDocumentBo bo, PageQuery pageQuery) {
        // 将分页参数设置到bo中
        bo.setPageNum(pageQuery.getPageNum());
        bo.setPageSize(pageQuery.getPageSize());
        bo.setOrderByColumn(pageQuery.getOrderByColumn());
        bo.setIsAsc(pageQuery.getIsAsc());

        return ragService.queryDocumentPageList(bo);
    }

    /**
     * 获取知识库文档列表（不分页）
     *
     * @param knowledgeBaseId 知识库ID
     * @param bo              查询条件
     * @return 文档列表
     */
    @GetMapping("/bases/{knowledgeBaseId}/documents")
    public R<List<KnowledgeDocumentVo>> getDocuments(@PathVariable Long knowledgeBaseId,
                                                     @Validated(QueryGroup.class) KnowledgeDocumentBo bo) {
        if (knowledgeBaseId == null) {
            return R.fail("知识库ID不能为空");
        }

        // 设置知识库ID过滤条件
        bo.setKnowledgeBaseId(knowledgeBaseId);
        List<KnowledgeDocumentVo> documents = ragService.queryDocumentList(bo);
        return R.ok(documents);
    }

    /**
     * 获取文档详情
     *
     * @param documentId 文档ID
     * @return 文档详情
     */
    @GetMapping("/documents/{documentId}")
    public R<KnowledgeDocumentVo> getDocument(@PathVariable Long documentId) {
        if (documentId == null) {
            return R.fail("文档ID不能为空");
        }

        KnowledgeDocumentVo document = ragService.queryDocumentById(documentId);
        if (document == null) {
            return R.fail("文档不存在");
        }
        return R.ok(document);
    }

    /**
     * 添加文档到知识库
     *
     * @param bo 文档信息
     * @return 操作结果
     */
    @Log(title = "知识库管理", businessType = BusinessType.INSERT)
    @PostMapping("/documents")
    public R<Void> addDocument(@RequestBody @Validated(AddGroup.class) KnowledgeDocumentBo bo) {
        // 转换为实体对象
        KnowledgeDocument document = new KnowledgeDocument();
        document.setKnowledgeBaseId(bo.getKnowledgeBaseId());
        document.setTitle(bo.getTitle());
        document.setContent(bo.getContent());
        document.setDocType(bo.getDocType());
        document.setSource(bo.getSource());
        document.setOriginalFilename(bo.getOriginalFilename());
        document.setFilePath(bo.getFilePath());
        document.setFileSize(bo.getFileSize());
        document.setStatus(bo.getStatus());
        document.setProcessStatus(bo.getProcessStatus());
        document.setSummary(bo.getSummary());
        document.setTags(bo.getTags());
        document.setMetadata(bo.getMetadata());
        document.setProcessConfig(bo.getProcessConfig());
        document.setSortOrder(bo.getSortOrder());
        document.setRemark(bo.getRemark());

        boolean success = ragService.addDocument(document);
        return toAjax(success);
    }

    /**
     * 更新文档信息
     *
     * @param bo 文档信息
     * @return 操作结果
     */
    @Log(title = "知识库管理", businessType = BusinessType.UPDATE)
    @PutMapping("/documents")
    public R<Void> updateDocument(@RequestBody @Validated(EditGroup.class) KnowledgeDocumentBo bo) {
        // 转换为实体对象
        KnowledgeDocument document = new KnowledgeDocument();
        document.setId(bo.getId());
        document.setKnowledgeBaseId(bo.getKnowledgeBaseId());
        document.setTitle(bo.getTitle());
        document.setContent(bo.getContent());
        document.setDocType(bo.getDocType());
        document.setSource(bo.getSource());
        document.setOriginalFilename(bo.getOriginalFilename());
        document.setFilePath(bo.getFilePath());
        document.setFileSize(bo.getFileSize());
        document.setStatus(bo.getStatus());
        document.setProcessStatus(bo.getProcessStatus());
        document.setSummary(bo.getSummary());
        document.setTags(bo.getTags());
        document.setMetadata(bo.getMetadata());
        document.setProcessConfig(bo.getProcessConfig());
        document.setSortOrder(bo.getSortOrder());
        document.setRemark(bo.getRemark());

        // 这里需要在Service中实现updateDocument方法
        // boolean success = ragService.updateDocument(document);
        // return toAjax(success);
        return R.fail("更新文档功能待实现");
    }

    /**
     * 删除文档
     *
     * @param documentId 文档ID
     * @return 操作结果
     */
    @Log(title = "知识库管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/documents/{documentId}")
    public R<Void> deleteDocument(@PathVariable Long documentId) {
        if (documentId == null) {
            return R.fail("文档ID不能为空");
        }

        boolean success = ragService.deleteDocument(documentId);
        return toAjax(success);
    }

    /**
     * 处理文档（重新向量化）
     *
     * @param documentId 文档ID
     * @return 操作结果
     */
    @Log(title = "知识库管理", businessType = BusinessType.UPDATE)
    @PostMapping("/documents/{documentId}/process")
    public R<Void> processDocument(@PathVariable Long documentId) {
        if (documentId == null) {
            return R.fail("文档ID不能为空");
        }

        boolean success = ragService.processDocument(documentId);
        return toAjax(success);
    }

    // ========== 搜索接口 ==========

    /**
     * 向量搜索知识库
     *
     * @param request 搜索请求
     * @return 搜索结果
     */
    @Operation(summary = "向量搜索知识库", description = "基于向量相似度搜索知识库内容")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "搜索成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "500", description = "搜索失败")
    })
    @PostMapping("/search")
    public R<List<VectorEmbedding>> searchKnowledge(@RequestBody @Validated SearchRequest request) {
        List<VectorEmbedding> results = ragService.searchKnowledge(
            request.getKnowledgeBaseId(),
            request.getQuery(),
            request.getTopK()
        );
        return R.ok(results);
    }

    /**
     * 混合搜索知识库（向量 + 关键词）
     *
     * @param request 搜索请求
     * @return 搜索结果
     */
    @Operation(summary = "混合搜索知识库", description = "基于向量相似度和关键词搜索知识库内容")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "搜索成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "500", description = "搜索失败")
    })
    @PostMapping("/search/hybrid")
    public R<List<VectorEmbedding>> hybridSearch(@RequestBody @Validated SearchRequest request) {
        List<VectorEmbedding> results = ragService.hybridSearch(
            request.getKnowledgeBaseId(),
            request.getQuery(),
            request.getTopK()
        );
        return R.ok(results);
    }

    /**
     * 搜索请求DTO
     */
    @Data
    public static class SearchRequest {

        /**
         * 知识库ID
         */
        @NotNull(message = "知识库ID不能为空")
        private Long knowledgeBaseId;

        /**
         * 查询内容
         */
        @NotBlank(message = "查询内容不能为空")
        private String query;

        /**
         * 返回结果数量，默认5条
         */
        private Integer topK = 5;

        /**
         * 相似度阈值，默认0.7
         */
        private Double threshold = 0.7;

        /**
         * 搜索类型：vector(向量搜索)、keyword(关键词搜索)、hybrid(混合搜索)
         */
        private String searchType = "vector";

        /**
         * 是否包含元数据
         */
        private Boolean includeMetadata = false;

        /**
         * 过滤条件（JSON格式）
         */
        private String filters;
    }
}
