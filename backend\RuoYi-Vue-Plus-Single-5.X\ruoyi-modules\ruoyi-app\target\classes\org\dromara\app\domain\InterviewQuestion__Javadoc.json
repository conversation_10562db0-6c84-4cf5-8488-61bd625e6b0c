{"doc": "\n 面试问题对象 app_interview_question\r\n\r\n <AUTHOR> Assistant\r\n @date 2025-07-19\r\n", "fields": [{"name": "id", "doc": "\n 问题ID\r\n"}, {"name": "jobId", "doc": "\n 关联岗位ID（NULL表示通用问题）\r\n"}, {"name": "categoryId", "doc": "\n 问题分类ID\r\n"}, {"name": "question", "doc": "\n 问题内容\r\n"}, {"name": "questionType", "doc": "\n 问题类型：choice-选择题，coding-编程题，open-开放题，multimodal-多模态\r\n"}, {"name": "difficulty", "doc": "\n 难度等级（1-5）\r\n"}, {"name": "category", "doc": "\n 问题分类\r\n"}, {"name": "tags", "doc": "\n 问题标签\r\n"}, {"name": "hint", "doc": "\n 提示信息\r\n"}, {"name": "answer", "doc": "\n 参考答案\r\n"}, {"name": "explanation", "doc": "\n 答案解释\r\n"}, {"name": "timeLimit", "doc": "\n 答题时间限制（秒）\r\n"}, {"name": "sortOrder", "doc": "\n 排序号\r\n"}, {"name": "multimodalRequirements", "doc": "\n 多模态评估要求（音频/视频/文本）\r\n"}, {"name": "evaluationCriteria", "doc": "\n 评估标准\r\n"}, {"name": "status", "doc": "\n 状态（0正常 1停用）\r\n"}, {"name": "delFlag", "doc": "\n 删除标志（0代表存在 1代表删除）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}