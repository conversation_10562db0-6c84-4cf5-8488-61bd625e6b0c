package org.dromara.common.rabbitmq.core;

import cn.hutool.core.util.IdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * RabbitMQ 模板封装
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "spring.rabbitmq", name = "enabled", havingValue = "true")
@RequiredArgsConstructor
public class RabbitMqTemplate {

    private final RabbitTemplate rabbitTemplate;

    /**
     * 发送消息到默认交换机
     *
     * @param routingKey 路由键
     * @param message    消息内容
     */
    public void send(String routingKey, Object message) {
        send("", routingKey, message);
    }

    /**
     * 发送消息到指定交换机
     *
     * @param exchange   交换机名称
     * @param routingKey 路由键
     * @param message    消息内容
     */
    public void send(String exchange, String routingKey, Object message) {
        CorrelationData correlationData = new CorrelationData(IdUtil.randomUUID());

        log.info("发送消息到交换机: [{}], 路由键: [{}], 消息ID: [{}]",
            exchange, routingKey, correlationData.getId());

        rabbitTemplate.convertAndSend(exchange, routingKey, message, correlationData);
    }

    /**
     * 发送延迟消息
     *
     * @param exchange   交换机名称
     * @param routingKey 路由键
     * @param message    消息内容
     * @param delayTime  延迟时间（毫秒）
     */
    public void sendDelay(String exchange, String routingKey, Object message, long delayTime) {
        CorrelationData correlationData = new CorrelationData(IdUtil.randomUUID());

        log.info("发送延迟消息到交换机: [{}], 路由键: [{}], 延迟时间: [{}]ms, 消息ID: [{}]",
            exchange, routingKey, delayTime, correlationData.getId());

        rabbitTemplate.convertAndSend(exchange, routingKey, message, msg -> {
            msg.getMessageProperties().setDelayLong(delayTime);
            return msg;
        }, correlationData);
    }

    /**
     * 发送消息并接收响应（RPC模式）
     *
     * @param exchange   交换机名称
     * @param routingKey 路由键
     * @param message    消息内容
     * @return 响应结果
     */
    public Object sendAndReceive(String exchange, String routingKey, Object message) {
        CorrelationData correlationData = new CorrelationData(IdUtil.randomUUID());

        log.info("发送RPC消息到交换机: [{}], 路由键: [{}], 消息ID: [{}]",
            exchange, routingKey, correlationData.getId());

        return rabbitTemplate.convertSendAndReceive(exchange, routingKey, message);
    }

    /**
     * 发送消息到死信队列
     *
     * @param deadExchange   死信交换机
     * @param deadRoutingKey 死信路由键
     * @param message        消息内容
     */
    public void sendToDead(String deadExchange, String deadRoutingKey, Object message) {
        CorrelationData correlationData = new CorrelationData(IdUtil.randomUUID());

        log.info("发送消息到死信队列，交换机: [{}], 路由键: [{}], 消息ID: [{}]",
            deadExchange, deadRoutingKey, correlationData.getId());

        rabbitTemplate.convertAndSend(deadExchange, deadRoutingKey, message, correlationData);
    }

    /**
     * 接收并转换消息
     *
     * @param queueName 队列名称
     * @return 消息内容
     */
    public Object receive(String queueName) {
        return rabbitTemplate.receiveAndConvert(queueName);
    }

    /**
     * 接收并转换消息（带超时）
     *
     * @param queueName 队列名称
     * @param timeout   超时时间（毫秒）
     * @return 消息内容
     */
    public Object receive(String queueName, long timeout) {
        return rabbitTemplate.receiveAndConvert(queueName, timeout);
    }

    /**
     * 创建文本消息
     *
     * @param content 消息内容
     * @return Message对象
     */
    public Message createTextMessage(String content) {
        MessageProperties properties = new MessageProperties();
        properties.setContentType(MessageProperties.CONTENT_TYPE_TEXT_PLAIN);
        properties.setContentEncoding(StandardCharsets.UTF_8.name());
        return new Message(content.getBytes(StandardCharsets.UTF_8), properties);
    }
}
