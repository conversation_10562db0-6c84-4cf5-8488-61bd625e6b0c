package org.dromara.system.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.common.mybatis.core.domain.QuestionBank;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T11:28:38+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.11 (Amazon.com Inc.)"
)
@Component
public class QuestionBankVoToQuestionBankMapperImpl implements QuestionBankVoToQuestionBankMapper {

    @Override
    public QuestionBank convert(QuestionBankVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        QuestionBank questionBank = new QuestionBank();

        questionBank.setCreateTime( arg0.getCreateTime() );
        questionBank.setUpdateTime( arg0.getUpdateTime() );
        questionBank.setBankId( arg0.getBankId() );
        questionBank.setBankCode( arg0.getBankCode() );
        questionBank.setTitle( arg0.getTitle() );
        questionBank.setDescription( arg0.getDescription() );
        questionBank.setMajorId( arg0.getMajorId() );
        questionBank.setIcon( arg0.getIcon() );
        questionBank.setColor( arg0.getColor() );
        questionBank.setDifficulty( arg0.getDifficulty() );
        questionBank.setTotalQuestions( arg0.getTotalQuestions() );
        questionBank.setPracticeCount( arg0.getPracticeCount() );
        questionBank.setCategories( arg0.getCategories() );
        questionBank.setSort( arg0.getSort() );
        questionBank.setStatus( arg0.getStatus() );
        questionBank.setRemark( arg0.getRemark() );
        questionBank.setIsBookmarked( arg0.getIsBookmarked() );
        questionBank.setProgress( arg0.getProgress() );

        return questionBank;
    }

    @Override
    public QuestionBank convert(QuestionBankVo arg0, QuestionBank arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setBankId( arg0.getBankId() );
        arg1.setBankCode( arg0.getBankCode() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setMajorId( arg0.getMajorId() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setColor( arg0.getColor() );
        arg1.setDifficulty( arg0.getDifficulty() );
        arg1.setTotalQuestions( arg0.getTotalQuestions() );
        arg1.setPracticeCount( arg0.getPracticeCount() );
        arg1.setCategories( arg0.getCategories() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setIsBookmarked( arg0.getIsBookmarked() );
        arg1.setProgress( arg0.getProgress() );

        return arg1;
    }
}
