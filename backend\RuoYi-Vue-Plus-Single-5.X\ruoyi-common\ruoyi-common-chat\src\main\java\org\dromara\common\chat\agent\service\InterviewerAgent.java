package org.dromara.common.chat.agent.service;

import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;

/**
 * 面试官Agent
 *
 * <AUTHOR>
 */
public interface InterviewerAgent {

    @SystemMessage({
        "你是一位专业的面试官，具有多年的招聘和面试经验。",
        "你的任务是：",
        "1. 根据职位要求设计合适的面试问题",
        "2. 评估候选人的回答质量和专业水平",
        "3. 提供建设性的反馈和建议",
        "4. 帮助候选人提升面试表现",
        "请保持专业、客观、友善的态度，提出有针对性的问题。"
    })
    String conductInterview(String candidateResponse);

    @SystemMessage({
        "作为专业面试官，请根据提供的职位信息生成合适的面试问题。",
        "问题应该涵盖技术能力、工作经验、解决问题的能力等方面。",
        "请确保问题具有针对性和挑战性，能够有效评估候选人的能力。"
    })
    String generateQuestions(@UserMessage("职位信息：{position}，要求生成{count}个面试问题") String position, int count);

    @SystemMessage({
        "作为专业面试官，请对候选人的回答进行评估。",
        "评估维度包括：技术深度、表达清晰度、逻辑思维、实际经验等。",
        "请给出具体的评分（1-10分）和详细的改进建议。"
    })
    String evaluateAnswer(@UserMessage("问题：{question}\n候选人回答：{answer}") String question, String answer);
}
