{"doc": " WebSocketSession 用于保存当前所有在线的会话信息\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "addSession", "paramTypes": ["java.lang.Long", "org.springframework.web.socket.WebSocketSession"], "doc": " 将WebSocket会话添加到用户会话Map中\n\n @param sessionKey 会话键，用于检索会话\n @param session    要添加的WebSocket会话\n"}, {"name": "removeSession", "paramTypes": ["java.lang.Long"], "doc": " 从用户会话Map中移除指定会话键对应的WebSocket会话\n\n @param sessionKey 要移除的会话键\n"}, {"name": "getSessions", "paramTypes": ["java.lang.Long"], "doc": " 根据会话键从用户会话Map中获取WebSocket会话\n\n @param sessionKey 要获取的会话键\n @return 与给定会话键对应的WebSocket会话，如果不存在则返回null\n"}, {"name": "getSessionsAll", "paramTypes": [], "doc": " 获取存储在用户会话Map中所有WebSocket会话的会话键集合\n\n @return 所有WebSocket会话的会话键集合\n"}, {"name": "existSession", "paramTypes": ["java.lang.Long"], "doc": " 检查给定的会话键是否存在于用户会话Map中\n\n @param sessionKey 要检查的会话键\n @return 如果存在对应的会话键，则返回true；否则返回false\n"}], "constructors": []}