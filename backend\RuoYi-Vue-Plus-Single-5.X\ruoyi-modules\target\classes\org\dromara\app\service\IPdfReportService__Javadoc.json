{"doc": " PDF报告生成服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "generatePdfReport", "paramTypes": ["org.dromara.app.domain.InterviewReport"], "doc": " 生成PDF报告\n\n @param report 面试报告\n @return PDF文件路径\n"}, {"name": "generatePdfReportStream", "paramTypes": ["org.dromara.app.domain.InterviewReport"], "doc": " 生成PDF报告流\n\n @param report 面试报告\n @return PDF文件流\n"}, {"name": "generatePdfReportBytes", "paramTypes": ["org.dromara.app.domain.InterviewReport"], "doc": " 生成PDF报告字节数组\n\n @param report 面试报告\n @return PDF字节数组\n"}, {"name": "isPdfFileExists", "paramTypes": ["java.lang.String"], "doc": " 验证PDF文件是否存在\n\n @param filePath 文件路径\n @return 是否存在\n"}, {"name": "deletePdfFile", "paramTypes": ["java.lang.String"], "doc": " 删除PDF文件\n\n @param filePath 文件路径\n @return 是否删除成功\n"}, {"name": "getPdfFileSize", "paramTypes": ["java.lang.String"], "doc": " 获取PDF文件大小\n\n @param filePath 文件路径\n @return 文件大小（字节）\n"}], "constructors": []}