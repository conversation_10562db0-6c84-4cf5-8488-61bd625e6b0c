# RabbitMQ 配置示例
# 请将以下配置复制到您的 application.yml 文件中并根据实际情况修改

spring:
  rabbitmq:
    # 是否启用RabbitMQ（默认true）
    enabled: true
    # RabbitMQ主机地址
    host: localhost
    # RabbitMQ端口
    port: 5672
    # 用户名
    username: admin
    # 密码
    password: admin
    # 虚拟主机
    virtual-host: /
    # 连接超时时间（毫秒）
    connection-timeout: 60000
    # 通道缓存大小
    channel-cache-size: 50

    # 生产者重试配置
    retry:
      # 是否启用重试
      enabled: true
      # 最大重试次数
      max-attempts: 3
      # 初始重试间隔（毫秒）
      initial-interval: 1000
      # 重试间隔倍数
      multiplier: 2.0
      # 最大重试间隔（毫秒）
      max-interval: 10000

    # 消费者监听配置
    listener:
      # 并发消费者数量
      concurrency: 1
      # 最大并发消费者数量
      max-concurrency: 10
      # 预取数量
      prefetch: 1
      # 消费者重试配置
      retry:
        # 是否启用重试
        enabled: true
        # 最大重试次数
        max-attempts: 3
        # 初始重试间隔（毫秒）
        initial-interval: 1000
        # 重试间隔倍数
        multiplier: 2.0
        # 最大重试间隔（毫秒）
        max-interval: 10000
