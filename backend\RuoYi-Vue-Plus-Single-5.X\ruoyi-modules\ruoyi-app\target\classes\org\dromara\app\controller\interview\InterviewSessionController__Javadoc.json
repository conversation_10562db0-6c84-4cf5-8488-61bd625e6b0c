{"doc": "\n 面试会话控制器\r\n 专门处理面试房间相关的API请求，集成多模态分析功能\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getSessionInfo", "paramTypes": ["java.lang.String"], "doc": "\n 获取会话信息\r\n"}, {"name": "getQuestion", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取面试问题\r\n"}, {"name": "submitAnswer", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "org.springframework.web.multipart.MultipartFile", "org.springframework.web.multipart.MultipartFile", "java.lang.Integer", "java.lang.String"], "doc": "\n 提交面试回答（集成多模态分析）\r\n"}, {"name": "endSession", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 结束面试会话\r\n"}, {"name": "submitFeedback", "paramTypes": ["java.lang.String", "java.lang.Integer", "java.lang.String"], "doc": "\n 提交面试反馈\r\n"}, {"name": "checkDevices", "paramTypes": [], "doc": "\n 检查设备状态\r\n"}, {"name": "getSessionStatus", "paramTypes": ["java.lang.String"], "doc": "\n 获取会话状态\r\n"}], "constructors": []}