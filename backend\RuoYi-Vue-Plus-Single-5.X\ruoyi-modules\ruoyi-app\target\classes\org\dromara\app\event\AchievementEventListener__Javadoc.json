{"doc": "\n 成就事件监听器\r\n 监听各种业务事件，自动触发成就检查和更新\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleUserRegistrationEvent", "paramTypes": ["org.dromara.app.event.AchievementEventListener.UserRegistrationEvent"], "doc": "\n 监听用户注册事件\r\n"}, {"name": "handleLearningCompletedEvent", "paramTypes": ["org.dromara.app.event.AchievementEventListener.LearningCompletedEvent"], "doc": "\n 监听学习完成事件\r\n"}, {"name": "handleInterviewCompletedEvent", "paramTypes": ["org.dromara.app.event.AchievementEventListener.InterviewCompletedEvent"], "doc": "\n 监听面试完成事件\r\n"}, {"name": "handleAbilityImproveEvent", "paramTypes": ["org.dromara.app.event.AchievementEventListener.AbilityImproveEvent"], "doc": "\n 监听能力提升事件\r\n"}, {"name": "checkLearningAchievements", "paramTypes": ["java.lang.String", "org.dromara.app.event.AchievementEventListener.LearningCompletedEvent"], "doc": "\n 检查学习相关成就\r\n"}, {"name": "checkInterviewAchievements", "paramTypes": ["java.lang.String", "org.dromara.app.event.AchievementEventListener.InterviewCompletedEvent"], "doc": "\n 检查面试相关成就\r\n"}, {"name": "checkAbilityAchievements", "paramTypes": ["java.lang.String", "org.dromara.app.event.AchievementEventListener.AbilityImproveEvent"], "doc": "\n 检查能力提升相关成就\r\n"}, {"name": "checkLearningCondition", "paramTypes": ["java.lang.String", "org.dromara.app.domain.Achievement", "org.dromara.app.event.AchievementEventListener.LearningCompletedEvent"], "doc": "\n 检查学习条件\r\n"}, {"name": "checkInterviewCondition", "paramTypes": ["java.lang.String", "org.dromara.app.domain.Achievement", "org.dromara.app.event.AchievementEventListener.InterviewCompletedEvent"], "doc": "\n 检查面试条件\r\n"}, {"name": "checkAbilityCondition", "paramTypes": ["java.lang.String", "org.dromara.app.domain.Achievement", "org.dromara.app.event.AchievementEventListener.AbilityImproveEvent"], "doc": "\n 检查能力提升条件\r\n"}, {"name": "calculateLearningProgress", "paramTypes": ["java.lang.String", "org.dromara.app.domain.Achievement"], "doc": "\n 计算学习进度\r\n"}, {"name": "calculateInterviewProgress", "paramTypes": ["java.lang.String", "org.dromara.app.domain.Achievement"], "doc": "\n 计算面试进度\r\n"}, {"name": "calculateAbilityProgress", "paramTypes": ["java.lang.String", "org.dromara.app.domain.Achievement"], "doc": "\n 计算能力提升进度\r\n"}], "constructors": []}