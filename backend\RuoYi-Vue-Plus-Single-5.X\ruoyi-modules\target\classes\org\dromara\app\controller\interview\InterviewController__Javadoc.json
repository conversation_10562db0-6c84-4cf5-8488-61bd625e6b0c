{"doc": " 面试控制器\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "getJobList", "paramTypes": ["org.dromara.app.domain.bo.JobQueryBo"], "doc": " 获取岗位列表\n"}, {"name": "getCategories", "paramTypes": ["java.lang.Bo<PERSON>an"], "doc": " 获取岗位分类列表\n"}, {"name": "getInterviewModes", "paramTypes": [], "doc": " 获取面试模式列表\n"}, {"name": "getSearchSuggestions", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取搜索建议\n"}, {"name": "createInterviewSession", "paramTypes": ["org.dromara.app.controller.interview.InterviewController.CreateSessionRequest"], "doc": " 创建面试会话\n"}, {"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["org.dromara.app.controller.interview.InterviewController.FavoriteJobRequest"], "doc": " 收藏/取消收藏岗位\n"}, {"name": "checkDevice", "paramTypes": [], "doc": " 设备检测\n"}, {"name": "getStatistics", "paramTypes": [], "doc": " 获取统计信息\n"}, {"name": "getInterviewHistory", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": " 获取面试历史记录\n"}, {"name": "getUserStatistics", "paramTypes": [], "doc": " 获取用户统计数据（用于历史页面）\n"}, {"name": "getJobDetail", "paramTypes": ["java.lang.Long"], "doc": " 获取岗位详情\n"}, {"name": "getSampleQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 获取示例问题\n"}, {"name": "getRelatedJobs", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 获取相关岗位\n"}, {"name": "getJobStatistics", "paramTypes": ["java.lang.Long"], "doc": " 获取岗位统计数据\n"}, {"name": "getJobInterviewModes", "paramTypes": ["java.lang.Long"], "doc": " 获取岗位面试模式列表\n"}, {"name": "checkUserReadiness", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 检查用户准备度\n"}, {"name": "shareJob", "paramTypes": ["org.dromara.app.controller.interview.InterviewController.ShareJobRequest"], "doc": " 分享岗位信息\n"}, {"name": "getInterviewResult", "paramTypes": ["java.lang.String"], "doc": " 获取面试结果\n"}, {"name": "getInterviewerInfo", "paramTypes": ["java.lang.String"], "doc": " 获取AI面试官信息\n"}, {"name": "submitAnswerForRoom", "paramTypes": ["org.dromara.app.controller.interview.InterviewController.SubmitAnswerForRoomRequest"], "doc": " 提交答案\n"}], "constructors": []}