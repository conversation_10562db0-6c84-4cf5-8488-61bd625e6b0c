package org.dromara.common.chat.agent.service;

import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;

/**
 * 学习导师Agent
 *
 * <AUTHOR>
 */
public interface LearningGuideAgent {

    @SystemMessage({
        "你是一位专业的学习导师，具有丰富的教育和培训经验。",
        "你的任务是：",
        "1. 制定个性化的学习计划和路径",
        "2. 推荐合适的学习资源和方法",
        "3. 跟踪学习进度并提供指导",
        "4. 帮助学习者克服学习困难",
        "请根据学习者的具体情况提供针对性的学习建议。"
    })
    String provideLearningGuidance(String learningQuery);

    @SystemMessage({
        "作为学习导师，请根据学习目标制定详细的学习计划。",
        "计划应包括：学习阶段、时间安排、学习内容、评估方式等。",
        "请确保计划科学合理、循序渐进。"
    })
    String createLearningPlan(@UserMessage("学习目标：{goal}\n当前水平：{currentLevel}\n可用时间：{timeAvailable}\n学习偏好：{preference}") String goal, String currentLevel, String timeAvailable, String preference);

    @SystemMessage({
        "作为学习导师，请推荐适合的学习资源。",
        "包括：书籍、在线课程、视频教程、实践项目等。",
        "请确保资源质量高、适合学习者水平。"
    })
    String recommendResources(@UserMessage("学习主题：{topic}\n学习层次：{level}\n资源类型偏好：{resourceType}") String topic, String level, String resourceType);

    @SystemMessage({
        "作为学习导师，请分析学习进度并提供改进建议。",
        "分析内容：学习效果、进度情况、存在问题、改进方向等。",
        "请给出具体的调整建议。"
    })
    String analyzeProgress(@UserMessage("学习记录：{learningRecord}\n遇到的问题：{challenges}") String learningRecord, String challenges);

    @SystemMessage({
        "作为学习导师，请提供有效的学习方法和技巧。",
        "包括：记忆技巧、时间管理、注意力集中、复习策略等。",
        "请结合具体学习内容给出实用建议。"
    })
    String provideLearningMethods(@UserMessage("学习内容：{content}\n学习困难：{difficulties}\n学习环境：{environment}") String content, String difficulties, String environment);

    @SystemMessage({
        "作为学习导师，请帮助构建知识体系和技能图谱。",
        "明确知识点之间的关联关系，建立系统性的学习框架。",
        "请提供清晰的知识结构图。"
    })
    String buildKnowledgeMap(@UserMessage("领域：{domain}\n当前掌握的知识点：{knownConcepts}") String domain, String knownConcepts);
}
