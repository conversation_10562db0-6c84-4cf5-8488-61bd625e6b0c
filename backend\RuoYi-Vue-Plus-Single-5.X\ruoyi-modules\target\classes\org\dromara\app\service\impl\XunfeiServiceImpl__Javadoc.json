{"doc": " 讯飞AI服务实现类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "sparkChat", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": " 智能体spark\n"}, {"name": "sparkChatAgentSync", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map"], "doc": " 调用智能体服务（非流式）\n"}, {"name": "performRealStreamRequest", "paramTypes": ["java.lang.String", "java.util.Map", "org.dromara.app.service.IXunfeiService.StreamCallback"], "doc": " 使用OkHttp执行真正的流式请求\n"}, {"name": "extractTokenFromResponse", "paramTypes": ["cn.hutool.json.JSONObject"], "doc": " 从响应JSON中提取token\n"}, {"name": "buildSparkChatAgentRequest", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map"], "doc": " 构建星火大模型智能体聊天请求（角色化）\n"}, {"name": "invokeStreamingAgentService", "paramTypes": ["org.dromara.app.service.impl.XunfeiServiceImpl.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String", "org.dromara.app.service.impl.XunfeiServiceImpl.StreamingChatResponseHandler"], "doc": " 调用流式Agent服务\n"}, {"name": "buildSparkChatRequest", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": " 构建星火大模型聊天请求（OpenAI兼容格式）\n"}, {"name": "buildSpeechRecognitionRequest", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 构建语音识别请求\n"}, {"name": "buildSpeechRecognitionRequest", "paramTypes": ["byte[]"], "doc": " 构建语音识别请求（字节数组）\n"}, {"name": "buildEmotionAnalysisRequest", "paramTypes": ["java.lang.String"], "doc": " 构建情感分析请求\n"}, {"name": "buildTextToSpeechRequest", "paramTypes": ["java.lang.String", "org.dromara.app.service.IXunfeiService.VoiceConfig"], "doc": " 构建语音合成请求\n"}, {"name": "generateSparkAuthHeaders", "paramTypes": [], "doc": " 生成星火大模型认证头（HTTP Bearer Token）\n"}, {"name": "generateAuthHeaders", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 生成认证头（其他API使用的签名认证）\n"}, {"name": "generateSignature", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 生成签名\n"}, {"name": "extractSparkResponse", "paramTypes": ["cn.hutool.json.JSONObject"], "doc": " 提取星火大模型响应（OpenAI兼容格式）\n"}, {"name": "parseSpeechRecognitionResponse", "paramTypes": ["cn.hutool.json.JSONObject"], "doc": " 解析语音识别响应\n"}, {"name": "parseEmotionAnalysisResponse", "paramTypes": ["cn.hutool.json.JSONObject"], "doc": " 解析情感分析响应\n"}, {"name": "isValidAudioFormat", "paramTypes": ["java.lang.String"], "doc": " 验证音频格式\n"}, {"name": "downloadAudioFromUrl", "paramTypes": ["java.lang.String"], "doc": " 从URL下载音频文件\n"}, {"name": "analyzeVoiceFeatures", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 分析语音特征（简化实现）\n"}, {"name": "combineEmotionResults", "paramTypes": ["org.dromara.app.service.IXunfeiService.EmotionAnalysisResult", "java.util.Map"], "doc": " 综合情感分析结果\n"}, {"name": "calculateOverallConfidence", "paramTypes": ["java.lang.Double", "java.util.Map"], "doc": " 计算综合置信度\n"}, {"name": "processStreamResponse", "paramTypes": ["java.lang.String", "org.dromara.app.service.IXunfeiService.StreamCallback"], "doc": " 处理流式响应（SSE格式）\n"}, {"name": "parseImageEmotionResult", "paramTypes": ["java.lang.String"], "doc": " 解析图像情感分析结果\n"}, {"name": "findDominantEmotion", "paramTypes": ["cn.hutool.json.JSONObject"], "doc": " 找出主导情感\n"}, {"name": "extractEmotionScores", "paramTypes": ["cn.hutool.json.JSONObject"], "doc": " 提取情感分数映射\n"}, {"name": "buildInterviewSuggestionPrompt", "paramTypes": ["java.util.Map", "java.util.Map"], "doc": " 构建面试建议的prompt\n"}], "constructors": []}