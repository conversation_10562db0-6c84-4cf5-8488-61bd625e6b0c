{"doc": "\n 成就事件发布器\r\n 提供便捷的方法来发布各种成就相关事件\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "publishUserRegistrationEvent", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 发布用户注册事件\r\n\r\n @param userId 用户ID\r\n @param source 注册来源\r\n"}, {"name": "publishLearningCompletedEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "int", "int"], "doc": "\n 发布学习完成事件\r\n\r\n @param userId       用户ID\r\n @param resourceId   资源ID\r\n @param resourceType 资源类型\r\n @param duration     学习时长（分钟）\r\n @param score        得分\r\n"}, {"name": "publishInterviewCompletedEvent", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.Long", "int", "int", "java.lang.String"], "doc": "\n 发布面试完成事件\r\n\r\n @param userId      用户ID\r\n @param interviewId 面试ID\r\n @param jobId       职位ID\r\n @param score       面试得分\r\n @param duration    面试时长（分钟）\r\n @param mode        面试模式\r\n"}, {"name": "publishAbilityImproveEvent", "paramTypes": ["java.lang.String", "java.lang.String", "int", "int"], "doc": "\n 发布能力提升事件\r\n\r\n @param userId      用户ID\r\n @param abilityType 能力类型\r\n @param oldScore    原分数\r\n @param newScore    新分数\r\n"}, {"name": "publishVideoWatchedEvent", "paramTypes": ["java.lang.String", "java.lang.String", "int", "int"], "doc": "\n 发布视频观看完成事件\r\n\r\n @param userId   用户ID\r\n @param videoId  视频ID\r\n @param duration 观看时长（秒）\r\n @param progress 观看进度（百分比）\r\n"}, {"name": "publishArticleReadEvent", "paramTypes": ["java.lang.String", "java.lang.String", "int"], "doc": "\n 发布文章阅读完成事件\r\n\r\n @param userId    用户ID\r\n @param articleId 文章ID\r\n @param readTime  阅读时长（分钟）\r\n"}, {"name": "publishQuestionBankPracticeEvent", "paramTypes": ["java.lang.String", "java.lang.String", "int", "int", "int"], "doc": "\n 发布题库练习完成事件\r\n\r\n @param userId       用户ID\r\n @param questionBankId 题库ID\r\n @param correctCount 正确题数\r\n @param totalCount   总题数\r\n @param duration     练习时长（分钟）\r\n"}, {"name": "publishResumeImproveEvent", "paramTypes": ["java.lang.String", "java.lang.String", "int"], "doc": "\n 发布简历完善事件\r\n\r\n @param userId       用户ID\r\n @param resumeId     简历ID\r\n @param completeness 完整度（百分比）\r\n"}, {"name": "publishConsecutiveLoginEvent", "paramTypes": ["java.lang.String", "int"], "doc": "\n 发布连续登录事件\r\n\r\n @param userId      用户ID\r\n @param consecutiveDays 连续登录天数\r\n"}, {"name": "publishShareEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 发布分享事件\r\n\r\n @param userId   用户ID\r\n @param shareType 分享类型\r\n @param targetId  分享目标ID\r\n @param platform  分享平台\r\n"}, {"name": "publishCommentEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "int"], "doc": "\n 发布评论事件\r\n\r\n @param userId    用户ID\r\n @param targetType 评论目标类型\r\n @param targetId   评论目标ID\r\n @param commentLength 评论长度\r\n"}, {"name": "publishFavoriteEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 发布收藏事件\r\n\r\n @param userId     用户ID\r\n @param targetType 收藏目标类型\r\n @param targetId   收藏目标ID\r\n"}, {"name": "publishLikeEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 发布点赞事件\r\n\r\n @param userId     用户ID\r\n @param targetType 点赞目标类型\r\n @param targetId   点赞目标ID\r\n"}, {"name": "publishFollowEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 发布关注事件\r\n\r\n @param userId     用户ID\r\n @param targetType 关注目标类型\r\n @param targetId   关注目标ID\r\n"}, {"name": "publishPaymentCompletedEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Long", "java.lang.String"], "doc": "\n 发布支付完成事件\r\n\r\n @param userId  用户ID\r\n @param orderId 订单ID\r\n @param amount  支付金额（分）\r\n @param productType 产品类型\r\n"}, {"name": "publishInviteFriendEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 发布邀请好友事件\r\n\r\n @param userId     邀请人用户ID\r\n @param inviteeId  被邀请人用户ID\r\n @param inviteType 邀请类型\r\n"}, {"name": "publishProfileUpdateEvent", "paramTypes": ["java.lang.String", "java.lang.String", "int"], "doc": "\n 发布完善个人信息事件\r\n\r\n @param userId       用户ID\r\n @param profileType  信息类型\r\n @param completeness 完整度（百分比）\r\n"}, {"name": "publishSimpleUserLoginEvent", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 发布简单的用户登录事件\r\n\r\n @param userId 用户ID\r\n @param source 登录来源\r\n"}, {"name": "publishSimpleVideoWatchEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": "\n 发布简单的视频观看事件\r\n\r\n @param userId        用户ID\r\n @param videoId       视频ID\r\n @param videoTitle    视频标题\r\n @param watchDuration 观看时长（秒）\r\n"}, {"name": "publishSimpleCommentEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 发布简单的评论事件\r\n\r\n @param userId         用户ID\r\n @param targetType     目标类型（video, article等）\r\n @param targetId       目标ID\r\n @param commentContent 评论内容\r\n"}, {"name": "publishSimpleLikeEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 发布简单的点赞事件\r\n\r\n @param userId     用户ID\r\n @param targetType 目标类型（video, article, comment等）\r\n @param targetId   目标ID\r\n"}, {"name": "publishSimpleStudyTimeEvent", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 发布简单的学习时长事件\r\n\r\n @param userId       用户ID\r\n @param studyMinutes 学习时长（分钟）\r\n"}], "constructors": []}