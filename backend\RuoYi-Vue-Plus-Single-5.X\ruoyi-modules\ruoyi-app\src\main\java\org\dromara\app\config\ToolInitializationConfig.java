package org.dromara.app.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.service.IToolService;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * 工具初始化配置类
 * 在应用启动时自动初始化工具系统
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@Order(1000) // 确保在其他组件初始化后再初始化工具
public class ToolInitializationConfig implements ApplicationRunner {

    private final IToolService toolService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始初始化AI工具系统...");

        try {
            // 初始化系统工具
            toolService.initSystemTools();

            log.info("AI工具系统初始化完成");
        } catch (Exception e) {
            log.error("AI工具系统初始化失败", e);
            // 不抛出异常，避免影响应用启动
        }
    }
}
