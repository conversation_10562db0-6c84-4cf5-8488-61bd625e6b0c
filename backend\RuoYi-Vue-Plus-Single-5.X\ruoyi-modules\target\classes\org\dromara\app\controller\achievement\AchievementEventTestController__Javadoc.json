{"doc": " 成就事件测试控制器\n 用于测试各种成就事件的发布和处理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "testLoginEvent", "paramTypes": ["java.lang.String"], "doc": " 测试用户登录事件\n"}, {"name": "testVideoWatchEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": " 测试视频观看事件\n"}, {"name": "testCommentEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 测试评论事件\n"}, {"name": "testLikeEvent", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 测试点赞事件\n"}, {"name": "testStudyTimeEvent", "paramTypes": ["java.lang.Integer"], "doc": " 测试学习时长事件\n"}, {"name": "testLearningCompletedEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.Integer"], "doc": " 测试学习完成事件（复杂事件）\n"}, {"name": "testInterviewCompletedEvent", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": " 测试面试完成事件（复杂事件）\n"}, {"name": "testAbilityImproveEvent", "paramTypes": ["java.lang.String", "java.lang.Integer", "java.lang.Integer"], "doc": " 测试能力提升事件（复杂事件）\n"}, {"name": "testBatchEvents", "paramTypes": [], "doc": " 批量测试多个事件\n"}, {"name": "testUserRegistrationEvent", "paramTypes": ["java.lang.String"], "doc": " 测试用户注册事件\n"}, {"name": "getTestHelp", "paramTypes": [], "doc": " 获取测试说明\n"}], "constructors": []}