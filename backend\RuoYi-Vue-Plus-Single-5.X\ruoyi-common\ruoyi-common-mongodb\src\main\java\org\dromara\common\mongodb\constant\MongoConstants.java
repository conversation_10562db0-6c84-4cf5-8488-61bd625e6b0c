package org.dromara.common.mongodb.constant;

/**
 * MongoDB常量
 *
 * <AUTHOR>
 */
public interface MongoConstants {

    /**
     * 默认数据库名称
     */
    String DEFAULT_DATABASE = "dromara";

    /**
     * ID字段名
     */
    String ID_FIELD = "id";

    /**
     * 创建时间字段名
     */
    String CREATE_TIME_FIELD = "createTime";

    /**
     * 更新时间字段名
     */
    String UPDATE_TIME_FIELD = "updateTime";

    /**
     * 创建者字段名
     */
    String CREATE_BY_FIELD = "createBy";

    /**
     * 更新者字段名
     */
    String UPDATE_BY_FIELD = "updateBy";

    /**
     * 版本字段名
     */
    String VERSION_FIELD = "version";

    /**
     * 删除标志字段名
     */
    String DEL_FLAG_FIELD = "delFlag";

    /**
     * 未删除标志
     */
    Integer DEL_FLAG_NORMAL = 0;

    /**
     * 已删除标志
     */
    Integer DEL_FLAG_DELETED = 1;

    /**
     * 默认分页大小
     */
    int DEFAULT_PAGE_SIZE = 10;

    /**
     * 最大分页大小
     */
    int MAX_PAGE_SIZE = 1000;
}
