{"doc": "\n 配置管理服务\r\n 提供动态配置管理功能\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getConfig", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 获取配置值\r\n"}, {"name": "setConfig", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 设置运行时配置\r\n"}, {"name": "isFeatureEnabled", "paramTypes": ["java.lang.String"], "doc": "\n 检查特性是否启用\r\n"}, {"name": "setFeatureEnabled", "paramTypes": ["java.lang.String", "boolean"], "doc": "\n 启用/禁用特性\r\n"}, {"name": "getAllConfig", "paramTypes": [], "doc": "\n 获取所有配置\r\n"}, {"name": "getAllFeatureFlags", "paramTypes": [], "doc": "\n 获取所有特性开关\r\n"}, {"name": "resetConfig", "paramTypes": [], "doc": "\n 重置配置\r\n"}, {"name": "resetFeatureFlags", "paramTypes": [], "doc": "\n 重置特性开关\r\n"}], "constructors": []}