{"doc": "\n 徽章数据层\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "countTotalBadges", "paramTypes": [], "doc": "\n 获取总徽章数\r\n\r\n @return 总徽章数\r\n"}, {"name": "countBadgesByCategory", "paramTypes": ["java.lang.String"], "doc": "\n 根据类别获取徽章数\r\n\r\n @param category 类别\r\n @return 该类别的徽章数\r\n"}, {"name": "countBadgesByRarity", "paramTypes": ["java.lang.String"], "doc": "\n 根据稀有度获取徽章数\r\n\r\n @param rarity 稀有度\r\n @return 该稀有度的徽章数\r\n"}, {"name": "selectByAchievementId", "paramTypes": ["java.lang.String"], "doc": "\n 根据成就ID查询徽章\r\n\r\n @param achievementId 成就ID\r\n @return 相关徽章列表\r\n"}, {"name": "selectByIds", "paramTypes": ["java.util.List"], "doc": "\n 根据多个ID批量查询徽章\r\n\r\n @param ids 徽章ID列表\r\n @return 徽章列表\r\n"}, {"name": "selectRecommendedBadges", "paramTypes": [], "doc": "\n 获取所有推荐的徽章\r\n\r\n @return 推荐徽章列表\r\n"}, {"name": "selectByCategory", "paramTypes": ["java.lang.String"], "doc": "\n 获取指定类别的徽章\r\n\r\n @param category 类别\r\n @return 徽章列表\r\n"}, {"name": "selectByRarity", "paramTypes": ["java.lang.String"], "doc": "\n 获取指定稀有度的徽章\r\n\r\n @param rarity 稀有度\r\n @return 徽章列表\r\n"}, {"name": "selectBySpecialFlag", "paramTypes": ["java.lang.String"], "doc": "\n 获取特殊徽章（限时、活动等）\r\n\r\n @param specialFlag 特殊标志\r\n @return 特殊徽章列表\r\n"}, {"name": "selectByTag", "paramTypes": ["java.lang.String"], "doc": "\n 根据标签查询徽章\r\n\r\n @param tag 标签\r\n @return 徽章列表\r\n"}, {"name": "selectAllEnabled", "paramTypes": [], "doc": "\n 获取所有启用的徽章\r\n\r\n @return 启用的徽章列表\r\n"}, {"name": "selectByUnlockCriteria", "paramTypes": ["java.lang.String"], "doc": "\n 根据解锁条件查询徽章\r\n\r\n @param unlockCriteria 解锁条件关键词\r\n @return 徽章列表\r\n"}], "constructors": []}