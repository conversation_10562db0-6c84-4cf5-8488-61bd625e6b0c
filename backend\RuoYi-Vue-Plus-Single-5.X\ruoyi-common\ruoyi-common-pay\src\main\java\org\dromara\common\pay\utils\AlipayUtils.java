package org.dromara.common.pay.utils;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.domain.AlipayTradePagePayModel;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.response.AlipayTradePagePayResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.pay.constant.PayConstants;
import org.dromara.common.pay.exception.PaymentException;
import org.dromara.common.pay.properties.AlipayProperties;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 支付宝支付工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class AlipayUtils {

    /**
     * 创建支付宝支付页面
     *
     * @param alipayClient     支付宝客户端
     * @param alipayProperties 支付宝配置
     * @param orderNo          订单号
     * @param subject          商品标题
     * @param totalAmount      支付金额
     * @param body             商品描述
     * @return 支付页面HTML
     */
    public static String createPayPage(AlipayClient alipayClient,
                                       AlipayProperties alipayProperties,
                                       String orderNo,
                                       String subject,
                                       BigDecimal totalAmount,
                                       String body) {

        // 参数验证
        validateCreatePayPageParams(alipayClient, alipayProperties, orderNo, subject, totalAmount);

        try {
            // 创建API对应的request
            AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();

            // 获取回调地址并验证
            String returnUrl = alipayProperties.getReturnUrl();
            String notifyUrl = alipayProperties.getNotifyUrl();

            log.info("支付宝支付配置 - 同步回调URL: {}, 异步通知URL: {}", returnUrl, notifyUrl);

            // 设置回调地址
            request.setReturnUrl(returnUrl);
            request.setNotifyUrl(notifyUrl);

            // 设置请求参数
            AlipayTradePagePayModel model = new AlipayTradePagePayModel();
            model.setOutTradeNo(orderNo);
            model.setSubject(subject);
            model.setTotalAmount(formatAmount(totalAmount));
            model.setBody(StringUtils.hasText(body) ? body : subject);
            model.setProductCode(PayConstants.Alipay.PRODUCT_CODE_FAST_INSTANT_TRADE_PAY);

            // 设置订单超时时间
            model.setTimeoutExpress(PayConstants.Common.ORDER_TIMEOUT_MINUTES + "m");

            request.setBizModel(model);

            log.info("支付宝支付请求参数 - 订单号: {}, 商品标题: {}, 支付金额: {}",
                orderNo, subject, totalAmount);

            // 调用SDK生成表单
            AlipayTradePagePayResponse response = alipayClient.pageExecute(request);

            if (response.isSuccess()) {
                log.info("支付宝支付页面创建成功，订单号：{}", orderNo);
                return response.getBody();
            } else {
                String errorMsg = String.format("支付宝支付页面创建失败，订单号：%s，错误码：%s，错误信息：%s",
                    orderNo, response.getCode(), response.getMsg());
                log.error(errorMsg);
                throw new PaymentException(response.getCode(), errorMsg);
            }
        } catch (AlipayApiException e) {
            String errorMsg = String.format("支付宝支付页面创建异常，订单号：%s，异常信息：%s", orderNo, e.getMessage());
            log.error(errorMsg, e);
            throw new PaymentException("ALIPAY_API_ERROR", errorMsg, e);
        } catch (Exception e) {
            String errorMsg = String.format("创建支付宝支付页面时发生未知异常，订单号：%s", orderNo);
            log.error(errorMsg, e);
            throw new PaymentException("UNKNOWN_ERROR", errorMsg, e);
        }
    }

    /**
     * 验证创建支付页面的参数
     *
     * @param alipayClient     支付宝客户端
     * @param alipayProperties 支付宝配置
     * @param orderNo          订单号
     * @param subject          商品标题
     * @param totalAmount      支付金额
     */
    private static void validateCreatePayPageParams(AlipayClient alipayClient,
                                                    AlipayProperties alipayProperties,
                                                    String orderNo,
                                                    String subject,
                                                    BigDecimal totalAmount) {
        if (alipayClient == null) {
            throw new PaymentException("ALIPAY_CLIENT_NULL", "支付宝客户端未初始化");
        }

        if (alipayProperties == null) {
            throw new PaymentException("ALIPAY_PROPERTIES_NULL", "支付宝配置信息为空");
        }

        if (!StringUtils.hasText(orderNo)) {
            throw new PaymentException("ORDER_NO_EMPTY", "订单号不能为空");
        }

        if (!StringUtils.hasText(subject)) {
            throw new PaymentException("SUBJECT_EMPTY", "商品标题不能为空");
        }

        // 验证支付金额
        validateAmount(totalAmount);

        // 验证关键配置
        validateAlipayConfig(alipayProperties);
    }

    /**
     * 验证支付宝关键配置
     *
     * @param alipayProperties 支付宝配置
     */
    private static void validateAlipayConfig(AlipayProperties alipayProperties) {
        if (!StringUtils.hasText(alipayProperties.getAppId())) {
            throw new PaymentException("ALIPAY_APP_ID_EMPTY", "支付宝应用ID未配置");
        }

        if (!StringUtils.hasText(alipayProperties.getPrivateKey())) {
            throw new PaymentException("ALIPAY_PRIVATE_KEY_EMPTY", "支付宝商户私钥未配置");
        }

        if (!StringUtils.hasText(alipayProperties.getAlipayPublicKey())) {
            throw new PaymentException("ALIPAY_PUBLIC_KEY_EMPTY", "支付宝公钥未配置");
        }

        String returnUrl = alipayProperties.getReturnUrl();
        String notifyUrl = alipayProperties.getNotifyUrl();

        if (!StringUtils.hasText(returnUrl)) {
            log.warn("支付宝同步回调URL未配置，将使用默认URL");
        }

        if (!StringUtils.hasText(notifyUrl)) {
            log.warn("支付宝异步通知URL未配置，将使用默认URL");
        }

        // 验证URL格式
        if (StringUtils.hasText(returnUrl) && !isValidUrl(returnUrl)) {
            throw new PaymentException("INVALID_RETURN_URL", "支付宝同步回调URL格式不正确：" + returnUrl);
        }

        if (StringUtils.hasText(notifyUrl) && !isValidUrl(notifyUrl)) {
            throw new PaymentException("INVALID_NOTIFY_URL", "支付宝异步通知URL格式不正确：" + notifyUrl);
        }
    }

    /**
     * 验证URL格式是否正确
     *
     * @param url URL地址
     * @return true-格式正确 false-格式错误
     */
    private static boolean isValidUrl(String url) {
        try {
            java.net.URL urlObj = new java.net.URL(url);
            return "http".equalsIgnoreCase(urlObj.getProtocol()) ||
                "https".equalsIgnoreCase(urlObj.getProtocol());
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 查询支付宝订单状态
     *
     * @param alipayClient 支付宝客户端
     * @param orderNo      订单号
     * @return 查询响应
     */
    public static AlipayTradeQueryResponse queryTradeStatus(AlipayClient alipayClient, String orderNo) {
        try {
            AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
            request.setBizContent("{\"out_trade_no\":\"" + orderNo + "\"}");

            AlipayTradeQueryResponse response = alipayClient.execute(request);

            if (response.isSuccess()) {
                log.info("支付宝订单状态查询成功，订单号：{}，交易状态：{}", orderNo, response.getTradeStatus());
            } else {
                log.warn("支付宝订单状态查询失败，订单号：{}，错误码：{}，错误信息：{}",
                    orderNo, response.getCode(), response.getMsg());
            }

            return response;
        } catch (AlipayApiException e) {
            String errorMsg = String.format("支付宝订单状态查询异常，订单号：%s", orderNo);
            log.error(errorMsg, e);
            throw new PaymentException("ALIPAY_QUERY_ERROR", errorMsg, e);
        }
    }

    /**
     * 验证支付宝异步通知签名
     *
     * @param params          通知参数
     * @param alipayPublicKey 支付宝公钥
     * @param charset         字符编码
     * @param signType        签名类型
     * @return true-验证成功 false-验证失败
     */
    public static boolean verifyNotify(Map<String, String> params,
                                       String alipayPublicKey,
                                       String charset,
                                       String signType) {
        try {
            return AlipaySignature.rsaCheckV1(params, alipayPublicKey, charset, signType);
        } catch (AlipayApiException e) {
            log.error("支付宝异步通知签名验证异常", e);
            return false;
        }
    }

    /**
     * 验证支付宝同步回调签名
     *
     * @param params          回调参数
     * @param alipayPublicKey 支付宝公钥
     * @param charset         字符编码
     * @param signType        签名类型
     * @return true-验证成功 false-验证失败
     */
    public static boolean verifyReturn(Map<String, String> params,
                                       String alipayPublicKey,
                                       String charset,
                                       String signType) {
        try {
            return AlipaySignature.rsaCheckV1(params, alipayPublicKey, charset, signType);
        } catch (AlipayApiException e) {
            log.error("支付宝同步回调签名验证异常", e);
            return false;
        }
    }

    /**
     * 根据支付宝交易状态转换为系统支付状态
     *
     * @param tradeStatus 支付宝交易状态
     * @return 系统支付状态
     */
    public static String convertTradeStatus(String tradeStatus) {
        if (!StringUtils.hasText(tradeStatus)) {
            return "unknown";
        }

        switch (tradeStatus) {
            case PayConstants.Alipay.TRADE_STATUS_WAIT_BUYER_PAY:
                return "paying";
            case PayConstants.Alipay.TRADE_STATUS_TRADE_SUCCESS:
            case PayConstants.Alipay.TRADE_STATUS_TRADE_FINISHED:
                return "paid";
            case PayConstants.Alipay.TRADE_STATUS_TRADE_CLOSED:
                return "cancelled";
            default:
                return "unknown";
        }
    }

    /**
     * 格式化金额为支付宝格式（保留两位小数）
     *
     * @param amount 金额
     * @return 格式化后的金额字符串
     */
    public static String formatAmount(BigDecimal amount) {
        return amount.setScale(2, BigDecimal.ROUND_HALF_UP).toString();
    }

    /**
     * 验证支付金额范围
     *
     * @param amount 支付金额（元）
     * @throws PaymentException 金额超出范围时抛出异常
     */
    public static void validateAmount(BigDecimal amount) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new PaymentException("INVALID_AMOUNT", "支付金额必须大于0");
        }

        BigDecimal minAmount = new BigDecimal("0.01");
        BigDecimal maxAmount = new BigDecimal("100000");

        if (amount.compareTo(minAmount) < 0) {
            throw new PaymentException("AMOUNT_TOO_SMALL", "支付金额不能小于0.01元");
        }

        if (amount.compareTo(maxAmount) > 0) {
            throw new PaymentException("AMOUNT_TOO_LARGE", "支付金额不能大于100000元");
        }
    }
}
