{"doc": "\n 题目评论控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "deleteComment", "paramTypes": ["java.lang.String"], "doc": "\n 删除题目评论\r\n\r\n @param commentId 评论ID\r\n @return 删除结果响应\r\n"}, {"name": "likeComment", "paramTypes": ["java.lang.String"], "doc": "\n 点赞/取消点赞评论\r\n\r\n @param commentId 评论ID\r\n @param request   HTTP请求对象\r\n @return 点赞结果响应\r\n"}, {"name": "reportComment", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": "\n 举报评论\r\n\r\n @param commentId 评论ID\r\n @param request   举报请求参数\r\n @return 举报结果响应\r\n"}, {"name": "getCommentDetail", "paramTypes": ["java.lang.String"], "doc": "\n 获取评论详情\r\n\r\n @param commentId 评论ID\r\n @return 评论详情响应\r\n"}], "constructors": []}