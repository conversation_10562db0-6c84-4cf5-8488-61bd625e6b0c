2025-08-01 16:38:42 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 16:38:42 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.11 with PID 24868 (C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend)
2025-08-01 16:38:42 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-01 16:39:12 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 16:39:14 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-01 16:39:14 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-01 16:39:31 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@57cfb4d8
2025-08-01 16:39:31 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-01 16:39:31 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-01 16:39:31 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-01 16:39:41 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-01 16:39:41 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-01 16:39:43 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for *************/*************:6379
2025-08-01 16:39:43 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for *************/*************:6379
2025-08-01 16:39:46 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: text_processor - 文本处理器
2025-08-01 16:39:46 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: datetime_helper - 时间助手
2025-08-01 16:39:49 [main] INFO  o.d.a.service.impl.AgentServiceImpl - 数据库中已存在 11 个代理，跳过初始化
2025-08-01 16:39:49 [main] INFO  o.d.app.service.ChatMemoryManager - 聊天内存管理器初始化完成，清理间隔: 30 分钟
2025-08-01 16:39:49 [main] INFO  o.d.app.service.SseConnectionManager - SSE连接管理器初始化完成，清理间隔: 5 分钟
2025-08-01 16:39:53 [main] INFO  o.d.c.mongodb.config.MongoConfig - 初始化 MongoDB 配置
2025-08-01 16:39:54 [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Windows", "name": "Windows 11", "architecture": "amd64", "version": "10.0"}, "platform": "Java/Amazon.com Inc./17.0.11+9-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@be1c8aa, com.mongodb.Jep395RecordCodecProvider@15bb3db9, com.mongodb.KotlinCodecProvider@287f1aaf]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[**************:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=30000, readTimeoutMS=15000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=30000, readTimeoutMS=30000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=50, minSize=5, maxWaitTimeMS=30000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=600000, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-08-01 16:40:03 [main] INFO  o.d.c.caffeine.config.CaffeineConfig - 初始化Caffeine缓存成功，最大容量: 1000, 过期时间: 3600秒
2025-08-01 16:40:03 [main] INFO  org.dromara.app.config.AsyncConfig - 分析任务执行器已初始化，核心线程数: 5, 最大线程数: 10, 队列容量: 100
2025-08-01 16:40:03 [main] INFO  org.dromara.app.config.AsyncConfig - 报告生成执行器已初始化
2025-08-01 16:40:03 [main] INFO  org.dromara.app.config.AsyncConfig - 缓存优化执行器已初始化
2025-08-01 16:40:03 [main] INFO  org.dromara.app.config.AsyncConfig - 数据清理执行器已初始化
2025-08-01 16:40:07 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5af97169
2025-08-01 16:40:09 [main] INFO  o.d.c.p.config.PayAutoConfiguration - 支付组件自动配置已启用
2025-08-01 16:40:15 [cluster-ClusterId{value='688c7d593efcf57cbd79de4c', description='null'}-**************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server **************:27017
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.lambda$open$0(SocketStream.java:85)
	at java.base/java.util.Optional.orElseThrow(Optional.java:403)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:85)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:233)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:219)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.ConnectException: Connection timed out: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:76)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:104)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:79)
	... 3 common frames omitted
2025-08-01 16:40:15 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-01 16:40:15 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-01 16:40:15 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-01 16:40:15 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-01 16:45:49 [main] INFO  org.dromara.DromaraApplication - Started DromaraApplication in 430.925 seconds (process running for 433.419)
2025-08-01 16:45:49 [schedule-pool-3] INFO  o.d.a.t.InterviewSessionCleanupTask - 开始清理过期的面试会话
2025-08-01 16:45:50 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-08-01 16:45:50 [main] INFO  o.d.a.c.ToolInitializationConfig - 开始初始化AI工具系统...
2025-08-01 16:45:50 [schedule-pool-3] INFO  o.d.a.t.InterviewSessionCleanupTask - 没有找到过期的面试会话
2025-08-01 16:45:50 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: text_processor - 文本处理器
2025-08-01 16:45:50 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: datetime_helper - 时间助手
2025-08-01 16:45:50 [main] INFO  o.d.a.c.ToolInitializationConfig - AI工具系统初始化完成
2025-08-01 16:45:50 [main] INFO  o.d.a.config.AppStartupConfiguration - 开始初始化Agent应用...
2025-08-01 16:45:50 [main] INFO  o.d.app.service.ConfigurationService - 配置管理服务初始化完成
2025-08-01 16:45:50 [main] INFO  o.d.a.config.AppStartupConfiguration - 配置服务初始化完成
2025-08-01 16:45:50 [main] INFO  o.d.a.config.AppStartupConfiguration - 工具执行器注册完成
2025-08-01 16:45:50 [main] INFO  o.d.a.config.AppStartupConfiguration - ========================================
2025-08-01 16:45:50 [main] INFO  o.d.a.config.AppStartupConfiguration - Agent应用启动信息:
2025-08-01 16:45:50 [main] INFO  o.d.a.config.AppStartupConfiguration - - 聊天功能: 启用
2025-08-01 16:45:50 [main] INFO  o.d.a.config.AppStartupConfiguration - - 流式聊天: 启用
2025-08-01 16:45:50 [main] INFO  o.d.a.config.AppStartupConfiguration - - RAG功能: 启用
2025-08-01 16:45:50 [main] INFO  o.d.a.config.AppStartupConfiguration - - 高级RAG: 启用
2025-08-01 16:45:50 [main] INFO  o.d.a.config.AppStartupConfiguration - - 工具调用: 启用
2025-08-01 16:45:50 [main] INFO  o.d.a.config.AppStartupConfiguration - - 限流功能: 启用
2025-08-01 16:45:50 [main] INFO  o.d.a.config.AppStartupConfiguration - - 监控功能: 启用
2025-08-01 16:45:50 [main] INFO  o.d.a.config.AppStartupConfiguration - - 审计日志: 启用
2025-08-01 16:45:50 [main] INFO  o.d.a.config.AppStartupConfiguration - ========================================
2025-08-01 16:45:50 [main] INFO  o.d.a.config.AppStartupConfiguration - Agent应用初始化完成
2025-08-01 16:45:50 [schedule-pool-5] INFO  o.d.a.t.InterviewSessionCleanupTask - 面试会话统计 - 总数: 67, 已创建: 0, 进行中: 0, 暂停: 0, 已完成: 0, 已过期: 67
2025-08-01 16:45:51 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-08-01 16:45:51 [main] INFO  o.d.app.config.PaymentTimeoutConfig - 初始化支付超时队列监听器，队列名：payment:timeout:queue
2025-08-01 16:45:51 [main] INFO  o.d.app.config.PaymentTimeoutConfig - 支付超时队列监听器初始化成功
2025-08-01 17:00:00 [schedule-pool-3] INFO  o.d.app.task.PaymentTokenCleanupTask - 开始清理过期的支付token
2025-08-01 17:00:00 [schedule-pool-3] INFO  o.d.app.task.PaymentTokenCleanupTask - 没有需要清理的过期支付token
2025-08-01 17:15:49 [schedule-pool-9] INFO  o.d.a.t.InterviewSessionCleanupTask - 开始清理过期的面试会话
2025-08-01 17:15:49 [schedule-pool-9] INFO  o.d.a.t.InterviewSessionCleanupTask - 没有找到过期的面试会话
2025-08-01 20:55:51 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 20:55:51 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.11 with PID 19732 (C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend)
2025-08-01 20:55:51 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-01 20:56:10 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 20:56:11 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-01 20:56:11 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-01 20:56:29 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@264c9994
2025-08-01 20:56:29 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-01 20:56:29 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-01 20:56:29 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-01 20:56:36 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-01 20:56:36 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-01 20:56:37 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for *************/*************:6379
2025-08-01 20:56:38 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for *************/*************:6379
2025-08-01 20:56:40 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: text_processor - 文本处理器
2025-08-01 20:56:40 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: datetime_helper - 时间助手
2025-08-01 20:56:41 [main] INFO  o.d.a.service.impl.AgentServiceImpl - 数据库中已存在 11 个代理，跳过初始化
2025-08-01 20:56:41 [main] INFO  o.d.app.service.ChatMemoryManager - 聊天内存管理器初始化完成，清理间隔: 30 分钟
2025-08-01 20:56:41 [main] INFO  o.d.app.service.SseConnectionManager - SSE连接管理器初始化完成，清理间隔: 5 分钟
2025-08-01 20:56:44 [main] INFO  o.d.c.mongodb.config.MongoConfig - 初始化 MongoDB 配置
2025-08-01 20:56:44 [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Windows", "name": "Windows 11", "architecture": "amd64", "version": "10.0"}, "platform": "Java/Amazon.com Inc./17.0.11+9-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@2b4a0be0, com.mongodb.Jep395RecordCodecProvider@72d44be3, com.mongodb.KotlinCodecProvider@4047ca4d]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[**************:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=30000, readTimeoutMS=15000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=30000, readTimeoutMS=30000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=50, minSize=5, maxWaitTimeMS=30000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=600000, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-08-01 20:56:48 [main] INFO  o.d.c.caffeine.config.CaffeineConfig - 初始化Caffeine缓存成功，最大容量: 1000, 过期时间: 3600秒
2025-08-01 20:56:48 [main] INFO  org.dromara.app.config.AsyncConfig - 分析任务执行器已初始化，核心线程数: 5, 最大线程数: 10, 队列容量: 100
2025-08-01 20:56:48 [main] INFO  org.dromara.app.config.AsyncConfig - 报告生成执行器已初始化
2025-08-01 20:56:48 [main] INFO  org.dromara.app.config.AsyncConfig - 缓存优化执行器已初始化
2025-08-01 20:56:48 [main] INFO  org.dromara.app.config.AsyncConfig - 数据清理执行器已初始化
2025-08-01 20:56:50 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5ac7aa18
2025-08-01 20:56:51 [main] INFO  o.d.c.p.config.PayAutoConfiguration - 支付组件自动配置已启用
2025-08-01 20:56:54 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-01 20:56:54 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-01 20:56:54 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-01 20:56:54 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-01 20:57:05 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 20:57:05 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /app/auth/login/password],参数类型[json],参数:[{"phone":"13333333333","password":"123456","clientId":"app","grantType":"password"}]
2025-08-01 20:57:05 [cluster-ClusterId{value='688cb98c4fabfb64a7c82ba0', description='null'}-**************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server **************:27017
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.lambda$open$0(SocketStream.java:85)
	at java.base/java.util.Optional.orElseThrow(Optional.java:403)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:85)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:233)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:219)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.ConnectException: Connection timed out: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:76)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:104)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:79)
	... 3 common frames omitted
2025-08-01 20:57:06 [XNIO-1 task-2] INFO  o.d.a.s.impl.AppUserServiceImpl - hashpw: $2a$10$er/sPITkcwL5dTHsnRHkJua5ZnBzaS4MKtBelxBpu9F6ltia0rOt.
2025-08-01 20:57:08 [schedule-pool-1] INFO  o.d.s.s.i.SysLogininforServiceImpl - [0:0:0:0:0:0:0:1]内网IP[][Success][登录成功]
2025-08-01 20:57:08 [XNIO-1 task-2] INFO  o.d.web.listener.UserActionListener - user doLogin, userId:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiY0l6VFRKa0hpcGVmZElMc0VFcnFCaEVhQlZNVXJlcEcifQ.m4stLNoHSlwdZLlXv_8Fc9dtkQ7TRosB3sXAtbNFwxM
2025-08-01 20:57:09 [XNIO-1 task-2] INFO  o.d.web.listener.UserActionListener - user doLogout, userId:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiQnowWGlCWjVJTmpmQjlnb041dU5JSDBiSm4xdUNZZWMifQ.7zXv8OK1-zVULc3XMHBp3ePXym38OC2S_AlFiohSpj4
2025-08-01 20:57:09 [XNIO-1 task-2] INFO  o.d.a.c.auth.AppAuthController - 手机号：13333333333 登录成功
2025-08-01 20:57:09 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /app/auth/login/password],耗时:[4141]毫秒
2025-08-01 20:57:12 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /dashboard/summary],无参数
2025-08-01 20:57:12 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /dashboard/abilities],无参数
2025-08-01 20:57:12 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /dashboard/study-stats],无参数
2025-08-01 20:57:12 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /dashboard/smart-tasks],参数类型[param],参数:[{"limit":["3"]}]
2025-08-01 20:57:12 [XNIO-1 task-5] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /dashboard/abilities],耗时:[4]毫秒
2025-08-01 20:57:12 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /dashboard/study-stats],耗时:[4]毫秒
2025-08-01 20:57:12 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /dashboard/smart-tasks],耗时:[4]毫秒
2025-08-01 20:57:12 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /dashboard/summary],耗时:[12]毫秒
2025-08-01 20:57:12 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /dashboard/study-stats],无参数
2025-08-01 20:57:12 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /dashboard/study-stats],耗时:[1]毫秒
2025-08-01 20:57:12 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /dashboard/abilities],无参数
2025-08-01 20:57:12 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /dashboard/abilities],耗时:[6]毫秒
2025-08-01 20:57:12 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /dashboard/smart-tasks],参数类型[param],参数:[{"limit":["3"]}]
2025-08-01 20:57:12 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /dashboard/smart-tasks],耗时:[1]毫秒
2025-08-01 20:57:12 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /dashboard/summary],无参数
2025-08-01 20:57:12 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /dashboard/summary],耗时:[1]毫秒
2025-08-01 21:11:43 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 21:11:43 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.11 with PID 29800 (C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend)
2025-08-01 21:11:43 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-01 21:12:14 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 21:12:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-01 21:12:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-01 21:12:29 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@********
2025-08-01 21:12:29 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-01 21:12:29 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-01 21:12:29 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-01 21:12:36 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-01 21:12:36 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-01 21:12:38 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for *************/*************:6379
2025-08-01 21:12:38 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for *************/*************:6379
2025-08-01 21:12:40 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: text_processor - 文本处理器
2025-08-01 21:12:40 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: datetime_helper - 时间助手
2025-08-01 21:12:42 [main] INFO  o.d.a.service.impl.AgentServiceImpl - 数据库中已存在 11 个代理，跳过初始化
2025-08-01 21:12:42 [main] INFO  o.d.app.service.ChatMemoryManager - 聊天内存管理器初始化完成，清理间隔: 30 分钟
2025-08-01 21:12:42 [main] INFO  o.d.app.service.SseConnectionManager - SSE连接管理器初始化完成，清理间隔: 5 分钟
2025-08-01 21:12:45 [main] INFO  o.d.c.mongodb.config.MongoConfig - 初始化 MongoDB 配置
2025-08-01 21:12:45 [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Windows", "name": "Windows 11", "architecture": "amd64", "version": "10.0"}, "platform": "Java/Amazon.com Inc./17.0.11+9-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@48405f04, com.mongodb.Jep395RecordCodecProvider@2a7af3d9, com.mongodb.KotlinCodecProvider@1c09a369]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[**************:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=30000, readTimeoutMS=15000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=30000, readTimeoutMS=30000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=50, minSize=5, maxWaitTimeMS=30000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=600000, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-08-01 21:12:51 [main] INFO  o.d.c.caffeine.config.CaffeineConfig - 初始化Caffeine缓存成功，最大容量: 1000, 过期时间: 3600秒
2025-08-01 21:12:51 [main] INFO  org.dromara.app.config.AsyncConfig - 分析任务执行器已初始化，核心线程数: 5, 最大线程数: 10, 队列容量: 100
2025-08-01 21:12:51 [main] INFO  org.dromara.app.config.AsyncConfig - 报告生成执行器已初始化
2025-08-01 21:12:51 [main] INFO  org.dromara.app.config.AsyncConfig - 缓存优化执行器已初始化
2025-08-01 21:12:51 [main] INFO  org.dromara.app.config.AsyncConfig - 数据清理执行器已初始化
2025-08-01 21:12:54 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6c37bd27
2025-08-01 21:12:56 [main] INFO  o.d.c.p.config.PayAutoConfiguration - 支付组件自动配置已启用
2025-08-01 21:12:59 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-01 21:12:59 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-01 21:12:59 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-01 21:12:59 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-01 21:13:00 [main] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-01 21:13:00 [main] INFO  o.d.app.service.SseConnectionManager - SSE连接管理器已销毁
2025-08-01 21:13:00 [main] INFO  o.d.app.service.ChatMemoryManager - 聊天内存管理器已销毁
2025-08-01 21:13:00 [main] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-08-01 21:13:00 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-01 21:13:00 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-01 21:13:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-01 21:13:16 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-01 21:13:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-01 21:14:19 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 21:14:19 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.11 with PID 29040 (C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code2\backend)
2025-08-01 21:14:19 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-01 21:14:56 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 21:14:59 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-01 21:14:59 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-01 21:15:13 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@26c494d7
2025-08-01 21:15:13 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-01 21:15:13 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-01 21:15:13 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-01 21:15:25 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-01 21:15:25 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-01 21:15:27 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for *************/*************:6379
2025-08-01 21:15:28 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for *************/*************:6379
2025-08-01 21:15:31 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: text_processor - 文本处理器
2025-08-01 21:15:31 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: datetime_helper - 时间助手
2025-08-01 21:15:34 [main] INFO  o.d.a.service.impl.AgentServiceImpl - 数据库中已存在 11 个代理，跳过初始化
2025-08-01 21:15:34 [main] INFO  o.d.app.service.ChatMemoryManager - 聊天内存管理器初始化完成，清理间隔: 30 分钟
2025-08-01 21:15:34 [main] INFO  o.d.app.service.SseConnectionManager - SSE连接管理器初始化完成，清理间隔: 5 分钟
2025-08-01 21:15:38 [main] INFO  o.d.c.mongodb.config.MongoConfig - 初始化 MongoDB 配置
2025-08-01 21:15:39 [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Windows", "name": "Windows 11", "architecture": "amd64", "version": "10.0"}, "platform": "Java/Amazon.com Inc./17.0.11+9-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@2d36a825, com.mongodb.Jep395RecordCodecProvider@68d751bf, com.mongodb.KotlinCodecProvider@7720e110]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[**************:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=30000, readTimeoutMS=15000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=30000, readTimeoutMS=30000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=50, minSize=5, maxWaitTimeMS=30000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=600000, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-08-01 21:15:44 [main] INFO  o.d.c.caffeine.config.CaffeineConfig - 初始化Caffeine缓存成功，最大容量: 1000, 过期时间: 3600秒
2025-08-01 21:15:44 [main] INFO  org.dromara.app.config.AsyncConfig - 分析任务执行器已初始化，核心线程数: 5, 最大线程数: 10, 队列容量: 100
2025-08-01 21:15:44 [main] INFO  org.dromara.app.config.AsyncConfig - 报告生成执行器已初始化
2025-08-01 21:15:44 [main] INFO  org.dromara.app.config.AsyncConfig - 缓存优化执行器已初始化
2025-08-01 21:15:44 [main] INFO  org.dromara.app.config.AsyncConfig - 数据清理执行器已初始化
2025-08-01 21:15:47 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5b5c0057
2025-08-01 21:15:50 [main] INFO  o.d.c.p.config.PayAutoConfiguration - 支付组件自动配置已启用
2025-08-01 21:15:54 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-01 21:15:54 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-01 21:15:54 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-01 21:15:54 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-01 21:15:54 [main] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-01 21:15:55 [main] INFO  o.d.app.service.SseConnectionManager - SSE连接管理器已销毁
2025-08-01 21:15:55 [main] INFO  o.d.app.service.ChatMemoryManager - 聊天内存管理器已销毁
2025-08-01 21:15:55 [main] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-08-01 21:15:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-01 21:15:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-01 21:16:02 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-01 21:16:02 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-01 21:16:02 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
