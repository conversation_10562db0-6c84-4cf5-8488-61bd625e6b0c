{"doc": "\n 用户活动总览Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-16\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询活动总览\r\n\r\n @param userId 用户ID\r\n @return 活动总览\r\n"}, {"name": "updateSummary", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType", "java.lang.Long"], "doc": "\n 更新用户活动总览\r\n\r\n @param userId       用户ID\r\n @param activityType 活动类型\r\n @param duration     时长\r\n @return 更新行数\r\n"}, {"name": "resetSummary", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.enums.ActivityType"], "doc": "\n 重置用户活动总览\r\n\r\n @param userId       用户ID\r\n @param activityType 活动类型(可选，为空时重置所有)\r\n @return 更新行数\r\n"}], "constructors": []}