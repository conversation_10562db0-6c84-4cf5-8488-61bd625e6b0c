{"doc": "\n 聊天消息Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectSessionMessages", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.String", "java.lang.Long"], "doc": "\n 分页查询会话消息列表\r\n\r\n @param page      分页对象\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @return 消息分页结果\r\n"}, {"name": "selectRecentMessages", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.Integer"], "doc": "\n 查询会话的最新N条消息（用于构建上下文）\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @param limit     消息数量限制\r\n @return 消息列表\r\n"}, {"name": "selectFirstUserMessage", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 查询会话的第一条用户消息（用于生成标题）\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @return 第一条用户消息\r\n"}, {"name": "countSessionMessages", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 统计会话消息数量\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @return 消息数量\r\n"}, {"name": "deleteSessionMessages", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 清空会话消息（软删除）\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @return 影响行数\r\n"}, {"name": "updateMessageStatus", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 更新消息状态\r\n\r\n @param messageId 消息ID\r\n @param status    消息状态\r\n @return 影响行数\r\n"}, {"name": "markMessagesAsRead", "paramTypes": ["java.util.List", "java.lang.Long"], "doc": "\n 标记消息为已读\r\n\r\n @param messageIds 消息ID列表\r\n @param userId     用户ID\r\n @return 影响行数\r\n"}, {"name": "countUnreadMessages", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户未读消息数量\r\n\r\n @param userId 用户ID\r\n @return 未读消息数量\r\n"}, {"name": "countMessagesByType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 根据消息类型统计数量\r\n\r\n @param userId      用户ID\r\n @param messageType 消息类型\r\n @return 消息数量\r\n"}, {"name": "batchInsertMessages", "paramTypes": ["java.util.List"], "doc": "\n 批量插入消息\r\n\r\n @param messages 消息列表\r\n @return 影响行数\r\n"}, {"name": "searchMessages", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.time.LocalDateTime", "java.time.LocalDateTime"], "doc": "\n 搜索消息内容\r\n\r\n @param userId    用户ID\r\n @param sessionId 会话ID（可选）\r\n @param keyword   搜索关键词\r\n @param startTime 开始时间（可选）\r\n @param endTime   结束时间（可选）\r\n @return 消息列表\r\n"}, {"name": "getMessageStats", "paramTypes": ["java.lang.Long", "java.time.LocalDateTime", "java.time.LocalDateTime"], "doc": "\n 获取用户消息统计信息\r\n\r\n @param userId    用户ID\r\n @param startTime 开始时间（可选）\r\n @param endTime   结束时间（可选）\r\n @return 统计信息\r\n"}, {"name": "selectLastMessage", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 获取会话中的最后一条消息\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @return 最后一条消息\r\n"}, {"name": "updateMessageError", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 更新消息错误信息\r\n\r\n @param messageId    消息ID\r\n @param errorMessage 错误信息\r\n @return 影响行数\r\n"}, {"name": "selectChildMessages", "paramTypes": ["java.lang.String"], "doc": "\n 根据父消息ID查询子消息\r\n\r\n @param parentMessageId 父消息ID\r\n @return 子消息列表\r\n"}, {"name": "countUserMessages", "paramTypes": ["java.lang.Long"], "doc": "\n 统计用户消息总数\r\n\r\n @param userId 用户ID\r\n @return 消息总数\r\n"}, {"name": "countTodayMessages", "paramTypes": ["java.lang.Long"], "doc": "\n 统计今日消息数\r\n\r\n @param userId 用户ID\r\n @return 今日消息数\r\n"}], "constructors": []}