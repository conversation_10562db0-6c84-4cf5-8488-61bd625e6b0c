{"doc": "\n 成就系统完整性检查工具\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "checkSystemIntegrity", "paramTypes": [], "doc": "\n 检查成就系统完整性\r\n\r\n @return 检查结果\r\n"}, {"name": "checkDatabaseTables", "paramTypes": [], "doc": "\n 检查数据库表是否存在\r\n\r\n @return 检查结果\r\n"}, {"name": "checkRabbitMQConfig", "paramTypes": [], "doc": "\n 检查RabbitMQ配置\r\n\r\n @return 检查结果\r\n"}, {"name": "generateHealthReport", "paramTypes": [], "doc": "\n 生成系统健康报告\r\n\r\n @return 健康报告\r\n"}, {"name": "printSystemStatus", "paramTypes": [], "doc": "\n 打印系统状态报告\r\n"}], "constructors": []}