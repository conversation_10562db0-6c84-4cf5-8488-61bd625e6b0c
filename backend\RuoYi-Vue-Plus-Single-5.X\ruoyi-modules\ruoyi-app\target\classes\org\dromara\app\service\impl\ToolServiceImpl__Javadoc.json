{"doc": "\n AI工具服务实现类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "executeSingleBatchCall", "paramTypes": ["org.dromara.app.service.IToolService.BatchToolCall", "java.lang.Long"], "doc": "\n 执行单个批量调用\r\n"}, {"name": "parseJsonArrayFormat", "paramTypes": ["java.lang.String"], "doc": "\n 解析JSON数组格式的工具调用\r\n 格式: [{\"tool\": \"calculator\", \"parameters\": {\"expression\": \"2+3\"}, \"reasoning\": \"计算表达式\"}]\r\n"}, {"name": "parseXmlFormat", "paramTypes": ["java.lang.String"], "doc": "\n 解析XML格式的工具调用\r\n 格式: <tool_call><tool>calculator</tool><parameters>{\"expression\": \"2+3\"}</parameters></tool_call>\r\n"}, {"name": "parseMarkdownFormat", "paramTypes": ["java.lang.String"], "doc": "\n 解析Markdown格式的工具调用\r\n 格式: ```tool:calculator\\n{\"expression\": \"2+3\"}\\n```\r\n"}, {"name": "parseFunctionCallFormat", "paramTypes": ["java.lang.String"], "doc": "\n 解析函数调用格式\r\n 格式: calculator(expression=\"2+3\")\r\n"}, {"name": "validateParametersAgainstSchema", "paramTypes": ["org.dromara.app.domain.AiTool.ParameterSchema", "java.util.Map", "java.util.List"], "doc": "\n 根据Schema验证参数\r\n"}, {"name": "validateParameterProperty", "paramTypes": ["java.lang.String", "java.lang.Object", "org.dromara.app.domain.AiTool.ParameterSchema.ParameterProperty", "java.util.List"], "doc": "\n 验证单个参数属性\r\n"}, {"name": "enhanceUserStats", "paramTypes": ["java.util.Map", "java.lang.Long"], "doc": "\n 增强用户统计信息\r\n"}, {"name": "enhanceToolStats", "paramTypes": ["java.util.Map", "java.lang.String"], "doc": "\n 增强工具统计信息\r\n"}, {"name": "validateToolConfiguration", "paramTypes": ["org.dromara.app.domain.AiTool"], "doc": "\n 验证工具配置完整性\r\n"}, {"name": "checkSpecificPermission", "paramTypes": ["org.dromara.app.domain.AiTool", "java.lang.Long"], "doc": "\n 检查特定权限\r\n"}, {"name": "clearToolCache", "paramTypes": ["java.lang.String"], "doc": "\n 清除工具相关缓存\r\n"}, {"name": "createFailureResult", "paramTypes": ["org.dromara.app.domain.ToolCall", "long", "java.lang.String"], "doc": "\n 创建失败结果并更新调用记录\r\n"}, {"name": "executeWithConfig", "paramTypes": ["org.dromara.app.tool.ToolExecutor", "java.util.Map", "org.dromara.app.service.IToolService.ToolCallContext", "org.dromara.app.domain.AiTool"], "doc": "\n 使用工具配置执行工具\r\n"}, {"name": "executeWithTimeout", "paramTypes": ["org.dromara.app.tool.ToolExecutor", "java.util.Map", "org.dromara.app.service.IToolService.ToolCallContext", "int"], "doc": "\n 带超时的工具执行\r\n"}, {"name": "getAsyncExecutor", "paramTypes": [], "doc": "\n 获取异步执行器\r\n"}, {"name": "validateAndDeduplicateParsedCalls", "paramTypes": ["java.util.List"], "doc": "\n 验证和去重解析的工具调用\r\n"}, {"name": "isValidToolName", "paramTypes": ["java.lang.String"], "doc": "\n 验证工具名是否有效\r\n"}, {"name": "parseFunctionParameters", "paramTypes": ["java.lang.String"], "doc": "\n 解析函数参数\r\n"}, {"name": "validateNormalizedParameters", "paramTypes": ["org.dromara.app.domain.AiTool.ParameterSchema", "java.util.Map", "java.util.List"], "doc": "\n 验证标准化后的参数\r\n"}, {"name": "isValidParameterType", "paramTypes": ["java.lang.Object", "java.lang.String"], "doc": "\n 验证参数类型\r\n"}, {"name": "isInteger", "paramTypes": ["java.lang.String"], "doc": "\n 检查字符串是否为整数\r\n"}, {"name": "isNumber", "paramTypes": ["java.lang.String"], "doc": "\n 检查字符串是否为数字\r\n"}, {"name": "isBooleanString", "paramTypes": ["java.lang.String"], "doc": "\n 检查字符串是否为布尔值\r\n"}, {"name": "processToolCallData", "paramTypes": ["org.dromara.app.domain.ToolCall"], "doc": "\n 处理工具调用记录数据\r\n"}, {"name": "updateAsyncCallFailure", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.String"], "doc": "\n 更新异步调用失败状态\r\n"}, {"name": "updateAsyncCallResult", "paramTypes": ["java.lang.String", "java.lang.Long", "org.dromara.app.service.IToolService.ToolCallResult", "java.lang.String"], "doc": "\n 更新异步调用结果\r\n"}], "constructors": []}