{"doc": "\n 讯飞数字人认证服务\r\n\r\n <AUTHOR>\r\n @date 2025-07-20\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "assembleRequestUrl", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 组装WebSocket请求URL（带认证信息）\r\n\r\n @param requestUrl WebSocket URL\r\n @param apiKey     API Key\r\n @param apiSecret  API Secret\r\n @return 带认证信息的URL\r\n"}, {"name": "assembleRequestUrl", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 组装WebSocket请求URL（带认证信息）\r\n\r\n @param requestUrl WebSocket URL\r\n @param apiKey     API Key\r\n @param apiSecret  API Secret\r\n @param method     HTTP方法\r\n @return 带认证信息的URL\r\n"}, {"name": "assembleRequestHeader", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "byte[]"], "doc": "\n 计算签名所需要的header参数（HTTP接口）\r\n\r\n @param requestUrl HTTP请求URL\r\n @param apiKey     API Key\r\n @param apiSecret  API Secret\r\n @param method     HTTP方法\r\n @param body       HTTP请求体\r\n @return header map，包含所有需要设置的请求头\r\n"}], "constructors": []}