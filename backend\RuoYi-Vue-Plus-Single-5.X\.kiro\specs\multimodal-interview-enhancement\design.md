# 多模态智能面试评测系统增强设计文档

## 概述

本设计文档基于需求分析，详细描述了多模态智能面试评测系统的技术架构、核心组件设计、数据流程和实现方案。系统将在现有RuoYi框架基础上，集成讯飞星火大模型和多模态AI分析能力，构建一个完整的智能面试评测平台。

## 系统架构

### 整体架构设计

```mermaid
graph TB
    subgraph "前端层"
        A[Web前端界面] --> B[面试录制组件]
        A --> C[实时反馈组件] 
        A --> D[报告展示组件]
        A --> E[学习路径组件]
    end
    
    subgraph "网关层"
        F[API网关] --> G[认证授权]
        F --> H[负载均衡]
        F --> I[限流熔断]
    end
    
    subgraph "业务服务层"
        J[面试管理服务] --> K[多模态分析服务]
        J --> L[评估引擎服务]
        J --> M[学习推荐服务]
        K --> N[语音分析模块]
        K --> O[视频分析模块]
        K --> P[文本分析模块]
    end
    
    subgraph "AI服务层"
        Q[讯飞星火大模型] --> R[自然语言处理]
        Q --> S[语音识别转换]
        Q --> T[情感分析引擎]
        U[计算机视觉服务] --> V[表情识别]
        U --> W[姿态分析]
        U --> X[眼神追踪]
    end
    
    subgraph "数据层"
        Y[MySQL数据库] --> Z[面试数据]
        Y --> AA[用户数据]
        Y --> BB[评估结果]
        CC[Redis缓存] --> DD[会话数据]
        CC --> EE[实时状态]
        FF[文件存储] --> GG[音视频文件]
        FF --> HH[报告文件]
    end
    
    A --> F
    F --> J
    J --> Q
    J --> U
    J --> Y
    J --> CC
    J --> FF
```

### 核心技术栈

- **后端框架**: Spring Boot 3.x + RuoYi-Plus
- **数据库**: MySQL 8.0 + Redis 7.x
- **AI引擎**: 讯飞星火大模型 + 科大讯飞AI工具链
- **多媒体处理**: FFmpeg + OpenCV
- **实时通信**: WebSocket + SSE
- **文件存储**: MinIO/阿里云OSS
- **消息队列**: RabbitMQ
- **监控**: Prometheus + Grafana

## 组件设计

### 1. 多模态数据采集组件

#### 1.1 前端采集模块
```typescript
interface MultiModalCollector {
  // 音频采集
  startAudioRecording(): Promise<MediaStream>;
  stopAudioRecording(): Promise<Blob>;
  
  // 视频采集
  startVideoRecording(): Promise<MediaStream>;
  stopVideoRecording(): Promise<Blob>;
  
  // 实时数据流
  streamAudioData(callback: (chunk: ArrayBuffer) => void): void;
  streamVideoFrame(callback: (frame: ImageData) => void): void;
  
  // 质量检测
  checkAudioQuality(): AudioQualityMetrics;
  checkVideoQuality(): VideoQualityMetrics;
}
```

#### 1.2 后端接收处理
```java
@RestController
@RequestMapping("/api/multimodal")
public class MultiModalController {
    
    @PostMapping("/upload/audio")
    public R<String> uploadAudio(@RequestParam("file") MultipartFile audioFile,
                                @RequestParam("sessionId") String sessionId);
    
    @PostMapping("/upload/video") 
    public R<String> uploadVideo(@RequestParam("file") MultipartFile videoFile,
                                @RequestParam("sessionId") String sessionId);
    
    @PostMapping("/stream/audio")
    public SseEmitter streamAudioAnalysis(@RequestParam("sessionId") String sessionId);
    
    @PostMapping("/stream/video")
    public SseEmitter streamVideoAnalysis(@RequestParam("sessionId") String sessionId);
}
```

### 2. 多模态分析引擎

#### 2.1 语音分析模块
```java
@Service
public class AudioAnalysisService {
    
    public AudioMetrics analyzeAudio(String audioFilePath, String sessionId) {
        AudioMetrics metrics = new AudioMetrics();
        
        // 使用讯飞语音识别
        String transcript = xunfeiSpeechRecognition.recognize(audioFilePath);
        
        // 语音质量分析
        metrics.setClarity(calculateClarity(audioFilePath));
        metrics.setFluency(calculateFluency(transcript));
        metrics.setPace(calculateSpeechRate(audioFilePath, transcript));
        
        // 情感分析
        EmotionResult emotion = xunfeiEmotionAnalysis.analyze(transcript);
        metrics.setConfidence(emotion.getConfidenceLevel());
        
        // 语言逻辑分析
        LogicAnalysisResult logic = analyzeLogic(transcript);
        metrics.setLogicalCoherence(logic.getCoherenceScore());
        
        return metrics;
    }
    
    private Integer calculateClarity(String audioFilePath) {
        // 使用音频信号处理算法计算清晰度
        // 分析信噪比、频谱特征等
        return audioQualityAnalyzer.calculateClarity(audioFilePath);
    }
    
    private Integer calculateFluency(String transcript) {
        // 分析停顿、重复、语法错误等
        return fluencyAnalyzer.analyze(transcript);
    }
}
```

#### 2.2 视频分析模块
```java
@Service
public class VideoAnalysisService {
    
    public VideoMetrics analyzeVideo(String videoFilePath, String sessionId) {
        VideoMetrics metrics = new VideoMetrics();
        
        // 使用OpenCV进行视频处理
        List<Mat> frames = videoProcessor.extractFrames(videoFilePath);
        
        // 眼神交流分析
        metrics.setEyeContact(analyzeEyeContact(frames));
        
        // 姿态分析
        metrics.setPosture(analyzePosture(frames));
        
        // 表情分析
        metrics.setExpressions(analyzeExpressions(frames));
        
        // 手势分析
        metrics.setGestures(analyzeGestures(frames));
        
        return metrics;
    }
    
    private Integer analyzeEyeContact(List<Mat> frames) {
        int eyeContactFrames = 0;
        for (Mat frame : frames) {
            if (eyeContactDetector.detectEyeContact(frame)) {
                eyeContactFrames++;
            }
        }
        return (eyeContactFrames * 100) / frames.size();
    }
    
    private Integer analyzeExpressions(List<Mat> frames) {
        Map<String, Integer> expressionCounts = new HashMap<>();
        for (Mat frame : frames) {
            String expression = faceExpressionDetector.detect(frame);
            expressionCounts.merge(expression, 1, Integer::sum);
        }
        return calculateExpressionScore(expressionCounts);
    }
}
```

#### 2.3 文本分析模块
```java
@Service
public class TextAnalysisService {
    
    public TextMetrics analyzeText(String transcript, String jobPosition, String sessionId) {
        TextMetrics metrics = new TextMetrics();
        
        // 使用讯飞星火大模型进行内容分析
        String prompt = buildAnalysisPrompt(transcript, jobPosition);
        String analysis = xunfeiSparkLLM.chat(prompt);
        
        // 解析分析结果
        ContentAnalysisResult result = parseAnalysisResult(analysis);
        
        metrics.setProfessionalKnowledge(result.getProfessionalScore());
        metrics.setLogicalThinking(result.getLogicScore());
        metrics.setInnovation(result.getInnovationScore());
        metrics.setSkillMatching(result.getSkillMatchScore());
        
        // STAR结构分析
        metrics.setStarStructure(analyzeStarStructure(transcript));
        
        return metrics;
    }
    
    private String buildAnalysisPrompt(String transcript, String jobPosition) {
        return String.format("""
            请分析以下面试回答的质量，针对%s岗位：
            
            回答内容：%s
            
            请从以下维度评分（1-100分）：
            1. 专业知识水平
            2. 逻辑思维能力  
            3. 创新能力
            4. 技能匹配度
            5. 是否使用STAR结构
            
            请以JSON格式返回评分和具体分析。
            """, jobPosition, transcript);
    }
}
```

### 3. 智能评估引擎

#### 3.1 综合评分算法
```java
@Service
public class InterviewEvaluationEngine {
    
    public InterviewResult evaluateInterview(String sessionId) {
        // 获取多模态分析结果
        AudioMetrics audioMetrics = audioAnalysisService.getMetrics(sessionId);
        VideoMetrics videoMetrics = videoAnalysisService.getMetrics(sessionId);
        TextMetrics textMetrics = textAnalysisService.getMetrics(sessionId);
        
        // 计算维度得分
        List<DimensionScore> dimensionScores = calculateDimensionScores(
            audioMetrics, videoMetrics, textMetrics);
        
        // 计算总分
        Integer totalScore = calculateTotalScore(dimensionScores);
        
        // 生成等级和百分位
        String rank = calculateRank(totalScore);
        Integer percentile = calculatePercentile(totalScore);
        
        // 生成优势和劣势分析
        List<String> strengths = identifyStrengths(dimensionScores);
        List<String> weaknesses = identifyWeaknesses(dimensionScores);
        
        // 生成总体反馈
        String overallFeedback = generateOverallFeedback(dimensionScores, strengths, weaknesses);
        
        return InterviewResult.builder()
            .sessionId(sessionId)
            .totalScore(totalScore)
            .rank(rank)
            .percentile(percentile)
            .topStrengths(strengths)
            .topWeaknesses(weaknesses)
            .overallFeedback(overallFeedback)
            .build();
    }
    
    private List<DimensionScore> calculateDimensionScores(
            AudioMetrics audio, VideoMetrics video, TextMetrics text) {
        
        List<DimensionScore> scores = new ArrayList<>();
        
        // 专业知识水平 (主要基于文本分析)
        scores.add(DimensionScore.builder()
            .dimension("专业知识水平")
            .score(text.getProfessionalKnowledge())
            .maxScore(100)
            .description("基于回答内容的专业深度和准确性评估")
            .build());
        
        // 语言表达能力 (音频 + 文本)
        int expressionScore = (int) (audio.getClarity() * 0.4 + 
                                   audio.getFluency() * 0.4 + 
                                   text.getLogicalThinking() * 0.2);
        scores.add(DimensionScore.builder()
            .dimension("语言表达能力")
            .score(expressionScore)
            .maxScore(100)
            .description("综合语音清晰度、流利度和逻辑性评估")
            .build());
        
        // 肢体语言 (主要基于视频分析)
        int bodyLanguageScore = (int) (video.getEyeContact() * 0.3 +
                                     video.getPosture() * 0.3 +
                                     video.getExpressions() * 0.2 +
                                     video.getGestures() * 0.2);
        scores.add(DimensionScore.builder()
            .dimension("肢体语言")
            .score(bodyLanguageScore)
            .maxScore(100)
            .description("眼神交流、姿态、表情和手势的综合评估")
            .build());
        
        // 应变抗压能力 (音频情感 + 视频表现)
        int stressResistanceScore = (int) (audio.getConfidence() * 0.6 +
                                         video.getExpressions() * 0.4);
        scores.add(DimensionScore.builder()
            .dimension("应变抗压能力")
            .score(stressResistanceScore)
            .maxScore(100)
            .description("基于语音情感和面部表情的抗压能力评估")
            .build());
        
        // 创新能力 (主要基于文本分析)
        scores.add(DimensionScore.builder()
            .dimension("创新能力")
            .score(text.getInnovation())
            .maxScore(100)
            .description("回答的创新性和独特见解评估")
            .build());
        
        return scores;
    }
}
```

### 4. 可视化报告生成

#### 4.1 报告生成服务
```java
@Service
public class ReportGenerationService {
    
    public InterviewReportVo generateReport(String sessionId) {
        InterviewResult result = evaluationEngine.evaluateInterview(sessionId);
        List<DimensionScore> dimensionScores = dimensionScoreService.getByResultId(result.getId());
        
        // 生成雷达图数据
        RadarChartData radarData = generateRadarChartData(dimensionScores);
        
        // 生成改进建议
        List<ImprovementSuggestion> suggestions = generateImprovementSuggestions(
            result.getTopWeaknesses(), dimensionScores);
        
        // 生成学习路径推荐
        List<LearningPathRecommendation> learningPaths = 
            learningRecommendationService.generateRecommendations(sessionId, result);
        
        return InterviewReportVo.builder()
            .basicInfo(getBasicInfo(sessionId))
            .overallScore(result.getTotalScore())
            .rank(result.getRank())
            .percentile(result.getPercentile())
            .radarChartData(radarData)
            .dimensionScores(dimensionScores)
            .strengths(result.getTopStrengths())
            .weaknesses(result.getTopWeaknesses())
            .improvementSuggestions(suggestions)
            .learningPaths(learningPaths)
            .overallFeedback(result.getOverallFeedback())
            .build();
    }
    
    private List<ImprovementSuggestion> generateImprovementSuggestions(
            List<String> weaknesses, List<DimensionScore> scores) {
        
        List<ImprovementSuggestion> suggestions = new ArrayList<>();
        
        for (String weakness : weaknesses) {
            ImprovementSuggestion suggestion = new ImprovementSuggestion();
            
            switch (weakness) {
                case "眼神交流不足":
                    suggestion.setTitle("改善眼神交流");
                    suggestion.setDescription("练习与摄像头保持自然的眼神接触");
                    suggestion.setActionItems(Arrays.asList(
                        "每天对着镜子练习3-5分钟自我介绍",
                        "录制视频练习，观察自己的眼神表现",
                        "与朋友进行模拟面试练习"
                    ));
                    suggestion.setPriority("高");
                    break;
                    
                case "回答缺乏STAR结构":
                    suggestion.setTitle("掌握STAR回答法");
                    suggestion.setDescription("使用情境-任务-行动-结果的结构化回答方式");
                    suggestion.setActionItems(Arrays.asList(
                        "学习STAR方法的理论知识",
                        "准备3-5个STAR结构的项目经历",
                        "练习用STAR结构回答行为面试问题"
                    ));
                    suggestion.setPriority("高");
                    break;
                    
                case "语速过快":
                    suggestion.setTitle("控制语速节奏");
                    suggestion.setDescription("保持适中的语速，增强表达效果");
                    suggestion.setActionItems(Arrays.asList(
                        "练习深呼吸和停顿技巧",
                        "录音练习，监控自己的语速",
                        "朗读文章练习语速控制"
                    ));
                    suggestion.setPriority("中");
                    break;
            }
            
            suggestions.add(suggestion);
        }
        
        return suggestions;
    }
}
```

#### 4.2 PDF报告生成
```java
@Service
public class PdfReportService {
    
    public String generatePdfReport(String sessionId) {
        InterviewReportVo report = reportGenerationService.generateReport(sessionId);
        
        try (PDDocument document = new PDDocument()) {
            // 创建页面
            PDPage page = new PDPage(PDRectangle.A4);
            document.addPage(page);
            
            try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                // 添加标题
                addTitle(contentStream, "面试评估报告");
                
                // 添加基本信息
                addBasicInfo(contentStream, report.getBasicInfo());
                
                // 添加总体评分
                addOverallScore(contentStream, report.getOverallScore(), report.getRank());
                
                // 添加维度评分表格
                addDimensionScoresTable(contentStream, report.getDimensionScores());
                
                // 添加优势和劣势
                addStrengthsAndWeaknesses(contentStream, report.getStrengths(), report.getWeaknesses());
                
                // 添加改进建议
                addImprovementSuggestions(contentStream, report.getImprovementSuggestions());
            }
            
            // 保存PDF文件
            String fileName = "interview_report_" + sessionId + "_" + System.currentTimeMillis() + ".pdf";
            String filePath = fileStorageService.getReportPath() + "/" + fileName;
            document.save(filePath);
            
            return filePath;
            
        } catch (IOException e) {
            log.error("生成PDF报告失败", e);
            throw new BusinessException("PDF报告生成失败");
        }
    }
}
```

### 5. 个性化学习推荐引擎

#### 5.1 学习路径推荐算法
```java
@Service
public class LearningRecommendationService {
    
    public List<LearningPathRecommendation> generateRecommendations(
            String sessionId, InterviewResult result) {
        
        List<LearningPathRecommendation> recommendations = new ArrayList<>();
        
        // 基于弱项生成推荐
        for (String weakness : result.getTopWeaknesses()) {
            LearningPathRecommendation recommendation = generateRecommendationForWeakness(weakness);
            if (recommendation != null) {
                recommendations.add(recommendation);
            }
        }
        
        // 基于岗位要求生成推荐
        String jobPosition = getJobPosition(sessionId);
        List<LearningPathRecommendation> jobBasedRecommendations = 
            generateJobBasedRecommendations(jobPosition, result);
        recommendations.addAll(jobBasedRecommendations);
        
        // 排序和去重
        return recommendations.stream()
            .distinct()
            .sorted(Comparator.comparing(LearningPathRecommendation::getPriority).reversed())
            .limit(10)
            .collect(Collectors.toList());
    }
    
    private LearningPathRecommendation generateRecommendationForWeakness(String weakness) {
        switch (weakness) {
            case "专业知识不足":
                return LearningPathRecommendation.builder()
                    .title("技术基础强化")
                    .description("针对岗位要求的核心技术知识学习")
                    .estimatedHours(20)
                    .difficulty("中等")
                    .resources(Arrays.asList(
                        createResource("算法与数据结构精讲", "video", "https://example.com/algorithm"),
                        createResource("系统设计面试题库", "practice", "https://example.com/system-design"),
                        createResource("技术面试宝典", "book", "https://example.com/tech-interview")
                    ))
                    .milestones(Arrays.asList(
                        "完成基础算法练习 (5小时)",
                        "掌握常见数据结构 (8小时)", 
                        "练习系统设计题目 (7小时)"
                    ))
                    .priority(90)
                    .build();
                    
            case "表达能力欠缺":
                return LearningPathRecommendation.builder()
                    .title("沟通表达训练")
                    .description("提升语言表达和沟通技巧")
                    .estimatedHours(15)
                    .difficulty("简单")
                    .resources(Arrays.asList(
                        createResource("演讲与表达技巧", "video", "https://example.com/speaking"),
                        createResource("STAR回答法训练", "practice", "https://example.com/star-method"),
                        createResource("面试表达实战", "course", "https://example.com/interview-expression")
                    ))
                    .milestones(Arrays.asList(
                        "学习STAR方法理论 (3小时)",
                        "准备个人项目故事 (5小时)",
                        "模拟面试练习 (7小时)"
                    ))
                    .priority(85)
                    .build();
                    
            default:
                return null;
        }
    }
}
```

## 数据模型

### 1. 核心实体设计

#### 1.1 多模态面试会话
```java
@Data
@TableName("app_multimodal_interview_session")
public class MultimodalInterviewSession extends BaseEntity {
    
    @TableId(value = "id")
    private String id;
    
    private Long userId;
    private Long jobId;
    private String jobPosition;
    private String interviewType; // technical/behavioral/mixed
    
    // 多模态数据路径
    private String audioFilePath;
    private String videoFilePath;
    private String transcriptText;
    
    // 会话状态
    private String status; // recording/analyzing/completed/failed
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Integer duration; // 秒
    
    // 技术参数
    private String audioFormat;
    private String videoFormat;
    private String audioQuality;
    private String videoQuality;
    
    // 分析进度
    private Integer analysisProgress; // 0-100
    private String analysisStatus;
    private String errorMessage;
}
```

#### 1.2 多模态分析结果
```java
@Data
@TableName("app_multimodal_analysis_result")
public class MultimodalAnalysisResult extends BaseEntity {
    
    @TableId(value = "id")
    private String id;
    
    private String sessionId;
    private String resultType; // audio/video/text/comprehensive
    
    // 音频分析结果
    private Integer audioClarity;
    private Integer audioFluency;
    private Integer audioConfidence;
    private Integer audioPace;
    private String emotionAnalysis; // JSON格式
    
    // 视频分析结果  
    private Integer eyeContactScore;
    private Integer postureScore;
    private Integer expressionScore;
    private Integer gestureScore;
    private String faceAnalysisData; // JSON格式
    
    // 文本分析结果
    private Integer professionalKnowledge;
    private Integer logicalThinking;
    private Integer innovation;
    private Integer skillMatching;
    private Integer starStructure;
    private String contentAnalysis; // JSON格式
    
    // 综合评分
    private Integer overallScore;
    private String analysisReport; // JSON格式详细分析
    
    private String processingTime;
    private String modelVersion;
}
```

#### 1.3 学习路径推荐
```java
@Data
@TableName("app_learning_path_recommendation")
public class LearningPathRecommendation extends BaseEntity {
    
    @TableId(value = "id")
    private Long id;
    
    private String sessionId;
    private Long userId;
    private String recommendationType; // weakness_based/job_based/skill_gap
    
    private String title;
    private String description;
    private Integer estimatedHours;
    private String difficulty; // 简单/中等/困难
    private Integer priority; // 1-100
    
    private String resources; // JSON格式资源列表
    private String milestones; // JSON格式里程碑
    private String prerequisites; // JSON格式前置条件
    
    private String status; // recommended/in_progress/completed/skipped
    private Integer progress; // 0-100
    private LocalDateTime startTime;
    private LocalDateTime completedTime;
    
    private String feedback; // 用户反馈
    private Integer rating; // 1-5星评分
}
```

## 错误处理

### 1. 异常分类和处理策略

```java
public enum MultimodalErrorCode {
    
    // 数据采集错误
    AUDIO_CAPTURE_FAILED("MM001", "音频采集失败"),
    VIDEO_CAPTURE_FAILED("MM002", "视频采集失败"),
    MEDIA_FORMAT_UNSUPPORTED("MM003", "媒体格式不支持"),
    
    // 分析处理错误
    AUDIO_ANALYSIS_FAILED("MM101", "音频分析失败"),
    VIDEO_ANALYSIS_FAILED("MM102", "视频分析失败"),
    TEXT_ANALYSIS_FAILED("MM103", "文本分析失败"),
    AI_SERVICE_UNAVAILABLE("MM104", "AI服务不可用"),
    
    // 报告生成错误
    REPORT_GENERATION_FAILED("MM201", "报告生成失败"),
    PDF_EXPORT_FAILED("MM202", "PDF导出失败"),
    
    // 推荐系统错误
    RECOMMENDATION_FAILED("MM301", "推荐生成失败"),
    LEARNING_PATH_ERROR("MM302", "学习路径错误");
    
    private final String code;
    private final String message;
}

@Component
public class MultimodalExceptionHandler {
    
    @EventListener
    public void handleAnalysisFailure(AnalysisFailureEvent event) {
        // 记录错误日志
        log.error("多模态分析失败: sessionId={}, error={}", 
            event.getSessionId(), event.getErrorMessage());
        
        // 更新会话状态
        updateSessionStatus(event.getSessionId(), "failed", event.getErrorMessage());
        
        // 发送通知给用户
        notificationService.sendAnalysisFailureNotification(
            event.getUserId(), event.getSessionId());
        
        // 尝试重试机制
        if (event.getRetryCount() < 3) {
            retryAnalysis(event.getSessionId(), event.getRetryCount() + 1);
        }
    }
}
```

## 测试策略

### 1. 单元测试覆盖

```java
@SpringBootTest
class MultimodalAnalysisServiceTest {
    
    @Test
    void testAudioAnalysis() {
        // 测试音频分析功能
        String audioPath = "test-resources/sample-audio.wav";
        AudioMetrics result = audioAnalysisService.analyzeAudio(audioPath, "test-session");
        
        assertThat(result.getClarity()).isBetween(0, 100);
        assertThat(result.getFluency()).isBetween(0, 100);
        assertThat(result.getConfidence()).isBetween(0, 100);
    }
    
    @Test
    void testVideoAnalysis() {
        // 测试视频分析功能
        String videoPath = "test-resources/sample-video.mp4";
        VideoMetrics result = videoAnalysisService.analyzeVideo(videoPath, "test-session");
        
        assertThat(result.getEyeContact()).isBetween(0, 100);
        assertThat(result.getPosture()).isBetween(0, 100);
    }
    
    @Test
    void testComprehensiveEvaluation() {
        // 测试综合评估
        InterviewResult result = evaluationEngine.evaluateInterview("test-session");
        
        assertThat(result.getTotalScore()).isBetween(0, 100);
        assertThat(result.getRank()).isIn("excellent", "good", "average", "poor");
        assertThat(result.getTopStrengths()).isNotEmpty();
    }
}
```

### 2. 集成测试

```java
@SpringBootTest
@AutoConfigureTestDatabase
class MultimodalInterviewIntegrationTest {
    
    @Test
    void testCompleteInterviewFlow() {
        // 1. 创建面试会话
        String sessionId = interviewService.createSession(userId, jobId);
        
        // 2. 上传多模态数据
        uploadTestData(sessionId);
        
        // 3. 触发分析
        analysisService.startAnalysis(sessionId);
        
        // 4. 等待分析完成
        waitForAnalysisCompletion(sessionId);
        
        // 5. 验证结果
        InterviewResult result = resultService.getResult(sessionId);
        assertThat(result).isNotNull();
        assertThat(result.getTotalScore()).isGreaterThan(0);
        
        // 6. 验证报告生成
        String reportPath = reportService.generatePdfReport(sessionId);
        assertThat(new File(reportPath)).exists();
        
        // 7. 验证学习推荐
        List<LearningPathRecommendation> recommendations = 
            recommendationService.getRecommendations(sessionId);
        assertThat(recommendations).isNotEmpty();
    }
}
```

## 部署架构

### 1. 容器化部署

```yaml
# docker-compose.yml
version: '3.8'
services:
  multimodal-interview-app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - XUNFEI_APP_ID=${XUNFEI_APP_ID}
      - XUNFEI_API_SECRET=${XUNFEI_API_SECRET}
    depends_on:
      - mysql
      - redis
      - rabbitmq
    volumes:
      - ./data/uploads:/app/uploads
      - ./data/reports:/app/reports
      
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: multimodal_interview
    volumes:
      - mysql_data:/var/lib/mysql
      
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
      
  rabbitmq:
    image: rabbitmq:3-management
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASS}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

volumes:
  mysql_data:
  redis_data:
  rabbitmq_data:
```

### 2. 监控和日志

```yaml
# monitoring/docker-compose.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      
  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    volumes:
      - es_data:/usr/share/elasticsearch/data
      
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch

volumes:
  grafana_data:
  es_data:
```

这个设计文档提供了完整的技术架构和实现方案，涵盖了多模态数据采集、AI分析、智能评估、可视化报告和个性化推荐等核心功能模块。