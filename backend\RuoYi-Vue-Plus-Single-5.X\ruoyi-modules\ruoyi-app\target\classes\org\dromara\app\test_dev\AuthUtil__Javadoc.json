{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "assembleRequestHeader", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "byte[]"], "doc": "\n 计算签名所需要的header参数 （http 接口）\r\n @param requestUrl like 'http://rest-api.xfyun.cn/v2/iat'\r\n @param apiKey\r\n @param apiSecret\r\n @method request method  POST/GET/PATCH/DELETE etc....\r\n @param body   http request body\r\n @return header map ，contains all headers should be set when access api\r\n"}], "constructors": []}