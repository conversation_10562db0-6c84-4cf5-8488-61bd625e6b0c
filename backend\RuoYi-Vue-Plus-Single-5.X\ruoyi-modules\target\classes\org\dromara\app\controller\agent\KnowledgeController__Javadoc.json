{"doc": " 知识库管理Controller\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeBaseBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询知识库列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 分页结果\n"}, {"name": "getKnowledgeBases", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeBaseBo"], "doc": " 获取知识库列表（不分页）\n\n @param bo 查询条件\n @return 知识库列表\n"}, {"name": "getKnowledgeBase", "paramTypes": ["java.lang.Long"], "doc": " 获取知识库详情\n\n @param knowledgeBaseId 知识库ID\n @return 知识库详情\n"}, {"name": "createKnowledgeBase", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeBaseBo"], "doc": " 创建知识库\n\n @param bo 知识库信息\n @return 操作结果\n"}, {"name": "updateKnowledgeBase", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeBaseBo"], "doc": " 更新知识库\n\n @param bo 知识库信息\n @return 操作结果\n"}, {"name": "deleteKnowledgeBase", "paramTypes": ["java.lang.Long"], "doc": " 删除知识库\n\n @param knowledgeBaseId 知识库ID\n @return 操作结果\n"}, {"name": "getKnowledgeBaseStats", "paramTypes": ["java.lang.Long"], "doc": " 获取知识库统计信息\n"}, {"name": "rebuildIndex", "paramTypes": ["java.lang.Long"], "doc": " 重建知识库索引\n"}, {"name": "documentList", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeDocumentBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询知识库文档列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 分页结果\n"}, {"name": "getDocuments", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.bo.KnowledgeDocumentBo"], "doc": " 获取知识库文档列表（不分页）\n\n @param knowledgeBaseId 知识库ID\n @param bo              查询条件\n @return 文档列表\n"}, {"name": "getDocument", "paramTypes": ["java.lang.Long"], "doc": " 获取文档详情\n\n @param documentId 文档ID\n @return 文档详情\n"}, {"name": "addDocument", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeDocumentBo"], "doc": " 添加文档到知识库\n\n @param bo 文档信息\n @return 操作结果\n"}, {"name": "updateDocument", "paramTypes": ["org.dromara.app.domain.bo.KnowledgeDocumentBo"], "doc": " 更新文档信息\n\n @param bo 文档信息\n @return 操作结果\n"}, {"name": "deleteDocument", "paramTypes": ["java.lang.Long"], "doc": " 删除文档\n\n @param documentId 文档ID\n @return 操作结果\n"}, {"name": "processDocument", "paramTypes": ["java.lang.Long"], "doc": " 处理文档（重新向量化）\n\n @param documentId 文档ID\n @return 操作结果\n"}, {"name": "searchKnowledge", "paramTypes": ["org.dromara.app.controller.agent.KnowledgeController.SearchRequest"], "doc": " 向量搜索知识库\n\n @param request 搜索请求\n @return 搜索结果\n"}, {"name": "hybridSearch", "paramTypes": ["org.dromara.app.controller.agent.KnowledgeController.SearchRequest"], "doc": " 混合搜索知识库（向量 + 关键词）\n\n @param request 搜索请求\n @return 搜索结果\n"}], "constructors": []}