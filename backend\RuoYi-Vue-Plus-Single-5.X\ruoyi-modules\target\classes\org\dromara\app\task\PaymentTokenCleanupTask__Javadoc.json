{"doc": " 支付token清理定时任务\n 定期清理过期的支付token，提高系统安全性\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "cleanupExpiredTokens", "paramTypes": [], "doc": " 清理过期的支付token\n 每小时执行一次，清理过期超过1小时的token\n"}, {"name": "cleanupPaidOrderTokens", "paramTypes": [], "doc": " 清理已完成订单的token\n 每天凌晨2点执行一次，清理已支付订单的token\n"}, {"name": "cleanupCancelledOrderTokens", "paramTypes": [], "doc": " 清理已取消订单的token\n 每天凌晨3点执行一次，清理已取消订单的token\n"}], "constructors": []}