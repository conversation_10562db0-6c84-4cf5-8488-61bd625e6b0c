package org.dromara.app.domain.enums;

/**
 * 错误码枚举
 *
 * <AUTHOR>
 */
public enum ErrorCode {

    // 通用错误 1000-1999
    SUCCESS("0000", "操作成功"),
    SYSTEM_ERROR("1000", "系统内部错误"),
    PARAM_ERROR("1001", "参数错误"),
    UNAUTHORIZED("1002", "未授权访问"),
    FORBIDDEN("1003", "访问被禁止"),
    NOT_FOUND("1004", "资源不存在"),
    METHOD_NOT_ALLOWED("1005", "请求方法不允许"),
    REQUEST_TIMEOUT("1006", "请求超时"),

    // 认证相关错误 2000-2999
    AUTH_TOKEN_INVALID("2000", "Token无效"),
    AUTH_TOKEN_EXPIRED("2001", "Token已过期"),
    AUTH_LOGIN_REQUIRED("2002", "请先登录"),
    AUTH_PERMISSION_DENIED("2003", "权限不足"),
    AUTH_USER_DISABLED("2004", "用户已被禁用"),

    // Agent相关错误 3000-3999
    AGENT_NOT_FOUND("3000", "rjb-sias不存在"),
    AGENT_DISABLED("3001", "rjb-sias已被禁用"),
    AGENT_TYPE_INVALID("3002", "rjb-sias类型无效"),
    AGENT_CONFIG_ERROR("3003", "rjb-sias配置错误"),
    AGENT_SERVICE_UNAVAILABLE("3004", "rjb-sias服务暂时不可用"),

    // 聊天相关错误 4000-4999
    CHAT_SESSION_NOT_FOUND("4000", "会话不存在"),
    CHAT_SESSION_EXPIRED("4001", "会话已过期"),
    CHAT_MESSAGE_EMPTY("4002", "消息内容不能为空"),
    CHAT_MESSAGE_TOO_LONG("4003", "消息内容过长"),
    CHAT_SERVICE_ERROR("4004", "聊天服务异常"),
    CHAT_RATE_LIMIT_EXCEEDED("4005", "请求过于频繁，请稍后再试"),

    // RAG相关错误 5000-5999
    RAG_KNOWLEDGE_BASE_NOT_FOUND("5000", "知识库不存在"),
    RAG_DOCUMENT_NOT_FOUND("5001", "文档不存在"),
    RAG_EMBEDDING_FAILED("5002", "向量化失败"),
    RAG_SEARCH_FAILED("5003", "知识检索失败"),
    RAG_SERVICE_UNAVAILABLE("5004", "RAG服务暂时不可用"),

    // 工具调用相关错误 6000-6999
    TOOL_NOT_FOUND("6000", "工具不存在"),
    TOOL_DISABLED("6001", "工具已被禁用"),
    TOOL_EXECUTION_FAILED("6002", "工具执行失败"),
    TOOL_TIMEOUT("6003", "工具执行超时"),
    TOOL_PERMISSION_DENIED("6004", "工具调用权限不足"),

    // 文件相关错误 7000-7999
    FILE_UPLOAD_FAILED("7000", "文件上传失败"),
    FILE_TYPE_NOT_SUPPORTED("7001", "文件类型不支持"),
    FILE_SIZE_EXCEEDED("7002", "文件大小超出限制"),
    FILE_NOT_FOUND("7003", "文件不存在"),
    FILE_PROCESSING_FAILED("7004", "文件处理失败"),

    // 数据库相关错误 8000-8999
    DATABASE_CONNECTION_FAILED("8000", "数据库连接失败"),
    DATABASE_OPERATION_FAILED("8001", "数据库操作失败"),
    DATA_INTEGRITY_VIOLATION("8002", "数据完整性约束违反"),
    DUPLICATE_KEY_ERROR("8003", "数据重复"),

    // 外部服务错误 9000-9999
    EXTERNAL_SERVICE_UNAVAILABLE("9000", "外部服务不可用"),
    EXTERNAL_API_ERROR("9001", "外部API调用失败"),
    NETWORK_ERROR("9002", "网络连接错误"),
    SERVICE_TIMEOUT("9003", "服务调用超时");

    private final String code;
    private final String message;

    ErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    @Override
    public String toString() {
        return String.format("[%s] %s", code, message);
    }
}
