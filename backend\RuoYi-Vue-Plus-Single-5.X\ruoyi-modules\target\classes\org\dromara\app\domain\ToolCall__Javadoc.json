{"doc": " 工具调用记录对象 app_tool_call\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 调用记录ID\n"}, {"name": "sessionId", "doc": " 会话ID\n"}, {"name": "messageId", "doc": " 消息ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "toolId", "doc": " 工具ID\n"}, {"name": "toolName", "doc": " 工具名称\n"}, {"name": "parameters", "doc": " 调用参数（JSON格式）\n"}, {"name": "result", "doc": " 调用结果（JSON格式）\n"}, {"name": "status", "doc": " 调用状态：0-调用中，1-成功，2-失败\n"}, {"name": "errorMessage", "doc": " 错误信息\n"}, {"name": "executionTime", "doc": " 执行时间（毫秒）\n"}, {"name": "startTime", "doc": " 调用开始时间\n"}, {"name": "endTime", "doc": " 调用结束时间\n"}, {"name": "retryCount", "doc": " 重试次数\n"}, {"name": "source", "doc": " 调用来源：user/system/auto\n"}, {"name": "context", "doc": " 调用上下文（JSON格式）\n"}, {"name": "parametersObject", "doc": " 参数对象（不存储到数据库）\n"}, {"name": "resultObject", "doc": " 结果对象（不存储到数据库）\n"}, {"name": "contextObject", "doc": " 上下文对象（不存储到数据库）\n"}], "enumConstants": [], "methods": [], "constructors": []}