package org.dromara.common.chat.agent;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * AI Agent上下文
 *
 * <AUTHOR>
 */
public class AgentContext {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * Agent类型
     */
    private AgentType agentType;

    /**
     * 上下文参数
     */
    private Map<String, Object> parameters;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;

    public AgentContext() {
        this.parameters = new HashMap<>();
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    public AgentContext(String sessionId, Long userId, AgentType agentType) {
        this();
        this.sessionId = sessionId;
        this.userId = userId;
        this.agentType = agentType;
    }

    /**
     * 设置参数
     */
    public void setParameter(String key, Object value) {
        this.parameters.put(key, value);
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 获取参数
     */
    public Object getParameter(String key) {
        return this.parameters.get(key);
    }

    /**
     * 获取参数（带默认值）
     */
    @SuppressWarnings("unchecked")
    public <T> T getParameter(String key, T defaultValue) {
        Object value = this.parameters.get(key);
        return value != null ? (T) value : defaultValue;
    }

    // Getters and Setters
    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public AgentType getAgentType() {
        return agentType;
    }

    public void setAgentType(AgentType agentType) {
        this.agentType = agentType;
    }

    public Map<String, Object> getParameters() {
        return parameters;
    }

    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
        this.updateTime = LocalDateTime.now();
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
