{"doc": "\n 用户成就控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getUserAchievements", "paramTypes": [], "doc": "\n 获取当前用户成就列表\r\n"}, {"name": "getAchievementStats", "paramTypes": [], "doc": "\n 获取用户成就统计\r\n"}, {"name": "getRecentAchievements", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取最近解锁的成就\r\n"}, {"name": "getInProgressAchievements", "paramTypes": [], "doc": "\n 获取进行中的成就\r\n"}, {"name": "getRecommendedAchievements", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取推荐成就\r\n"}, {"name": "getAchievementDetail", "paramTypes": ["java.lang.String"], "doc": "\n 获取成就详情\r\n"}, {"name": "checkAndUpdateAchievements", "paramTypes": [], "doc": "\n 手动检查并更新成就\r\n"}, {"name": "initializeUserAchievements", "paramTypes": [], "doc": "\n 初始化用户成就\r\n"}, {"name": "shareAchievements", "paramTypes": ["java.lang.String"], "doc": "\n 分享成就墙\r\n"}, {"name": "getCategories", "paramTypes": [], "doc": "\n 获取成就分类列表\r\n"}, {"name": "getUserAchievementCompletion", "paramTypes": [], "doc": "\n 获取用户成就完成度\r\n"}, {"name": "recalculateUserProgress", "paramTypes": [], "doc": "\n 重新计算用户成就进度\r\n"}, {"name": "getEventStatistics", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取事件统计\r\n"}, {"name": "unlockAchievement", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 手动解锁成就（管理员功能）\r\n"}, {"name": "recordEvent", "paramTypes": ["java.lang.String", "java.util.Map", "java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": "\n 记录用户事件（测试用）\r\n"}], "constructors": []}