package org.dromara.app.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 相关推荐视频视图对象
 *
 * <AUTHOR>
 */
@Data
public class RelatedVideoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 视频ID
     */
    private Long id;

    /**
     * 视频标题
     */
    private String title;

    /**
     * 讲师名称
     */
    private String instructor;

    /**
     * 视频时长
     */
    private String duration;

    /**
     * 缩略图
     */
    private String thumbnail;

    /**
     * 播放次数
     */
    private Integer viewCount;

    /**
     * 评分
     */
    private BigDecimal rating;

    /**
     * 分类
     */
    private String category;

    /**
     * 是否免费
     */
    private Boolean free;

    /**
     * 价格
     */
    private BigDecimal price;
}
