package org.dromara.common.chat.agent;

/**
 * AI Agent类型枚举
 *
 * <AUTHOR>
 */
public enum AgentType {

    /**
     * 通用聊天助手
     */
    GENERAL_CHAT("general_chat", "通用助手", "全能rjb-sias，帮助您解决各种问题"),

    /**
     * 智能面试官Agent
     */
    INTERVIEW("interview", "面试", "智能面试官，提供专业面试问题和评估"),
    /**
     * 面试官Agent
     */
    INTERVIEWER("interviewer", "面试官", "智能面试官，提供专业面试问题和评估"),

    /**
     * 简历分析Agent
     */
    RESUME_ANALYZER("resume_analyzer", "简历分析师", "专业简历分析和优化建议"),

    /**
     * 技能评估Agent
     */
    SKILL_ASSESSOR("skill_assessor", "技能评估师", "技能水平评估和学习路径规划"),

    /**
     * 职业顾问Agent
     */
    CAREER_ADVISOR("career_advisor", "职业顾问", "职业规划和发展建议"),

    /**
     * 模拟面试Agent
     */
    MOCK_INTERVIEWER("mock_interviewer", "模拟面试官", "真实面试场景模拟和反馈"),

    /**
     * 学习导师Agent
     */
    LEARNING_GUIDE("learning_guide", "学习导师", "个性化学习计划和指导");

    private final String code;
    private final String name;
    private final String description;

    AgentType(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public static AgentType getByCode(String code) {
        for (AgentType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return GENERAL_CHAT;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }
}
