package org.dromara.app.config;

import lombok.RequiredArgsConstructor;
import org.dromara.app.websocket.InterviewWebSocketHandler;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * 面试WebSocket配置
 *
 * <AUTHOR>
 */
@AutoConfiguration
@EnableWebSocket
@RequiredArgsConstructor
public class InterviewWebSocketConfig implements WebSocketConfigurer {

    private final InterviewWebSocketHandler interviewWebSocketHandler;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 注册面试WebSocket处理器
        registry.addHandler(interviewWebSocketHandler, "/interview/ws")
            .setAllowedOriginPatterns("*"); // 使用allowedOriginPatterns代替allowedOrigins，不使用SockJS避免CORS问题
    }
}
