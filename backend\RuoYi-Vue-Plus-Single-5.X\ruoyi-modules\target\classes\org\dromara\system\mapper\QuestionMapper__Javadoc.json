{"doc": " 题目Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectVoById", "paramTypes": ["java.lang.Long"], "doc": " 查询题目\n\n @param questionId 题目主键\n @return 题目\n"}, {"name": "selectVoList", "paramTypes": ["org.dromara.system.domain.bo.QuestionBo"], "doc": " 查询题目列表\n\n @param question 题目\n @return 题目集合\n"}, {"name": "selectVoPage", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "org.dromara.system.domain.bo.QuestionBo"], "doc": " 分页查询题目列表\n\n @param page     分页参数\n @param question 题目查询条件\n @return 题目集合\n"}, {"name": "selectPageQuestionList", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery", "org.dromara.system.domain.bo.QuestionBo"], "doc": " 根据条件分页查询题目列表\n\n @param pageQuery 分页参数\n @param question  题目查询条件\n @return 题目集合\n"}, {"name": "selectVoByIds", "paramTypes": ["java.util.Collection"], "doc": " 查询题目列表\n\n @param questionIds 题目主键集合\n @return 题目集合\n"}, {"name": "selectVoByBankId", "paramTypes": ["java.lang.Long"], "doc": " 根据题库ID查询题目列表\n\n @param bankId 题库ID\n @return 题目集合\n"}, {"name": "selectPageQuestionByBankId", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery", "java.lang.Long", "org.dromara.system.domain.bo.QuestionBo"], "doc": " 根据题库ID分页查询题目列表\n\n @param pageQuery 分页参数\n @param bankId    题库ID\n @param question  题目查询条件\n @return 题目集合\n"}, {"name": "selectVoByQuestionCode", "paramTypes": ["java.lang.String"], "doc": " 根据题目编码查询题目\n\n @param questionCode 题目编码\n @return 题目\n"}, {"name": "selectVoByCategory", "paramTypes": ["java.lang.String"], "doc": " 根据分类查询题目列表\n\n @param category 分类\n @return 题目集合\n"}, {"name": "selectVoByDifficulty", "paramTypes": ["java.lang.Integer"], "doc": " 根据难度查询题目列表\n\n @param difficulty 难度\n @return 题目集合\n"}, {"name": "selectVoByType", "paramTypes": ["java.lang.Integer"], "doc": " 根据题目类型查询题目列表\n\n @param type 题目类型\n @return 题目集合\n"}, {"name": "selectBookmarkedQuestionsByUserId", "paramTypes": ["java.lang.Long"], "doc": " 查询用户收藏的题目列表\n\n @param userId 用户ID\n @return 题目集合\n"}, {"name": "selectHotQuestions", "paramTypes": ["java.lang.Integer"], "doc": " 查询热门题目列表\n\n @param limit 限制数量\n @return 题目集合\n"}, {"name": "selectRandomQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 随机查询题目列表\n\n @param bankId 题库ID\n @param limit  限制数量\n @return 题目集合\n"}, {"name": "updatePracticeCount", "paramTypes": ["java.lang.Long"], "doc": " 更新题目练习次数\n\n @param questionId 题目ID\n @return 影响行数\n"}, {"name": "updateCommentCount", "paramTypes": ["java.lang.Long"], "doc": " 更新题目评论数\n\n @param questionId 题目ID\n @return 影响行数\n"}, {"name": "updateCorrectRate", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 更新题目正确率\n\n @param questionId  题目ID\n @param correctRate 正确率\n @return 影响行数\n"}, {"name": "deleteQuestionByIds", "paramTypes": ["java.util.Collection"], "doc": " 批量删除题目\n\n @param questionIds 需要删除的题目主键集合\n @return 影响行数\n"}, {"name": "deleteQuestionByBankId", "paramTypes": ["java.lang.Long"], "doc": " 根据题库ID删除题目\n\n @param bankId 题库ID\n @return 影响行数\n"}, {"name": "checkQuestionCodeUnique", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 检查题目编码是否唯一\n\n @param questionCode 题目编码\n @param questionId   题目ID（编辑时排除自己）\n @return 数量\n"}, {"name": "countQuestionsByBankId", "paramTypes": ["java.lang.Long"], "doc": " 统计题库下的题目数量\n\n @param bankId 题库ID\n @return 题目数量\n"}], "constructors": []}