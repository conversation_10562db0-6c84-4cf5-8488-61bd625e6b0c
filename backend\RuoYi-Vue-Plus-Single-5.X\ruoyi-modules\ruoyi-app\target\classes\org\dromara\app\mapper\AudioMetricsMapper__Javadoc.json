{"doc": "\n 音频指标Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-17\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByResultId", "paramTypes": ["java.lang.String"], "doc": "\n 根据结果ID查询音频指标\r\n\r\n @param resultId 结果ID\r\n @return 音频指标\r\n"}, {"name": "deleteByResultId", "paramTypes": ["java.lang.String"], "doc": "\n 根据结果ID删除音频指标\r\n\r\n @param resultId 结果ID\r\n @return 删除数量\r\n"}, {"name": "selectHistoryByUserId", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询用户音频指标历史记录\r\n\r\n @param userId 用户ID\r\n @param limit  限制数量\r\n @return 音频指标列表\r\n"}, {"name": "selectAverageMetrics", "paramTypes": [], "doc": "\n 查询音频指标平均值\r\n\r\n @return 音频指标平均值\r\n"}, {"name": "selectAverageMetricsByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户音频指标平均值\r\n\r\n @param userId 用户ID\r\n @return 用户音频指标平均值\r\n"}], "constructors": []}