package org.dromara.common.rabbitmq.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * RabbitMQ 配置属性
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "spring.rabbitmq")
public class RabbitMqProperties {

    /**
     * 是否启用 RabbitMQ
     */
    private boolean enabled = false;

    /**
     * 主机地址
     */
    private String host = "localhost";

    /**
     * 端口
     */
    private int port = 5672;

    /**
     * 用户名
     */
    private String username = "guest";

    /**
     * 密码
     */
    private String password = "guest";

    /**
     * 虚拟主机
     */
    private String virtualHost = "/";

    /**
     * 连接超时时间（毫秒）
     */
    private int connectionTimeout = 60000;

    /**
     * 通道缓存大小
     */
    private int channelCacheSize = 50;

    /**
     * 生产者重试配置
     */
    private Retry retry = new Retry();

    /**
     * 消费者监听配置
     */
    private Listener listener = new Listener();

    /**
     * 重试配置
     */
    @Data
    public static class Retry {
        /**
         * 是否启用重试
         */
        private boolean enabled = true;

        /**
         * 最大重试次数
         */
        private int maxAttempts = 3;

        /**
         * 初始重试间隔（毫秒）
         */
        private long initialInterval = 1000;

        /**
         * 重试间隔倍数
         */
        private double multiplier = 2.0;

        /**
         * 最大重试间隔（毫秒）
         */
        private long maxInterval = 10000;
    }

    /**
     * 监听器配置
     */
    @Data
    public static class Listener {
        /**
         * 并发消费者数量
         */
        private int concurrency = 1;

        /**
         * 最大并发消费者数量
         */
        private int maxConcurrency = 10;

        /**
         * 预取数量
         */
        private int prefetch = 1;

        /**
         * 消费者重试配置
         */
        private Retry retry = new Retry();
    }
}
