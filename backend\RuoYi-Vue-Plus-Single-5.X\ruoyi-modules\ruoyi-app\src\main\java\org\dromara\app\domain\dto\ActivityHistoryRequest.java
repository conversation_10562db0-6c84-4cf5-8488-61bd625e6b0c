package org.dromara.app.domain.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.app.domain.enums.ActivityType;

import java.time.LocalDateTime;

/**
 * 查询活动历史记录请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class ActivityHistoryRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 活动类型(可选)
     */
    private ActivityType type;

    /**
     * 开始时间(可选)
     */
    private LocalDateTime startDate;

    /**
     * 结束时间(可选)
     */
    private LocalDateTime endDate;

    /**
     * 限制数量(可选，默认20)
     */
    private Integer limit = 20;

    /**
     * 页码(可选，默认1)
     */
    private Integer page = 1;

    /**
     * 每页大小(可选，默认10)
     */
    private Integer pageSize = 10;
}
