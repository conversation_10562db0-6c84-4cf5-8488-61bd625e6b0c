{"doc": " AI Agent聊天WebSocket处理器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleStreamingMessage", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "org.springframework.web.socket.WebSocketSession"], "doc": " 处理流式消息\n"}, {"name": "processStreamingMessage", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": " 处理流式消息内容\n"}, {"name": "processMessage", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "cn.hutool.json.JSONObject"], "doc": " 处理非流式消息\n"}, {"name": "sendErrorMessage", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "java.lang.String"], "doc": " 发送错误消息\n"}, {"name": "getActiveConnectionCount", "paramTypes": [], "doc": " 获取当前活跃连接数\n"}, {"name": "getSession", "paramTypes": ["java.lang.String"], "doc": " 获取指定会话\n"}, {"name": "broadcastMessage", "paramTypes": ["java.lang.String"], "doc": " 广播消息到所有连接\n"}], "constructors": []}