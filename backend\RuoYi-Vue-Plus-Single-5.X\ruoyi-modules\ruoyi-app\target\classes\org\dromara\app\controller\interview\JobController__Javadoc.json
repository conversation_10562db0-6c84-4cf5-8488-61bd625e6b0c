{"doc": "\n 岗位管理控制器\r\n\r\n <AUTHOR> Assistant\r\n @date 2025-07-19\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.app.domain.bo.JobQueryBo"], "doc": "\n 分页查询岗位列表\r\n"}, {"name": "getJobsByDomain", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": "\n 根据技术领域查询岗位\r\n"}, {"name": "getJobDetail", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 获取岗位详情\r\n"}, {"name": "getRecommendedJobs", "paramTypes": ["java.lang.Integer", "java.lang.Long"], "doc": "\n 获取推荐岗位\r\n"}, {"name": "getHotJobs", "paramTypes": ["java.lang.Integer", "java.lang.Long"], "doc": "\n 获取热门岗位\r\n"}, {"name": "getTechnicalDomainStats", "paramTypes": [], "doc": "\n 获取技术领域统计\r\n"}, {"name": "getJobQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 根据岗位获取面试问题\r\n"}, {"name": "getDomainQuestions", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": "\n 根据技术领域获取面试问题\r\n"}, {"name": "getMultimodalQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 获取多模态面试问题\r\n"}, {"name": "getQuestionsByTags", "paramTypes": ["java.util.List", "java.lang.Integer"], "doc": "\n 根据标签获取面试问题\r\n"}, {"name": "getGradedQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 获取分级面试问题\r\n"}], "constructors": []}