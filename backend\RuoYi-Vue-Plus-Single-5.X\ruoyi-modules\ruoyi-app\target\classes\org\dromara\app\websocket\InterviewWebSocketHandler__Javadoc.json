{"doc": "\n 面试WebSocket处理器\r\n 处理实时智能建议和表情分析功能\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleConnect", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "cn.hutool.json.JSONObject"], "doc": "\n 处理连接确认\r\n"}, {"name": "handleEmotionRequest", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "cn.hutool.json.JSONObject"], "doc": "\n 处理表情检测请求\r\n"}, {"name": "handleSpeechAnalysis", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "cn.hutool.json.JSONObject"], "doc": "\n 处理语音分析请求\r\n"}, {"name": "handleHeartbeat", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "cn.hutool.json.JSONObject"], "doc": "\n 处理心跳\r\n"}, {"name": "generateEmotionBasedSuggestion", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "cn.hutool.json.JSONObject", "java.lang.Integer"], "doc": "\n 基于情绪分析生成智能建议\r\n"}, {"name": "generateSpeechBasedSuggestion", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "cn.hutool.json.JSONObject", "java.lang.Integer"], "doc": "\n 基于语音分析生成智能建议\r\n"}, {"name": "updateContext", "paramTypes": ["java.lang.String", "java.lang.String", "cn.hutool.json.JSONObject"], "doc": "\n 更新会话上下文\r\n"}, {"name": "sendMessage", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "cn.hutool.json.JSONObject"], "doc": "\n 发送消息\r\n"}, {"name": "sendErrorMessage", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "java.lang.String"], "doc": "\n 发送错误消息\r\n"}], "constructors": []}