{"doc": " 学习推荐服务实现类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "initializeSkillAreaMapping", "paramTypes": [], "doc": " 初始化技能领域映射\n"}, {"name": "initializeJobSkillMapping", "paramTypes": [], "doc": " 初始化岗位技能映射\n"}, {"name": "initializeResourceLibrary", "paramTypes": [], "doc": " 初始化资源库\n"}, {"name": "createResource", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.Double", "java.lang.String", "java.lang.Bo<PERSON>an"], "doc": " 创建学习资源\n"}, {"name": "createTechnicalSkillPath", "paramTypes": ["java.lang.String", "int"], "doc": " 创建技术技能学习路径\n"}, {"name": "createCommunicationSkillPath", "paramTypes": ["int"], "doc": " 创建沟通技能学习路径\n"}, {"name": "createLogicalThinkingPath", "paramTypes": ["int"], "doc": " 创建逻辑思维学习路径\n"}, {"name": "createBodyLanguagePath", "paramTypes": ["int"], "doc": " 创建肢体语言学习路径\n"}, {"name": "createInnovationPath", "paramTypes": ["int"], "doc": " 创建创新能力学习路径\n"}, {"name": "createInterviewSkillsPath", "paramTypes": ["int"], "doc": " 创建面试技巧学习路径\n"}], "constructors": []}