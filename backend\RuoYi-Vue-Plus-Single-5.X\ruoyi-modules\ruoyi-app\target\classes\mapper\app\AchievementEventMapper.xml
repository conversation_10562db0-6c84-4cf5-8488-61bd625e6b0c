<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.AchievementEventMapper">

    <resultMap type="org.dromara.app.domain.AchievementEvent" id="AchievementEventResult">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="eventType" column="event_type"/>
        <result property="eventData" column="event_data"/>
        <result property="eventTime" column="event_time"/>
        <result property="relatedObjectId" column="related_object_id"/>
        <result property="relatedObjectType" column="related_object_type"/>
        <result property="eventValue" column="event_value"/>
        <result property="eventSource" column="event_source"/>
        <result property="sessionId" column="session_id"/>
        <result property="clientInfo" column="client_info"/>
        <result property="processStatus" column="process_status"/>
        <result property="processTime" column="process_time"/>
        <result property="processResult" column="process_result"/>
        <result property="triggeredAchievements" column="triggered_achievements"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectAchievementEventVo">
        select
            ae.id, ae.user_id, ae.event_type, ae.event_data, ae.event_time,
            ae.related_object_id, ae.related_object_type, ae.event_value, ae.event_source,
            ae.session_id, ae.client_info, ae.process_status, ae.process_time,
            ae.process_result, ae.triggered_achievements, ae.create_by, ae.create_time,
            ae.update_by, ae.update_time, ae.remark
        from app_achievement_event ae
    </sql>

    <!-- 获取未处理的事件 -->
    <select id="selectUnhandledEvents" parameterType="int" resultMap="AchievementEventResult">
        <include refid="selectAchievementEventVo"/>
        where ae.process_status = 0
        order by ae.event_time asc
        limit #{limit}
    </select>

    <!-- 查询用户特定类型的事件 -->
    <select id="selectUserEvents" resultMap="AchievementEventResult">
        <include refid="selectAchievementEventVo"/>
        where ae.user_id = #{userId}
        <if test="eventType != null and eventType != ''">
            and ae.event_type = #{eventType}
        </if>
        order by ae.event_time desc
        <if test="limit != null and limit > 0">
            limit #{limit}
        </if>
    </select>

    <!-- 统计用户特定类型的事件数量 -->
    <select id="countUserEvents" resultType="int">
        select count(1) from app_achievement_event
        where user_id = #{userId} and event_type = #{eventType}
    </select>

    <!-- 统计用户特定类型的事件值总和 -->
    <select id="sumUserEventValues" resultType="int">
        select COALESCE(sum(event_value), 0) from app_achievement_event
        where user_id = #{userId} and event_type = #{eventType}
    </select>

    <!-- 获取用户最近的事件 -->
    <select id="selectRecentEvents" resultMap="AchievementEventResult">
        <include refid="selectAchievementEventVo"/>
        where ae.user_id = #{userId}
        order by ae.event_time desc
        limit #{limit}
    </select>

    <!-- 批量更新事件处理状态 -->
    <update id="batchUpdateStatus">
        update app_achievement_event
        set process_status = #{status},
            process_time = now(),
            update_time = now()
        where id in
        <foreach collection="eventIds" item="eventId" open="(" separator="," close=")">
            #{eventId}
        </foreach>
    </update>

    <!-- 删除过期事件 -->
    <delete id="deleteExpiredEvents" parameterType="int">
        delete from app_achievement_event
        where create_time &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
    </delete>

</mapper>
