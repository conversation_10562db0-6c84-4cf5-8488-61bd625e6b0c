{"doc": "\n 报告生成服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "generateInterviewReport", "paramTypes": ["java.lang.String"], "doc": "\n 生成面试报告\r\n\r\n @param sessionId 面试会话ID\r\n @return 报告数据\r\n"}, {"name": "generatePdfReport", "paramTypes": ["java.lang.String"], "doc": "\n 生成PDF报告\r\n\r\n @param sessionId 面试会话ID\r\n @return PDF文件路径\r\n"}, {"name": "generateRadarChartData", "paramTypes": ["java.util.List"], "doc": "\n 生成雷达图数据\r\n\r\n @param dimensionScores 维度评分列表\r\n @return 雷达图数据\r\n"}, {"name": "generateImprovementSuggestions", "paramTypes": ["java.util.List", "java.util.List"], "doc": "\n 生成改进建议\r\n\r\n @param weaknesses 弱点列表\r\n @param dimensionScores 维度评分列表\r\n @return 改进建议列表\r\n"}, {"name": "generateLearningPathRecommendations", "paramTypes": ["java.lang.String", "org.dromara.app.service.IReportGenerationService.InterviewReportData"], "doc": "\n 生成学习路径推荐\r\n\r\n @param sessionId 会话ID\r\n @param reportData 报告数据\r\n @return 学习路径推荐\r\n"}], "constructors": []}