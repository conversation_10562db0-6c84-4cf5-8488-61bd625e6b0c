package org.dromara.app.controller.interview;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.InterviewQuestion;
import org.dromara.app.domain.QuestionTag;
import org.dromara.app.service.IInterviewQuestionService;
import org.dromara.app.service.IQuestionTagService;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 面试问题管理控制器
 *
 * <AUTHOR> Assistant
 * @date 2025-07-19
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/question")
public class InterviewQuestionController extends BaseController {

    private final IInterviewQuestionService questionService;
    private final IQuestionTagService tagService;

    /**
     * 分页查询问题列表
     */
    @GetMapping("/list")
    public R<Page<InterviewQuestion>> list(@RequestParam(defaultValue = "1") Integer pageNum,
                                         @RequestParam(defaultValue = "10") Integer pageSize,
                                         @RequestParam(required = false) Long jobId,
                                         @RequestParam(required = false) String questionType,
                                         @RequestParam(required = false) Integer difficulty,
                                         @RequestParam(required = false) String category) {
        Page<InterviewQuestion> page = new Page<>(pageNum, pageSize);
        Page<InterviewQuestion> result = (Page<InterviewQuestion>) questionService.selectQuestionPage(
            page, jobId, questionType, difficulty, category
        );
        return R.ok(result);
    }

    /**
     * 根据技术领域查询问题
     */
    @GetMapping("/domain/{technicalDomain}")
    public R<List<InterviewQuestion>> getQuestionsByDomain(@PathVariable String technicalDomain,
                                                         @RequestParam(required = false) String questionType,
                                                         @RequestParam(defaultValue = "20") Integer limit) {
        List<InterviewQuestion> questions = questionService.selectByTechnicalDomain(technicalDomain, questionType, limit);
        return R.ok(questions);
    }

    /**
     * 获取多模态问题
     */
    @GetMapping("/multimodal")
    public R<List<InterviewQuestion>> getMultimodalQuestions(@RequestParam(required = false) Long jobId,
                                                           @RequestParam(defaultValue = "10") Integer limit) {
        List<InterviewQuestion> questions = questionService.selectMultimodalQuestions(jobId, limit);
        return R.ok(questions);
    }

    /**
     * 根据难度分级获取问题
     */
    @GetMapping("/graded")
    public R<IInterviewQuestionService.QuestionsByDifficulty> getGradedQuestions(
            @RequestParam(required = false) Long jobId,
            @RequestParam(defaultValue = "3") Integer easyCount,
            @RequestParam(defaultValue = "5") Integer mediumCount,
            @RequestParam(defaultValue = "2") Integer hardCount) {

        IInterviewQuestionService.QuestionsByDifficulty questions =
            questionService.getQuestionsByDifficulty(jobId, easyCount, mediumCount, hardCount);
        return R.ok(questions);
    }

    /**
     * 根据标签查询问题
     */
    @GetMapping("/by-tags")
    public R<List<InterviewQuestion>> getQuestionsByTags(@RequestParam List<String> tags,
                                                       @RequestParam(defaultValue = "20") Integer limit) {
        List<InterviewQuestion> questions = questionService.selectByTags(tags, limit);

        // 增加标签使用次数
        tags.forEach(tagService::incrementUsageCount);

        return R.ok(questions);
    }

    /**
     * 获取问题标签
     */
    @GetMapping("/tags")
    public R<List<QuestionTag>> getTags(@RequestParam(required = false) String category) {
        List<QuestionTag> tags = tagService.selectByCategory(category);
        return R.ok(tags);
    }

    /**
     * 获取热门标签
     */
    @GetMapping("/tags/hot")
    public R<List<QuestionTag>> getHotTags(@RequestParam(defaultValue = "20") Integer limit) {
        List<QuestionTag> tags = tagService.selectHotTags(limit);
        return R.ok(tags);
    }

    /**
     * 获取所有分类的标签
     */
    @GetMapping("/tags/categories")
    public R<IQuestionTagService.TagsByCategory> getTagsByCategory() {
        IQuestionTagService.TagsByCategory tags = tagService.getAllTagsByCategory();
        return R.ok(tags);
    }

    /**
     * 获取问题难度分级建议
     */
    @GetMapping("/difficulty/suggestion")
    public R<DifficultyDistribution> getDifficultySuggestion(@RequestParam(required = false) String technicalDomain,
                                                           @RequestParam(defaultValue = "20") Integer totalQuestions) {
        // 根据技术领域和总题数，建议难度分布
        DifficultyDistribution suggestion = calculateDifficultyDistribution(technicalDomain, totalQuestions);
        return R.ok(suggestion);
    }

    /**
     * 获取问题类型统计
     */
    @GetMapping("/types/stats")
    public R<List<QuestionTypeStats>> getQuestionTypeStats(@RequestParam(required = false) String technicalDomain) {
        // 这里可以添加具体的统计逻辑
        List<QuestionTypeStats> stats = getQuestionTypeStatistics(technicalDomain);
        return R.ok(stats);
    }

    /**
     * 计算难度分布建议
     */
    private DifficultyDistribution calculateDifficultyDistribution(String technicalDomain, Integer totalQuestions) {
        DifficultyDistribution distribution = new DifficultyDistribution();

        // 根据不同技术领域调整难度分布
        switch (technicalDomain != null ? technicalDomain : "General") {
            case "AI":
                // AI领域偏向高难度
                distribution.setEasyCount((int) (totalQuestions * 0.2));
                distribution.setMediumCount((int) (totalQuestions * 0.4));
                distribution.setHardCount((int) (totalQuestions * 0.4));
                break;
            case "BigData":
                // 大数据领域平衡分布
                distribution.setEasyCount((int) (totalQuestions * 0.3));
                distribution.setMediumCount((int) (totalQuestions * 0.4));
                distribution.setHardCount((int) (totalQuestions * 0.3));
                break;
            case "IoT":
                // 物联网领域偏向实践
                distribution.setEasyCount((int) (totalQuestions * 0.25));
                distribution.setMediumCount((int) (totalQuestions * 0.5));
                distribution.setHardCount((int) (totalQuestions * 0.25));
                break;
            case "SmartSystems":
                // 智能系统偏向中高难度
                distribution.setEasyCount((int) (totalQuestions * 0.2));
                distribution.setMediumCount((int) (totalQuestions * 0.5));
                distribution.setHardCount((int) (totalQuestions * 0.3));
                break;
            default:
                // 通用分布
                distribution.setEasyCount((int) (totalQuestions * 0.3));
                distribution.setMediumCount((int) (totalQuestions * 0.5));
                distribution.setHardCount((int) (totalQuestions * 0.2));
                break;
        }

        distribution.setTotalQuestions(totalQuestions);
        distribution.setTechnicalDomain(technicalDomain);

        return distribution;
    }

    /**
     * 获取问题类型统计
     */
    private List<QuestionTypeStats> getQuestionTypeStatistics(String technicalDomain) {
        // 这里应该从数据库查询实际统计数据
        // 暂时返回模拟数据
        return List.of(
            new QuestionTypeStats("open", "开放题", 60),
            new QuestionTypeStats("multimodal", "多模态题", 25),
            new QuestionTypeStats("coding", "编程题", 10),
            new QuestionTypeStats("choice", "选择题", 5)
        );
    }

    /**
     * 难度分布建议
     */
    public static class DifficultyDistribution {
        private String technicalDomain;
        private Integer totalQuestions;
        private Integer easyCount;
        private Integer mediumCount;
        private Integer hardCount;

        // getters and setters
        public String getTechnicalDomain() {
            return technicalDomain;
        }

        public void setTechnicalDomain(String technicalDomain) {
            this.technicalDomain = technicalDomain;
        }

        public Integer getTotalQuestions() {
            return totalQuestions;
        }

        public void setTotalQuestions(Integer totalQuestions) {
            this.totalQuestions = totalQuestions;
        }

        public Integer getEasyCount() {
            return easyCount;
        }

        public void setEasyCount(Integer easyCount) {
            this.easyCount = easyCount;
        }

        public Integer getMediumCount() {
            return mediumCount;
        }

        public void setMediumCount(Integer mediumCount) {
            this.mediumCount = mediumCount;
        }

        public Integer getHardCount() {
            return hardCount;
        }

        public void setHardCount(Integer hardCount) {
            this.hardCount = hardCount;
        }
    }

    /**
     * 问题类型统计
     */
    public static class QuestionTypeStats {
        private String type;
        private String typeName;
        private Integer percentage;

        public QuestionTypeStats(String type, String typeName, Integer percentage) {
            this.type = type;
            this.typeName = typeName;
            this.percentage = percentage;
        }

        // getters and setters
        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getTypeName() {
            return typeName;
        }

        public void setTypeName(String typeName) {
            this.typeName = typeName;
        }

        public Integer getPercentage() {
            return percentage;
        }

        public void setPercentage(Integer percentage) {
            this.percentage = percentage;
        }
    }

}
