{"doc": " 题目Service接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询题目\n\n @param questionId 题目主键\n @return 题目\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.QuestionBo"], "doc": " 查询题目列表\n\n @param bo 题目查询条件\n @return 题目集合\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.system.domain.bo.QuestionBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询题目列表\n\n @param bo        题目查询条件\n @param pageQuery 分页参数\n @return 题目分页集合\n"}, {"name": "queryPageListByBankId", "paramTypes": ["java.lang.Long", "org.dromara.system.domain.bo.QuestionBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 根据题库ID分页查询题目列表\n\n @param bankId    题库ID\n @param bo        题目查询条件\n @param pageQuery 分页参数\n @return 题目分页集合\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.QuestionBo"], "doc": " 新增题目\n\n @param bo 题目信息\n @return 新增结果\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.QuestionBo"], "doc": " 修改题目\n\n @param bo 题目信息\n @return 修改结果\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection"], "doc": " 校验并批量删除题目信息\n\n @param ids 需要删除的题目主键集合\n @return 删除结果\n"}, {"name": "queryByQuestionCode", "paramTypes": ["java.lang.String"], "doc": " 根据题目编码查询题目\n\n @param questionCode 题目编码\n @return 题目信息\n"}, {"name": "queryByBankId", "paramTypes": ["java.lang.Long"], "doc": " 根据题库ID查询题目列表\n\n @param bankId 题库ID\n @return 题目集合\n"}, {"name": "queryByCategory", "paramTypes": ["java.lang.String"], "doc": " 根据分类查询题目列表\n\n @param category 分类\n @return 题目集合\n"}, {"name": "queryByDifficulty", "paramTypes": ["java.lang.Integer"], "doc": " 根据难度查询题目列表\n\n @param difficulty 难度\n @return 题目集合\n"}, {"name": "queryByType", "paramTypes": ["java.lang.Integer"], "doc": " 根据题目类型查询题目列表\n\n @param type 题目类型\n @return 题目集合\n"}, {"name": "queryBookmarkedQuestions", "paramTypes": ["java.lang.Long"], "doc": " 查询用户收藏的题目列表\n\n @param userId 用户ID\n @return 题目集合\n"}, {"name": "queryHotQuestions", "paramTypes": ["java.lang.Integer"], "doc": " 查询热门题目列表\n\n @param limit 限制数量\n @return 题目集合\n"}, {"name": "queryRandomQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 随机查询题目列表\n\n @param bankId 题库ID\n @param limit  限制数量\n @return 题目集合\n"}, {"name": "updatePracticeCount", "paramTypes": ["java.lang.Long"], "doc": " 更新题目练习次数\n\n @param questionId 题目ID\n @return 更新结果\n"}, {"name": "updateCommentCount", "paramTypes": ["java.lang.Long"], "doc": " 更新题目评论数\n\n @param questionId 题目ID\n @return 更新结果\n"}, {"name": "updateCorrectRate", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 更新题目正确率\n\n @param questionId  题目ID\n @param correctRate 正确率\n @return 更新结果\n"}, {"name": "checkQuestionCodeUnique", "paramTypes": ["org.dromara.system.domain.bo.QuestionBo"], "doc": " 检查题目编码是否唯一\n\n @param bo 题目信息\n @return 是否唯一\n"}, {"name": "importQuestion", "paramTypes": ["java.util.List"], "doc": " 批量导入题目\n\n @param list 题目列表\n @return 导入结果\n"}, {"name": "exportQuestion", "paramTypes": ["org.dromara.system.domain.bo.QuestionBo"], "doc": " 导出题目数据\n\n @param bo 题目查询条件\n @return 题目集合\n"}, {"name": "changeStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 启用/停用题目\n\n @param questionId 题目ID\n @param status     状态\n @return 操作结果\n"}, {"name": "copyQuestion", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 复制题目\n\n @param questionId 源题目ID\n @param title      新题目标题\n @return 操作结果\n"}, {"name": "deleteByBankId", "paramTypes": ["java.lang.Long"], "doc": " 根据题库ID删除题目\n\n @param bankId 题库ID\n @return 删除结果\n"}, {"name": "countByBankId", "paramTypes": ["java.lang.Long"], "doc": " 统计题库下的题目数量\n\n @param bankId 题库ID\n @return 题目数量\n"}], "constructors": []}