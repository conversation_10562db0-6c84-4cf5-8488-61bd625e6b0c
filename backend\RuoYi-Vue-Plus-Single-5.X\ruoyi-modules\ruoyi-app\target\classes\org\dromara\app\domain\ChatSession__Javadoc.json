{"doc": "\n 聊天会话对象 app_chat_session\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 会话ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "title", "doc": "\n 会话标题\r\n"}, {"name": "agentType", "doc": "\n Agent类型：general/interviewer/resume_analyzer/skill_assessor/career_advisor/mock_interviewer/learning_guide\r\n"}, {"name": "messageCount", "doc": "\n 消息总数\r\n"}, {"name": "lastMessage", "doc": "\n 最后一条消息内容预览\r\n"}, {"name": "lastActiveTime", "doc": "\n 最后活跃时间\r\n"}, {"name": "status", "doc": "\n 会话状态：0-已归档，1-活跃\r\n"}, {"name": "sessionConfig", "doc": "\n 会话配置（JSON格式）\r\n"}, {"name": "messages", "doc": "\n 消息列表（不存储到数据库，用于返回给前端）\r\n"}, {"name": "stats", "doc": "\n 会话统计信息（不存储到数据库）\r\n"}, {"name": "delFlag", "doc": "\n 删除标志（0-正常，1-删除）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}