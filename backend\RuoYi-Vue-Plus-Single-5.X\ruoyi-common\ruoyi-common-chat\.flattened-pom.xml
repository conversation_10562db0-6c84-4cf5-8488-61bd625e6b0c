<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.dromara</groupId>
    <artifactId>ruoyi-common</artifactId>
    <version>5.4.0</version>
  </parent>
  <groupId>org.dromara</groupId>
  <artifactId>ruoyi-common-chat</artifactId>
  <version>5.4.0</version>
  <description>ruoyi-common-chat 模块</description>
  <properties>
    <okhttp.version>2.7.5</okhttp.version>
    <jtokkit.version>0.5.0</jtokkit.version>
    <azure.version>1.0.0-beta.12</azure.version>
    <chatglm.version>release-V4-2.3.0</chatglm.version>
    <retrofit2.version>2.9.0</retrofit2.version>
    <langchain4j.version>1.0.1</langchain4j.version>
  </properties>
  <dependencies>
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
      <version>5.8.39</version>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>ruoyi-common-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-websocket</artifactId>
      <version>3.5.0</version>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>ruoyi-common-json</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>ruoyi-common-redis</artifactId>
    </dependency>
    <dependency>
      <groupId>dev.langchain4j</groupId>
      <artifactId>langchain4j-open-ai-spring-boot-starter</artifactId>
      <version>1.0.0-beta3</version>
    </dependency>
    <dependency>
      <groupId>dev.langchain4j</groupId>
      <artifactId>langchain4j-core</artifactId>
      <version>${langchain4j.version}</version>
    </dependency>
    <dependency>
      <groupId>dev.langchain4j</groupId>
      <artifactId>langchain4j-open-ai</artifactId>
      <version>${langchain4j.version}</version>
    </dependency>
    <dependency>
      <groupId>dev.langchain4j</groupId>
      <artifactId>langchain4j</artifactId>
      <version>${langchain4j.version}</version>
    </dependency>
    <dependency>
      <groupId>dev.langchain4j</groupId>
      <artifactId>langchain4j-ollama</artifactId>
      <version>1.0.1-beta6</version>
      <exclusions>
        <exclusion>
          <groupId>dev.langchain4j</groupId>
          <artifactId>langchain4j-http-client-jdk</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>dev.langchain4j</groupId>
      <artifactId>langchain4j-community-dashscope</artifactId>
      <version>1.0.1-beta6</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-json</artifactId>
    </dependency>
  </dependencies>
</project>
