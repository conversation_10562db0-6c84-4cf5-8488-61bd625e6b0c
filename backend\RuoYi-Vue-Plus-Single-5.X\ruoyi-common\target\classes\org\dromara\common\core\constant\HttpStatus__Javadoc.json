{"doc": " 返回状态码\n\n <AUTHOR>\n", "fields": [{"name": "SUCCESS", "doc": " 操作成功\n"}, {"name": "CREATED", "doc": " 对象创建成功\n"}, {"name": "ACCEPTED", "doc": " 请求已经被接受\n"}, {"name": "NO_CONTENT", "doc": " 操作已经执行成功，但是没有返回数据\n"}, {"name": "MOVED_PERM", "doc": " 资源已被移除\n"}, {"name": "SEE_OTHER", "doc": " 重定向\n"}, {"name": "NOT_MODIFIED", "doc": " 资源没有被修改\n"}, {"name": "BAD_REQUEST", "doc": " 参数列表错误（缺少，格式不匹配）\n"}, {"name": "UNAUTHORIZED", "doc": " 未授权\n"}, {"name": "FORBIDDEN", "doc": " 访问受限，授权过期\n"}, {"name": "NOT_FOUND", "doc": " 资源，服务未找到\n"}, {"name": "BAD_METHOD", "doc": " 不允许的http方法\n"}, {"name": "CONFLICT", "doc": " 资源冲突，或者资源被锁\n"}, {"name": "UNSUPPORTED_TYPE", "doc": " 不支持的数据，媒体类型\n"}, {"name": "ERROR", "doc": " 系统内部错误\n"}, {"name": "NOT_IMPLEMENTED", "doc": " 接口未实现\n"}, {"name": "WARN", "doc": " 系统警告消息\n"}], "enumConstants": [], "methods": [], "constructors": []}