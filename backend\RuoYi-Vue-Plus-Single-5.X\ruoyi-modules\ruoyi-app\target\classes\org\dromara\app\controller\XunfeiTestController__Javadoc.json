{"doc": "\n 讯飞AI测试控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "testChat", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 测试星火大模型聊天（非流式）\r\n"}, {"name": "testChatStream", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 测试星火大模型流式聊天\r\n"}, {"name": "getConfig", "paramTypes": [], "doc": "\n 测试配置信息\r\n"}, {"name": "healthCheck", "paramTypes": [], "doc": "\n 健康检查\r\n"}], "constructors": []}