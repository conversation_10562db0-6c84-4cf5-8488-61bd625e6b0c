<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.AssessmentQuestionMapper">

    <resultMap type="org.dromara.app.domain.AssessmentQuestion" id="AssessmentQuestionResult">
        <result property="questionId" column="question_id"/>
        <result property="questionCode" column="question_code"/>
        <result property="questionType" column="question_type"/>
        <result property="category" column="category"/>
        <result property="questionContent" column="question_content"/>
        <result property="minValue" column="min_value"/>
        <result property="maxValue" column="max_value"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <resultMap type="org.dromara.app.domain.AssessmentQuestion" id="AssessmentQuestionWithOptionsResult"
               extends="AssessmentQuestionResult">
        <collection property="options" ofType="org.dromara.app.domain.AssessmentQuestionOption">
            <result property="optionId" column="option_id"/>
            <result property="questionId" column="question_id"/>
            <result property="optionCode" column="option_code"/>
            <result property="optionText" column="option_text"/>
            <result property="optionScore" column="option_score"/>
            <result property="sortOrder" column="option_sort_order"/>
        </collection>
    </resultMap>

    <sql id="selectAssessmentQuestionVo">
        select question_id,
               question_code,
               question_type,
               category,
               question_content,
               min_value,
               max_value,
               sort_order,
               status,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from assessment_question
    </sql>

    <sql id="selectAssessmentQuestionWithOptionsVo">
        select q.question_id,
               q.question_code,
               q.question_type,
               q.category,
               q.question_content,
               q.min_value,
               q.max_value,
               q.sort_order,
               q.status,
               q.create_by,
               q.create_time,
               q.update_by,
               q.update_time,
               q.remark,
               o.option_id,
               o.option_code,
               o.option_text,
               o.option_score,
               o.sort_order as option_sort_order
        from assessment_question q
                 left join assessment_question_option o on q.question_id = o.question_id
    </sql>

    <select id="selectQuestionsWithOptions" resultMap="AssessmentQuestionWithOptionsResult">
        <include refid="selectAssessmentQuestionWithOptionsVo"/>
        where q.status = '0'
        order by q.sort_order, o.sort_order
    </select>

    <select id="selectQuestionsWithOptionsByStatus" resultMap="AssessmentQuestionWithOptionsResult">
        <include refid="selectAssessmentQuestionWithOptionsVo"/>
        where q.status = #{status}
        order by q.sort_order, o.sort_order
    </select>

    <select id="selectQuestionWithOptionsById" resultMap="AssessmentQuestionWithOptionsResult">
        <include refid="selectAssessmentQuestionWithOptionsVo"/>
        where q.question_id = #{questionId}
        order by o.sort_order
    </select>

    <select id="selectQuestionWithOptionsByCode" resultMap="AssessmentQuestionWithOptionsResult">
        <include refid="selectAssessmentQuestionWithOptionsVo"/>
        where q.question_code = #{questionCode}
        order by o.sort_order
    </select>

</mapper>
