{"doc": " 讯飞数字人WebSocket连接管理服务\n\n <AUTHOR>\n @date 2025-07-20\n", "fields": [{"name": "messageHandler", "doc": " -- SETTER --\n  设置消息处理器\n\n @param messageHandler 消息处理器\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "doc": " -- SETTER --\n  设置错误处理器\n\n @param errorHandler 错误处理器\n"}], "enumConstants": [], "methods": [{"name": "connect", "paramTypes": ["java.lang.String", "long"], "doc": " 建立WebSocket连接\n\n @param requestUrl 请求URL（已包含认证信息）\n @param timeout    连接超时时间（秒）\n @return 是否连接成功\n"}, {"name": "sendMessage", "paramTypes": ["com.alibaba.fastjson.JSONObject"], "doc": " 发送消息\n\n @param request 请求消息\n @return 是否发送成功\n"}, {"name": "startAndWait", "paramTypes": ["com.alibaba.fastjson.JSONObject", "long"], "doc": " 发送启动请求并等待响应\n\n @param request 启动请求\n @param timeout 超时时间（秒）\n @return 是否启动成功\n"}, {"name": "close", "paramTypes": [], "doc": " 关闭连接\n"}, {"name": "isConnected", "paramTypes": [], "doc": " 检查连接状态\n\n @return 是否已连接\n"}, {"name": "buildWebSocketListener", "paramTypes": [], "doc": " 构建WebSocket监听器\n"}, {"name": "extractStreamUrl", "paramTypes": ["com.alibaba.fastjson.JSONObject"], "doc": " 从WebSocket消息中提取推流地址\n"}, {"name": "extractFromAvatar", "paramTypes": ["com.alibaba.fastjson.JSONObject"], "doc": " 从avatar字段中提取推流地址\n"}, {"name": "extractFromPayload", "paramTypes": ["com.alibaba.fastjson.JSONObject"], "doc": " 从payload根级别提取推流地址\n"}, {"name": "extractFromOtherFields", "paramTypes": ["com.alibaba.fastjson.JSONObject"], "doc": " 从其他可能的字段中提取推流地址\n"}, {"name": "notifyStreamUrlFound", "paramTypes": [], "doc": " 通知推流地址已找到\n"}], "constructors": []}